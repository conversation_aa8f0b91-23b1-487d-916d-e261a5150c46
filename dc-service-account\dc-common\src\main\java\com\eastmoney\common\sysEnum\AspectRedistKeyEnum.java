package com.eastmoney.common.sysEnum;

/**
 * <AUTHOR>
 * @description 切面Redis缓存Key
 * @date 2025/6/16 16:07
 */
public enum AspectRedistKeyEnum {

    FUND_IA_ACCOUNT("'jzjy_fundiafundaccount_' + #params.get('fundId')", 1800, "用于判断是否为基金投顾账号的缓存"),
    FUND_IA_ASSET("'jzjy_fundiafundasset_' + #params.get('fundId')", 600, "基金投顾柜台资产缓存（资金资产+基金资产）"),
    CLEAR_POSITION("'jzjy_clearposition_' + #params.get('fundId') + '_' + #params.get('unit')+ '_' + #params.get('profitFlag')", null, "清仓数据缓存（按照股票归总）"),
    POSITION_SECTION("'jzjy_positionsection_' + #params.get('fundId') + '_' + #params.get('unit')", null, "区间清仓数据缓存"),
    PROFIT_RATE_CONTRAST("'jzjy_profitratecontrast_' + #unit", null, "收益率参照表缓存"),
    PROFIT_SECTION("'jzjy_profitsection'+'_'+#params.get('fundId') + '_' + #params.get('unit')", null, "区间收益缓存"),
    PROFIT_RATE_SECTION("'jzjy_profitratesection_' + #params.get('fundId') + '_' + #params.get('unit')", null, "区间收益率缓存"),
    T_PROFIT_SECTION("'jzjy_tprofitsection_' + #params.get('fundId') + '_' + #params.get('unit')", null, "区间T收益缓存"),
    FUND_ASSET("'jzjy_fundasset_'+ #fundId +'_'+ #bizDate", null, "期初可用资金缓存"),
    STK_ASSET("'jzjy_stkasset_'+ #fundId + '_' + #bizDate", null, "持仓缓存"),
    YEAR_BILL("'jzjy_yearbill_' + #fundId + '_' + #indexKey", null, "年账单缓存"),
    BUSINESS_TASK_STATUS("'jzjy_businesstaskstatus_' + #businessTaskHandle + '_' + #serverId", null, "清算状态缓存"),
    ;

    private final String key;
    private final Integer expireSeconds;
    private final String desc;

    AspectRedistKeyEnum(String key, Integer expireSeconds, String desc) {
        this.key = key;
        this.expireSeconds = expireSeconds;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public Integer getExpireSeconds() {
        return expireSeconds;
    }

    public String getDesc() {
        return desc;
    }
}
