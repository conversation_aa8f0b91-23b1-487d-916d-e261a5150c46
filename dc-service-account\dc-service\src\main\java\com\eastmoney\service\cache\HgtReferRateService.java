package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.BusinessDictDao;
import com.eastmoney.accessor.dao.sqlserver.OggHgtReferRateDao;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.BusinessDict;
import com.eastmoney.common.entity.HgtReferRate;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2021/2/3-13:40.
 *
 * <AUTHOR>
 */
@Service
public class HgtReferRateService {
    private static Logger LOG = LoggerFactory.getLogger(HgtReferRateService.class);

    // 上一日结算汇率
    @Resource(name = "hgtReferRateCache")
    private LoadingCache<String, Optional<Double>> hgtReferRateCache;
    // 当日最新结算汇率
    @Resource(name = "oggHgtReferRateCache")
    private LoadingCache<String, Optional<HgtReferRate>> oggHgtReferRateCache;

    @Resource(name = "businessDictDao")
    private BusinessDictDao businessDictDao;
    @Autowired
    private OggHgtReferRateDao oggHgtReferRateDao;
    @Autowired
    private CoreConfigService coreConfigService;

    @Bean(name = "hgtReferRateCache")
    public LoadingCache<String, Optional<Double>> hgtReferRateCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<Double>>() {
                    @Override
                    public Optional<Double> load(String key) throws Exception {
                        Double refereRate = null;
                        try {
                            Map<String, Object> param = new HashMap<>(4);
                            param.put("serverId", -1);
                            param.put("dictCode", "ggtReferRate");
                            param.put("dictKey", key);
                            // 查询上一日结算汇率
                            List<BusinessDict> hgtReferRateList = businessDictDao.query(param);
                            if (!CollectionUtils.isEmpty(hgtReferRateList)) {
                                BusinessDict businessDict = hgtReferRateList.get(0);
                                if (businessDict != null) {
                                    refereRate = Double.valueOf(businessDict.getDictValue());
                                }
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(refereRate);
                    }
                });
    }

    public Double getBuySettRateService(String market) {
        try {
            if(StringUtils.isEmpty(market)){
                market = "5";
            }
            return hgtReferRateCache.get(market + "-buySettRate").orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取沪港通汇率失败", e);
        }
        return null;
    }

    public Double getSellSettRateService(String market) {
        try {
            if(StringUtils.isEmpty(market)){
                market = "5";
            }
            return hgtReferRateCache.get(market + "-sellSettRate").orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取沪港通汇率失败", e);
        }
        return null;
    }

    @Bean(name = "oggHgtReferRateCache")
    public LoadingCache<String, Optional<HgtReferRate>> oggHgtReferRateCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(20)
                .refreshAfterWrite(30, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<HgtReferRate>>() {
                           @Override
                           public Optional<HgtReferRate> load(String key) throws Exception {
                               HgtReferRate hgtReferRate = null;
                               try {
                                   // serverId-market
                                   String[] keys = key.split("-");
                                   if (keys.length == 2) {
                                       Map<String, Object> param = new HashMap<>(4);
                                       param.put("serverId", keys[0]);
                                       param.put("market", keys[1]);
                                       // 查询柜台备库结算汇率
                                       hgtReferRate = oggHgtReferRateDao.getHgtReferRate(param);
                                   }
                               } catch (Exception ex) {
                                   LOG.error(ex.getMessage(), ex);
                               }
                               return Optional.ofNullable(hgtReferRate);
                           }
                       }
                );
    }


    /**
     * 查询柜台备库买卖结算汇率
     * @param
     * @param market
     * @return
     */
    public HgtReferRate getOggHgtReferRateService(Long fundId, String market) {
        try {
            if(StringUtils.isEmpty(market)){
                market = "5";
            }
            Integer serverId = coreConfigService.getServerId(fundId);
            if (serverId == -1) {
                serverId = 1;
            }
            HgtReferRate hgtReferRate = oggHgtReferRateCache.get(String.join("-", serverId.toString(), market)).orElse(null);
            if(Objects.nonNull(hgtReferRate)){
                return hgtReferRate;
            }
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取柜台备库-沪港通汇率失败", e);
        }
        return null;
    }
}
