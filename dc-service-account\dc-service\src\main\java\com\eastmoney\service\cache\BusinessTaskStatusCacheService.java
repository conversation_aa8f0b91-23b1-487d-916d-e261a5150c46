package com.eastmoney.service.cache;

import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.BusinessTaskStatusDO;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.BusinessTaskStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("businessTaskStatusCacheService")
public class BusinessTaskStatusCacheService {

    @Autowired
    private CoreConfigService coreConfigService;

    @Autowired
    private BusinessTaskStatusService businessTaskStatusService;

    public BusinessTaskStatusDO getBusinessTaskStatus(Map<String, Object> dealParams, String businessTaskHandle) {
        Long fundId = CommonUtil.convert(dealParams, "fundId", Long.class);
        Integer serverId = coreConfigService.getServerId(fundId);
        return businessTaskStatusService.getBusinessTaskStatus(serverId, businessTaskHandle);
    }


}
