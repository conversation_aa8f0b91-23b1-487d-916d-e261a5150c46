package com.eastmoney.accessor.datasource.enhance;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * @program: LoadBalanceDruidDataSource
 * @description:
 * @author: <EMAIL>
 * @create: 2022/11/2
 */
public class LoadBalanceDruidDataSource extends DruidDataSource {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoadBalanceDruidDataSource.class);

    /**
     * 使用,作为多个负载服务器的分隔符
     */
    private static final String SERVER_SPLIT = ",";

    /**
     * 左括号
     */
    private static final String LEFT_PARENTHESIS = "{";

    /**
     * 右括号
     */
    private static final String RIGHT_PARENTHESIS = "}";

    /**
     * 多数据源
     */
    private LinkedBlockingDeque<DruidDataSource> druidDataSources = null;

    /**
     * 多数据源URL
     */
    private List<String> urlList = new CopyOnWriteArrayList<>();

    @Override
    public void setUrl(String jdbcUrl) {
        if (jdbcUrl.indexOf(LEFT_PARENTHESIS) > 0 && jdbcUrl.indexOf(RIGHT_PARENTHESIS) > 0) {
            String jdbcPrefix = jdbcUrl.substring(0, jdbcUrl.indexOf(LEFT_PARENTHESIS));
            String jdbcSuffix = jdbcUrl.substring(jdbcUrl.indexOf(RIGHT_PARENTHESIS) + 1);

            String serverPorts =
                jdbcUrl.substring(jdbcUrl.indexOf(LEFT_PARENTHESIS) + 1, jdbcUrl.indexOf(RIGHT_PARENTHESIS));
            String[] serverPortArr = serverPorts.split(SERVER_SPLIT);

            for (String serverPort : serverPortArr) {
                String url = jdbcPrefix + serverPort + jdbcSuffix;
                urlList.add(url);
            }
        } else {
            urlList.add(jdbcUrl);
        }
        super.setUrl(jdbcUrl);
    }

    @Override
    public DruidPooledConnection getConnection() throws SQLException {
        DruidPooledConnection connection = getConnection(urlList.size());
        if (connection == null) {
            throw new SQLException(String.format("not active datasource, url {%s}", super.getUrl()));
        }
        return connection;
    }

    /**
     * 获取连接
     *
     * @param retryCount
     * @return
     */
    private DruidPooledConnection getConnection(int retryCount) throws SQLException {
        DruidPooledConnection connection = null;
        initMultipleDataSources();
        if (retryCount > 0) {
            try {
                DruidDataSource druidDataSource = druidDataSources.takeFirst();
                druidDataSources.putLast(druidDataSource);
                connection = druidDataSource.getConnection();
            } catch (SQLException sqlException) {
                LOGGER.error("从数据源{}获取连接失败，切换到下一数据源", druidDataSources.getLast().getUrl(), sqlException);
                connection = getConnection(--retryCount);
            } catch (InterruptedException interruptedException) {
                LOGGER.error(interruptedException.getMessage(), interruptedException);
            }
        }
        return connection;
    }

    /**
     * 初始化多数据源
     */
    private void initMultipleDataSources() {
        if (druidDataSources == null) {
            synchronized (this) {
                if (druidDataSources == null) {
                    druidDataSources = new LinkedBlockingDeque<>(urlList.size());
                    for (int index = 0; index < urlList.size(); index++) {
                        try {
                            String indexJdbcUrl = urlList.get(index);
                            DruidDataSource druidDataSource = (DruidDataSource)this.clone();
                            druidDataSource.setUrl(indexJdbcUrl);
                            druidDataSources.addLast(druidDataSource);
                        } catch (CloneNotSupportedException e) {
                            LOGGER.error(e.getMessage(), e);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void close() {
        if (druidDataSources != null) {
            for (DruidDataSource druidDataSource : druidDataSources) {
                if (!druidDataSource.isClosed()) {
                    druidDataSource.close();
                }
            }
        }
    }
}
