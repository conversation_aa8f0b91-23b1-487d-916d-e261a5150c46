package com.eastmoney.accessor.service;

import com.eastmoney.common.entity.PosDBItem;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
public interface FundStockInnovateService {
    List query(Map<String, Object> params);


    /**
     * 处理存过返回集合
     *
     * @param
     * @param
     */
    PosDBItem getDBResultInfo(Map params);

    PosDBItem getDBResultInfoOld(Map params);
}
