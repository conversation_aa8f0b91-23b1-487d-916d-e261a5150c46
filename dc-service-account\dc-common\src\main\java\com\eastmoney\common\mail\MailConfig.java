package com.eastmoney.common.mail;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.Properties;

/**
 * Created by huanglucheng
 */
public class MailConfig {

    private final static Logger LOGGER = LoggerFactory.getLogger(MailConfig.class);
    public static String mailServerHost;
    public static String mailServerPort;
    public static String userName;
    public static String password;
    static {
        try{
            InputStream is = MailConfig.class.getClassLoader().getResourceAsStream("mail.properties");
            Properties props = new Properties();
            props.load(is);
            is.close();
            mailServerHost = props.getProperty("mailServerHost");
            mailServerPort = props.getProperty("mailServerPort");
            userName = props.getProperty("userName");
            password = props.getProperty("password");
        }catch (Exception e){
            e.printStackTrace();
            LOGGER.error("加载mail配置信息失败",e.getMessage());
        }
    }
}
