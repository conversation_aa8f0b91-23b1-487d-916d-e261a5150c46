package com.eastmoney.quote.mdstp.pull.serializer;


import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.LoggerFactory;

import java.nio.ByteOrder;


/**
 * Created by 1 on 15-7-7.
 */
public class RpcEncoder extends MessageToByteEncoder<Packet> {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(RpcEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, Packet packet, ByteBuf buf) throws Exception {
        try {
            buf = buf.order(ByteOrder.LITTLE_ENDIAN);
            buf.writeByte(packet.getType());
        }catch (Exception e){
            LOG.error(e.getMessage(), e);
        }
    }

}
