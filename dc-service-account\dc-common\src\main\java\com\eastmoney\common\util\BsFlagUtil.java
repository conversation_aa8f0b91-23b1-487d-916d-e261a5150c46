package com.eastmoney.common.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/10/28.
 */
public class BsFlagUtil {

    private static Map<String, String> bsFlagMap = new HashMap<>();
    private static String BSFLAG_BUY = "B";
    private static String BSFLAG_SELL = "S";
    static {
        bsFlagMap.put("B", "B");
        bsFlagMap.put("S", "S");
        bsFlagMap.put("0B", "B");
        bsFlagMap.put("0S", "S");
        bsFlagMap.put("0a", "B");
        bsFlagMap.put("a", "B");
        bsFlagMap.put("0b", "B");
        bsFlagMap.put("b", "B");
        bsFlagMap.put("0c", "B");
        bsFlagMap.put("c", "B");
        bsFlagMap.put("0d", "B");
        bsFlagMap.put("d", "B");
        bsFlagMap.put("0e", "B");
        bsFlagMap.put("e", "B");
        bsFlagMap.put("0f", "S");
        bsFlagMap.put("f", "S");
        bsFlagMap.put("0g", "S");
        bsFlagMap.put("g", "S");
        bsFlagMap.put("0h", "S");
        bsFlagMap.put("h", "S");
        bsFlagMap.put("0i", "S");
        bsFlagMap.put("i", "S");
        bsFlagMap.put("0j", "S");
        bsFlagMap.put("j", "S");
        bsFlagMap.put("0q", "B");
        bsFlagMap.put("q", "B");
        bsFlagMap.put("0r", "S");
        bsFlagMap.put("r", "S");
        bsFlagMap.put("1G", "B");
        bsFlagMap.put("1H", "S");
        bsFlagMap.put("1I", "B");
        bsFlagMap.put("1J", "S");
        bsFlagMap.put("2B", "B");
        bsFlagMap.put("2S", "S");
        bsFlagMap.put("3B", "B");
        bsFlagMap.put("03", "B");
        bsFlagMap.put("3", "B");
        bsFlagMap.put("04", "S");
        bsFlagMap.put("4", "S");
        bsFlagMap.put("3S", "S");
        bsFlagMap.put("4S", "S");
        bsFlagMap.put("n", "0n");
        bsFlagMap.put("p", "0p");
        bsFlagMap.put("3m", "B");
        bsFlagMap.put("3n", "S");
        bsFlagMap.put("2I", "B");
        bsFlagMap.put("2J", "S");
        bsFlagMap.put("1j", "B");
        bsFlagMap.put("1k", "S");
        bsFlagMap.put("4m", "B");
        bsFlagMap.put("4n", "S");
        bsFlagMap.put("4o", "B");
        bsFlagMap.put("4p", "S");
    }

    public static boolean isBuy(String bsFlag) {
        return BSFLAG_BUY.equals(bsFlagMap.get(bsFlag));
    }
    public static boolean isSell(String bsFlag) {
        return BSFLAG_SELL.equals(bsFlagMap.get(bsFlag));
    }
}
