package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.AssetNewDao;
import com.eastmoney.accessor.mapper.tidb.TiAssetNewMapper;

import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.AssetNew;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2016/3/17
 *
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("assetNewDao")
public class TiAssetNewDaoImpl extends BaseDao<TiAssetNewMapper, AssetNew, Integer> implements AssetNewDao {

}
