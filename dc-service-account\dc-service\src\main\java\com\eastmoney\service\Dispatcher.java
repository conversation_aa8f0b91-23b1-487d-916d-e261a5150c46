package com.eastmoney.service;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.JsonSerializeFilter;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.model.MethodMapping;
import com.eastmoney.common.serializer.AbstractSerializeFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;

/**
 * Created by sunyuncai on 2016/3/8.
 */
@Service
public class Dispatcher implements ApplicationContextAware,InitializingBean {
    private Logger log = LoggerFactory.getLogger(Dispatcher.class);
    private static final String FUNDID = "fundId";
    private ApplicationContext applicationContext;
    private final Map<String, MethodMapping> methodMappingMap = new HashMap<>();
    private final Map<String, AbstractSerializeFilter> serializeFilters = new HashMap<>();
    /**
     * 请求接口
     */
    public Object service(Map<String, Object> params) throws Exception {
        String requestPath = (String) params.get("funCode");
        MethodMapping methodMapping = methodMappingMap.get(requestPath);
        if (methodMapping == null) {
            throw new RuntimeException("找不到对应的请求方法 funCode=" + requestPath);
        }

        // fundId 数据类型转成Long，与数据库类型一致，与数据库类型不一致时数据库负载较大
        if(params.containsKey(FUNDID)) {
            try {
                Long fundId = CommonUtil.convert(params.get(FUNDID), Long.class);
                params.put(FUNDID, fundId);
            } catch (Exception ex) {
                log.error("fundId 转类型失败,requestPath:{},fundId:{}", requestPath, params.get("fundId"));
            }
        }

        Object result = methodMapping.getMethod().invoke(methodMapping.getActionBean(), params);
        if (result == null) {
            return Collections.emptyList();
        }
        if (result instanceof List) {
            return result;
        }
        List list = new ArrayList(1);
        list.add(result);
        return list;
    }

    public AbstractSerializeFilter getSerializeFilter(String requestPath) {
        return serializeFilters.get(requestPath);
    }

    public void init() {
        Map<String, Object> actionMap = applicationContext.getBeansWithAnnotation(Action.class);
        for (Map.Entry entry : actionMap.entrySet()) {
            // 获取并遍历该 Action 类中所有的方法
            Object actionBean = entry.getValue();
            Method[] actionMethods = actionBean.getClass().getDeclaredMethods();
            for (Method actionMethod : actionMethods) {
                if (actionMethod.isAnnotationPresent(FunCodeMapping.class)) {
                    MethodMapping methodMapping = new MethodMapping();
                    String requestPath = actionMethod.getAnnotation(FunCodeMapping.class).value();
                    methodMapping.setRequestPath(requestPath);
                    methodMapping.setActionBean(actionBean);
                    methodMapping.setMethod(actionMethod);
                    methodMappingMap.put(requestPath, methodMapping);

                    JsonSerializeFilter jsonSerializeFilter = actionMethod.getAnnotation(JsonSerializeFilter.class);
                    if (jsonSerializeFilter != null) {
                        serializeFilters.put(requestPath, applicationContext.getBean(jsonSerializeFilter.value()));
                    }
                }
            }
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
