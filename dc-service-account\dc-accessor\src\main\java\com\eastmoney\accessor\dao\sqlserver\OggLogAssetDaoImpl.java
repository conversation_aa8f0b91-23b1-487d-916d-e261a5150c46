package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggLogAssetMapper;
import com.eastmoney.common.entity.LogAsset;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2017/2/3
 */
@SqlServerTarget()
@Service
public class OggLogAssetDaoImpl implements OggLogAssetDao {
    @Autowired
    private OggLogAssetMapper tradeLogAssetMapper;

    /**
     * 银证划转、台账间划转记录
     * @param params
     * @return
     */
    @Override
    public List<LogAsset> selectTransferAmt(Map<String, Object> params) {
        return tradeLogAssetMapper.selectTransferAmt(params);
    }

    @Override
    public List<LogAsset> getRealTimeLogassetList(Map<String, Object> params) {
        return tradeLogAssetMapper.getRealTimeLogassetList(params);
    }

}
