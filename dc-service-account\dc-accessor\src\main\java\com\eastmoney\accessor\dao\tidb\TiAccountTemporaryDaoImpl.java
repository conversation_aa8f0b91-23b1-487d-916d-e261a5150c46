package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.AccountTemporaryDao;
import com.eastmoney.accessor.mapper.tidb.TiAccountTemporaryMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.AccountTemporary;
import com.eastmoney.common.entity.DayProfitBean;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by huachengqi on 2016/7/18.
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("accountTemporaryDao")
public class TiAccountTemporaryDaoImpl extends BaseDao<TiAccountTemporaryMapper, AccountTemporary, Integer> implements AccountTemporaryDao {

    @Override
    public DayProfitBean getDayProfitBean(long fundId) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        List<AccountTemporary> accountTemporaries = super.query(params);
        DayProfitBean dayProfitBean = null;
        if (accountTemporaries.size() > 0) {
            AccountTemporary accountTemporary = accountTemporaries.get(0);
            dayProfitBean = new DayProfitBean();
            dayProfitBean.setEuTime(accountTemporary.getEuTime());
            dayProfitBean.setBizDate(accountTemporary.getBizDate());
            dayProfitBean.setAsset(accountTemporary.getAsset());
            dayProfitBean.setProfitRate(accountTemporary.getProfitRate());
            dayProfitBean.setProfit(accountTemporary.getProfit());
            dayProfitBean.setShiftIn(accountTemporary.getShiftIn());
            dayProfitBean.setShiftOut(accountTemporary.getShiftOut());
            dayProfitBean.setOtcAsset(accountTemporary.getOtcAsset());
            dayProfitBean.setServerId(accountTemporary.getServerId());
        }
        return dayProfitBean;
    }
}
