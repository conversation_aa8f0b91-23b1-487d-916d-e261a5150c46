<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.otckg.KgdbOtcAssetDetailMapper">
    <select id="getRealTimeOtcAsset"  resultType="com.eastmoney.common.entity.OtcAssetDetail">
        SELECT
        sum( round(nvl(CASE WHEN nvl(C.INST_CLS, '@@') = '76' THEN A.INST_BAL / 100.0
        ELSE A.INST_BAL * NVL(B.LAST_HIGH_NET, 100000000) / 10000000000.0 END, 0)
        + nvl(A.UNSETT_QTY_ASSET / 100, 0)
        + nvl(A.UNSETT_FUND / 100, 0), 2)) otcAsset,
            C.CURRENCY
        FROM otcts.OTC_ASSET A
        INNER JOIN otcts.OTC_INST_EXT_INFO B
            ON A.INST_SNO = B.INST_SNO
        INNER JOIN otcts.OTC_INST_BASE_INFO C
            ON A.INST_SNO = C.INST_SNO
        WHERE NVL(C.CURRENCY, '@')= '0'
            AND A.CUACCT_CODE = #{fundId}
            AND TRANS_ACCT_TYPE = '0'
            AND C.INST_CLS <![CDATA[<>]]> '76'
            <if test="moneyType != null">
                AND C.CURRENCY =#{moneyType}
            </if>
        GROUP BY C.CURRENCY
    </select>
</mapper>