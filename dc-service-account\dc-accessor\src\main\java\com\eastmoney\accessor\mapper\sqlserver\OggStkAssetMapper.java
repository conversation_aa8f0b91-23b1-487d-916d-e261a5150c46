package com.eastmoney.accessor.mapper.sqlserver;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.StkAsset;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/8
 */
@Repository
public interface OggStkAssetMapper extends BaseMapper<StkAsset, Integer> {
    List<StkAsset> getRealTimePosition(Map<String, Object> map);

    List<StkAsset> selectOne(Map<String, Object> param);
}
