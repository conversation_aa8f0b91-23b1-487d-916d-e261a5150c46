package com.eastmoney.common.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;
import java.util.Objects;

/**
 * Created by sunyuncai on 2016/10/19.
 */
public class QryFund {
    @JSONField(serialize = false)
    public String custId;
    @JSONField(serialize = false)
    public String orgId;
    @JSONField(serialize = false)
    public String brhId;
    @JSONField(serialize = false)
    public String operWay;
    @JSONField(serialize = false)
    public String bankCode;
    @JSONField(serialize = false)
    public String status;
    @JSONField(serialize = false)
    public String fundName;
    @JSONField(serialize = false)
    public String idNo;
    @JSONField(serialize = false)
    public String idType;
    public long fundId;
    @JSONField(serialize = false)
    public int fundSeq;
    @JSONField(serialize = false)
    public Double fundFetch;
//    @JSONField(serialize = false)
    public Double fundBuySale;
    @JSONField(serialize = false)
    public Double fundBrkBuy; //报价回购终止买入差额, lIds, ********, 报价回购
//    @JSONField(serialize = false)
    public Double fundUncomeBuy;
//    @JSONField(serialize = false)
    public Double fundUncomeSale;
//    @JSONField(serialize = false)
    public Double fundBuy;
//    @JSONField(serialize = false)
    public Double fundSale;
//    @JSONField(serialize = false)
    public Double bbContamt;
    @JSONField(serialize = false)
    public Double fundLastBal;
    @JSONField(serialize = false)
    public Double fundAsset;
//    @JSONField(serialize = false)
    public Double creditSval; // 买券卖券资金
//    @JSONField(serialize = false)
    public Double openFundVal; //开放基金市值
//    @JSONField(serialize = false)
    public Double realRqAmt;   //实时融券资产, add by lIds, 20111102, ***********-ZXJT-005
    @JSONField(serialize = false)
    public Double fundLoan;//add by fengjy 20100612
//    @JSONField(serialize = false)
    public Double xjbFundVal; //SPB-2765,liyi,20150413,增加现金宝市值输出
    @JSONField(serialize = false)
    public Double fundPartAvl;    //二级资金可用，tanwj，港股通
    @JSONField(serialize = false)
	public Double avlAmt;

    public Double fundAssetAdjamt;//补偿资金资产
    public Double stkAssetAdjamt;//补偿证券资产

    public Double fundAll; // 总资产
    public Double fundMktVal; // 总市值

    public Double stkMktVal; // 证券市值
    public Double fundAvl; // 总可用
    public Double fundBal; // 资金余额
    public Double fundFrz; // 冻结金额
    public String moneyType;//货币种类
    public Double dayProfit; //当日盈亏
    public Double maxDraw;//可取金额
    public String fundType;
    public Double sumIncome;//持仓盈亏
    public Double otcAsset;//OTC资产
    public Double cashProVal;//现金宝资产---2020/3/26 存储过程更新，现金宝不再存在xjbFundVal字段
    @JSONField(serialize = false)
    public Double fundEffect7X24;
    @JSONField(serialize = false)
    public Double fundEffect7X24ShiftIn;//7*24银证转账转入
    @JSONField(serialize = false)
    public Double fundEffect7X24ShiftOut;//7*24银证转账转
    public Double fundIaAsset;//基金投顾总资产

    private List<PositionInfo> positionInfoList;
    private List<PositionInfo> stkInfoList;
    private List<LogAsset> logAssetList;

    // 持仓信息用于计算当日盈亏
    List<PositionInfo> profitStkInfoList;
    // 流水信息用于计算当日盈亏
    List<LogAsset> profitLogAssetList;

    /**
     * 仓位
     */
    private Double positionRate;
    /**
     * 总资产（包含otc）
     */
    private Double fundAllWithOtc;

    /**
     * 7*24银证转账（三合一存过）
     */
    @JSONField(serialize = false)
    public Double f7X24zzAmt;

    /**
     * 补偿资产 V1.0.9.0 （三合一存过）
     */
    public Double assetAdjAmt;

    public Double getFundAssetAdjamt() {
        return fundAssetAdjamt;
    }

    public void setFundAssetAdjamt(Double fundAssetAdjamt) {
        this.fundAssetAdjamt = fundAssetAdjamt;
    }

    public Double getStkAssetAdjamt() {
        return stkAssetAdjamt;
    }

    public void setStkAssetAdjamt(Double stkAssetAdjamt) {
        this.stkAssetAdjamt = stkAssetAdjamt;
    }

    public Double getSumIncome() {
        return sumIncome;
    }

    public void setSumIncome(Double sumIncome) {
        this.sumIncome = sumIncome;
    }

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public List<PositionInfo> getStkInfoList() {
        return stkInfoList;
    }

    public void setStkInfoList(List<PositionInfo> stkInfoList) {
        this.stkInfoList = stkInfoList;
    }

    public List<PositionInfo> getPositionInfoList() {
        return positionInfoList;
    }

    public void setPositionInfoList(List<PositionInfo> positionInfoList) {
        this.positionInfoList = positionInfoList;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId;
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public Double getAvlAmt() {
        return avlAmt;
    }

    public void setAvlAmt(Double avlAmt) {
        this.avlAmt = avlAmt;
    }

    public Double getDayProfit() {
        return dayProfit;
    }

    public void setDayProfit(Double dayProfit) {
        this.dayProfit = dayProfit;
    }

    public Double getMaxDraw() {
        return maxDraw;
    }

    public void setMaxDraw(Double maxDraw) {
        this.maxDraw = maxDraw;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public long getFundId() {
        return fundId;
    }

    public void setFundId(long fundId) {
        this.fundId = fundId;
    }

    public int getFundSeq() {
        return fundSeq;
    }

    public void setFundSeq(int fundSeq) {
        this.fundSeq = fundSeq;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getFundFetch() {
        return fundFetch;
    }

    public void setFundFetch(Double fundFetch) {
        this.fundFetch = fundFetch;
    }

    public Double getFundAll() {
        return fundAll;
    }

    public void setFundAll(Double fundAll) {
        this.fundAll = fundAll;
    }

    public Double getFundMktVal() {
        return fundMktVal;
    }

    public void setFundMktVal(Double fundMktVal) {
        this.fundMktVal = fundMktVal;
    }

    public Double getStkMktVal() {
        return stkMktVal;
    }

    public void setStkMktVal(Double stkMktVal) {
        this.stkMktVal = stkMktVal;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundBrkBuy() {
        return fundBrkBuy;
    }

    public void setFundBrkBuy(Double fundBrkBuy) {
        this.fundBrkBuy = fundBrkBuy;
    }

    public Double getFundUncomeBuy() {
        return fundUncomeBuy;
    }

    public void setFundUncomeBuy(Double fundUncomeBuy) {
        this.fundUncomeBuy = fundUncomeBuy;
    }

    public Double getFundUncomeSale() {
        return fundUncomeSale;
    }

    public void setFundUncomeSale(Double fundUncomeSale) {
        this.fundUncomeSale = fundUncomeSale;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getBbContamt() {
        return bbContamt;
    }

    public void setBbContamt(Double bbContamt) {
        this.bbContamt = bbContamt;
    }

    public Double getFundLastBal() {
        return fundLastBal;
    }

    public void setFundLastBal(Double fundLastBal) {
        this.fundLastBal = fundLastBal;
    }

    public Double getFundFrz() {
        return fundFrz;
    }

    public void setFundFrz(Double fundFrz) {
        this.fundFrz = fundFrz;
    }

    public Double getFundAsset() {
        return fundAsset;
    }

    public void setFundAsset(Double fundAsset) {
        this.fundAsset = fundAsset;
    }

    public Double getCreditSval() {
        return creditSval;
    }

    public void setCreditSval(Double creditSval) {
        this.creditSval = creditSval;
    }

    public Double getOpenFundVal() {
        return openFundVal;
    }

    public void setOpenFundVal(Double openFundVal) {
        this.openFundVal = openFundVal;
    }

    public Double getRealRqAmt() {
        return realRqAmt;
    }

    public void setRealRqAmt(Double realRqAmt) {
        this.realRqAmt = realRqAmt;
    }

    public Double getFundLoan() {
        return fundLoan;
    }

    public void setFundLoan(Double fundLoan) {
        this.fundLoan = fundLoan;
    }

    public Double getXjbFundVal() {
        return xjbFundVal;
    }

    public void setXjbFundVal(Double xjbFundVal) {
        this.xjbFundVal = xjbFundVal;
    }

    public Double getFundPartAvl() {
        return fundPartAvl;
    }

    public void setFundPartAvl(Double fundPartAvl) {
        this.fundPartAvl = fundPartAvl;
    }

    public Double getCashProVal() {
        return cashProVal;
    }

    public void setCashProVal(Double cashProVal) {
        this.cashProVal = cashProVal;
    }
    public Double getFundEffect7X24() {
        return fundEffect7X24;
    }

    public void setFundEffect7X24(Double fundEffect7X24) {
        this.fundEffect7X24 = fundEffect7X24;
    }

    public Double getFundEffect7X24ShiftIn() {
        return fundEffect7X24ShiftIn;
    }

    public void setFundEffect7X24ShiftIn(Double fundEffect7X24ShiftIn) {
        this.fundEffect7X24ShiftIn = fundEffect7X24ShiftIn;
    }

    public Double getFundEffect7X24ShiftOut() {
        return fundEffect7X24ShiftOut;
    }

    public void setFundEffect7X24ShiftOut(Double fundEffect7X24ShiftOut) {
        this.fundEffect7X24ShiftOut = fundEffect7X24ShiftOut;
    }

    public Double getPositionRate() {
        return positionRate;
    }

    public void setPositionRate(Double positionRate) {
        this.positionRate = positionRate;
    }

    public Double getFundAllWithOtc() {
        return fundAllWithOtc;
    }

    public void setFundAllWithOtc(Double fundAllWithOtc) {
        this.fundAllWithOtc = fundAllWithOtc;
    }

    public Double getFundIaAsset() {
        return fundIaAsset;
    }

    public void setFundIaAsset(Double fundIaAsset) {
        this.fundIaAsset = fundIaAsset;
    }

    public Double getF7X24zzAmt() {
        return f7X24zzAmt;
    }

    public void setF7X24zzAmt(Double f7X24zzAmt) {
        this.f7X24zzAmt = f7X24zzAmt;
    }

    public Double getAssetAdjAmt() {
        return assetAdjAmt;
    }

    public void setAssetAdjAmt(Double assetAdjAmt) {
        this.assetAdjAmt = assetAdjAmt;
    }

    public List<LogAsset> getLogAssetList() {
        return logAssetList;
    }

    public void setLogAssetList(List<LogAsset> logAssetList) {
        this.logAssetList = logAssetList;
    }

    public List<PositionInfo> getProfitStkInfoList() {
        return profitStkInfoList;
    }

    public void setProfitStkInfoList(List<PositionInfo> profitStkInfoList) {
        this.profitStkInfoList = profitStkInfoList;
    }

    public List<LogAsset> getProfitLogAssetList() {
        return profitLogAssetList;
    }

    public void setProfitLogAssetList(List<LogAsset> profitLogAssetList) {
        this.profitLogAssetList = profitLogAssetList;
    }

    public static QryFund of(PosDBItem object){
        QryFund item = new QryFund();
        if(Objects.isNull(object)){
            return item;
        }
        item.setCustId(object.getCustId());
        item.setOrgId(object.getOrgId());
        item.setFundId(object.getFundId());
        item.setMoneyType(object.getMoneyType());
        item.setFundBal(object.getFundBal());
        item.setFundAvl(object.getFundAvl());
        item.setFundSeq(object.getFundSeq());
        item.setBbContamt(object.getBbContamt());
        item.setFundLastBal(object.getFundLastBal());
        item.setFundFrz(object.getFundFrz());
        item.setFundBuy(object.getFundBuy());
        item.setFundSale(object.getFundSale());
        item.setFundLoan(object.getFundLoan());
        item.setXjbFundVal(object.getXjbFundVal());
        item.setF7X24zzAmt(object.getF7X24zzAmt());
        item.setAssetAdjAmt(object.getAssetAdjAmt());
        item.setMaxDraw(object.getMaxDraw());
        item.setFundAll(object.getFundAll());
        return item;
    }
}
