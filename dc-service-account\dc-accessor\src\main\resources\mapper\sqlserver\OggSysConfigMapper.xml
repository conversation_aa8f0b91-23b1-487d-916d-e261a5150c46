<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggSysConfigMapper">
    <sql id="All_Column">
        serverid,
        lastsysdate,
        sysdate,
        orderdate,
        status
    </sql>

    <select id="selectByCondition" resultType="as_sysConfig">
        SELECT
        <include refid="All_Column"/>
        FROM run.dbo.sysconfig with (nolock)
    </select>
</mapper>