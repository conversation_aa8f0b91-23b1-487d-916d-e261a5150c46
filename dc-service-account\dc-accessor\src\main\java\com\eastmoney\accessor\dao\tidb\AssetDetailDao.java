package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.AssetDetailDO;

import java.util.List;
import java.util.Map;

/**
 * 资产明细表dao层
 *
 * <AUTHOR>
 * @date 2024/5/13
 */
public interface AssetDetailDao extends IBaseDao<AssetDetailDO, Long> {
    /**
     * 查询期初可用资金
     * @param params
     * @return
     */
    List<AssetDetailDO> getAllFundAsset(Map<String, Object> params);
}
