package com.eastmoney.accessor.dao.oracle;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateDayMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xiaoyongyong on 2016/7/19.
 * ProfitRateDayServiceImpl
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitRateDayDao")
public class ProfitRateDayDaoImpl extends BaseDao<ProfitRateDayMapper, ProfitRateDay, Long> implements ProfitRateDayDao {

    @Override
    public Double getSectionProfitRate(Long fundId, Integer startDate, Integer endDate) {
        Map<String,Object> param=new HashMap();
        param.put("fundId",fundId);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        return mapper.getSectionProfitRate(param);
    }
}
