package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @Project dc-service-account
 * @Description 概念板块------ 2023年账单新增表,由数据中心提供
 * <AUTHOR>
 * @Date 2023/11/17 14:03
 * @Version 1.0
 */
public class BdTradeBillConceptSectorsDO {
    /**
     * 抓住的热门概念板块个数
     */
    private Long conceptSectorsCnt;
    /**
     * 抓住的热门概念名称
     */
    private String conceptSectorsNames;
    /**
     * 抓住的热门概念中年内最大涨幅概念名称
     */
    private String conceptSectorsMost;
    /**
     * 抓住的热门概念中年内最大涨幅
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal conceptSectorsUpperc;

    public Long getConceptSectorsCnt() {
        return conceptSectorsCnt;
    }

    public void setConceptSectorsCnt(Long conceptSectorsCnt) {
        this.conceptSectorsCnt = conceptSectorsCnt;
    }

    public String getConceptSectorsNames() {
        return conceptSectorsNames;
    }

    public void setConceptSectorsNames(String conceptSectorsNames) {
        this.conceptSectorsNames = conceptSectorsNames;
    }

    public String getConceptSectorsMost() {
        return conceptSectorsMost;
    }

    public void setConceptSectorsMost(String conceptSectorsMost) {
        this.conceptSectorsMost = conceptSectorsMost;
    }

    public BigDecimal getConceptSectorsUpperc() {
        return conceptSectorsUpperc;
    }

    public void setConceptSectorsUpperc(BigDecimal conceptSectorsUpperc) {
        this.conceptSectorsUpperc = conceptSectorsUpperc;
    }
}
