package com.eastmoney.transport.client;

import com.eastmoney.transport.exception.RemotingException;
import com.eastmoney.transport.model.ServerInfo;
import com.eastmoney.transport.util.ServerXmlConfig;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class NettyClientHolder {
    private static org.slf4j.Logger LOG = LoggerFactory.getLogger(NettyClientHolder.class);
    private final static Thread thread = new Thread(new ReconnectTask(),"ReconnectTask");
    private static volatile boolean inited = false;
    private static final Map<String,List<NettyClient>> aliveGroupMap = new HashMap<>();
    private static final Map<String,List<NettyClient>> groupMap = new HashMap<>();
    private static Random random = new Random();
    private static AtomicInteger seq = new AtomicInteger(0);
    static {
        try {
            init();
            //断线重连线程
            startReconnectTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static ResponseFuture request(String groupName,String content) throws RemotingException {
        List<NettyClient> aliveNettyClientList = aliveGroupMap.get(groupName);
        int aliveNum = aliveNettyClientList.size();
        if (aliveNum == 0) {
            throw new RuntimeException("没有可用的服务器");
        }
        List<NettyClient> invalidNettyClientList = new ArrayList();
        int index = seq.getAndIncrement()%aliveNum;
        NettyClient client = aliveNettyClientList.get(index);
        if (!client.isConnected()) {
            invalidNettyClientList.add(aliveNettyClientList.get(index));
            for(int i=1;i<aliveNum;i++){
                client = aliveNettyClientList.get((index + i) % aliveNum);
                if (client.isConnected()) {
                    break;
                } else {
                    invalidNettyClientList.add(aliveNettyClientList.get(index + i));
                }
            }
        }
        for (NettyClient nc: invalidNettyClientList) {
            aliveNettyClientList.remove(nc);
        }
        if (!client.isConnected()) {
            throw new RuntimeException("服务器都已断开连接");
        }
        return client.request(content);
    }
    public static NettyClient getNettyClient(String group) throws Exception {
        List<NettyClient> aliveNettyClientList = aliveGroupMap.get(group);
        if (aliveNettyClientList == null) {
            throw new Exception("不存在服务组:" + group);
        }
        //计算存活的节点
        int aliveNum = 0;
        for (NettyClient nettyClient : aliveNettyClientList) {
            if (nettyClient.isConnected()) {
                aliveNum++;
            } else {
                //CopyOnWriteArrayList 可以直接remove
                aliveNettyClientList.remove(nettyClient);
            }
        }
        if (aliveNum == 0) {
            throw new Exception(group + "没有可用的服务器");
        }
        //随机获取
        return aliveNettyClientList.get(random.nextInt(aliveNum));
    }

    public static void init() throws Exception {
        Map<String, List<ServerInfo>> serverMap = ServerXmlConfig.SERVER_CONFIG_MAP;
        for (Map.Entry<String, List<ServerInfo>> entry : serverMap.entrySet()) {
            String groupName = entry.getKey();
            List<ServerInfo> serverInfos = entry.getValue();
            List<NettyClient> nettyClientList = new CopyOnWriteArrayList<>();
            List<NettyClient> aliveNettyClientList = new CopyOnWriteArrayList<>();
            for (ServerInfo serverInfo : serverInfos) {
                //创建各个服务的连接
                for(int i=0;i<serverInfo.getConnectNum();i++) {
                    try {
                        NettyClient nettyClient = new NettyClient(serverInfo.getIp(), serverInfo.getPort());
                        nettyClientList.add(nettyClient);
                        nettyClient.connect();
                        aliveNettyClientList.add(nettyClient);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            groupMap.put(groupName, nettyClientList);
            aliveGroupMap.put(groupName, aliveNettyClientList);
            NettyClientHolder.inited = true;
        }

    }

    public static void startReconnectTask() {
        //关掉自动连接
        thread.start();
    }

    private static class ReconnectTask implements Runnable {
        @Override
        public void run() {
            while (true) {
                for (Map.Entry<String, List<NettyClient>> entry : groupMap.entrySet()) {
                    List<NettyClient> nettyClientList = entry.getValue();
                    List<NettyClient> aliveNettyClientList = aliveGroupMap.get(entry.getKey());
                    for (NettyClient nettyClient : nettyClientList) {
                        try {
                            if (nettyClient != null) {
                                long currentTime = System.currentTimeMillis();
                                //如果连接已断开，且大于等于重连间隔
                                // && (currentTime - nettyClient.getLastConnectTime()) >= Constants.RECONNECT_INTERVAL*1000
                                if (!nettyClient.isConnected()) {
                                    nettyClient.connect();
                                    if (!aliveNettyClientList.contains(nettyClient)) {
                                        aliveNettyClientList.add(nettyClient);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            LOG.error(e.getMessage());
                        }
                    }
                }
                try {
                    Thread.sleep(5*1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
