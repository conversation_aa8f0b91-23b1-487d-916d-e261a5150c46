package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.ChoiceStock;

import java.util.List;


/**
 * <AUTHOR>
 *         Date：2016/11/3  9:53
 */
public interface TiChoiceStockMapper extends BaseMapper<ChoiceStock, Integer> {
    /**
     *
     * @return SECURITYCODE股票代码, TRADEMARKETCODE交易市场, SECURITYSHORTNAME股票名称
     */
    List<ChoiceStock> queryAllStock();

}
