package com.eastmoney.common.entity;


import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-30 16:40
 */
public class CustCalenderDO {

    /**
     * 逻辑主键
     */
    private Long eid;
    /**
     * 阳历日期
     */
    private Date scal;
    /**
     * 阴历日期
     */
    private String lcal;
    /**
     * 星期
     */
    private String week;
    /**
     * 节日
     */
    private String holiday;
    /**
     * 是否开市
     */
    private Long isom;
    /**
     * 星期开始日期
     */
    private Date weeks;
    /**
     * 星期结束日期
     */
    private Date weeke;
    /**
     * 月结束日期
     */
    private Date monthe;
    /**
     * 季度开始日期
     */
    private Date quarts;
    /**
     * 季度结束日期
     */
    private Date quarte;
    /**
     * 年开始日期
     */
    private Date years;
    /**
     * 数据入库时间
     */
    private Date eitime;
    /**
     * 数据更新时间
     */
    private Date eutime;
    /**
     * 当日最大交易日期
     */
    private Date maxTDay;
    /**
     * 当日的上一个交易日
     */
    private Date lastTDay;
    /**
     * 是否补班
     */
    private String isWorkday;
    /**
     * 假期
     */
    private String vacation;

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Date getScal() {
        return scal;
    }

    public void setScal(Date scal) {
        this.scal = scal;
    }

    public String getLcal() {
        return lcal;
    }

    public void setLcal(String lcal) {
        this.lcal = lcal;
    }

    public String getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week = week;
    }

    public String getHoliday() {
        return holiday;
    }

    public void setHoliday(String holiday) {
        this.holiday = holiday;
    }

    public Long getIsom() {
        return isom;
    }

    public void setIsom(Long isom) {
        this.isom = isom;
    }

    public Date getWeeks() {
        return weeks;
    }

    public void setWeeks(Date weeks) {
        this.weeks = weeks;
    }

    public Date getWeeke() {
        return weeke;
    }

    public void setWeeke(Date weeke) {
        this.weeke = weeke;
    }

    public Date getMonthe() {
        return monthe;
    }

    public void setMonthe(Date monthe) {
        this.monthe = monthe;
    }

    public Date getQuarts() {
        return quarts;
    }

    public void setQuarts(Date quarts) {
        this.quarts = quarts;
    }

    public Date getQuarte() {
        return quarte;
    }

    public void setQuarte(Date quarte) {
        this.quarte = quarte;
    }

    public Date getYears() {
        return years;
    }

    public void setYears(Date years) {
        this.years = years;
    }

    public Date getEitime() {
        return eitime;
    }

    public void setEitime(Date eitime) {
        this.eitime = eitime;
    }

    public Date getEutime() {
        return eutime;
    }

    public void setEutime(Date eutime) {
        this.eutime = eutime;
    }

    public Date getMaxTDay() {
        return maxTDay;
    }

    public void setMaxTDay(Date maxTDay) {
        this.maxTDay = maxTDay;
    }

    public Date getLastTDay() {
        return lastTDay;
    }

    public void setLastTDay(Date lastTDay) {
        this.lastTDay = lastTDay;
    }

    public String getIsWorkday() {
        return isWorkday;
    }

    public void setIsWorkday(String isWorkday) {
        this.isWorkday = isWorkday;
    }

    public String getVacation() {
        return vacation;
    }

    public void setVacation(String vacation) {
        this.vacation = vacation;
    }
}
