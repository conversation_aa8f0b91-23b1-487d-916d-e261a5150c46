<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.LogAssetMapper">

    <select id="getStkTradeList" resultType="as_logAsset">
        SELECT /*+ index(a,IDX_LOGASSET_0502_F)*/ a.STKCODE,
            CASE WHEN MARKET IN ('5', 'S') THEN a.CLEARDATE
                ELSE a.BIZDATE
            END AS BIZDATE,
            a.matchprice,a.MATCHQTY,a.matchamt,a.market,a.matchTime, a.bsflag
        FROM ATCenter.logasset a
        where
            digestid in (220000,220003,220012,220023,220024,220027,220028,220029,220031,220032,
                220033,220034,220049,221049, 220036,220037,221003,220035,221023,220006,221033,221034,
                220100,220101,221001,221002,221035,221101,221102,221024,220188,220189,
                -- 20210315 港股通新增digestid
                220094,220095,220098,220099)
            AND a.FUNDID = #{fundId}
            and a.MARKET = #{market}
            and a.bsflag is not null and a.bsflag != '  '
            AND (
                (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                    OR
                (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
            <if test="oldStkCode == null">
                AND a.STKCODE = #{stkCode}
            </if>
            <if test="oldStkCode != null">
                AND a.STKCODE in (#{stkCode},#{oldStkCode})
            </if>
        order by a.BIZDATE ASC
    </select>

    <select id="getStkShiftList" resultType="as_logAsset">
        select a.FUNDID,a.digestid,a.STKCODE,
            CASE WHEN MARKET IN ('5', 'S') THEN a.CLEARDATE
                ELSE a.BIZDATE
            END AS BIZDATE,
            a.MARKET, a.stkEffect,a.market,a.bsflag,a.matchAmt,b.DIGESTNAME
        from ATCenter.logasset a
        inner join ATCENTER.B_CAL_DIGEST b on a.digestid = b.digestid and a.stktype = b.stktype
        where b.CALINDEX = 'positionShareChange' and useFlag = '1'
          and a.digestid not in ('220000','221001','220049','221049','220188','220189',
                                -- 20210315 港股通新增digestid
                                '220094','220095','220098','220099')
            and a.FUNDID = #{fundId} and a.MARKET = #{market}
            AND (
                (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                    OR
                (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
            <if test="oldStkCode == null">
                AND a.STKCODE = #{stkCode}
            </if>
            <if test="oldStkCode != null">
                AND a.STKCODE in (#{stkCode},#{oldStkCode})
            </if>
        order by a.BIZDATE ASC
    </select>

    <select id="selectByCondition" resultType="as_logAsset">
        SELECT EID, BIZDATE, MONEYTYPE, SNO, STKCODE, SECUID, MATCHAMT, FUNDEFFECT, BSFLAG, MATCHPRICE, MATCHQTY,
        FEE_YHS, FEE_SXF, FEE_JYGF, FEE_GHF, FUNDID
        FROM ATCENTER.LogAsset
        <where>
            <if test="bizDate != null">
                AND BIZDATE = #{bizDate}
            </if>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
        </where>
    </select>

    <select id="getOtherDetailList" resultType="com.eastmoney.common.entity.LogAsset">
        SELECT d.* FROM (
        select c.*, rownum as rno from (
            SELECT /*+ index(a,IDX_LOGASSET_0502_F)*/ a.FUNDID,a.digestid,a.STKCODE,
                   TRIM(a.STKNAME) STKNAME,a.bizDate,a.MARKET, a.STKEFFECT,a.FUNDEFFECT,a.bsflag,a.sno
            FROM ATCENTER.LOGASSET a
            <where>
                a.MONEYTYPE = '0'
                AND a.BIZDATE BETWEEN #{startDate} and #{endDate}
                AND (
                    a.digestid in (
                        SELECT DISTINCT DIGESTID
                        from ATCENTER.B_CAL_DIGEST
                        where  CALINDEX = 'assetOther' and USEFLAG=1
                    )
                    OR
                    (
                        a.digestid in (
                            SELECT DISTINCT DIGESTID
                            from ATCENTER.B_CAL_DIGEST
                            where CALINDEX = 'assetShareIO'
                            and stktype in ('0','1','2','5','8','C','E','L','e','h','g','i','k','o','r','R','U','W','X','u')
                            and USEFLAG = 1
                        )
                        AND a.market in ('0', '1')
                    )
                    OR
                    (
                        a.digestid in (
                            SELECT DISTINCT DIGESTID
                            from ATCENTER.B_CAL_DIGEST
                            where CALINDEX = 'assetShareIO'
                            and (stktype = '0' or stktype = 'J')
                            and USEFLAG = 1
                        )
                        AND a.market = '6'
                    )
                )
                <if test="fundId != null">
                    AND a.FUNDID = #{fundId}
                </if>
            </where>
            <if test='sort != null'>
                ${sort}
            </if>
        ) c
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and ROWNUM <![CDATA[<=]]> #{pageSize} * #{pageNo}
        </if>
        ) d
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and d.rno <![CDATA[>]]> #{pageSize} * (#{pageNo} - 1)
        </if>
    </select>

    <sql id="Base_Column_List">
        EID,EITIME,EUTIME,RESEND_FLAG
        ,FUNDID,SERVERID,BIZDATE,MONEYTYPE,SNO
        ,STKCODE,SECUID,MATCHAMT,FUNDEFFECT,BSFLAG
        ,MATCHPRICE,MATCHQTY,FEE_YHS,FEE_SXF,FEE_JYGF,FEE_GHF
    </sql>

    <select id="getTradeShiftList" resultType="as_logAsset">
        select  * from (
        select t.* ,rownum as rno from(
        SELECT /*+ index(a,IDX_LOGASSET_0502_F)*/ a.STKCODE,a.DIGESTID,
            CASE WHEN MARKET IN ('5', 'S') THEN a.CLEARDATE
                ELSE a.BIZDATE
            END AS BIZDATE,
            a.MARKET,a.MATCHAMT,a.MATCHPRICE,a.MATCHQTY,a.MATCHTIME,a.ORDERTIME,b.DIGESTNAME,
        a.STKEFFECT,a.STKBAL, a.SNO
        FROM ATCENTER.LOGASSET a
        left join ATCENTER.B_CAL_DIGEST b on b.DIGESTID=a.DIGESTID and a.STKTYPE = b.STKTYPE
        where FUNDID = #{fundId}
        <if test="startDate != null and endDate != null">
            AND (
                (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                    OR
                (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND a.STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND a.STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            AND a.MARKET = #{market}
        </if>
        <if test="calIndexes != null">
            and   b.CALINDEX IN
            <foreach item="item" index="index" collection="calIndexes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stkTypes != null">
            and  b.STKTYPE IN
            <foreach item="item" index="index" collection="stkTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND b.useFlag = '1'
        order by a.BIZDATE desc,a.SNO desc
        ) t where 1 = 1
        <choose>
            <when test="pageSize != null and pageNo != null">
                and ROWNUM <![CDATA[<=]]> #{pageSize} * #{pageNo}
            </when>
            <when test="endIndex != null">
                and ROWNUM <![CDATA[<=]]> #{endIndex}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ) b where 1 = 1
        <choose>
            <when test="pageSize != null and pageNo != null">
                and b.rno  <![CDATA[>]]> #{pageSize} * (#{pageNo} - 1)
            </when>
            <when test="startIndex != null">
                and b.rno  <![CDATA[>]]> #{startIndex}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="getBSTradeList" resultType="as_logAsset">
        select * from (
        select t.* ,rownum as rno from(
        select /*+ index(a,IDX_LOGASSET_0502_F)*/
            a.DIGESTID,a.MATCHAMT,a.MATCHPRICE,a.MATCHQTY,
            CASE WHEN MARKET IN ('5', 'S') THEN a.CLEARDATE
                ELSE a.BIZDATE
            END AS BIZDATE
        FROM ATCENTER.LOGASSET a
        where FUNDID = #{fundId}
        <if test="startDate != null and endDate != null">
            AND (
            (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
            OR
            (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND a.STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND a.STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            AND a.MARKET = #{market}
        </if>
        <if test="digestIdList != null">
            and a.DIGESTID IN
            <foreach item="item" index="index" collection="digestIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.BIZDATE desc,a.SNO desc
        )t
      )b
        <where>
            <if test="maxSize != null">
                and b.rno <![CDATA[<=]]> #{maxSize}
            </if>
        </where>
    </select>

</mapper>