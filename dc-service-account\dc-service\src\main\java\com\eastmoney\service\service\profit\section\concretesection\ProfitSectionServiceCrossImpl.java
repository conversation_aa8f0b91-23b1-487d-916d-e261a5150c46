package com.eastmoney.service.service.profit.section.concretesection;

import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.profit.section.AbstractProfitSectionService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created on 2020/8/12-19:35.
 * 整合会有可能跨区间的时间片(W,M,Y)清算 - 实时 - 临时收益
 *
 * <AUTHOR>
 */

@Service("profitSectionServiceCross")
public class ProfitSectionServiceCrossImpl extends AbstractProfitSectionService {

    @Override
    protected void setSectionParams(Map<String, Object> params, AssetNew assetNew, SectionProfitBean sectionProfitSettle) {
        Integer startDate = sectionProfitSettle.getIndexDate();
        startDate = startDate < assetNew.getStartDate() ? assetNew.getStartDate() : startDate;
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        int normalStartDate = CommonUtil.getDateRange(unit).getStartDate();
        if (normalStartDate > startDate) {
            //跨周期
            startDate = normalStartDate;
            sectionProfitSettle.setProfit(0d);
            sectionProfitSettle.setProfitRate(0d);
        }
        params.put("startDate", startDate);
        params.put("accountBizDate", sectionProfitSettle.getBizDate());
        params.put("isProfitRateCalWindow", sectionProfitSettle.isProfitRateCalWindow());
    }
}
