package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.sqlserver.OggLogBankTranMapper;
import com.eastmoney.common.entity.LogBankTran;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MsLogBankTranDaoImpl
 * @Description
 * @date 2022/11/8
 */
@Service
@SqlServerTarget()
public class OggLogBankTranDaoImpl extends BaseDao<OggLogBankTranMapper, LogBankTran, Integer> implements OggLogBankTranDao {
    @Override
    public List<LogBankTran> selectBySysDate(Map<String, Object> params) {
        return getMapper().selectBySysDate(params);
    }

   @Override
   public Double selectFundIaAdjustFundAsset(Map<String, Object> params) {
        return getMapper().selectFundIaAdjustFundAsset(params);
   }
   
}
