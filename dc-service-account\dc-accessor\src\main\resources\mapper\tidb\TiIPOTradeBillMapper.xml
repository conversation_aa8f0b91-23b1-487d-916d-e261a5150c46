<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiIPOTradeBillMapper">

    <select id="selectYearBill" resultType="as_ipoTradeBill">
        SELECT
        IFNULL(STOCKNUM,0) STOCKNUM,
        IFNULL(BONDNUM,0) BONDNUM,
        IFNULL(PROFIT,0) TOTALPROFIT
        FROM ATCENTER.IPO_TRADE_BILL
        <where>
            <if test="indexKey!=null">
                AND INDEXKEY=#{indexKey}
            </if>
            <if test="fundId != null">
                AND FUNDID=#{fundId}
            </if>
        </where>
    </select>

</mapper>