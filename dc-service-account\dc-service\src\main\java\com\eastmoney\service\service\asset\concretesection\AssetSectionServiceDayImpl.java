package com.eastmoney.service.service.asset.concretesection;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.service.asset.AbstractAssetSectionService;
import com.eastmoney.service.service.asset.base.AssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 当日区间-资产变动
 * <p>
 * T日查询：
 * <p>
 * 1. T日交易日
 * *** 区间资产变动【T日清算完成】：上一交易日小于清算日期  期初资产（T-1日资产） 、期末资产（实时计算）、日收益（清算数据）、净流入（T日实时数据 + asset_new净流入）
 * *** 区间资产变动【T日未清算】:  上一交易日等于清算日期  期初资产（asset_new）、期末资产（实时计算）、日收益（实时计算）、净流入（T日实时数据）
 * *** 区间资产变动【T日跨天未清算】：（仅适用于盘前查询） 期初资产（实时资产扣除银证划转）、期末资产（实时资产）、日收益（不计算）、净流入（T日实时数据）
 * <p>
 * 2. T日非交易日
 * *** 区间资产变动【T-1日清算完成】：期初资产（实时资产扣除银证划转）、期末资产（实时资产）、日收益（不计算）、净流入（T日实时数据）
 * *** 区间资产变动【T-1日未清算】： 期初资产（实时资产扣除银证划转）、期末资产（实时资产）、日收益（不计算）、净流入（T日实时数据）
 * <p>
 * 7*24银证划转:
 * *** T日盘中 ~ T日切前：三合一存过返回
 * *** T日切 ~ T账户分析清算前：T日（run.dbo.logbanktran）、T+1（三合一存过返回）
 * *** T日账户分析清算后：T日（asset_new）、T+1（三合一存过返回）
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service("assetSectionServiceDay")
public class AssetSectionServiceDayImpl extends AbstractAssetSectionService {
    @Autowired
    protected AssetNewService assetNewService;
    @Resource(name = "assetServiceSettle")
    protected AssetService assetServiceSettle;
    @Autowired
    protected TradeDateDao tradeDateDao;

    public AssetSection getAssetSection(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }
        // 查询区间资产变动数据
        return getAssetSection(params, assetNew);
    }

    @Override
    protected AssetHis getAssetStart(Map<String, Object> params, AssetHis assetNew) {
        return null;
    }

    /**
     * 查询期初、期末资产
     * @param params
     * @param assetNew
     * @return
     */
    protected AssetSection getAssetSection(Map<String, Object> params, AssetNew assetNew) {
        Integer today = DateUtil.getCuryyyyMMddInteger();
        Integer preStartDate = Integer.valueOf(tradeDateDao.getPreMarketDay(today));
        Integer settleDate = assetNew.getBizDate();
        AssetHis assetStart = new AssetHis();
        AssetHis assetNewObj =  assetNew;

        // 查询期末实时资产
        params.put("accountBizDate", settleDate);
        AssetHis assetRealTime = assetServiceRealTime.getAsset(params);

        // 非交易日 或者 跨天未清算
        if (!tradeDateDao.isMarket(today) || preStartDate > settleDate) {
            assetStart = AssetHis.of(assetRealTime);
            // 期初资产=期末资产+银证流出-银证流入
            assetStart.setAsset(
                    ArithUtil.add(assetStart.getAsset(),
                    ArithUtil.sub(assetStart.getShiftOutTotal(), assetStart.getShiftInTotal()))
            );
            // 区间首日和T-1日资产相等
            assetStart.setShiftOutTotal(0d);
            assetStart.setShiftInTotal(0d);
            assetNewObj = assetStart;
            assetNewObj.setServerId(assetNew.getServerId());
        } else {
            // T日未清算
            if (Objects.equals(preStartDate, settleDate)) {
                assetStart = assetNew;
            } else if (Objects.equals(today, settleDate)) {
                // T日清算完成
                Map<String, Object> requestParams = new HashMap<>();
                requestParams.put("bizDate", preStartDate);
                requestParams.put("fundId", params.get("fundId"));
                assetStart = assetServiceSettle.getAsset(requestParams);
            }
        }
        assetStart = Objects.nonNull(assetStart) ? assetStart : new AssetHis();

        // 构造区间资产变动信息
        AssetSection assetSection = generateAssetSection(assetStart, assetRealTime, assetNewObj);


        return assetSection;
    }

}
