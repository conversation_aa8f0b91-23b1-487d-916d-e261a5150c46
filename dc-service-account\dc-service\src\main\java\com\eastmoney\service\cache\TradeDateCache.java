package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.TradeDate;
import com.eastmoney.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2017/7/19.
 */
@Service
public class TradeDateCache {
    private static SortedMap<Integer, Integer> TRADE_DATE_MAP = new TreeMap<>();
    private static final Logger LOG = LoggerFactory.getLogger(TradeDateCache.class);
    protected static final Integer START_DATE = 20150106;//账户分析起始日
    private static final Integer DELAY_TIMES = 3600;//1h刷新一次交易日缓存

    @Autowired
    private TradeDateDao tradeDateDao;

    @PostConstruct
    public void init() {
        ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("tradeDate-cache-flush-thread");
            return thread;
        });
        initTradeDateList(START_DATE);
        Runnable runnable = () -> initTradeDateList(Integer.valueOf(DateUtil.getCuryyyyMMdd()));
        scheduledExecutorService.scheduleWithFixedDelay(runnable, 600, DELAY_TIMES, TimeUnit.SECONDS);
    }

    /**
     * @param startDate 入参有两种情况，首次启动为20150106，后续同步为当前日期
     */
    public void initTradeDateList(Integer startDate) {
        try {
            long startTime = System.currentTimeMillis();
            Map<String, Object> params = new HashMap<>(2);
            if (startDate != null) {
                params.put("startDate", String.valueOf(startDate));
            }
            List<TradeDate> tradeDateList = tradeDateDao.getAllTradeDateList(params);
            int index = 1;
            if (CollectionUtils.isEmpty(tradeDateList)) {
                LOG.warn("刷新交易日缓存数据,交易日历表查询数据为空!");
                return;
            }
            //获取结果中最早的日期
            Integer startTradeDate = Integer.valueOf(tradeDateList.get(0).getTradeDate());
            //截取结果中过去的日期
            SortedMap<Integer, Integer> beforeStartDateMap = TRADE_DATE_MAP.subMap(START_DATE, startTradeDate);
            if (!CollectionUtils.isEmpty(beforeStartDateMap)) {
                //如果结果不为空，则说明是定时刷新逻辑，将index置为map中最后一条的index + 1
                index = beforeStartDateMap.get(beforeStartDateMap.lastKey()) + 1;
                TRADE_DATE_MAP = new TreeMap<>(beforeStartDateMap);
            }
            //将后续结果写入到beforStartDateMap
            for (TradeDate bizDate : tradeDateList) {
                TRADE_DATE_MAP.put(Integer.valueOf(bizDate.getTradeDate()), index);
                index++;
            }
            LOG.info("刷新交易日缓存数据完成,共{}条,耗时{}ms", TRADE_DATE_MAP.size(), System.currentTimeMillis() - startTime);
        } catch (Throwable e) {
            LOG.error("刷新交易日缓存数据报错!", e);
        }
    }

    public static boolean isTradeDate(Integer date) {
        return TRADE_DATE_MAP.containsKey(date);
    }

    public static boolean isTradeDate() {
        return TRADE_DATE_MAP.containsKey(Integer.parseInt(DateUtil.getCuryyyyMMdd()));
    }

    /**
     * 获取两个交易日之间的间隔交易日天数，入参必须为交易日
     * 包括第一天和最后一天
     * eg 20220725 20220726 之间交易日天数为 2
     *
     * @param startDate 起始交易日
     * @param endDate   终止交易日
     * @return
     */
    public Integer getIntervalTradeDates(Integer startDate, Integer endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }

        if (!tradeDateDao.isMarket(startDate)) {
            startDate = Integer.valueOf(tradeDateDao.getPreMarketDay(startDate));
        }

        if (!tradeDateDao.isMarket(endDate)) {
            endDate = Integer.valueOf(tradeDateDao.getPreMarketDay(endDate));
        }

        Integer endIndex = TRADE_DATE_MAP.get(endDate);
        Integer startIndex = TRADE_DATE_MAP.get(startDate);
        if (endIndex != null && startIndex != null) {
            return endIndex - startIndex + 1;
        }
        return 0;

    }


}
