package com.eastmoney.common.entity;

/**
 * Created by sunyuncai on 2016/10/28.
 */
public class DayStkProfit {
    public String market;/*交易市场*/
    public String stkCode;/*证券代码*/
    public String moneyType;/*货币代码*/
    public long stkQty; /*股票数量*/
    public double dwPrice; //最新价
    public double dwClose; //昨日收盘价
    public long buyCount;//当日日买入该股总数量
    public double buyAmt; //当日买入该股总成本
    public long sellCount;//当日卖出该股数量
    public double sellAmt; ////当日卖出该股总金额
    public double profit; //单日盈亏
    /**
     * 盈亏比例
     * 当日盈亏/（期初市值+当日买入金额）
     */
    public double profitRate;
    public String stkType;//证券类型
    public double buyAmtWithIntr;//计算盈亏比例的买入金额包含利息

    public Double buySettRate;// T-1日买入结算汇率

    /**
     * 交易类型
     * 0 正常交易
     */
    public String trdId;

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public double getDwPrice() {
        return dwPrice;
    }

    public void setDwPrice(double dwPrice) {
        this.dwPrice = dwPrice;
    }

    public double getDwClose() {
        return dwClose;
    }

    public void setDwClose(double dwClose) {
        this.dwClose = dwClose;
    }

    public double getProfit() {
        return profit;
    }

    public void setProfit(double profit) {
        this.profit = profit;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public long getStkQty() {
        return stkQty;
    }

    public void setStkQty(long stkQty) {
        this.stkQty = stkQty;
    }

    public void setDwPrice(int dwPrice) {
        this.dwPrice = dwPrice;
    }

    public void setDwClose(int dwClose) {
        this.dwClose = dwClose;
    }

    public double getBuyAmt() {
        return buyAmt;
    }

    public void setBuyAmt(double buyAmt) {
        this.buyAmt = buyAmt;
    }

    public double getSellAmt() {
        return sellAmt;
    }

    public void setSellAmt(double sellAmt) {
        this.sellAmt = sellAmt;
    }

    public void setBuyCount(int buyCount) {
        this.buyCount = buyCount;
    }

    public void setSellCount(int sellCount) {
        this.sellCount = sellCount;
    }

    public long getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(long buyCount) {
        this.buyCount = buyCount;
    }

    public long getSellCount() {
        return sellCount;
    }

    public void setSellCount(long sellCount) {
        this.sellCount = sellCount;
    }

    public double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(double profitRate) {
        this.profitRate = profitRate;
    }

    public double getBuyAmtWithIntr() {
        return buyAmtWithIntr;
    }

    public void setBuyAmtWithIntr(double buyAmtWithIntr) {
        this.buyAmtWithIntr = buyAmtWithIntr;
    }

    public Double getBuySettRate() {
        return buySettRate;
    }

    public void setBuySettRate(Double buySettRate) {
        this.buySettRate = buySettRate;
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = trdId;
    }
}
