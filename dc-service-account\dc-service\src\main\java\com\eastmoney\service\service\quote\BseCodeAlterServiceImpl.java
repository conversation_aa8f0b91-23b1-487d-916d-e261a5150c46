package com.eastmoney.service.service.quote;


import com.eastmoney.accessor.dao.tidb.TiBseCodeAlterDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.BseCodeAlterDO;
import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.service.cache.NodeConfigService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 * @description
 * @date 2025/1/10 13:26
 */
@Service("bseCodeAlterService")
public class BseCodeAlterServiceImpl implements BseCodeAlterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BseCodeAlterServiceImpl.class);

    private static Map<String, BseCodeAlterDO> codeAlterMap = new HashMap<>();
    private static Map<String, BseCodeAlterDO> codeAlterReverseMap = new HashMap<>();

    @Autowired
    private TiBseCodeAlterDao tiBseCodeAlterDao;


    @Override
    public void doLoadInfo() {
        Map<String, BseCodeAlterDO> codeAlterTempMap = new HashMap<>();
        Map<String, BseCodeAlterDO> codeAlterReverseTempMap = new HashMap<>();
        List<BseCodeAlterDO> codeAlterList = tiBseCodeAlterDao.query(new HashMap<>());
        for(BseCodeAlterDO codeAlter : codeAlterList) {
            codeAlterTempMap.put(codeAlter.getMarket() + "-" + codeAlter.getStkCode(), codeAlter);
            codeAlterReverseTempMap.put(codeAlter.getMarket() + "-" + codeAlter.getCorResCode(), codeAlter);
        }
        codeAlterMap = codeAlterTempMap;
        codeAlterReverseMap = codeAlterReverseTempMap;

    }


    @Override
    public StkInfo transStkInfoCode(StkInfo stkInfo, boolean direction) {
        if(stkInfo == null || StringUtils.isEmpty(stkInfo.getStkCode()) || StringUtils.isEmpty(stkInfo.getMarket())) {
            return stkInfo;
        }
        String key = stkInfo.getMarket() + "-" + StringUtils.trim(stkInfo.getStkCode());
        if(codeAlterMap.containsKey(key) && direction) {
            //830 -> 920
            stkInfo.setStkCode(codeAlterMap.get(key).getCorResCode());
            return stkInfo;
        }
        if(codeAlterReverseMap.containsKey(key) && !direction) {
            //920 -> 830
            stkInfo.setStkCode(codeAlterReverseMap.get(key).getStkCode());
        }
        return stkInfo;
    }

    @Override
    public String getCodeAlter(String stkCode, String market) {
        if(MarketEnum.BJ_A.getValue().equals(market)) {
            BseCodeAlterDO bseCodeAlterDO = codeAlterMap.get(market + "-" + StringUtils.trim(stkCode));
            return bseCodeAlterDO == null ? stkCode : bseCodeAlterDO.getCorResCode();
        }
        return stkCode;
    }

    @Override
    public String getCodeAlterWithBizDate(String stkCode, String market, Integer date) {
        if(MarketEnum.BJ_A.getValue().equals(market)) {
            BseCodeAlterDO bseCodeAlterDO = codeAlterMap.get(market + "-" + StringUtils.trim(stkCode));
            if(bseCodeAlterDO != null && date.equals(bseCodeAlterDO.getBizDate())) {
                return bseCodeAlterDO.getCorResCode();
            }
        }
        return stkCode;
    }

    @Override
    public String getCodeAlterXsbAndBjs(String stkCode, String market) {
        //如果不为三板 和 北交所  直板返回
        if(!MarketEnum.STOCK_A.getValue().equals(market) && !MarketEnum.BJ_A.getValue().equals(market)) {
            return stkCode;
        }

        return getCodeAlter(stkCode, MarketEnum.BJ_A.getValue());
    }

    @Override
    public String getCodeAlterReverse(String stkCode, String market) {
        if(MarketEnum.BJ_A.getValue().equals(market)) {
            BseCodeAlterDO bseCodeAlterDO = codeAlterReverseMap.get(market + "-" + StringUtils.trim(stkCode));
            return bseCodeAlterDO == null ? stkCode : bseCodeAlterDO.getStkCode();
        }
       return stkCode;
    }

    @Override
    public String getCodeAlterReverseWithBizDate(String stkCode, String market, Integer date) {
        if(MarketEnum.BJ_A.getValue().equals(market)) {
            BseCodeAlterDO bseCodeAlterDO = codeAlterReverseMap.get(market + "-" + StringUtils.trim(stkCode));
            if(bseCodeAlterDO != null && date.equals(bseCodeAlterDO.getBizDate())) {
                return bseCodeAlterDO.getStkCode();
            }
        }
        return stkCode;
    }

    @Override
    public Map<String, Object> getCodeAlterParams(Map<String, Object> params) {
        String stkCode = (String) params.get("stkCode");
        String market = (String) params.get("market");
        if (StringUtils.isEmpty(stkCode) || StringUtils.isEmpty(market) || !Objects.equals(market, MarketEnum.BJ_A.getValue())) {
            return params;
        }
        stkCode = StringUtils.trim(stkCode);
        if(codeAlterMap.containsKey(market + "-" + stkCode)){
            List<Object> stkCodes = Arrays.asList(stkCode, codeAlterMap.get(market + "-" + stkCode).getCorResCode());
            params.put("stkCodeList", stkCodes);
        }
        if(codeAlterReverseMap.containsKey(market + "-" + stkCode)){
            List<Object> stkCodes = Arrays.asList(stkCode, codeAlterReverseMap.get(market + "-" + stkCode).getStkCode());
            params.put("stkCodeList", stkCodes);
        }
        return params;
    }


    @Override
    public String getCodeAlterOrReverse(String stkCode, String market) {
        if(StringUtils.isAnyBlank(stkCode, market) || !MarketEnum.BJ_A.getValue().equals(market)) {
            return stkCode;
        }
        if(codeAlterMap.containsKey(market + "-" + StringUtils.trim(stkCode))) {
            return codeAlterMap.get(market + "-" + StringUtils.trim(stkCode)).getCorResCode();
        }
        if(codeAlterReverseMap.containsKey(market + "-" + StringUtils.trim(stkCode))) {
            return codeAlterReverseMap.get(market + "-" + StringUtils.trim(stkCode)).getStkCode();
        }
        return stkCode;
    }
}
