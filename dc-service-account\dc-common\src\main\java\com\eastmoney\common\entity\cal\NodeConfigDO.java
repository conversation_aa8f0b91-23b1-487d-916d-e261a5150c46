package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

import java.io.Serializable;

/**
 * Created by robin on 2017/3/24.
 * 节点系统变量配置表bean
 * <AUTHOR>
 */
public class NodeConfigDO extends BaseEntity {

    private static final long serialVersionUID = 6918936059862877268L;

    /*配置级别:枚举值从com.eastmoney.common.enumerate.NodeConfigAccessEnum.getValue()
        相同的key配置项的level值越大优先级越高*/
    private String configLevel;

    private String nodeCode;//节点唯一标识，节点唯一标识,公共权限，则nodeCode为127.0.0.1:0

    private String key;//配置项的英文名称，

    private String name;//配置项的中文名称

    private String value;//配置项的值

    private String explain;//配置项的说明

    public String getConfigLevel() {
        return configLevel;
    }

    public void setConfigLevel(String configLevel) {
        this.configLevel = configLevel;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getExplain() {
        return explain;
    }

    public void setExplain(String explain) {
        this.explain = explain;
    }
}
