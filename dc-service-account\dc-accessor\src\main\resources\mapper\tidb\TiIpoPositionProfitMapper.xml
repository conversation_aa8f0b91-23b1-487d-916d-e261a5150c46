<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiIpoPositionProfitMapper">
    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.IPOPositionProfitDO">
        SELECT stkcode,stkname,market,calflag,TOTAL_BUY_AMT totalBuyAmt,profit,PROFIT_RATE profitRate
        FROM ATCENTER.IPO_POSITION_PROFIT use index (IPO_POSITION_PROFIT_FUNDID_IDX)
        <where>
            <if test="fundId != null">
                and FUNDID= #{fundId}
            </if>
            <if test="startDate != null and endDate != null">
                AND STARTDATE between #{startDate} and #{endDate}
            </if>
        </where>
    </select>
</mapper>