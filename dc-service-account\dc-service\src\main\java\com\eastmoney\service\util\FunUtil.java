package com.eastmoney.service.util;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by sunyuncai on 2016/3/10.
 */
public class FunUtil {

    public static Set<String> pushSet = new HashSet<>();

    static {
        pushSet.add(FunCodeConstants.GET_BANK_TRAN_REC);
        pushSet.add(FunCodeConstants.GET_ORDER_REC);
        pushSet.add(FunCodeConstants.GET_ORDER_REC_HGT);
        pushSet.add(FunCodeConstants.GET_TRADE_REC);
        pushSet.add(FunCodeConstants.GET_TRADE_REC_HGT);
        pushSet.add(FunCodeConstants.GET_BIND_CHANGE_LIST);
    }

    public static boolean isPushExist(String funCode) {
        return pushSet.contains(funCode);
    }

}
