package com.eastmoney.accessor.config;

import com.eastmoney.datacenter.redis.client.RedisProxy;
import com.eastmoney.datacenter.redis.client.common.model.RedisConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 20241010
 *
 * <AUTHOR> by longchen
 */
@ConfigurationProperties(prefix = "redis")
@Configuration
public class RedisClientConfig {
    private List<String> servers;
    private String password;
    private int maxTotal = 15;
    private int maxIdle = 15;
    private int minIdle = 5;
    private boolean testOnBorrow = false;
    private boolean testWhileIdle = true;

    public List<String> getServers() {
        return servers;
    }

    public void setServers(List<String> servers) {
        this.servers = servers;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getMaxTotal() {
        return maxTotal;
    }

    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public int getMaxIdle() {
        return maxIdle;
    }

    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(int minIdle) {
        this.minIdle = minIdle;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

    public boolean isTestWhileIdle() {
        return testWhileIdle;
    }

    public void setTestWhileIdle(boolean testWhileIdle) {
        this.testWhileIdle = testWhileIdle;
    }

    @Bean
    public RedisConfig redisConfig() {
        RedisConfig poolConfig = new RedisConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setTestOnBorrow(testOnBorrow);
        poolConfig.setTestWhileIdle(testWhileIdle);
        return poolConfig;
    }


    @Bean
    public RedisProxy redisProxy() {
        return new RedisProxy(redisConfig(), servers, password);
    }

}
