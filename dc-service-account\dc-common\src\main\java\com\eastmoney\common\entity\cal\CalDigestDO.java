package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

import java.util.Date;

/**
 * Created by wangchun
 * Date:2017/7/24 17:49.
 * 计算对应摘要表Bean
 */
public class CalDigestDO extends BaseEntityCal {
    private Long eid;//系统物理主键
    private Date eiTime;//数据入库时间
    private Date euTime;//数据修改时间
    private String calIndex;//计算指标
    private String stkType;//证券类型
    private String stkTypeName;//证券类型名称
    private Long digestId;//摘要ID
    private String digestName;//摘要名称
    private String remark;//备注
    private Long useFlag;//使用标记(0-不可使用;1-可以使用)

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Date getEiTime() {
        return eiTime;
    }

    public void setEiTime(Date eiTime) {
        this.eiTime = eiTime;
    }

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public String getCalIndex() {
        return calIndex;
    }

    public void setCalIndex(String calIndex) {
        this.calIndex = calIndex;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public String getStkTypeName() {
        return stkTypeName;
    }

    public void setStkTypeName(String stkTypeName) {
        this.stkTypeName = stkTypeName;
    }

    public Long getDigestId() {
        return digestId;
    }

    public void setDigestId(Long digestId) {
        this.digestId = digestId;
    }

    public String getDigestName() {
        return digestName;
    }

    public void setDigestName(String digestName) {
        this.digestName = digestName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(Long useFlag) {
        this.useFlag = useFlag;
    }
}
