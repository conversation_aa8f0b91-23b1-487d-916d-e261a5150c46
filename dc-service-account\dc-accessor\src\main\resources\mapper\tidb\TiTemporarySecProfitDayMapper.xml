<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiTemporarySecProfitDayMapper">

    <select id="getSecTemporaryProfitDay" resultType="as_secProfitDay">
        SELECT BIZDATE, MARKET, STKCODE, PROFIT, PROFITRATE, OPENVALUE
        from ATCENTER.SEC_TEMPORARY_PROFIT_DAY
        <where>
            <if test="fundId != null">
                fundid = #{fundId}
            </if>
            <if test="markets != null and markets.size()>0">
                and market in
                <foreach collection="markets" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>