package com.eastmoney.common.entity;

/**
 * <AUTHOR>
 * @description 920代码转换映射表
 * @date 2025/1/10 9:10
 */
public class BseCodeAlterDO extends BaseEntity {
    /**
     * 证券代码
     */
    private String stkCode;
    /**
     * 证券名称
     */
    private String stkName;
    /**
     * 证券市场
     */
    private String market;
    /**
     * 转换920代码
     */
    private String corResCode;
    /**
     * 转换920代码名称
     */
    private String corResCodeName;
    /**
     * 上市日期
     */
    private Integer listingDate;
    /**
     * 切换日期
     */
    private Integer bizDate;
    /**
     * 删除代码
     */
    private Integer del;

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getCorResCode() {
        return corResCode;
    }

    public void setCorResCode(String corResCode) {
        this.corResCode = corResCode;
    }

    public String getCorResCodeName() {
        return corResCodeName;
    }

    public void setCorResCodeName(String corResCodeName) {
        this.corResCodeName = corResCodeName;
    }

    public Integer getListingDate() {
        return listingDate;
    }

    public void setListingDate(Integer listingDate) {
        this.listingDate = listingDate;
    }

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }
}
