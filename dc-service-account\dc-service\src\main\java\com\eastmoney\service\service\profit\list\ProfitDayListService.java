package com.eastmoney.service.service.profit.list;

import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;

import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/12-18:26.
 *
 * <AUTHOR>
 */
public interface ProfitDayListService {
    List<ProfitDay> getDayProfitList(Map<String, Object> params);

    List<ProfitRateDay> getDayProfitRateList(Map<String, Object> params);
}
