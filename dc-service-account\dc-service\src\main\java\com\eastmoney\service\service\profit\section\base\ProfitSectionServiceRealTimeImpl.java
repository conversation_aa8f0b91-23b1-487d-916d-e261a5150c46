package com.eastmoney.service.service.profit.section.base;

import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.profit.base.ProfitService;
import com.eastmoney.service.service.profit.hgt.HgtHolidayProfitService;
import com.eastmoney.service.service.profit.section.ProfitSectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * Created on 2020/8/12-15:39.
 * 包装实时收益，临时收益
 * 因为要处理包括实时收益与临时收益，所以将跨周期时的开始日期判断下沉到这一层来处理，上层只需要将startDate传过来
 *
 * <AUTHOR>
 */
@Service("profitSectionServiceRealTime")
public class ProfitSectionServiceRealTimeImpl implements ProfitSectionService {
    @Resource(name = "profitServiceRealTime")
    private ProfitService profitServiceRealTime;
    @Resource(name = "profitServiceTemp")
    private ProfitService profitServiceTemp;
    @Autowired
    private CommonService commonService;
    @Autowired
    private HgtHolidayProfitService hgtHolidayProfitService;

    /**
     * @param params accountBizDate : 最新清算日期 startDate : 该区间的开始日期 fundId : 资金账号 unit : 查询区间
     * @return
     */
    @Override
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        SectionProfitBean result = new SectionProfitBean();
        Integer accountBizDate = CommonUtil.convert(params.get("accountBizDate"), Integer.class);
        Boolean isProfitRateCalWindow = CommonUtil.convert(params.get("isProfitRateCalWindow"),Boolean.class);
        if(isProfitRateCalWindow == null){
            isProfitRateCalWindow = false;
        }
        int calRealTimeProfitBizDate = commonService.calRealTimeProfitBizDate(accountBizDate);
        if (calRealTimeProfitBizDate == 0 && !isProfitRateCalWindow) {
            return null;
        }
        DayProfitBean profitDayTemp = profitServiceTemp.getProfitDay(params);
        DayProfitBean profitDayRealTime = null;
        if (profitDayTemp == null || profitDayTemp.getBizDate() < calRealTimeProfitBizDate) {
            params.put("realBizDate", calRealTimeProfitBizDate);
            profitDayRealTime = profitServiceRealTime.getProfitDay(params);
            profitDayRealTime.setBizDate(calRealTimeProfitBizDate);
            setEutime(profitDayRealTime);
        }

        // 获取港股通节假日收益
        double hgtHolidayProfit = 0;
        long fundId = CommonUtil.convert(params, "fundId", Long.class);
        Boolean filterHgtProfit = CommonUtil.convert(params.get("filterHgtProfit"), Boolean.class);
        if (Objects.isNull(filterHgtProfit) || Objects.equals(filterHgtProfit, Boolean.FALSE)) {
            hgtHolidayProfit = hgtHolidayProfitService.getHgtHolidayProfitWithTryCatch(fundId, accountBizDate);
        }

        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        buildSectionProfitBean(result, profitDayTemp, accountBizDate, startDate, isProfitRateCalWindow, hgtHolidayProfit);
        buildSectionProfitBean(result, profitDayRealTime, accountBizDate, startDate, isProfitRateCalWindow, hgtHolidayProfit);
        return result;

    }

    private void buildSectionProfitBean(SectionProfitBean result, DayProfitBean dayProfitBean,
                                        Integer accountBizDate, Integer startDate, boolean isProfitRateCalWindow,
                                        double hgtHolidayProfit) {
        if (dayProfitBean == null) {
            return;
        }
        int bizDate = dayProfitBean.getBizDate();
        if (bizDate > accountBizDate && bizDate >= startDate) {
            if(!isProfitRateCalWindow){
                result.setProfit(ArithUtil.add(result.getProfit(), dayProfitBean.getProfit()));
                // 港股通假期收益
                result.setHkProfit(hgtHolidayProfit);
            }
            result.setProfitRate(CommonUtil.multiply(result.getProfitRate(), dayProfitBean.getProfitRate()));
            result.setBizDate(bizDate);
            result.setEuTime(dayProfitBean.getEuTime());
        }
    }

    /**
     * 对于实时计算的收益，如果时间小于请求日当天的16:00分，则更新时间为当前时间
     * 如果时间大于16:00分，则更新时间为交易日当日的16:00分。
     *
     * @param dayProfitBean
     */
    private void setEutime(DayProfitBean dayProfitBean) {
        int bizDate = dayProfitBean.getBizDate();
        Date closeDate = DateUtil.strToDate(bizDate + "160000", DateUtil.yyyyMMddHHmmss);
        Date now = new Date();
        dayProfitBean.setEuTime(DateUtil.compareDate(now, closeDate) < 0 ? now : closeDate);
    }
}
