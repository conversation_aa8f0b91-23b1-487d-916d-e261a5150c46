package com.eastmoney.service.util;

import com.eastmoney.common.entity.MonthBillBO;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.entity.cal.dw.bill.*;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;
import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Pool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;

/**
 * Kryo序列化反序列化
 * 1. 对象序列化为字节数组
 * 2. 字节数组反序列化为对象
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
public class KryoSerializerUtil {
    public static Logger LOG = LoggerFactory.getLogger(KryoSerializerUtil.class);

    // 创建Kryo对象池
    // 参数分别为：是否线程安全、是否使用软引用、池的大小
    static Pool<Kryo> kryoPool = new Pool<Kryo>(true, true, 100) {
        @Override
        protected Kryo create() {
            Kryo kryo = new Kryo();
            // 这里可以进行Kryo的配置，例如注册类、设置序列化策略等
            // 注册需要序列化的类
            kryo.register(YearBillExtend.class);
            kryo.register(BdTradeBillMaxProfitDO.class);
            kryo.register(MajorBillStrInfo.class);
            kryo.register(MonthBillBO.class);
            kryo.register(StockBill.class);
            kryo.register(PublishBill.class);
            kryo.register(IPOTradeBill.class);
            kryo.register(BdUserTradeBillDO.class);
            kryo.register(BdTradeBillFinal.class);
            kryo.register(BdTradeBillPrate.class);
            kryo.register(BdTradeBillBaseInfoDO.class);
            kryo.register(BdTradeBillFirstDayDO.class);
            kryo.register(BdTradeBillMostTradeDO.class);
            kryo.register(BdTradeBillConceptSectorsDO.class);
            kryo.register(BdTradeBillMaxProfitDO.class);
            kryo.register(BdTradeBillYearClearProfitDO.class);
            kryo.register(BdTradeBillYearBullProfitDO.class);
            kryo.register(BdTradeBillYearBullTradeDO.class);
            kryo.register(BdTradeBillYearHoldDO.class);
            kryo.register(BdTradeBillYearTProfitDO.class);
            kryo.register(BdTradeBillYearInvestAdvisDO.class);
            kryo.register(BdTradeBillYearReviewDO.class);
            kryo.register(BigDecimal.class);
            return kryo;
        }
    };


    /**
     * 使用Kryo对对象进行序列化，返回字节数组表示的序列化结果
     *
     * @param object 要序列化的对象
     * @return 序列化后的字节数组
     */
    public static byte[] serialize(Object object) {
        if (object == null) {
            return null;
        }

        // 使用对象池获取Kryo实例
        Kryo kryo = kryoPool.obtain();
        try (Output out = new Output(new ByteArrayOutputStream())) {
            kryo.writeObject(out, object);
            return out.toBytes();
        } catch (Exception e) {
            // 异常处理，确保资源正确释放
            LOG.error("Kryo-serialize 出现异常", e);
            return null;
        } finally {
            // 使用完毕后，将Kryo实例释放回对象池
            kryoPool.free(kryo);
        }
    }

    /**
     * 对给定的字节数组进行反序列化，恢复出原始对象
     *
     * @param bytes 序列化后的字节数组
     * @param clazz 要反序列化得到的对象类型
     * @param <T>   泛型类型，表示要反序列化得到的对象类型
     * @return 反序列化后的对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T deserialize(byte[] bytes, Class<T> clazz) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        // 使用对象池获取Kryo实例
        Kryo kryo = kryoPool.obtain();
        try (Input in = new Input(new ByteArrayInputStream(bytes))) {
            return kryo.readObject(in, clazz);
        } catch (Exception e) {
            // 异常处理，确保资源正确释放
            LOG.error("Kryo-deserialize 出现异常", e);
            return null;
        } finally {
            // 使用完毕后，将Kryo实例释放回对象池
            kryoPool.free(kryo);
        }
    }

}
