package com.eastmoney.common.entity;

/**
 * 码表信息
 *
 * <AUTHOR>
 * Date：2016/11/3  9:38
 */
public class Stock extends BaseEntity implements Cloneable{

    private String market;
    private String stkId;
    private String stkCode;
    private String linkStk;
    private String frzStk;
    private String stkName;
    private String stkType;
    private String moneyType;
    private String spellId;
    private String stkStatus;
    private String stkLevel;
    private String stkRight;
    private String othbusiStatus;
    /**
     * 股转分层数据
     * 0：基础层
     * 1：创新层
     * 2：精选层
     * <p>
     * 北交所柜台临时修改：将2做为北交所处理
     */
    private String stkFc;
    /**
     * 扩位简称
     *
     */
    private String expandNameAbbr;

    public Stock() {
    }

    @Override
    public Stock clone() throws CloneNotSupportedException {
        return (Stock) super.clone();
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkId() {
        return stkId;
    }

    public void setStkId(String stkId) {
        this.stkId = stkId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getLinkStk() {
        return linkStk;
    }

    public void setLinkStk(String linkStk) {
        this.linkStk = linkStk;
    }

    public String getFrzStk() {
        return frzStk;
    }

    public void setFrzStk(String frzStk) {
        this.frzStk = frzStk;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getSpellId() {
        return spellId;
    }

    public void setSpellId(String spellId) {
        this.spellId = spellId;
    }

    public String getStkStatus() {
        return stkStatus;
    }

    public void setStkStatus(String stkStatus) {
        this.stkStatus = stkStatus;
    }

    public String getStkLevel() {
        return stkLevel;
    }

    public void setStkLevel(String stkLevel) {
        this.stkLevel = stkLevel;
    }

    public String getStkRight() {
        return stkRight;
    }

    public void setStkRight(String stkRight) {
        this.stkRight = stkRight;
    }

    public String getOthbusiStatus() {
        return othbusiStatus;
    }

    public void setOthbusiStatus(String othbusiStatus) {
        this.othbusiStatus = othbusiStatus;
    }

    public String getStkFc() {
        return stkFc;
    }

    public void setStkFc(String stkFc) {
        this.stkFc = stkFc;
    }

    public String getExpandNameAbbr() {
        return expandNameAbbr;
    }

    public void setExpandNameAbbr(String expandNameAbbr) {
        this.expandNameAbbr = expandNameAbbr;
    }
}
