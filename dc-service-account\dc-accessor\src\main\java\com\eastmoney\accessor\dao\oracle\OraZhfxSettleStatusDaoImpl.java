package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.OraZhfxSettleStatusMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ZhfxSettleStatus;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource
@Service("zhfxSettleStatusDao")
public class OraZhfxSettleStatusDaoImpl extends BaseDao<OraZhfxSettleStatusMapper, ZhfxSettleStatus, Long> implements ZhfxSettleStatusDao {
}
