package com.eastmoney.quote.mdstp.pull.client;

import java.net.InetSocketAddress;

/**
 * Created by 1 on 15-7-7.
 */
public class RpcConnectionFactory implements RpcConnFactInterface {
    private InetSocketAddress serverAddr;
    private String quoteType;

    public RpcConnectionFactory(String quoteType,String host, int port) {
        this.quoteType = quoteType;
        this.serverAddr = new InetSocketAddress(host, port);
    }

    public RpcConnection getConnection() throws Throwable {
        return new RpcConnection(this.quoteType,this.serverAddr.getHostName(), this.serverAddr.getPort());
    }

    public void recycle(RpcConnection connection) throws Throwable {
        if (null != connection) {
            connection.close();
        }
    }

}
