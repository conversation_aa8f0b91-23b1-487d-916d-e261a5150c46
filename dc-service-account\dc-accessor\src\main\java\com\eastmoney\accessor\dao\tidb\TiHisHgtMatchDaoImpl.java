package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.HisHgtMatchDao;
import com.eastmoney.accessor.mapper.tidb.TiHgtMatchMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.HgtMatch;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/9 17:19
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("hisHgtMatchDao")
public class TiHisHgtMatchDaoImpl extends BaseDao<TiHgtMatchMapper, HgtMatch, Integer> implements HisHgtMatchDao {

    @Override
    public List<HgtMatch> getUndeliveredMatchList(Map<String, Object> params) {
        return getMapper().getUndeliveredMatchList(params);
    }

}
