package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.tidb.HgtTradeDateDao;
import com.eastmoney.common.entity.HgtTradeDayDO;
import com.eastmoney.common.util.DateUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class HgtTradeDateService {
    private static final Logger LOG = LoggerFactory.getLogger(HgtReferRateService.class);

    @Resource(name = "hgtTradeDateCache")
    private LoadingCache<Integer, Optional<HgtTradeDayDO>> hgtTradeDayCache;
    @Resource(name = "hgtTradeDateDao")
    private HgtTradeDateDao hgtTradeDateDao;

    @Bean(name = "hgtTradeDateCache")
    public LoadingCache<Integer, Optional<HgtTradeDayDO>> hgtReferRateCache() {
        LoadingCache<Integer, Optional<HgtTradeDayDO>> build = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(300, TimeUnit.SECONDS)
                .build(new CacheLoader<Integer, Optional<HgtTradeDayDO>>() {
                    @Override
                    public Optional<HgtTradeDayDO> load(Integer key) {
                        HgtTradeDayDO hgtTradeDay = null;
                        try {
                            hgtTradeDay = hgtTradeDateDao.isMarket(key);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(hgtTradeDay);
                    }
                });
        return build;
    }

    public Boolean isMarket(Integer bizDate) {
        try {
            HgtTradeDayDO hgtTradeDayDO = hgtTradeDayCache.get(bizDate).orElse(null);
            if(hgtTradeDayDO == null){
                //当日非交易日
                return false;
            }
            if(hgtTradeDayDO.getAuctionOpAm() == null){
                return Integer.parseInt(DateUtil.getCurHHmmss())>= hgtTradeTimeParser(hgtTradeDayDO.getOpenTradePm());
            }
            return true;
        } catch (ExecutionException e) {
            LOG.error("判断港股交易日时间错误", e);
        }
        return false;
    }

    public Boolean todayIsMarket(){
        return isMarket(DateUtil.getCuryyyyMMddInteger());
    }

    private Integer hgtTradeTimeParser(String openTradePmStr){
        Integer openTradePm = 130000;
        if(StringUtils.isEmpty(openTradePmStr))
            return openTradePm;
        try{
            String[] timeParts = openTradePmStr.split(":");
            int hours = Integer.parseInt(timeParts[0]);
            int minutes = Integer.parseInt(timeParts[1]);
            openTradePm = hours * 10000 + minutes * 100;
        }catch (Exception e){
            LOG.info("港股通交易时间解析错误,解析对象:{}",openTradePmStr);
        }
        return openTradePm;
    }
}
