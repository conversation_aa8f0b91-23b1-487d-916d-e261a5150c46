package com.eastmoney.common.cache;

import com.eastmoney.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by robin on 2016/8/26.
 * 系统本地缓存数据
 * <AUTHOR>
 */
public class LocalCache {

    private static final Logger LOG = LoggerFactory.getLogger(LocalCache.class);

    private static boolean isMarketFlag = false;
    private static boolean previousMarketFlag = false;
    private static boolean nextMarketFlag = false;
    /**
     * 当日是否交易日缓存
     */
    private static Map<Integer,Boolean> isMarketMap = new HashMap<>();
    /**
     * 上一个交易日缓存
     */
    private static Map<Integer,String> previousMarketMap = new HashMap<>();
    /**
     * 下一个交易日缓存
     */
    private static Map<Integer,String> nextMarketMap = new HashMap<>();

    /**
     * 获取指定日期是否是交易日
     * @param bizdate
     * @return
     */
    public static Boolean IsMarket(Integer bizdate){
        return isMarketMap.get(bizdate);
    }

    /**
     * 添加日期 是否交易日
     * @param bizdate
     * @param market
     */
    public synchronized static void addIsMarket(Integer bizdate,Boolean market) {
        if(!isMarketFlag) {
            isMarketFlag =true;
            clearIsMarket();
        }
        isMarketMap.put(bizdate,market);
    }

    /**
     * 执行清除今天是交易日缓存
     */
    private static void clearIsMarket() {

        LOG.info("IsMarket clear local cache timing start...");

        Runnable run = new Runnable() {
            @Override
            public void run() {
                try {
                    while(true){
                        LOG.info("IsMarket clear local cache starting");
                        Integer bizDate = Integer.parseInt(DateUtil.dateToStr(new Date(), DateUtil.yyyyMMdd));
                        Integer yesterday = DateUtil.getYesterday(bizDate);
                        Iterator<Map.Entry<Integer, Boolean>> entries = isMarketMap.entrySet().iterator();
                        while (entries.hasNext()) {
                            Map.Entry<Integer, Boolean> entry = entries.next();
                            if (!entry.getKey().equals(bizDate) && !entry.getKey().equals(yesterday)) {
                                entries.remove();
                            }
                        }
                        LOG.info("IsMarket clear local cache end");
                        Thread.sleep(7 * 24 * 60 * 60 * 1000);
                    }
                } catch (Exception e){
                    LOG.error("IsMarket clear local cache timing broken", e);
                    isMarketFlag = false;
                }

            }
        };

        Thread cealr = new Thread(run);

        cealr.start();
    }

    /**
     * 获取指定日期的上一个交易日
     * @param bizdate
     * @return
     */
    public static String getPreMarketDay(Integer bizdate){
        return previousMarketMap.get(bizdate);
    }

    /**
     * 添加日期 的上一个交易
     * @param bizdate
     * @param preMarket
     */
    public synchronized static void addPreMarketDay(Integer bizdate,String preMarket) {
        if(!previousMarketFlag){
            previousMarketFlag =true;
            clearPreMarket();
        }
        previousMarketMap.put(bizdate,preMarket);
    }

    /**
     * 执行清除获取上一个交易日缓存
     */
    private static void clearPreMarket() {
        LOG.info("PreMarket clear local cache timing start...");

        Runnable run = new Runnable() {
            @Override
            public void run() {
                try {
                    while(true){
                        LOG.info("PreMarket clear local cache starting");
                        Integer bizDate = Integer.parseInt(DateUtil.dateToStr(new Date(), DateUtil.yyyyMMdd));
                        Integer pastOneYearDay = DateUtil.getBeforeOffestDay(bizDate, 367);
                        Iterator<Map.Entry<Integer, String>> entries = previousMarketMap.entrySet().iterator();
                        while (entries.hasNext()) {
                            Map.Entry<Integer, String> entry = entries.next();
                            Integer cacheKey = entry.getKey();
                            if(cacheKey < pastOneYearDay || cacheKey > bizDate){
                                entries.remove();
                            }
                        }
                        LOG.info("PreMarket clear local cache end");
                        Thread.sleep(7 * 24 * 60 * 60 * 1000);
                    }
                }catch (Exception e){
                    LOG.error("PreMarket clear local cache timing broken",e);
                    previousMarketFlag = false;
                }

            }
        };

        Thread cealr = new Thread(run);

        cealr.start();
    }

    /**
     * 获取下一个交易日缓存
     * @param today
     * @return
     */
    public static String getNextMarketDay(Integer today){
        return nextMarketMap.get(today);
    }

    /**
     * 添加日期 下一个交易日缓存
     * @param today
     * @param nextMarket
     */
    public synchronized static void addNextMarketDay(Integer today,String nextMarket){
        if(!nextMarketFlag){
            nextMarketFlag =true;
            clearNextMarket();
        }
        nextMarketMap.put(today,nextMarket);
    }

    /**
     * 执行清除获取下一个交易日缓存
     */
    private static void clearNextMarket(){
        LOG.info("nextMarket clear local cache timing start...");

        Runnable run = new Runnable() {
            @Override
            public void run() {
                try {
                    while(true){
                        LOG.info("nextMarket clear local cache starting");
                        Integer bizDate = Integer.parseInt(DateUtil.dateToStr(new Date(), DateUtil.yyyyMMdd));
                        Integer yesterday = DateUtil.getYesterday(bizDate);
                        Iterator<Map.Entry<Integer, String>> entries = nextMarketMap.entrySet().iterator();
                        while (entries.hasNext()) {
                            Map.Entry<Integer, String> entry = entries.next();
                            if (!entry.getKey().equals(bizDate) && !entry.getKey().equals(yesterday)) {
                                entries.remove();
                            }
                        }
                        LOG.info("nextMarket clear local cache end");
                        Thread.sleep(7 * 24 * 60 * 60 * 1000);
                    }
                }catch (Exception e){
                    LOG.error("nextMarket clear local cache timing broken",e);
                    nextMarketFlag = false;
                }

            }
        };

        Thread cealr = new Thread(run);

        cealr.start();
    }

    public static void stop(){

    }
}
