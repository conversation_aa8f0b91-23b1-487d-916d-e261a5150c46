package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntityCal;

import java.util.Objects;

/**
 * Created on 2016/3/3
 * 收益
 *
 * <AUTHOR>
 */
public class ProfitStat extends BaseEntityCal {
    /**
     * 资金帐号
     */
    private Long fundId;
    /**
     * 收益额
     */
    private Double profit;
    /**
     * 收益率
     */
    private Double profitRate;
    /**
     * 业务日期
     */
    private Integer bizDate;
    /**
     * 单位(例如：每日(D)/周(W)/两周(2W)/月(M)/三月(3M)/半年(6M)/年(Y))
     */
    private String unit;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit.trim();
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProfitStat)) return false;
        ProfitStat that = (ProfitStat) o;
        return Objects.equals(fundId, that.fundId) && Objects.equals(profit, that.profit) && Objects.equals(profitRate, that.profitRate) && Objects.equals(bizDate, that.bizDate) && Objects.equals(unit, that.unit);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fundId, profit, profitRate, bizDate, unit);
    }

    @Override
    public String toString() {
        return "ProfitStat{" +
                "fundId=" + fundId +
                ", profit=" + profit +
                ", profitRate=" + profitRate +
                ", bizDate=" + bizDate +
                ", unit='" + unit + '\'' +
                "} " + super.toString();
    }
}
