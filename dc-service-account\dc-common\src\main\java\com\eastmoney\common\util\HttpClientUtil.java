package com.eastmoney.common.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyongyong
 * on 2016/11/22.
 */
public class HttpClientUtil {
    private static Logger LOG = LoggerFactory.getLogger(HttpClientUtil.class);
    public static CloseableHttpClient httpclient;
    public static  CloseableHttpClient getHttpClient() {
        if (httpclient == null) {
            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
            // Configure total max or per route limits for persistent connections
            // that can be kept in the pool or leased by the connection manager.
            connManager.setMaxTotal(100);
            connManager.setDefaultMaxPerRoute(10);
            httpclient = HttpClients.custom().setConnectionManager(connManager).build();
        }
        return httpclient;
    }

    /**
     * 发送post请求
     * @throws Exception
     */
    public static String sendPostRequest(String url, String content) throws Exception{
//        url = URLEncoder.encode(url, "UTF-8");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new ByteArrayEntity(content.getBytes("UTF-8")));
        return sendRequest(httpPost);
    }

    public static String sendPostRequest(String url, Map<String, String> params) throws Exception{
        List<NameValuePair> nvps = new ArrayList<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            nvps.add(new BasicNameValuePair(entry.getKey(),entry.getValue()));
        }
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new UrlEncodedFormEntity(nvps));
        return sendRequest(httpPost);
    }

    /**
     * 发送get请求
     * @throws Exception
     */
    public static String sendGetRequest(String url) throws Exception{
//        url = URLEncoder.encode(url, "UTF-8");
        HttpGet httpGet = new HttpGet(url);
        return sendRequest(httpGet);
    }
    /**
     * 发送请求
     * @throws Exception
     */
    public static String sendRequest(HttpUriRequest httpRequest) throws Exception {
        CloseableHttpClient httpclient = getHttpClient();//HttpClients.createDefault();
        HttpResponse response = null;
        String responseBody = "";
        try {
//            System.out.println("Executing request " + httpRequest.getRequestLine());
            ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
                @Override
                public String handleResponse(
                        final HttpResponse response) throws ClientProtocolException, IOException {
                    int status = response.getStatusLine().getStatusCode();
//                    System.out.println(status + ":" + response.getStatusLine().getReasonPhrase());
                    if (status >= 200 && status < 300) {
                        HttpEntity entity = response.getEntity();
                        return entity != null ? EntityUtils.toString(entity) : null;
                    } else {
                        throw new ClientProtocolException("Unexpected response status: " + status);
                    }
                }
            };
            responseBody = httpclient.execute(httpRequest, responseHandler);
        }catch (Exception e){
            LOG.error("url:{} {}" , httpRequest.getURI(),e);
            throw e;
        }finally {
            if (response != null) {
                EntityUtils.consume(response.getEntity());
            }
        }
        return responseBody;
    }

    public static void main(String[] args) throws Exception {
        sendPostRequest("http://localhost:8080/oas/openAccountServlet","list=*********");
    }

    public static String postJson(String url, String content) throws Exception{
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setEntity(new ByteArrayEntity(content.getBytes("UTF-8")));
        return sendRequest(httpPost);
    }

    /**
     * 下载
     * @throws Exception
     */
    public static byte[] download(String url) throws Exception{
        CloseableHttpClient httpclient = getHttpClient();
        HttpGet httpGet = new HttpGet(url);
        HttpResponse response = null;
        byte[] responseBody = null;
        try {
            ResponseHandler<byte[]> responseHandler = new ResponseHandler<byte[]>() {
                @Override
                public byte[] handleResponse(
                        final HttpResponse response) throws ClientProtocolException, IOException {
                    int status = response.getStatusLine().getStatusCode();
//                    System.out.println(status + ":" + response.getStatusLine().getReasonPhrase());
                    if (status >= 200 && status < 300) {
                        HttpEntity entity = response.getEntity();
                        return entity != null ? EntityUtils.toByteArray(entity) : null;
                    } else {
                        throw new ClientProtocolException("Unexpected response status: " + status);
                    }
                }
            };
            responseBody = httpclient.execute(httpGet, responseHandler);
        } catch (Exception e){
            LOG.error("url:{} {}" , url,e);
            throw e;
        }finally {
            if (response != null) {
                EntityUtils.consume(response.getEntity());
            }
        }
        return responseBody;
    }
}


