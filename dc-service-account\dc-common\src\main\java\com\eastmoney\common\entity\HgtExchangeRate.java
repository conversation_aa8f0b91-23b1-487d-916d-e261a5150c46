package com.eastmoney.common.entity;

/**
 * 港股通结算汇率
 *
 * <AUTHOR>
 * @create 2022/11/20
 */
public class HgtExchangeRate extends BaseEntity {
    /**
     * 柜台市场代码
     */
    private String market;
    /**
     * 汇率类型
     * ExchangeRateTypeEnum
     */
    private String type;
    /**
     * 汇率
     */
    private Double exchangeRate;

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(Double exchangeRate) {
        this.exchangeRate = exchangeRate;
    }
}
