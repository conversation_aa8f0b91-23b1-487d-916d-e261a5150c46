package com.eastmoney.quote.mdstp.serializer;/**
 * Created by 1 on 16-10-31.
 */


import com.eastmoney.quote.mdstp.container.QuoteContainer;
import com.eastmoney.quote.mdstp.model.BuySellInfo;
import com.eastmoney.quote.mdstp.model.HkQtRec;
import com.eastmoney.quote.mdstp.pull.serializer.Packet;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created on 16-10-31
 *
 * <AUTHOR>
 */
public class HkQtDecoder {
    private static Logger logger = LoggerFactory.getLogger(HkQtDecoder.class);

    public static void decode(ByteBuf buffer,boolean isPush){
        try {
            if (buffer != null) {
                processSnapShot(buffer,isPush);
            }

        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
    }

    private static void processSnapShot(ByteBuf buffer,boolean isPush) throws UnsupportedEncodingException {
        if(isPush){
            int stdQt = buffer.readInt();
        }
        int size = buffer.readInt();
        for (int i = 1; i <= size; i++) {
            HkQtRec qtRec = new HkQtRec();
            //dwDate;
            qtRec.setDwDate(buffer.readInt());

            //dwTime;
            qtRec.setDwTime(buffer.readInt());

            //uniqueId;
            qtRec.setUniqueId(readString(buffer));

            //name;
            qtRec.setName(readString(buffer));

            //code
            qtRec.setCode(readString(buffer));

            // market
            qtRec.setwMarket(buffer.readShort());

            // type
            qtRec.setwType(buffer.readShort());

            //tradeFlag;
            qtRec.setTradeFlag(buffer.readByte());

            //dwClose;  // 前日收盘价
            qtRec.setDwClose(buffer.readInt());

            //开盘价
            qtRec.setDwOpen(buffer.readInt());

            //最高价
            qtRec.setDwHigh(buffer.readInt());

            //最低价
            qtRec.setDwLow(buffer.readInt());

            //最新价
            qtRec.setDwPrice(buffer.readInt());

            //成交量
            qtRec.setVolume(buffer.readLong());

            //成交额
            qtRec.setAmount(buffer.readDouble());

            //当日没有成交时，mdstp推送最新价为0，需要特殊处理
            if (qtRec.getDwPrice() == 0 && qtRec.getVolume() == 0 && qtRec.getAmount() == 0) {
                qtRec.setDwPrice(qtRec.getDwClose());
            }

            // 成交笔数
            qtRec.setTradeNum(buffer.readLong());

            // numBuy;
            qtRec.setNumBuy(buffer.readByte());

            //numSell;
            qtRec.setNumSell(buffer.readByte());

            // 买卖盘信息
            if (qtRec.getNumBuy() + qtRec.getNumSell() > 0) {
                List<BuySellInfo> buySellInfos = new ArrayList<BuySellInfo>();
                for (int m = 0; m < qtRec.getNumSell(); m++) {
                    BuySellInfo buySellInfo = new BuySellInfo();
                    buySellInfo.setDwMMp(buffer.readInt());
                    buySellInfo.setxMMPVol(buffer.readLong());
                    buySellInfo.setBuyFlag((byte)1);
                    buySellInfos.add(buySellInfo);
                }


                for (int m = 0; m < qtRec.getNumBuy(); m++) {
                    BuySellInfo buySellInfo = new BuySellInfo();
                    buySellInfo.setDwMMp(buffer.readInt());
                    buySellInfo.setxMMPVol(buffer.readLong());
                    buySellInfo.setBuyFlag((byte) 0);
                    buySellInfos.add(buySellInfo);

                }
                qtRec.setBuySellInfos(buySellInfos);
            }

            //外盘
            qtRec.setWaiPan(buffer.readLong());

            //现手
            qtRec.setxCurVol(buffer.readLong());

            //现手方向
            qtRec.setcCurVol(buffer.readByte());

            //持仓量变化
            qtRec.setxCurOI(buffer.readLong());

            //均价
            qtRec.setDwAvg(buffer.readInt());

            //持仓量
            qtRec.setDwOpenInterest(buffer.readLong());

            //今结算价
            qtRec.setDwAvgPrice(buffer.readInt());

            //昨持仓量
            qtRec.setPreOpenInterest(buffer.readLong());

            //昨结算价
            qtRec.setDwPreAvgPrice(buffer.readInt());

            //涨停价
            qtRec.setDwPriceZT(buffer.readInt());

            //跌停价
            qtRec.setDwPriceDT(buffer.readInt());

            QuoteContainer.updateHkQtRec(qtRec.getCode(), qtRec);
        }

    }

    private static String readString(ByteBuf buffer) throws UnsupportedEncodingException {
        byte len = buffer.readByte();
        byte[] bytes = new byte[len];
        buffer.readBytes(bytes);
        return new String(bytes, "GBK");
    }
}
