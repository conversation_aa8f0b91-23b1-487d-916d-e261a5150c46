package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntity;

/**
 * 主账单表
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/31.
 */
public class MajorBill extends BaseEntity {

    private Long fundId; //资金账号
    private Integer indexKey; //时间槽索引key，年:yyyy,月：yyyyMM
    private String timeSlot; //时间槽类型，年:Y，月:M
    private Double profitRate; //收益率
    private Double profit; //收益额
    private Integer profitRankIndex; //收益额排名
    private Double profitRankPercent; //收益额排名百分比
    private String remark; //备注
    private Double gainRate; //胜率
    private Double maxGain;	//单笔最大盈利
    private Double maxLoss;	//单笔最大亏损
    private Double maximum;	//最大回撤率;
    private Integer totalCount; //总交易次数
    private Integer gainCount;	//盈利次数
    private Integer lossCount;	//亏损次数
    private Double avgPosition;	//平均持仓
    private Double volatility;  //波动率
    private Double sharpeRatio;    //夏普比率
    private Double tradeCount;  //交易次数
    private Integer buyCount; //买入次数

    public Integer getBuyCount() {
        return buyCount;
    }

    public void setBuyCount(Integer buyCount) {
        this.buyCount = buyCount;
    }

    public Double getVolatility() {
        return volatility;
    }

    public void setVolatility(Double volatility) {
        this.volatility = volatility;
    }

    public Double getSharpeRatio() {
        return sharpeRatio;
    }

    public void setSharpeRatio(Double sharpeRatio) {
        this.sharpeRatio = sharpeRatio;
    }

    public Double getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Double tradeCount) {
        this.tradeCount = tradeCount;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Integer getProfitRankIndex() {
        return profitRankIndex;
    }

    public void setProfitRankIndex(Integer profitRankIndex) {
        this.profitRankIndex = profitRankIndex;
    }

    public Double getProfitRankPercent() {
        return profitRankPercent;
    }

    public void setProfitRankPercent(Double profitRankPercent) {
        this.profitRankPercent = profitRankPercent;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getGainRate() {
        return gainRate;
    }

    public void setGainRate(Double gainRate) {
        this.gainRate = gainRate;
    }

    public Double getMaxGain() {
        return maxGain;
    }

    public void setMaxGain(Double maxGain) {
        this.maxGain = maxGain;
    }

    public Double getMaxLoss() {
        return maxLoss;
    }

    public void setMaxLoss(Double maxLoss) {
        this.maxLoss = maxLoss;
    }

    public Double getMaximum() {
        return maximum;
    }

    public void setMaximum(Double maximum) {
        this.maximum = maximum;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getGainCount() {
        return gainCount;
    }

    public void setGainCount(Integer gainCount) {
        this.gainCount = gainCount;
    }

    public Integer getLossCount() {
        return lossCount;
    }

    public void setLossCount(Integer lossCount) {
        this.lossCount = lossCount;
    }

    public Double getAvgPosition() {
        return avgPosition;
    }

    public void setAvgPosition(Double avgPosition) {
        this.avgPosition = avgPosition;
    }
}
