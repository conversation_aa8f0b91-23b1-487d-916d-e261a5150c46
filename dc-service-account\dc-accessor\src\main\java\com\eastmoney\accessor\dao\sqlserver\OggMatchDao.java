package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.Match;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/3/8
 */
public interface OggMatchDao extends IBaseDao<Match,Integer> {

    /**
     * 获取盘中非融券相关交易记录
     *
     * 220000证券买入 0B
     * 221001证券卖出 0S
     * 220188盘后定价买 3m
     * 220189盘后定价卖 3n
     *
     * @param params fundId, market, stkCode, serverId
     * @return
     */
    List<Match> getRealTimeMatchList(Map<String, Object> params);

    List<Match> getAllRealTimeMatchList(Map<String, Object> params);
}
