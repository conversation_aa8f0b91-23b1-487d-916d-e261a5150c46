package com.eastmoney.service.util;

import com.alibaba.fastjson.JSON;
import com.eastmoney.common.model.LogBean;
import com.eastmoney.common.model.Output;
import com.eastmoney.common.model.PushOutput;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.model.ChannelData;
import com.eastmoney.transport.util.ChannelUtil;
import io.netty.channel.Channel;
import org.apache.commons.lang3.StringUtils;

import java.net.InetSocketAddress;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/4/19.
 */
public class ServiceUtil {
    public static LogBean composeAccessLogBean(ChannelData channelData, Output output, String errorType, String errorMsg, long waitTime, long spentTime) {
        LogBean logBean = new LogBean();
        Map params = channelData.getParams();
        logBean.setAction((String) params.get("funCode"));
        Channel channel = channelData.getCtx().channel();
        InetSocketAddress cip = ChannelUtil.getRemoteAddress(channel);
        if (cip != null) {
            logBean.setC_ip(getIp(cip.toString()));
        }
        InetSocketAddress sip = ChannelUtil.getLocalAddress(channel);
        if (sip != null) {
            logBean.setS_ip(getIp(sip.toString()));
        }

        logBean.setResult(output.getErrCode() + "");
        String remark = "";
        String lastReqId = CommonUtil.convert(params, "lastReqId", String.class);
        if (StringUtils.isNotBlank(lastReqId)) {
            remark += "lastReqId=" + lastReqId + " ";
            params.remove("lastReqId");
        }
        String nextReqId = CommonUtil.convert(params, "nextReqId", String.class);
        if (StringUtils.isNotBlank(nextReqId)) {
            remark += "nextReqId=" + nextReqId;
            params.remove("nextReqId");
        }
        logBean.setRemark(remark);
        logBean.setIn_param(JSON.toJSONString(params));

        logBean.setU_id(CommonUtil.convert(channelData.getParams().get("fundId"), String.class));
        logBean.setT_id(new String(channelData.getMessage().getRequestId()));
        if (output.getErrCode() == -1) {
            logBean.setError_no(errorType);
            logBean.setError_msg(errorMsg);
        } else {
            String msg = "size=" + output.getSize();
            if (output instanceof PushOutput) {
                msg += " beginId=" + ((PushOutput) output).getBeginId() + " endId=" + ((PushOutput) output).getEndId();
            }
            logBean.setError_msg(msg);
        }

        if (output.getData() != null) {
            String jsonString = JSON.toJSONString(output.getData());
            logBean.setOut_param(jsonString);
        }

        logBean.setValue1(waitTime);
        logBean.setSpent_time(spentTime + "");
        logBean.setLog_type("info");
        return logBean;
    }


    private static String getIp(String address) {
        if (address == null) {
            address = "";
        }
        String[] array = address.split(":");
        if (array != null && array.length > 1) {
            return array[0].replaceFirst("/", "");
        } else {
            return address;
        }
    }

}
