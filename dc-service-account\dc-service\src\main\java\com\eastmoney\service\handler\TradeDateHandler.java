package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.TradeDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/3.
 */
@Service
public class TradeDateHandler {
    @Autowired
    TradeDateDao tradeDateService;

    //获取交易时间
    public List<TradeDate> getTradeDateList(Map<String,Object> params) {
        return tradeDateService.getTradeDateList(params);
    }

}
