package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.enums.MoneyTypeEnum;
import com.eastmoney.accessor.mapper.sqlserver.OggFundStockInnovateMapper;
import com.eastmoney.accessor.util.StoreProcedureUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * Created by sunyuncai on 2016/10/19.
 * update by xiaoyongyong
 */

@SqlServerTarget()
@Service
public class OggFundStockInnovateDaoImpl implements OggFundStockInnovateDao {

    @Autowired
    private OggFundStockInnovateMapper oggFundStockInnovateMapper;

    @Override
    public List queryOld(Map<String, Object> params) {
        Object market = Objects.nonNull(params.get("market")) ? params.get("market") : "";
        Object secuId = Objects.nonNull(params.get("secuId")) ? params.get("secuId") : "";
        Object stkCode = Objects.nonNull(params.get("stkCode")) ? params.get("stkCode") : "";
        Object qryFlag = Objects.nonNull(params.get("qryFlag")) ? params.get("qryFlag") : "";
        Object postStr = Objects.nonNull(params.get("postStr")) ? params.get("postStr") : "";
        Object funcId = Objects.nonNull(params.get("funcId")) ? params.get("funcId") : 1002080;
        Object bsFlag = Objects.nonNull(params.get("bsFlag")) ? params.get("bsFlag") : "";
        Object nCount = Objects.nonNull(params.get("nCount")) ? params.get("nCount") : 1500;
        Object cEmckykzlkg = Objects.nonNull(params.get("cEmckykzlkg")) ? params.get("cEmckykzlkg") : "";
        Object kqFlag = Objects.nonNull(params.get("kqFlag")) ? params.get("kqFlag") : "1";

        params.put("market", market);
        params.put("secuId", secuId);
        params.put("stkCode", stkCode);
        params.put("qryFlag", qryFlag);
        params.put("postStr", postStr);
        params.put("moneyType", MoneyTypeEnum.RMB.getValue());
        params.put("funcId", funcId);
        params.put("bsFlag", bsFlag);
        params.put("nCount", nCount);
        params.put("cEmckykzlkg", cEmckykzlkg);
        params.put("kqFlag", kqFlag);
        List result = StoreProcedureUtil.getMsStoreProcedureList(oggFundStockInnovateMapper.queryOld(params));
        if(CollectionUtils.isEmpty(result)){
            return null;
        }
        return result;
    }
    @Override
    public List query(Map<String, Object> params) {
        Object market = Objects.nonNull(params.get("market")) ? params.get("market") : "";
        Object secuId = Objects.nonNull(params.get("secuId")) ? params.get("secuId") : "";
        Object stkCode = Objects.nonNull(params.get("stkCode")) ? params.get("stkCode") : "";
        Object qryFlag = Objects.nonNull(params.get("qryFlag")) ? params.get("qryFlag") : "";
        Object postStr = Objects.nonNull(params.get("postStr")) ? params.get("postStr") : "";
        Object funcId = Objects.nonNull(params.get("funcId")) ? params.get("funcId") : 1002080;
        Object bsFlag = Objects.nonNull(params.get("bsFlag")) ? params.get("bsFlag") : "";
        Object nCount = Objects.nonNull(params.get("nCount")) ? params.get("nCount") : 1500;
        Object cEmckykzlkg = Objects.nonNull(params.get("cEmckykzlkg")) ? params.get("cEmckykzlkg") : "";
        Object kqFlag = Objects.nonNull(params.get("kqFlag")) ? params.get("kqFlag") : "1";

        params.put("market", market);
        params.put("secuId", secuId);
        params.put("stkCode", stkCode);
        params.put("qryFlag", qryFlag);
        params.put("postStr", postStr);
        params.put("moneyType", MoneyTypeEnum.RMB.getValue());
        params.put("funcId", funcId);
        params.put("bsFlag", bsFlag);
        params.put("nCount", nCount);
        params.put("cEmckykzlkg", cEmckykzlkg);
        params.put("kqFlag", kqFlag);
        List result = StoreProcedureUtil.getMsStoreProcedureList(oggFundStockInnovateMapper.query(params));
        if(CollectionUtils.isEmpty(result)){
            return null;
        }
        return result;
    }
}
