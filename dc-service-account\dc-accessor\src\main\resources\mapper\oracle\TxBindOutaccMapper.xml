<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.TxBindOutaccMapper">
    <select id="selectByPushCondition" resultType="com.eastmoney.common.entity.TxBindOutacc">
        SELECT * FROM(
        SELECT Eid,CuaccId, OpenId, BindAccId, BindAccType, BindTime, BidnStatus
        FROM run.em_tx_bind_outacc
        WHERE
            bidnStatus = 0
        <if test="beginId > 0">
            AND EID > #{beginId}
        </if>
        ORDER BY EID)
        WHERE ROWNUM &lt;= #{pageSize}
    </select>
</mapper>