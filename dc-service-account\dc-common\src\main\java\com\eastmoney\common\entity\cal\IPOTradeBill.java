package com.eastmoney.common.entity.cal;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.entity.BaseEntity;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 打新账单表
 * <AUTHOR>
 * @date 2021/11/30
 */
public class IPOTradeBill extends BaseEntity {
    /**
     * 打新次数
     */
    private Long tradeCount;
    /**
     * 中签次数
     */
    private Long investCount;
    /**
     * 中签可转债只数
     */
    private Long bondNum;
    /**
     * 中签个股只数
     */
    private Long stockNum;
    /**
     * 中签累计获利金额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal totalProfit;
    /**
     * 是否中过签
     */
    private Boolean investSuccess;

    /**
     * 打新次数排名占比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tradeCountPercent;

    public IPOTradeBill() {
        this.tradeCount = 0L;
        this.investCount = 0L;
        this.bondNum = 0L;
        this.stockNum = 0L;
        this.totalProfit = BigDecimal.ZERO;
        this.investSuccess = false;
        this.tradeCountPercent = BigDecimal.ZERO;
    }

    public Long getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Long tradeCount) {
        this.tradeCount = tradeCount;
    }

    public Long getInvestCount() {
        return investCount;
    }

    public void setInvestCount(Long investCount) {
        this.investCount = investCount;
    }

    public Long getBondNum() {
        return bondNum;
    }

    public void setBondNum(Long bondNum) {
        this.bondNum = bondNum;
    }

    public Long getStockNum() {
        return stockNum;
    }

    public void setStockNum(Long stockNum) {
        this.stockNum = stockNum;
    }

    public BigDecimal getTotalProfit() {
        return totalProfit;
    }

    public void setTotalProfit(BigDecimal totalProfit) {
        this.totalProfit = totalProfit;
    }

    public Boolean getInvestSuccess() {
        return investSuccess;
    }

    public void setInvestSuccess(Boolean investSuccess) {
        this.investSuccess = investSuccess;
    }

    public BigDecimal getTradeCountPercent() {
        return tradeCountPercent;
    }

    public void setTradeCountPercent(BigDecimal tradeCountPercent) {
        this.tradeCountPercent = tradeCountPercent;
    }
}
