package com.eastmoney.accessor.dao.oracle;


import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.CalDigestMapper;
import com.eastmoney.common.entity.cal.CalDigestDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by wangchun
 * Date:2017/7/25 10:03.
 * 计算对应摘要表
 */
@Service("calDigestDao")
public class CalDigestDaoImpl extends BaseDao<CalDigestMapper, CalDigestDO,Long> implements CalDigestDao {

    @Override
    public List<CalDigestDO> getDigestIdAndName(Map<String, Object> params) {
        return getMapper().getDigestIdAndName(params);
    }
}
