<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.LogAssetBankTranMapper">

    <sql id="Base_Column_List">
        EID,EITIME,EUTIME,RESEND_FLAG
        ,FUNDID,SERVERID,BIZDATE,MONEYTYPE,SNO
        ,STKCODE,SECUID,MATCHAMT,FUNDEFFECT,BSFLAG
        ,MATCHPRICE,MATCHQTY,FEE_YHS,FEE_SXF,FEE_JYGF,FEE_GHF
    </sql>

    <sql id="Base_Column_Value">
        XTZX.SEQ_EID.NEXTVAL,SYSDATE,SYSDATE,0
        ,#{fundId},#{serverId},#{bizDate},#{moneyType},#{sno}
        ,#{stkCode},#{secuId},#{matchAmt},#{fundEffect},#{bsFlag}
        ,#{matchPrice},#{matchQty},#{fee_yhs},#{fee_sxf},#{fee_jygf},#{fee_ghf}
    </sql>

    <sql id="All_Column_Value">
        XTZX.SEQ_EID.NEXTVAL,SYSDATE,SYSDATE,0
        ,#{serverId},#{operDate},#{clearDate},#{bizDate},#{sno},#{relativeSno}
        ,#{custId},#{custName},#{fundId},#{moneyType},#{orgId}
        ,#{brhId},#{custKind},#{custGroup},#{fundKind},#{fundLevel}
        ,#{fundGroup},#{digestId},#{fundEffect},#{fundBal},#{secuId}
        ,#{market},#{bizType},#{stkCode},#{stkType},#{bankCode}
        ,#{trdBankCode},#{bankBranch},#{bankNetPlace},#{stkName},#{stkEffect}
        ,#{stkBal},#{orderId},#{trdId},#{orderQty},#{orderPrice}
        ,#{bondIntr},#{orderDate},#{orderTime},#{matchQty},#{matchAmt}
        ,#{seat},#{matchTimes},#{matchPrice},#{matchTime},#{matchCode}
        ,#{fee_jsxf},#{fee_sxf},#{fee_yhs},#{fee_ghf},#{fee_qsf}
        ,#{fee_jygf},#{fee_jsf},#{fee_zgf},#{fee_qtf},#{bsFlag}
        ,#{feeFront},#{sourceType},#{bankId},#{agentId},#{operId}
        ,#{operWay},#{operOrg},#{operLevel},#{netAddr},#{chkOper}
        ,#{checkFlag},#{brokerId},#{custMgrId},#{fundUser0},#{fundUser1}
        ,#{privilege},#{remark},#{orderSno},#{pathId},#{cancelFlag}
        ,#{reportKind},#{creditId},#{creditFlag},#{prodCode},#{setTrate}
    </sql>

</mapper>