package com.eastmoney.common.entity;

import com.eastmoney.common.entity.cal.ProfitStat;
import com.eastmoney.common.util.CommonUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.eastmoney.common.util.ArithUtil.*;

/**
 * 账户表现 - 盈亏日历
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/12 13:18
 */
public class ProfitSectionStat {

    /**
     * 总收益额
     */
    private Double profitTotal;
    /**
     * 总收益率
     */
    private Double profitRateTotal;
    /**
     * 收益列表
     */
    private List<ProfitStat> statList = new ArrayList<>();

    public ProfitSectionStat(Double profitTotal, Double profitRateTotal, List<ProfitStat> statList) {
        this.profitTotal = profitTotal;
        this.profitRateTotal = profitRateTotal;
        this.statList = statList;
    }

    /**
     * @param profitStatList
     * @param profitType     收益额1 收益率2
     * @param unit           月M 年Y
     * @return
     */
    public static ProfitSectionStat build(List<ProfitStat> profitStatList, String profitType, String unit) {
        ProfitSectionStat profitSectionStat = new ProfitSectionStat(0.0, 0.0, Collections.emptyList());
        if (CollectionUtils.isEmpty(profitStatList)) {
            return profitSectionStat;
        }
        profitSectionStat.setStatList(new ArrayList<>(profitStatList.stream()
                .filter(profitStat -> profitStat.getBizDate() != null)
                .peek(profitStat -> {
                    if ("Y".equals(unit)) {
                        profitStat.setBizDate(profitStat.getBizDate() / 100);
                    }
                })
                .collect(
                        Collectors.toMap(
                                ProfitStat::getBizDate,
                                profitStat -> profitStat,
                                (oldProfitStat, newProfitStat) -> {
                                    if ("1".equals(profitType)) {
                                        oldProfitStat.setProfit(add(oldProfitStat.getProfit(), newProfitStat.getProfit()));
                                    } else {
                                        oldProfitStat.setProfitRate(CommonUtil.multiplyNoHalf(oldProfitStat.getProfitRate(), newProfitStat.getProfitRate()));
                                    }
                                    return oldProfitStat;
                                }
                        )
                )
                .values()));
        if ("1".equals(profitType)) {
            Double profitTotal = 0d;
            for (ProfitStat profitStat : profitSectionStat.getStatList()) {
                profitTotal= add(profitTotal, profitStat.getProfit());
                profitStat.setProfit(round(profitStat.getProfit()));
            }
            profitSectionStat.setProfitTotal(round(profitTotal));
        } else {
            Double profitRateTotal = 0d;
            for (ProfitStat profitStat : profitSectionStat.getStatList()) {
                profitRateTotal = CommonUtil.multiplyNoHalf(profitRateTotal, profitStat.getProfitRate());
                profitStat.setProfitRate(round(profitStat.getProfitRate(), 4));
            }
            profitSectionStat.setProfitRateTotal(round(profitRateTotal, 4));
        }
        return profitSectionStat;
    }

    public Double getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(Double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public Double getProfitRateTotal() {
        return profitRateTotal;
    }

    public void setProfitRateTotal(Double profitRateTotal) {
        this.profitRateTotal = profitRateTotal;
    }

    public List<ProfitStat> getStatList() {
        return statList;
    }

    public void setStatList(List<ProfitStat> statList) {
        this.statList = statList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ProfitSectionStat)) return false;
        ProfitSectionStat that = (ProfitSectionStat) o;
        return Objects.equals(profitTotal, that.profitTotal) && Objects.equals(profitRateTotal, that.profitRateTotal) && Objects.equals(statList, that.statList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(profitTotal, profitRateTotal, statList);
    }

    @Override
    public String toString() {
        return "ProfitSectionStat{" +
                "profitTotal=" + profitTotal +
                ", profitRateTotal=" + profitRateTotal +
                ", statList=" + statList +
                '}';
    }
}
