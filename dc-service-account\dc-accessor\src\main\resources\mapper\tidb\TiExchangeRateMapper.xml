<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiHgtExchangeRateMapper">
    <resultMap id="BaseResultMap" type="as_exchangeRate">
        <result column="BIZDATE" property="bizDate"/>
        <result column="MARKET" property="market"/>
        <result column="TYPE" property="type"/>
        <result column="EXCHANGE_RATE" property="exchangeRate"/>
    </resultMap>

    <select id="getLastestExchangeRate" resultMap="BaseResultMap">
        SELECT BIZDATE, MARKET, TYPE, EXCHANGE_RATE FROM (
        SELECT BIZDATE, MARKET, TYPE, EXCHANGE_RATE, row_number() over(partition by MARKET, TYPE order by BIZDATE desc) rn
        FROM ATCENTER.EXCHANGE_RATE
        <where>
            <if test="bizDate != null">
                AND BIZDATE <![CDATA[<=]]> #{bizDate}
            </if>
            <if test="market != null">
                AND MARKET = #{market}
            </if>
            <if test="type != null">
                AND TYPE = #{type}
            </if>
        </where>
        )t WHERE t.rn <![CDATA[<=]]> 2 order by BIZDATE desc
    </select>
    <select id="getRateListByBizdate" resultMap="BaseResultMap">
        select BIZDATE, MARKET, TYPE, EXCHANGE_RATE
        FROM ATCENTER.EXCHANGE_RATE
        <where>
            <if test="bizDate!=null">
                and BIZDATE <![CDATA[=]]> #{bizDate}
            </if>
        </where>

    </select>

    <select id="getLatestRateList" resultMap="BaseResultMap">
        select er2.BIZDATE,
               er2.MARKET,
               er2.type,
               er2.EXCHANGE_RATE
        from atcenter.EXCHANGE_RATE er2
        where er2.BIZDATE = (select max(bizdate)
                             from atcenter.EXCHANGE_RATE er);
    </select>
</mapper>