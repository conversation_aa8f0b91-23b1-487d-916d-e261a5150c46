package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntity;

/**
 * 能力账单表
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/31.
 */
public class AbilityBill extends BaseEntity {

    private Long fundId; //资金账号
    private Integer indexKey; //时间槽索引key，年:yyyy,月：yyyyMM
    private String timeSlot; //时间槽类型，年:Y，月:M
    private String name; //能力名称
    private String code; //能力标识，唯一性
    private Integer rankIndex; //排名
    private Double rankPercent; //排名百分比

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }

    public Double getRankPercent() {
        return rankPercent;
    }

    public void setRankPercent(Double rankPercent) {
        this.rankPercent = rankPercent;
    }
}
