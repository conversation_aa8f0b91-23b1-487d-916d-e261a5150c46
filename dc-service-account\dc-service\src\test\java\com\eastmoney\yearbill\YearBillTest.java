package com.eastmoney.yearbill;

import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.service.handler.BillHandler;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * @Project dc-service-account
 * @Description
 * <AUTHOR>
 * @Date 2023/11/8 16:57
 * @Version 1.0
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class YearBillTest {

    @Autowired
    BillHandler billHandler;

    @Test
    public void queryYearBill() {
        Map<String, Object> params = new HashMap<>();
        params.put("indexKey", 2023);
        params.put("fundId", "************");
        //************  ATCENTER.BD_TRADE_BILL_FINAL表中 subs_new_cnt：16  subs_new_perc：0.2345   atcenter.IPO_TRADE_BILL该资金账号tradecount：19   tradecountpercent：0
        YearBillExtend userYearBill = billHandler.getUserYearBill(params);
        Assertions.assertNotNull(userYearBill.getIpoTradeBill());
        Assertions.assertTrue(16 == userYearBill.getIpoTradeBill().getTradeCount());
        Assertions.assertTrue(0.2345 == userYearBill.getIpoTradeBill().getTradeCountPercent().doubleValue());

        params.put("fundId", "************");
        //************  ATCENTER.BD_TRADE_BILL_FINAL表中 subs_new_cnt：77  subs_new_perc：0.3194   atcenter.IPO_TRADE_BILL 无该资金账号
        userYearBill = billHandler.getUserYearBill(params);
        Assertions.assertNotNull(userYearBill.getIpoTradeBill());
        Assertions.assertTrue(77 == userYearBill.getIpoTradeBill().getTradeCount());
        Assertions.assertTrue(0.3194 == userYearBill.getIpoTradeBill().getTradeCountPercent().doubleValue());

        params.put("fundId", "321100010483");
        //321100010483  ATCENTER.BD_TRADE_BILL_FINAL表中无记录      atcenter.IPO_TRADE_BILL 该资金账号  tradecount：2   tradecountpercent：0.1
        userYearBill = billHandler.getUserYearBill(params);
        Assertions.assertNotNull(userYearBill.getIpoTradeBill());
        Assertions.assertTrue(0 == userYearBill.getIpoTradeBill().getTradeCount());
        Assertions.assertTrue(0 == userYearBill.getIpoTradeBill().getTradeCountPercent().doubleValue());

        params.put("fundId", "1111111111111");
        //321100010483  ATCENTER.BD_TRADE_BILL_FINAL表中无记录      atcenter.IPO_TRADE_BILL 表中无记录
        userYearBill = billHandler.getUserYearBill(params);
        Assertions.assertNotNull(userYearBill.getIpoTradeBill());
        Assertions.assertTrue(0 == userYearBill.getIpoTradeBill().getTradeCount());
        Assertions.assertTrue(0 == userYearBill.getIpoTradeBill().getTradeCountPercent().doubleValue());
    }

    @Test
    public void queryYearBill2023() {

        Map<String, Object> params = new HashMap<>();
        params.put("indexKey", 2023);
        params.put("fundId", "************");

        //INSERT INTO ATCENTER.BD_TRADE_BILL_YEAR (FUND_CODE,INDEXKEY,USER_AGE,USER_SEX,OPEN_DATE,OPEN_DAYS_PERC,FTRADE_DATE,FTRADE_SKTNAME,FTRADE_ALL_CNT,FTRADE_ALL_PROFIT,FSUBS_NEW_DATE,FSTOCK_BALLOT_DATE,FSTOCK_BALLOT_SKTNAME,FSTOCK_BALLOT_PROFIT,CLEAR_SECURITY_NUM,CLEAR_PROFITSECURITY_NUM,CLEAR_PROFIT_RATE,Clear_Most_Profit_LDate,MOST_TRADES_DATE,MOST_TRADES_CNT,PROFIT_BESTMONTH_DATE,CONCEPT_SECTORS_CNT,CONCEPT_SECTORS_NAMES,CONCEPT_SECTORS_MOST,CONCEPT_SECTORS_UPPERC,TURN_EARN_PERC,FP_FDATE,FP_FNAME,FP_MAX_VAL,FP_NUM_PERC) VALUES
        //(************,2023,18,'男',20231017,0.1700,20231116,'牛才',10,0.3000,20231115,20231114,'经济',0.4000,20,30,0.5800,20231113,20231112,40,20231111,50,'阿松大','GPT',0.4800,0.4900,20231110,'123',NULL,NULL)
        //;
        YearBillExtend userYearBill = billHandler.getUserYearBill(params);
        //用户年龄
        Assertions.assertTrue(18 == userYearBill.getBaseInfo().getUserAge());
        //用户性别
        Assertions.assertTrue("男".equals(userYearBill.getBaseInfo().getUserSex()));
        //开户日期
        Assertions.assertTrue(20231017 == userYearBill.getBaseInfo().getOpenDate());
        //开户天数超越股友百分比
        Assertions.assertTrue(0.17 == userYearBill.getBaseInfo().getOpenDaysPerc().doubleValue());
        //首次交易日
        Assertions.assertTrue(20231116 == userYearBill.getFirstDay().getFTradeDate());
        //首次交易股票名
        Assertions.assertTrue("牛才".equals(userYearBill.getFirstDay().getFTradeStkName()));
        //首次交易的产品在当年累计交易次数
        Assertions.assertTrue(10 == userYearBill.getFirstDay().getFTradeAllCnt());
        //首次交易的产品在当年的盈亏金额
        Assertions.assertTrue(0.3 == userYearBill.getFirstDay().getFTradeAllProfit().doubleValue());
        //首次打新的日期
        Assertions.assertTrue(20231115 == userYearBill.getFirstDay().getFSubsNewDate());
        //首次中签的日期
        Assertions.assertTrue(20231114 == userYearBill.getFirstDay().getFStockBallotDate());
        //首次中签的产品名称
        Assertions.assertTrue("经济".equals(userYearBill.getFirstDay().getFStockBallotStkName()));
        //首次中签的产品的中签收益金额
        Assertions.assertTrue(0.4 == userYearBill.getFirstDay().getFStockBallotProfit().doubleValue());
        //全年累计清仓次数
        Assertions.assertTrue(20 == userYearBill.getClearNum());
        //全年获利清仓次数
        Assertions.assertTrue(30 == userYearBill.getClearProfitNum());
        //清仓盈利率
        Assertions.assertTrue(0.58 == userYearBill.getClearProfitRate().doubleValue());
        //全年赚钱最多的产品的最后一次清仓日期
        Assertions.assertTrue(20231113 == userYearBill.getClearMostProfitLDate());
        //单日交易次数最多的日期
        Assertions.assertTrue(20231112 == userYearBill.getMostTrdDay().getMostTradesDate());
        //单日交易次数最多的次数
        Assertions.assertTrue(40 == userYearBill.getMostTrdDay().getMostTradesCnt());
        //月收益金额最高的月份
        Assertions.assertTrue(20231111 == userYearBill.getProfitBestMonthDate());
        //抓住的热门概念板块个数
        Assertions.assertTrue(50 == userYearBill.getConceptSectors().getConceptSectorsCnt());
        //抓住的热门概念名称
        Assertions.assertTrue("阿松大".equals(userYearBill.getConceptSectors().getConceptSectorsNames()));
        //抓住的热门概念中年内最大涨幅概念名称
        Assertions.assertTrue("GPT".equals(userYearBill.getConceptSectors().getConceptSectorsMost()));
        //抓住的热门概念中年内最大涨幅
        Assertions.assertTrue(0.48 == userYearBill.getConceptSectors().getConceptSectorsUpperc().doubleValue());
        //扭亏为盈的人数占普通交易用户的百分比
        Assertions.assertTrue(0.49 == userYearBill.getTurnEarnPerc().doubleValue());
    }


    @Test
    public void queryYearBill2023_2() {
        Map<String, Object> params = new HashMap<>();
        params.put("indexKey", 2023);
        params.put("fundId", "540700002502");
        YearBillExtend userYearBill = billHandler.getUserYearBill(params);
        //首次交易买/卖的产品编码
        Assertions.assertTrue("00023".equals(userYearBill.getFirstDay().getFTradeStkCode()));
        //首次交易买/卖的市场
        Assertions.assertTrue("1".equals(userYearBill.getFirstDay().getFTradeMarket()));
    }
}