package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggHgtReferRateMapper;
import com.eastmoney.common.entity.HgtReferRate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@SqlServerTarget()
@Service
public class OggHgtReferRateDaoImpl implements OggHgtReferRateDao {
    @Autowired
    private OggHgtReferRateMapper oggHgtReferRateMapper;

    @Override
    public HgtReferRate getHgtReferRate(Map<String, Object> param) {
        return oggHgtReferRateMapper.getHgtReferRate(param);
    }
}
