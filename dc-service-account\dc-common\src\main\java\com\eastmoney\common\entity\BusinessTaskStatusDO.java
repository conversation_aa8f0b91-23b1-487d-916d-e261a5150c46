package com.eastmoney.common.entity;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/11 9:02
 */
public class BusinessTaskStatusDO extends BaseEntity{
    private String businessTaskHandle;
    private Integer executeStatus;
    private String businessTaskHandleName;

    public String getBusinessTaskHandle() {
        return businessTaskHandle;
    }

    public void setBusinessTaskHandle(String businessTaskHandle) {
        this.businessTaskHandle = businessTaskHandle;
    }

    public Integer getExecuteStatus() {
        return executeStatus;
    }

    public void setExecuteStatus(Integer executeStatus) {
        this.executeStatus = executeStatus;
    }

    public String getBusinessTaskHandleName() {
        return businessTaskHandleName;
    }

    public void setBusinessTaskHandleName(String businessTaskHandleName) {
        this.businessTaskHandleName = businessTaskHandleName;
    }
}
