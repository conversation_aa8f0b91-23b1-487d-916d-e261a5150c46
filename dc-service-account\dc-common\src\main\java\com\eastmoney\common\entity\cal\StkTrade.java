package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created by robin on 2016/8/24.
 * 持仓收益 每一笔成交信息
 * 数据来源：清算后的交割单信息
 * <AUTHOR>
 */
public class StkTrade extends BaseEntityCal{

    private Long fundId;//资金账号

    private String market;//交易市场

    private String stkCode;//证券代码

    private Integer bizDate;//交易时间

    private Integer matchQty;//成交数量

    private Double matchAmt;//成交金额

    private Double matchPrice;//成交价格

    private Double totalSxf;//总佣金(净佣金+交易规费)

    private Double feeYhs;//印花税

    private Double other;//其他费用(过户费+清算费+其他费)

    private String bsFlag;//买卖类别 买入：'0B' 卖出 '0S'

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Integer getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Integer matchQty) {
        this.matchQty = matchQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public Double getTotalSxf() {
        return totalSxf;
    }

    public void setTotalSxf(Double totalSxf) {
        this.totalSxf = totalSxf;
    }

    public Double getFeeYhs() {
        return feeYhs;
    }

    public void setFeeYhs(Double feeYhs) {
        this.feeYhs = feeYhs;
    }

    public Double getOther() {
        return other;
    }

    public void setOther(Double other) {
        this.other = other;
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag;
    }
}
