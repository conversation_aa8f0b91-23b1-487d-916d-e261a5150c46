package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/2.
 */
public class HgtRefRate {
    public Integer serverid;
    public String market;
    public String moneytype;
    public Double buyrate;
    public Double daybuyriserate;
    public Double nightbuyriserate;
    public Double salerate;
    public Double daysaleriserate;
    public Double nightsaleriserate;
    public Double midrate;
    public Double settrate;
    public Integer sysdate;
    public String remark;

    public Integer getServerid() {
        return serverid;
    }

    public void setServerid(Integer serverid) {
        this.serverid = serverid;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getMoneytype() {
        return moneytype;
    }

    public void setMoneytype(String moneytype) {
        this.moneytype = moneytype;
    }

    public Double getBuyrate() {
        return buyrate;
    }

    public void setBuyrate(Double buyrate) {
        this.buyrate = buyrate;
    }

    public Double getDaybuyriserate() {
        return daybuyriserate;
    }

    public void setDaybuyriserate(Double daybuyriserate) {
        this.daybuyriserate = daybuyriserate;
    }

    public Double getNightbuyriserate() {
        return nightbuyriserate;
    }

    public void setNightbuyriserate(Double nightbuyriserate) {
        this.nightbuyriserate = nightbuyriserate;
    }

    public Double getSalerate() {
        return salerate;
    }

    public void setSalerate(Double salerate) {
        this.salerate = salerate;
    }

    public Double getDaysaleriserate() {
        return daysaleriserate;
    }

    public void setDaysaleriserate(Double daysaleriserate) {
        this.daysaleriserate = daysaleriserate;
    }

    public Double getNightsaleriserate() {
        return nightsaleriserate;
    }

    public void setNightsaleriserate(Double nightsaleriserate) {
        this.nightsaleriserate = nightsaleriserate;
    }

    public Double getMidrate() {
        return midrate;
    }

    public void setMidrate(Double midrate) {
        this.midrate = midrate;
    }

    public Double getSettrate() {
        return settrate;
    }

    public void setSettrate(Double settrate) {
        this.settrate = settrate;
    }

    public Integer getSysdate() {
        return sysdate;
    }

    public void setSysdate(Integer sysdate) {
        this.sysdate = sysdate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
