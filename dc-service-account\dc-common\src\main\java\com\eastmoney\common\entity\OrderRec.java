package com.eastmoney.common.entity;

import com.eastmoney.common.annotation.ZTFiled;

/**
 * Created on 2016/3/1
 * 委托流水
 *
 * <AUTHOR>
 */
public class OrderRec extends PushEntity {
    @ZTFiled("wtbh")
    private Long orderSno;  /*委托序号*/
    @ZTFiled("wtph")
    private String orderGroup;  /*批量委托批号*/
    @ZTFiled("htxh")
    private String orderId;  /*申报合同序号*/
    @ZTFiled("wtrq")
    private Integer orderDate;  /*委托交易日期*/
    @ZTFiled("czrq")
    private Integer operDate;/*操作日期*/
    @ZTFiled("wtsj")
    private Integer operTime;  /*发生委托时间*/
    @ZTFiled("khdm")
    private Long custId;  /*客户代码*/
    @ZTFiled("khxm")
    private String custName;  /*客户姓名*/
    @ZTFiled("jgbm")
    private String orgId;  /*分支机构*/
    private String brhId;  /*机构分支*/
    @ZTFiled("zjzh")
    private Long fundId;  /*资产帐户*/
    @ZTFiled("hb")
    private String moneyType;  /*货币代码*/
    private String fundKind;  /*资金类别*/
    private String fundLevel;  /*资金级别*/
    private String fundGroup;  /*资金分组*/
    @ZTFiled("gddm")
    private String secuId;  /*股东代码*/
    private String rptSecuId;  /*报盘股东代码*/
    @ZTFiled("market")
    private String market;  /*交易市场*/
    @ZTFiled("jyxw")
    private String seat;  /*报盘席位*/
    @ZTFiled("zqdm")
    private String stkCode;  /*证券代码*/
    @ZTFiled("zqmc")
    private String stkName;  /*证券名称*/
    private String stkType;  /*证券类别*/
    @ZTFiled("wtjg")
    private Double orderPrice; /*委托价格*/
    @ZTFiled("yjlx")
    private Double bondIntr;  /*国债利息*/
    @ZTFiled("wtsl")
    private Long orderQty;   /*委托数量*/
    @ZTFiled("cjsl")
    private Long matchQty;   /*成交数量*/
    @ZTFiled("cdsl")
    private Long cancelQty;   /*撤单数量*/
    private Long reportQty;   /*申报数量*/
    private Double tradeFee;   /*交易费用*/
    @ZTFiled("djje")
    private Double orderFrzAmt;   /*委托冻结金额*/
    private Double clearAmt;   /*实时清算金额*/
    @ZTFiled("cjje")
    private Double matchAmt;   /*成交金额*/
    private String orderType;  /*订单类型, 0普通 1境外委托（无客户）*/
    private String nightFlag;  /*夜市委托标志 '0' 普通委托 '1' 夜市委托*/
    @ZTFiled("mmlb")
    private String bsFlag;  /*买卖方向,数据字典[Emmlb]*/
    @ZTFiled("cdbz")
    private String cancelFlag;  /*撤单标志, 'F' 正常 'T' 撤单*/
    @ZTFiled("cjjg")
    private Double matchPrice;/*成交价格*/
    private String combType;  /*组合类型  '0'普通委托 '1'组合宝委托 '2'委托补单 '3'大宗交易委托 'V'订单系统灾难回切补录委托*/
    @ZTFiled("bpsj")
    private Long reportTime;  /*申报时间*/
    @ZTFiled("wtzt")
    private String orderStatus;  /*委托状态*/
    private Long recNum;  /*接口记录号, -1未发 0内部撤单> 0 已成功写委托或撤单DBF*/
    private String jysOrderId;  /*交易所的合同序号*/
    private String cancelOrderId;  /*撤单时，填写本笔撤单的券商合同序号(每次撤单时重新产生撤单的合同序号)*/
    private String sourceType;  /*发起方*/
    private String bankCode;  /*银行代码*/
    private String channel;  /*通道号*/
    private Long agentId;  /*代理人代码*/
    private Long operId;  /*操作柜员代码*/
    private String netAddr;  /*操作站点*/
    @ZTFiled("wtqd")
    private String operWay;  /*操作方式*/
    private String reportKind; /*资金报表分类*/
    @ZTFiled("bzxx")
    private String remark;  /*其他备注*/
    private String creditId;  /*融资品种标识*/
    private String creditFlag;  /*融资开仓平仓强平*/
    private String remark1;  /*其他备注*/
    private String mkt_branchId; /*交易所营业部代码*/
    private String rptSeat;  /*申报席位*/
    @ZTFiled("dwc")
    private String postStr;

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public String getPostStr() {
        return postStr;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public Integer getOperDate() {
        return operDate;
    }

    public void setOperDate(Integer operDate) {
        this.operDate = operDate;
    }

    public Long getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Long orderSno) {
        this.orderSno = orderSno;
    }

    public String getOrderGroup() {
        return orderGroup;
    }

    public void setOrderGroup(String orderGroup) {
        this.orderGroup = orderGroup.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId.trim();
    }

    public Integer getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Integer orderDate) {
        this.orderDate = orderDate;
    }

    public Integer getOperTime() {
        return operTime;
    }

    public void setOperTime(Integer operTime) {
        this.operTime = operTime;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName.trim();
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId.trim();
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType.trim();
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind.trim();
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel.trim();
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup.trim();
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId.trim();
    }

    public String getRptSecuId() {
        return rptSecuId;
    }

    public void setRptSecuId(String rptSecuId) {
        this.rptSecuId = rptSecuId.trim();
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market.trim();
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat.trim();
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode.trim();
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName.trim();
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType.trim();
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public Long getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Long orderQty) {
        this.orderQty = orderQty;
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Long getCancelQty() {
        return cancelQty;
    }

    public void setCancelQty(Long cancelQty) {
        this.cancelQty = cancelQty;
    }

    public Long getReportQty() {
        return reportQty;
    }

    public void setReportQty(Long reportQty) {
        this.reportQty = reportQty;
    }

    public Double getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(Double tradeFee) {
        this.tradeFee = tradeFee;
    }

    public Double getOrderFrzAmt() {
        return orderFrzAmt;
    }

    public void setOrderFrzAmt(Double orderFrzAmt) {
        this.orderFrzAmt = orderFrzAmt;
    }

    public Double getClearAmt() {
        return clearAmt;
    }

    public void setClearAmt(Double clearAmt) {
        this.clearAmt = clearAmt;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType.trim();
    }

    public String getNightFlag() {
        return nightFlag;
    }

    public void setNightFlag(String nightFlag) {
        this.nightFlag = nightFlag.trim();
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag;
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag.trim();
    }

    public String getCombType() {
        return combType;
    }

    public void setCombType(String combType) {
        this.combType = combType.trim();
    }

    public Long getReportTime() {
        return reportTime;
    }

    public void setReportTime(Long reportTime) {
        this.reportTime = reportTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus.trim();
    }

    public Long getRecNum() {
        return recNum;
    }

    public void setRecNum(Long recNum) {
        this.recNum = recNum;
    }

    public String getJysOrderId() {
        return jysOrderId;
    }

    public void setJysOrderId(String jysOrderId) {
        this.jysOrderId = jysOrderId.trim();
    }

    public String getCancelOrderId() {
        return cancelOrderId;
    }

    public void setCancelOrderId(String cancelOrderId) {
        this.cancelOrderId = cancelOrderId.trim();
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel.trim();
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getNetAddr() {
        return netAddr;
    }

    public void setNetAddr(String netAddr) {
        this.netAddr = netAddr.trim();
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay.trim();
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = reportKind.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark.trim();
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId.trim();
    }

    public String getCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(String creditFlag) {
        this.creditFlag = creditFlag.trim();
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1.trim();
    }

    public String getMkt_branchId() {
        return mkt_branchId;
    }

    public void setMkt_branchId(String mkt_branchId) {
        this.mkt_branchId = mkt_branchId.trim();
    }

    public String getRptSeat() {
        return rptSeat;
    }

    public void setRptSeat(String rptSeat) {
        this.rptSeat = rptSeat.trim();
    }

}
