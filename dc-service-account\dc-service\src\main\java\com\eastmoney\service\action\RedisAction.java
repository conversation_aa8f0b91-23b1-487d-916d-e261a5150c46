package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.datacenter.redis.client.RedisProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description redis操作接口
 * @date 2024/10/14 15:58
 */
@Action
@Component
public class RedisAction {

    @Autowired
    private RedisProxy redisProxy;

    @RequestMapping("/delRedisCache")
    public String delRedisCache(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"redisKey"});
        String redisKey = CommonUtil.convert(params.get("redisKey"), String.class);
        redisProxy.del(redisKey);
        return redisKey + "删除成功";
    }

    @RequestMapping("/getRedisCache")
    public String getRedisCache(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"redisKey"});
        String redisKey = CommonUtil.convert(params.get("redisKey"), String.class);
        return redisKey + ":" + redisProxy.get(redisKey);
    }
}
