package com.eastmoney.service.util;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by sunyuncai on 2016/10/5.
 */
public class SQLPreventAttackUtil {
    private static String[] badSqlStrArray = null;
    static {
        String badStr = "'|and |exec |execute |insert |create |drop |table |from |grant |use |group_concat |column_name |" +
                "information_schema.columns |table_schema |union |where |select |delete |update |order |by |count |*|" +
                "chr |mid |master |truncate |char |declare |or |;|--|+|like|//|/|%|#";
        badSqlStrArray = badStr.split("\\|");
    }

    public static boolean hasAttackStr(String str){
        if (StringUtils.isNotBlank(str)) {
            for (String badSqlStr : badSqlStrArray) {
                if (str.toLowerCase().contains(badSqlStr))
                    return true;
            }
        }
        return false;
    }

}
