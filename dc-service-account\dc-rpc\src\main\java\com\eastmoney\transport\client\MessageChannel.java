package com.eastmoney.transport.client;

import com.eastmoney.transport.exception.RemotingException;
import com.eastmoney.transport.model.Message;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import org.slf4j.LoggerFactory;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/31
 * Time: 10:31
 */
public class MessageChannel {
    private static org.slf4j.Logger LOG = LoggerFactory.getLogger(MessageChannel.class);
    private final Channel channel;

    public MessageChannel(Channel channel) {
        this.channel = channel;
    }

    public void send(Message message) throws Exception{
        try {
            ChannelFuture future = channel.writeAndFlush(message);
            Throwable cause = future.cause();
            if (cause != null) {
                throw cause;
            }
        } catch (Throwable e) {
            throw new RemotingException(this,"发送出现异常," + message +  e.getMessage());
        }
    }

    public Channel getChannel() {
        return channel;
    }

}
