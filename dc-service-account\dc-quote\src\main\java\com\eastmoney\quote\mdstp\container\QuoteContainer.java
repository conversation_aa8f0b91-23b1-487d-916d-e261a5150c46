package com.eastmoney.quote.mdstp.container;/**
 * Created by 1 on 16-10-24.
 */

import com.eastmoney.quote.mdstp.model.HkQtRec;
import com.eastmoney.quote.mdstp.model.QtRec;
import com.eastmoney.quote.mdstp.model.Rec;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Created on 16-10-24
 *
 * <AUTHOR>
 */
public class QuoteContainer {

    private static final Logger LOGGER = LoggerFactory.getLogger(QuoteContainer.class);

    private static ConcurrentHashMap<String, QtRec> qtRecContainer = new ConcurrentHashMap<String, QtRec>();
    private static ConcurrentHashMap<String, HkQtRec> hkQtRecContainer = new ConcurrentHashMap<String, HkQtRec>();

    public static void updateQtRec(String emCode, QtRec qtRec) {
        qtRecContainer.put(emCode, qtRec);
    }

    public static void updateHkQtRec(String code, HkQtRec hkQtRec) {
        hkQtRecContainer.put(code, hkQtRec);
    }

    public static Rec getRec(String stkCode, String market) {
        if (StringUtils.isBlank(stkCode)) {
//            throw new RuntimeException("stkCode为空");
            LOGGER.warn("stkCode为空");
            return null;
        }
        stkCode = StringUtils.trim(stkCode);
        /**
         * select * from run..dictvalue where  dictitem = 'Ajys'
         * --0 深A
         --1 沪A
         --2 深B
         --3 沪B
         --5 沪港通
         --6 股转A（没有）
         --7 股转B (没有)
         --A 非交易债券 (需要区分沪深市场)
         --J 开放式基金
         */
        return getRecByStock(stkCode, market);

    }

    public static Set<String> getAllRecKey(Set<String> stockSet) {
        return stockSet.stream()
                .filter(s -> {
                    String[] split = s.split("-");
                    String stkCode = split[0];
                    String market = split[1];
                    return getRecByStock(stkCode, market) != null;
                })
                .collect(Collectors.toSet());

    }

    /**
     * 将码表的key转化为行情map的key
     *
     * @param stkCode
     * @param market
     * @return
     */
    private static Rec getRecByStock(String stkCode, String market) {
        Rec rec;
        if ("5".equals(market) || "S".equals(market)) {
            rec = hkQtRecContainer.get(stkCode);
        } else {
            String suffix = "";
            if ("0".equals(market) || "2".equals(market)) {
                suffix = ".SZ";
            } else if ("1".equals(market) || "3".equals(market)) {
                suffix = ".SH";
            } else if ("6".equals(market) || "7".equals(market) || "B".equals(market)) {
                suffix = ".OC";
            }
            rec = qtRecContainer.get(stkCode + suffix);
        }
        return rec;
    }
}
