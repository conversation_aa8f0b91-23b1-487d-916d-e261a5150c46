package com.eastmoney.common.entity;

import com.eastmoney.common.entity.cal.AssetHis;

import static com.eastmoney.common.util.ArithUtil.*;

/**
 * <AUTHOR>
 * @create 2024/2/5
 */
public class AssetDay {
    private Integer bizDate;//日期

    private Long fundId; //资金账号
    private Double asset;//资产
    private Double mktVal; // 市值

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Double getAsset() {
        return asset;
    }

    public void setAsset(Double asset) {
        this.asset = asset;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    /**
     * 将assetHis转为assetDay
     * @return
     */
    public static AssetDay of(AssetHis assetHis) {
        AssetDay assetDay = new AssetDay();
        assetDay.setBizDate(assetHis.getBizDate());
        assetDay.setFundId(assetHis.getFundId());
        assetDay.setAsset(
                round(
                        add(assetHis.getAsset(), assetHis.getOtcAsset())
                )
        );
        assetDay.setMktVal(round(assetHis.getMktval()));
        return assetDay;
    }
}
