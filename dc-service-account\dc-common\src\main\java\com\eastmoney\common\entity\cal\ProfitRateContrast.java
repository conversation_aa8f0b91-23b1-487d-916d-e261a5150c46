package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created by robin on 2016/7/18.
 * 收益率参照表
 * <AUTHOR>
 */
public class ProfitRateContrast extends BaseEntityCal {


    private String unit;//单位
    private Integer allNum;//投资者总人数
    private Double highAvg;//高手平均收益率
    private Double allAvg;//所有人平均收益率
    private Integer gainNum;//盈利人数
    private Double middleRate;//收益率中位数
    private Integer lossNum;//亏损人数

    public String getUnit() {
        return unit;
    }

    public Integer getAllNum() {
        return allNum;
    }

    public Double getHighAvg() {
        return highAvg;
    }

    public Double getAllAvg() {
        return allAvg;
    }

    public Integer getGainNum() {
        return gainNum;
    }

    public void setUnit(String unit) {
        this.unit = unit.trim();
    }

    public void setAllNum(Integer allNum) {
        this.allNum = allNum;
    }

    public void setHighAvg(Double highAvg) {
        this.highAvg = highAvg;
    }

    public void setAllAvg(Double allAvg) {
        this.allAvg = allAvg;
    }

    public void setGainNum(Integer gainNum) {
        this.gainNum = gainNum;
    }

    public Double getMiddleRate() {
        return middleRate;
    }

    public void setMiddleRate(Double middleRate) {
        this.middleRate = middleRate;
    }

    public Integer getLossNum() {
        return lossNum;
    }

    public void setLossNum(Integer lossNum) {
        this.lossNum = lossNum;
    }
}
