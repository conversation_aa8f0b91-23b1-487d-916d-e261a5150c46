package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.AbilityBillDao;
import com.eastmoney.accessor.dao.oracle.AssetHisDao;
import com.eastmoney.accessor.dao.oracle.MajorBillDao;
import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.dao.oracle.PublishBillDao;
import com.eastmoney.accessor.dao.oracle.ReferenceProfitRateDao;
import com.eastmoney.accessor.dao.oracle.StockBillDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.enums.ExchangeRateTypeEnum;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.StkAsset;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.AbilityBill;
import com.eastmoney.common.entity.cal.AcctDiagnoseBO;
import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.entity.cal.BestMonth;
import com.eastmoney.common.entity.cal.HgtRateChangeDO;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.MajorBillYear;
import com.eastmoney.common.entity.cal.PublishBill;
import com.eastmoney.common.entity.cal.ReferenceProfitRate;
import com.eastmoney.common.entity.cal.StockBill;
import com.eastmoney.common.entity.cal.ZhfxSettleStatus;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.HgtRateChangeResultEnum;
import com.eastmoney.common.sysEnum.ZhfxSettleStatusEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.HgtExchangeRateService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.YearBillService;
import com.eastmoney.service.cache.ZhfxSettleStatusService;
import com.eastmoney.service.service.DiagnoseService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import com.eastmoney.service.util.IndexKeyUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Administrator on 2017/4/1.
 */
@Service
public class BillHandler {
    private static final Logger LOG = LoggerFactory.getLogger(BillHandler.class);
    private static final Integer YEAR_2000 = 2000;
    private static final Integer YEAR_2100 = 2100;
    private static final Integer MONTH_200001 = 200001;
    private static final Integer MONTH_210001 = 210001;
    @Autowired
    private MajorBillDao majorBillDao;
    @Autowired
    private StockBillDao stockBillDao;
    @Autowired
    private ProfitDayDao profitDayDao;
    @Autowired
    private ReferenceProfitRateDao referenceProfitRateDao;
    @Autowired
    private PublishBillDao publishBillDao;
    @Autowired
    private AbilityBillDao abilityBillDao;
    @Autowired
    private AssetHisDao assetHisDao;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Autowired
    private YearBillService yearBillService;
    @Autowired
    private ZhfxSettleStatusService zhfxSettleStatusService;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private HgtExchangeRateService hgtExchangeRateService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private StkAssetService stkAssetService;
    @Autowired
    private DiagnoseService diagnoseService;
    @Autowired
    private FundAssetHandler fundAssetHandler;

    /**
     * 帐单基本信息
     *
     * @param params
     * @return
     */
    public List<MajorBill> getMajorBill(Map<String, Object> params) {
        List<MajorBill> list = majorBillDao.query(params);
        if (list.size() > 0) {
            int indexKey = CommonUtil.convert(params, "indexKey", Integer.class);
            Integer startDate = null;
            Integer endDate = null;
            //年度2017  月度201704
            if (indexKey > YEAR_2000 && indexKey < YEAR_2100) {
                startDate = indexKey * 10000;
                endDate = (indexKey + 1) * 10000;
            } else if (indexKey > MONTH_200001 && indexKey < MONTH_210001) {
                startDate = indexKey * 100;
                endDate = (indexKey + 1) * 100;
            }
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            Double profit = profitDayDao.getSumProfit(params);
            list.get(0).setProfit(profit);

            // 当清仓的次数为null或者0时，将交易胜率设置为null
            list.forEach(majorBill -> {
                if (Objects.isNull(majorBill.getTotalCount()) || majorBill.getTotalCount() <= 0) {
                    majorBill.setGainRate(null);
                }
            });
        }
        return list;
    }

    /**
     * 股票盈亏排名
     *
     * @param params
     * @return
     */
    public List<StockBill> getStockBill(Map<String, Object> params) {
        List<StockBill> list = stockBillDao.query(params);
        for (StockBill stockBill : list) {
            //总卖出金额
            double totalSale = stockBill.getTotalSale();
            //总买入金额
            double totalBuy = stockBill.getTotalBuy();
            //总卖出份额
            Long totalMatchQtySale = stockBill.getTotalMatchQtySale();
            //平均买卖差价
            double avgSpread = (totalSale - totalBuy) / totalMatchQtySale;
            stockBill.setAvgSpread(avgSpread);
        }
        return list;
    }

    /**
     * 业绩比较
     *
     * @param params
     * @return
     */
    public List<ReferenceProfitRate> getReferenceProfitRateList(Map<String, Object> params) {
        List<ReferenceProfitRate> referenceProfitRates = referenceProfitRateDao.query(params);
        return referenceProfitRates;
    }

    /**
     * 投资标签
     */
    public List<PublishBill> getInvestLabelList(Map<String, Object> params) {
        params.put("count", CommonUtil.convert(params.get("count"), Integer.class));
        return publishBillDao.query(params);
    }

    /**
     * 五维分析
     */
    public List<AbilityBill> getAbilityBillList(Map<String, Object> params) {
        List<AbilityBill> list = abilityBillDao.query(params);
        for (AbilityBill abilityBill : list) {
            abilityBill.setRankPercent(ArithUtil.sub(1.0, abilityBill.getRankPercent()));
        }
        return list;
    }

    /**
     * 年度帐单基本信息
     *
     * @param params
     * @return
     */
    public MajorBillYear getYearMajorBill(Map<String, Object> params) {
        MajorBillYear yearBillList = new MajorBillYear();
        int indexKey = CommonUtil.convert(params, "indexKey", Integer.class);
        List<MajorBill> billList = majorBillDao.query(params);
        if (billList.size() > 0) {
            Integer startDate = null;
            Integer endDate = null;
            //年度2018
            if (indexKey > YEAR_2000 && indexKey < YEAR_2100) {
                startDate = indexKey * 10000;
                endDate = (indexKey + 1) * 10000;
            }
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            Double profit = profitDayDao.getSumProfit(params);
            billList.get(0).setProfit(profit);
            yearBillList.setMajorBill(billList.get(0));
        }
        List<MajorBill> monthList = majorBillDao.getBestMonth(params);
        if (monthList.size() > 0) {
            BestMonth bestMonth = new BestMonth();
            bestMonth.setIndexKey(monthList.get(0).getIndexKey());
            bestMonth.setProfit(monthList.get(0).getProfit());
            bestMonth.setProfitRate(monthList.get(0).getProfitRate());
            yearBillList.setBestMonth(bestMonth);
        }

        return yearBillList;
    }

    public AssetSection getAssetSectionBill(Map<String, Object> params) {
        AssetSection assetSection = new AssetSection();
        String fundId = CommonUtil.convert(params.get("fundId"), String.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);//月开始日或年开始日
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);//月截止日或当前日期
        Integer bizDate;

        if (startDate > endDate) {
            return null;
        }

        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }
        bizDate = assetNew.getBizDate();

        //开始日>最新清算日
        if (startDate > bizDate) {
            // 开始日>最新清算日 此时处于月初 并且清算bizdate在上个月末
            // 最新资产 = 期初资产  = 上个清算完的数据
            // 区间变动收益什么的都是0
            assetSection.setAsset(assetNew.getAsset());
            assetSection.setOtcAsset(assetNew.getOtcAsset());
            assetSection.setOpenAsset(assetNew.getAsset());
            assetSection.setOpenOtcAsset(assetNew.getOtcAsset());
            assetSection.setProfit(0.0);
            assetSection.setShiftInTotal(0.0);
            assetSection.setShiftOutTotal(0.0);
            assetSection.setNetTransfer(0.0);
            assetSection.setStartDate(startDate);
            assetSection.setBizDate(bizDate);
        }

        if (!tradeDateDao.isMarket(startDate)) {
            startDate = tradeDateDao.getNextMarketDay(startDate);
        }
        if (assetNew.getStartDate() != null) {
            startDate = assetNew.getStartDate() > startDate ? assetNew.getStartDate() : startDate;
        }

        //截止日>=最新清算日
        if (endDate >= bizDate) {
            endDate = bizDate;
        } else {
            //截止日<当前日期
            if (!tradeDateDao.isMarket(endDate)) {
                endDate = Integer.parseInt(tradeDateDao.getPreMarketDay(endDate));
            }
        }

        if (startDate > endDate) {
            return null;
        }

        Map<String, Object> obj = new HashMap<>(2);
        obj.put("fundId", fundId);
        obj.put("bizDate", endDate);
        //查询该区间截止日是否有历史资产
        List<AssetHis> assetHis = assetHisDao.query(obj);
        if (assetHis == null || assetHis.size() == 0) {
            return null;
        }
        //获取截止日资产
        AssetHis endAsset = assetHis.get(0);

        params.put("startDate", startDate);
        //计算区间总收益
        Double profitSum = profitDayDao.getSumProfit(params);
        //获取区间首日前一交易日
        Integer preStartDate = Integer.valueOf(tradeDateDao.getPreMarketDay(startDate));

        obj.put("bizDate", preStartDate);
        List<AssetHis> preAssetList = assetHisDao.query(obj);
        if (preAssetList == null) {
            preAssetList = new ArrayList<AssetHis>();
        }
        if (preAssetList.size() == 0) {
            AssetHis preAsset = new AssetHis();
            preAsset.setAsset(0.0);
            preAsset.setShiftInTotal(0.0);
            preAsset.setShiftOutTotal(0.0);
            preAsset.setNetTransfer(0.0);
            preAsset.setOtcAsset(0.0);
            preAssetList.add(preAsset);
        }
        AssetHis preAsset = preAssetList.get(0);

        assetSection.setAsset(endAsset.getAsset());
        assetSection.setOtcAsset(endAsset.getOtcAsset());
        assetSection.setOpenAsset(preAsset.getAsset());
        assetSection.setOpenOtcAsset(preAsset.getOtcAsset());
        assetSection.setProfit(profitSum);
        assetSection.setShiftInTotal(ArithUtil.sub(endAsset.getShiftInTotal(), preAsset.getShiftInTotal()));
        assetSection.setShiftOutTotal(ArithUtil.sub(endAsset.getShiftOutTotal(), preAsset.getShiftOutTotal()));
        assetSection.setNetTransfer(ArithUtil.sub(endAsset.getNetTransfer(), preAsset.getNetTransfer()));
        assetSection.setOtherNetTransfer(ArithUtil.sub(endAsset.getOtherNetTransfer(), preAsset.getOtherNetTransfer()));
        assetSection.setStartDate(startDate);
        assetSection.setBizDate(endDate);

        // 增加serverId相关的开关
        Integer serverId = assetNew.getServerId();
        if (nodeConfigService.getOtcNetTransferCalFlag(serverId)) {
            // 开关生效，则需要将 otherNetTransfer 字段去除 otcNetTransfer，也就是 netTransfer = otherNetTransfer
            // ((SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) OTHER_NET_TRANSFER,
            // ((OTC_SHIFT_IN_TOTAL + SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (OTC_SHIFT_OUT_TOTAL + SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) netTransfer
            assetSection.setNetTransfer(assetSection.getOtherNetTransfer());
        }

        return assetSection;
    }

    public YearBillExtend getUserYearBill(Map<String, Object> params) {
        Integer indexKey = CommonUtil.convert(params.get("indexKey"), Integer.class);
        if (indexKey > YEAR_2000 && indexKey < YEAR_2100) {
            params.put("startDate", indexKey * 10000);
            params.put("endDate", indexKey * 10000 + 1231);
        }

        return yearBillService.getYearBill(params);

    }

    public YearBillExtend getUserHalfYearBill(Map<String, Object> params) {
        Integer indexKey = CommonUtil.convert(params.get("indexKey"), Integer.class);
        params.put("startDate", indexKey * 10000);
        params.put("endDate", indexKey * 10000 + 630);
        return yearBillService.getYearBill(params);
    }

    public ZhfxSettleStatus getZhfxSettleStatus(Map<String, Object> params) {
        AssetNew assetInfo = assetNewService.getAssetInfo(params);
        if (assetInfo == null) {
            return new ZhfxSettleStatus(ZhfxSettleStatusEnum.INIT.getStatus());
        }
        return zhfxSettleStatusService.getZhfxSettleStatus();
    }

    /**
     * 港股通汇率波动提示
     *
     * @param params fundId 资金账号
     * @return
     */
    public Object getHgtRateChange(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        ZhfxSettleStatus zhfxSettleStatus = getZhfxSettleStatus(params);
        Integer bizDate = zhfxSettleStatus.getBizDate();
        if (bizDate == null) {
            // 包含两种情况：1.AssetNew没有该fundId；2.清算状态表没有查询到数据
            return new HgtRateChangeDO(HgtRateChangeResultEnum.NO_SKTASSET.getValue(), null);
        }
        // 如果正在清算，则不展示汇率波动提示
        if (ZhfxSettleStatusEnum.IN_PROCESS.getStatus().intValue() == zhfxSettleStatus.getStatus()) {
            return new HgtRateChangeDO(HgtRateChangeResultEnum.SETTLING.getValue(), bizDate);
        }

        // 如果用户没有港股通持仓，直接返回，不需要展示汇率波动提示
        List<StkAsset> hgtStkasset = stkAssetService.getStkAsset(fundId, bizDate);
        if (CollectionUtils.isEmpty(hgtStkasset)) {
            return new HgtRateChangeDO(HgtRateChangeResultEnum.NO_SKTASSET.getValue(), bizDate);
        }

        // 获取最新两日的港股通买入结算汇率波动
        Map<String, Object> rateParams = new HashMap<>();
        rateParams.put("bizDate", bizDate);
        rateParams.put("market", MarketEnum.SZ_HK.getValue());
        rateParams.put("type", ExchangeRateTypeEnum.BUY_SETT.getType());
        Double exchangeRateRatio = hgtExchangeRateService.getExchangeRateRatio(rateParams);

        // 汇率波动提示阈值
        Double rateThresh = CommConstants.RATE_THRESH;
        String rateConfig = nodeConfigService.getNodeConfig(CommConstants.HGT_RATE_CHANGE);
        if (!StringUtils.isEmpty(rateConfig)) {
            try {
                rateThresh = Double.valueOf(rateConfig.trim());
            } catch (NumberFormatException e) {
                LOG.warn("汇率波动提示阈值[{}]错误，使用默认值[{}]", rateConfig.trim(), CommConstants.RATE_THRESH);
            }
        }

        HgtRateChangeDO hgtExchangeRate = new HgtRateChangeDO(HgtRateChangeResultEnum.RATE_IN_RANGE.getValue(),
                exchangeRateRatio, bizDate);
        if (exchangeRateRatio != null && ArithUtil.ge(exchangeRateRatio, rateThresh)) {
            hgtExchangeRate.setResult(HgtRateChangeResultEnum.RATE_EXCEED_RANGE.getValue());
        }

        return hgtExchangeRate;
    }

    public AcctDiagnoseBO getAcctDiagnose(Map<String, Object> params) {
        if ("1".equals(params.get("isFiveDimension"))) {
            return diagnoseService.getFiveDimensionDiagnose(params);
        } else {
            return diagnoseService.getAcctUserDiagnose(params);
        }
    }

    /**
     * 获取账单中的资产走势
     *
     * @param params fundId, indexKey
     * @return 日资产
     */
    public List<AssetDay> getBillAssetTrend(Map<String, Object> params) {
        // 计算区间上一个交易日的资产
        final int CAL_LAST_TRADE_DAY_FLAG = -1;

        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String indexKey = CommonUtil.convert(params.get("indexKey"), String.class);

        DateRange dateRange = IndexKeyUtil.getDateRange(indexKey);
        int startDate = dateRange.getStartDate();
        int endDate = dateRange.getEndDate();
        // 只获取清算资产，不获取实时资产
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (Objects.isNull(assetNew)) {
            return Collections.emptyList();
        }
        endDate = Math.min(endDate, assetNew.getBizDate());

        Map<String, Object> newParams = new HashMap<>();
        newParams.put("fundId", fundId);
        newParams.put("startDate", startDate);
        newParams.put("endDate", endDate);
        newParams.put("beginIndex", CAL_LAST_TRADE_DAY_FLAG);
        return fundAssetHandler.getAssetDayTrendCustomize(newParams);
    }
}
