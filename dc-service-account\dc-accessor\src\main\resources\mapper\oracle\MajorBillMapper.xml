<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.MajorBillMapper">

    <sql id="All_Column">
        indexKey,profitRate,profit,profitRankIndex,profitRankPercent,VOLATILITY,SHARPERATIO,TRADECOUNT,
        gainRate,maxGain,maxLoss,maximum,totalCount,gainCount,lossCount,avgPosition,buyCount
    </sql>


    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.MajorBill">
        select <include refid="All_Column"/>
        from atcenter.B_Major_Bill
        where fundid = #{fundId} and indexKey = #{indexKey}
    </select>

    <select id="getBestMonth" resultType="com.eastmoney.common.entity.cal.MajorBill">
    SELECT <include refid="All_Column"/>
    FROM (SELECT *
          FROM ATCENTER.B_MAJOR_BILL
         WHERE FUNDID = #{fundId}
           AND TIMESLOT = 'M'
           AND INDEXKEY BETWEEN to_char(#{indexKey})||'01' AND to_char(#{indexKey})||'12'
         ORDER BY PROFIT DESC, INDEXKEY DESC)
    WHERE ROWNUM = 1
    </select>

    <select id="selectMonthProfitRate" resultType="com.eastmoney.common.entity.cal.ProfitRateDay">
        select indexKey bizDate,profitRate
        from atcenter.B_Major_Bill
        <where>
            timeSlot='M'
            <if test="fundId != null">
                and fundId=#{fundId}
            </if>
            <if test="startDate != null">
                and indexKey >= #{startDate}
            </if>
            <if test="endDate != null">
                and indexKey <![CDATA[<=]]> #{endDate}
            </if>
        </where>
    </select>
</mapper>