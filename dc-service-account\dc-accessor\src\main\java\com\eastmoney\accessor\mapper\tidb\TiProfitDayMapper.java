package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.ProfitDay;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/19.
 */
@Repository
public interface TiProfitDayMapper extends BaseMapper<ProfitDay, Long> {

    List getProfitStatList(Map<String, Object> params);

    Double getSumProfit(Map<String, Object> params);
}
