package com.eastmoney.service.service.profit.list;

import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateDayDao;
import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/12-18:27.
 *
 * <AUTHOR>
 */
@Service("profitDayListServiceSettle")
public class ProfitDayListServiceSettleImpl implements ProfitDayListService {
    @Autowired
    private ProfitDayDao profitDayDao;
    @Autowired
    private ProfitRateDayDao profitRateDayDao;

    @Override
    public List<ProfitDay> getDayProfitList(Map<String, Object> params) {
        return profitDayDao.query(params);
    }

    @Override
    public List<ProfitRateDay> getDayProfitRateList(Map<String, Object> params) {
        return profitRateDayDao.query(params);
    }
}
