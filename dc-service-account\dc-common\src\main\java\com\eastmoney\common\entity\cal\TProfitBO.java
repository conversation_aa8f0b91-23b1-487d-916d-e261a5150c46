package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;
import com.eastmoney.common.util.ArithUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/29 17:25
 */
public class TProfitBO extends BaseEntityCal {
    /**
     * 区间做T收益总和
     */
    private Double totalTProfit;
    /**
     * 区间做T收益总数
     */
    private Long totalTTimes;
    /**
     * 区间做T成功次数
     */
    private Long totalTSuccess;
    /**
     * 区间做T成功率
     */
    private Double totalTSuccessRate;
    /**
     * 做T操作列表
     */
    private List<tProfitRank> tProfitRankList;

    private List<TSecProfitDayDO> tProfitDayList;

    public static TProfitBO mergeTProfitBO(TProfitBO offLineTProfitBO, TProfitBO realTimeTProfitBO) {
        TProfitBO tProFitBO = new TProfitBO();
        tProFitBO.setTotalTSuccess(ArithUtil.addIgnoreNull(realTimeTProfitBO.getTotalTSuccess(),
                offLineTProfitBO.getTotalTSuccess()));
        tProFitBO.setTotalTTimes(ArithUtil.addIgnoreNull(realTimeTProfitBO.getTotalTTimes(),
                offLineTProfitBO.getTotalTTimes()));
        tProFitBO.setTotalTProfit(ArithUtil.add(realTimeTProfitBO.getTotalTProfit(),
                offLineTProfitBO.getTotalTProfit()));
        if (tProFitBO.getTotalTTimes() != 0) {
            tProFitBO.setTotalTSuccessRate(ArithUtil.div(tProFitBO.getTotalTSuccess(),
                    tProFitBO.getTotalTTimes(), 4));
        }
        tProFitBO.setEndDate(offLineTProfitBO.getEndDate());
        return tProFitBO;
    }


    public List<TProfitDetail> getTProfitDetailList() {
        return tProfitDetailList;
    }

    public void setTProfitDetailList(List<TProfitDetail> tProfitDetailList) {
        this.tProfitDetailList = tProfitDetailList;
    }

    /**
     * 个股每日明细
     */
    private List<TProfitDetail> tProfitDetailList;

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * 数据截止日期
     */
    private String endDate;

    public List<TSecProfitDayDO> getTProfitDayList() {
        return tProfitDayList;
    }

    public void setTProfitDayList(List<TSecProfitDayDO> tProfitDayList) {
        this.tProfitDayList = tProfitDayList;
    }

    public Double getTotalTProfit() {
        return totalTProfit;
    }

    public void setTotalTProfit(Double totalTProfit) {
        this.totalTProfit = totalTProfit;
    }


    public Long getTotalTTimes() {
        return totalTTimes;
    }

    public void setTotalTTimes(Long totalTTimes) {
        this.totalTTimes = totalTTimes;
    }

    public Long getTotalTSuccess() {
        return totalTSuccess;
    }

    public void setTotalTSuccess(Long totalTSuccess) {
        this.totalTSuccess = totalTSuccess;
    }

    public Double getTotalTSuccessRate() {
        return totalTSuccessRate;
    }

    public void setTotalTSuccessRate(Double totalTSuccessRate) {
        this.totalTSuccessRate = totalTSuccessRate;
    }

    public List<tProfitRank> getTProfitStockList() {
        return tProfitRankList;
    }

    public void setTProfitStockList(List<tProfitRank> tProfitRankList) {
        this.tProfitRankList = tProfitRankList;
    }

}
