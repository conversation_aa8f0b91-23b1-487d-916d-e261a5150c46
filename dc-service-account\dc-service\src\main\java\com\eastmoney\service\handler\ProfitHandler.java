package com.eastmoney.service.handler;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.tidb.CustCalenderDao;
import com.eastmoney.accessor.enums.SecProfitDayShowEnum;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.VO.CustCalenderVO;
import com.eastmoney.common.entity.VO.SecProfitDayInfoVO;
import com.eastmoney.common.entity.VO.SecProfitDayVO;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.model.ProfitInfo;
import com.eastmoney.common.sysEnum.ConstantEnum;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.sysEnum.PositionCalFlagEnum;
import com.eastmoney.common.sysEnum.RedisKeyEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.datacenter.redis.client.RedisProxy;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.ProfitRateContrastChartCacheService;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.ProfitDayService;
import com.eastmoney.service.service.ProfitRateDayService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.profit.base.SecProfitServiceSettleImpl;
import com.eastmoney.service.service.profit.list.ProfitDayListService;
import com.eastmoney.service.service.profit.list.ProfitDayListServiceFacade;
import com.eastmoney.service.service.profit.list.SecProfitDayListServiceFacade;
import com.eastmoney.service.service.profit.realtime.ProfitRealTimeServiceFacade;
import com.eastmoney.service.service.profit.section.ProfitSectionServiceFacade;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.HoldPositionService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.eastmoney.accessor.enums.ProfitSectionStatEnum.*;
import static com.eastmoney.common.util.ArithUtil.add;


/**
 * Created by sunyuncai on 2016/7/19.
 * 收益Handler
 */
@Service
public class ProfitHandler {

    @Autowired
    private ProfitDayDao profitDayDao;
    @Autowired
    private ProfitDayService profitDayService;
    @Autowired
    private ProfitRateDayService profitRateDayService;
    @Resource(name = "profitRateContrastChartCacheService")
    private ProfitRateContrastChartCacheService profitRateContrastChartCacheService;
    @Resource(name = "profitDayListServiceFacade")
    private ProfitDayListServiceFacade profitDayListServiceFacade;
    @Resource(name = "profitSectionServiceFacade")
    private ProfitSectionServiceFacade profitSectionServiceFacade;
    @Resource(name = "profitRealTimeServiceFacade")
    private ProfitRealTimeServiceFacade profitRealTimeServiceFacade;
    @Resource(name = "commonService")
    private CommonService commonService;
    @Autowired
    private AssetNewService assetNewService;
    @Resource(name = "profitDayListServiceRealTime")
    private ProfitDayListService profitDayListServiceRealTime;
    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Resource(name = "secProfitDayListServiceFacade")
    private SecProfitDayListServiceFacade secProfitDayListServiceFacade;
    @Resource(name = "secProfitServiceSettle")
    private SecProfitServiceSettleImpl secProfitServiceSettle;
    @Autowired
    private StockService stockService;
    @Autowired
    private HoldPositionService holdPositionService;
    @Autowired
    private TpseStSechyreDao tpseStSechyreDao;

    @Autowired
    private CoreConfigService coreConfigService;

    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;
    @Autowired
    private CustCalenderDao custCalenderDao;
    @Autowired
    private RedisProxy redisProxy;

    // 是否计算港股通假期收益标识
    public static final String CAL_HK_PROFIT_FLAG = "calHkProfitFlag";

    //每页条数上限
    private static final Integer MAX_PAGE_SIZE = 1000;

    /**
     * 总收益
     * 目前bizDate和startDate前端没有使用
     * fundId unit
     */
    public ProfitInfo getProfitInfo(Map<String, Object> params) {
        params.put("calProfitRate", true);
        SectionProfitBean profitSection = profitSectionServiceFacade.getProfitSection(params);
        if (profitSection == null) {
            return null;
        }
        ProfitInfo profitInfo = new ProfitInfo();
        //收益
        profitInfo.setProfitTotal(profitSection.getProfit());
        //收益率
        profitInfo.setProfitRateTotal(profitSection.getProfitRate());
        //更新时间
        profitInfo.setUpdateTime(profitSection.getEuTime());
        //开始日期
        profitInfo.setStartDate(profitSection.getIndexDate());
        //最新日期
        profitInfo.setBizDate(profitSection.getBizDate());
        //港股通假期收益
        profitInfo.setHkProfit(ArithUtil.round(profitSection.getHkProfit(), 2));
        //账户清算标识
        profitInfo.setNoInit(profitSection.getNoInit());
        return profitInfo;
    }

    /**
     * 收益明细列表
     *
     * @param params
     * @return
     */
    public List<ProfitDay> getProfitDayList(Map<String, Object> params) {
        return profitDayListServiceFacade.getDayProfitList(params);
    }

    /**
     * 年收益列表
     */
    public List<ProfitStat> getProfitStatList(Map<String, Object> params) {
        Integer bizYear = CommonUtil.convert(params.get("bizYear"), Integer.class);
        params.put("startDate", bizYear * 10000);
        params.put("endDate", bizYear * 10000 + 10000);
        List<ProfitStat> profitStatList = profitDayDao.getProfitStatList(params);
        Integer today = DateUtil.getCuryyyyMMddInteger();
        Integer curYear = today / 10000;
        // 如果不是本年或去年 则直接返回
        if ((!Objects.equals(bizYear, curYear)) && bizYear != curYear - 1) {
            return profitStatList;
        }
        // 如果是本年或去年 则需要判断是否加上实时收益
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null || assetNew.getBizDate() == null) {
            return profitStatList;
        }
        Integer bizDate = assetNew.getBizDate();
        // 计算实时收益日期
        int tradeDay = commonService.calRealTimeProfitBizDate(bizDate);
        if (tradeDay == 0) {
            return profitStatList;
        }
        params.put("accountBizDate", bizDate);
        // 计算盘中收益
        List<ProfitDay> realTimeDayProfitList = profitDayListServiceRealTime.getDayProfitList(params);
        if (CollectionUtils.isEmpty(realTimeDayProfitList)) {
            return profitStatList;
        }
        for (ProfitDay profitDay : realTimeDayProfitList) {
            ProfitStat profitStat = new ProfitStat();
            profitStat.setProfit(profitDay.getProfit());
            profitStat.setBizDate(profitDay.getBizDate() / 100);
            profitStatList.add(profitStat);
        }
        return new ArrayList<>(profitStatList.stream()
                .filter(profitStat -> profitStat.getBizDate() != null && profitStat.getBizDate() / 100 == bizYear)
                .collect(
                        Collectors.toMap(
                                BaseEntityCal::getBizDate,
                                profitStat -> profitStat,
                                (oldProfitStat, newProfitStat) -> {
                                    oldProfitStat.setProfit(add(oldProfitStat.getProfit(), newProfitStat.getProfit()));
                                    return oldProfitStat;
                                }
                        )
                )
                .values());
    }

    /**
     * 收益率分布列表
     */
    public List<ProfitRateContrastChart> getProfitRateDistributeList(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        return profitRateContrastChartCacheService.getProfitRateContrastChart(unit);
    }

    /**
     * 账户表现-盈亏日历
     * 2021/4/12 APPAGILE-70926
     *
     * @param params fundId, indexKey 区间日期 月yyyyMM 年yyyy, unit 区间单位 日历M 月历Y 年历YS, profitType 类型 收益额1 收益率2
     * @return
     */
    public ProfitSectionStat getProfitSectionStatList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer indexKey = CommonUtil.convert(params.get("indexKey"), Integer.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        String profitType = CommonUtil.convert(params.get("profitType"), String.class);
        Integer startDate;
        Integer endDate;
        int curSection;

        if (Y.getType().equals(unit)) {
            startDate = indexKey * 10000;
            endDate = indexKey * 10000 + 9999;
            curSection = DateUtil.getCuryyyyMMddInteger() / 10000;
        } else if (M.getType().equals(unit)) {
            startDate = indexKey * 100;
            endDate = indexKey * 100 + 99;
            curSection = DateUtil.getCuryyyyMMddInteger() / 100;
        } else if (YS.getType().equals(unit)) {
            indexKey = DateUtil.getCuryyyyMMddInteger() / 10000;
            startDate = CommConstants.START_DATE;
            endDate = indexKey * 10000 + 9999;
            curSection = indexKey;
        } else {
            throw new RuntimeException("参数错误:" + unit);
        }
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("startDate", startDate);
        queryParams.put("endDate", endDate);
        // 查询清算后的收益
        List<ProfitStat> profitStatList;
        if ("1".equals(profitType)) {
            profitStatList = profitDayService.queryProfitDayByUnit(fundId, startDate, endDate, unit).stream()
                    .filter(profitDay -> profitDay.getBizDate() != null && profitDay.getProfit() != null)
                    .map(profitDay -> {
                        ProfitStat profitStat = new ProfitStat();
                        profitStat.setProfit(profitDay.getProfit());
                        profitStat.setBizDate(profitDay.getBizDate());
                        return profitStat;
                    })
                    .collect(Collectors.toList());
        } else if ("2".equals(profitType)) {
            profitStatList = profitRateDayService.queryProfitRateDayByUnit(fundId, startDate, endDate, unit).stream()
                    .filter(profitDay -> profitDay.getBizDate() != null && profitDay.getProfitRate() != null)
                    .map(profitRateDay -> {
                        ProfitStat profitStat = new ProfitStat();
                        profitStat.setProfitRate(profitRateDay.getProfitRate());
                        profitStat.setBizDate(profitRateDay.getBizDate());
                        return profitStat;
                    })
                    .collect(Collectors.toList());
        } else {
            throw new RuntimeException("参数错误:" + profitType);
        }
        // 如果是本年/本月或上一年/上一月，则需要计算当日收益
        if (!Objects.equals(curSection, indexKey) && !Objects.equals(curSection - 1, indexKey)) {
            return ProfitSectionStat.build(profitStatList, profitType, unit);
        }
        AssetNew assetNew = assetNewService.getAssetInfo(queryParams);
        if (assetNew == null || assetNew.getBizDate() == null) {
            return ProfitSectionStat.build(profitStatList, profitType, unit);
        }
        Integer accountBizDate = assetNew.getBizDate();
        // 计算实时收益日期
        int tradeDay = commonService.calRealTimeProfitBizDate(accountBizDate);
        if (tradeDay == 0) {
            return ProfitSectionStat.build(profitStatList, profitType, unit);
        }
        queryParams.put("accountBizDate", accountBizDate);
        // 计算盘中收益
        if ("1".equals(profitType)) {
            List<ProfitDay> realTimeDayProfitList = profitDayListServiceRealTime.getDayProfitList(queryParams);
            for (ProfitDay profitDay : realTimeDayProfitList) {
                if (profitDay == null || profitDay.getBizDate() == null || profitDay.getProfit() == null) {
                    continue;
                }
                if (profitDay.getBizDate() < startDate || profitDay.getBizDate() > endDate) {
                    continue;
                }
                ProfitStat profitStat = new ProfitStat();
                profitStat.setProfit(profitDay.getProfit());
                if (YS.getType().equals(unit)) {
                    profitStat.setBizDate(profitDay.getBizDate() / 10000);
                } else {
                    profitStat.setBizDate(profitDay.getBizDate());
                }

                profitStatList.add(profitStat);
            }
        } else {
            List<ProfitRateDay> realTimeDayProfitList = profitDayListServiceRealTime.getDayProfitRateList(queryParams);
            for (ProfitRateDay profitRateDay : realTimeDayProfitList) {
                if (profitRateDay == null || profitRateDay.getBizDate() == null || profitRateDay.getProfitRate() == null) {
                    continue;
                }
                if (profitRateDay.getBizDate() < startDate || profitRateDay.getBizDate() > endDate) {
                    continue;
                }
                ProfitStat profitStat = new ProfitStat();
                profitStat.setProfitRate(profitRateDay.getProfitRate());
                if (YS.getType().equals(unit)) {
                    profitStat.setBizDate(profitRateDay.getBizDate() / 10000);
                } else {
                    profitStat.setBizDate(profitRateDay.getBizDate());
                }
                profitStatList.add(profitStat);
            }
        }
        return ProfitSectionStat.build(profitStatList, profitType, unit);
    }


    /**
     * 柜台接口 获取当日收益率
     * 目前bizDate和startDate前端没有使用
     * fundId
     */
    public DayProfitBean getRealTimeProfitInfo(Map<String, Object> params) {

        DayProfitBean profitSection = profitRealTimeServiceFacade.getRealTimeProfit(params);
        if (profitSection == null) {
            return null;
        }

        return profitSection;
    }

    /**
     * 日收益额走势
     *
     * @param params
     * @return
     */
    public List<ProfitDay> getProfitDayTrend(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        int today = DateUtil.getCuryyyyMMddInteger();
        String unit = CommonUtil.convert(params.get("unit"), String.class);

        //确定账户分析数据计算到了哪天
        ProfitSection profitSection = getProfitSection(params);
        if (profitSection == null) {
            return null;
        }
        int startDate = profitSection.getIndexDate();
        int endDate = tradeDateDao.getNextMarketDay(profitSection.getBakBizDate());
        if (unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
            DateRange dateRange = CommonUtil.getDateRange(unit);
            //如果大于说明已经跨月或跨周，使用新的起止时间
            if (dateRange.getStartDate() > startDate) {
                startDate = dateRange.getStartDate();
                endDate = dateRange.getEndDate();
            }
        }
        //查询的开始时间不得小于首次拥有资产时间
        if (unit.equals(DateUnitEnum.ALL.getValue()) || startDate < assetNew.getStartDate()) {
            startDate = assetNew.getStartDate();

            //至今区间startDate小于设置的日期,使用月收益  APPAGILE-80289
            if (unit.equals(DateUnitEnum.ALL.getValue()) && startDate < CommConstants.calProfitRateStartDate()) {
                params.put(CommConstants.CAL_MONTH_PROFIT, true);
                params.put("settleDate", assetNew.getBizDate());
            }
        }
        if (today > endDate) {
            endDate = today;
        }

        params.put("assetStartDate", assetNew.getStartDate());
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put(CommConstants.CAL_REAL_TIME_PROFIT, true);
        return profitDayListServiceFacade.getDayProfitTrend(params);
    }

    /**
     * 自定义区间日收益额走势
     *
     * @param params
     * @return
     */
    public List<ProfitDay> getProfitDayTrendCustomize(Map<String, Object> params) {
        if (!commonService.formatCustomizeQueryTime(params)) {
            return null;
        }

        Integer settleDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        if (endDate > settleDate) {
            params.put("calRealTimeProfit", true);
        }

        return profitDayListServiceFacade.getDayProfitTrend(params);
    }

    /**
     * 区间收益
     *
     * @param params params
     * @return ProfitRateSection
     */
    protected ProfitSection getProfitSection(Map<String, Object> params) {
        return profitSectionCacheService.getProfitSection(params);
    }

    /**
     * 盈亏日历-个股日收益明细查询 APPAGILE-135268
     *
     * @param params fundId; indexKey-区间日期：日历yyyyMMdd 月历yyyyMM 年历yyyy; unit-区间单位：日历M 月历Y 年历YS;
     *               profitType-类型：收益额1 收益率2; orderFlag-排序方式：升序1 降序2
     *               cashProfitFlag-是否返回其它收益，0-否，1-是；totalNumFlag-是否返回记录总数，0-否，1-是
     * @return
     */
    public SecProfitDayInfoVO getSecProfitSectionStatList(Map<String, Object> params) {
        Integer indexKey = CommonUtil.convert(params.get("indexKey"), Integer.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);

        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null || assetNew.getBizDate() == null) {
            return null;
        }
        Integer accountBizDate = assetNew.getBizDate();

        Integer startDate;
        Integer endDate;
        int curSection;

        // 是否查询历史日收益明细
        boolean calRealTimeProfit;
        if (M.getType().equals(unit)) {
            startDate = indexKey;
            endDate = indexKey;
            calRealTimeProfit = indexKey > accountBizDate;
        } else if (Y.getType().equals(unit)) {
            startDate = indexKey * 100;
            endDate = indexKey * 100 + 99;
            curSection = DateUtil.getCuryyyyMMddInteger() / 100;
            calRealTimeProfit = indexKey >= curSection;
        } else if (YS.getType().equals(unit)) {
            startDate = indexKey * 10000;
            endDate = indexKey * 10000 + 9999;
            curSection = DateUtil.getCuryyyyMMddInteger() / 10000;
            calRealTimeProfit = indexKey >= curSection;
        } else {
            throw new RuntimeException("参数错误:" + unit);
        }

        Integer serverId = coreConfigService.getServerId(fundId);
        Integer useSecProfitDayFlag = nodeConfigService.getSecProfitDetailShowFlag(startDate, serverId);

        // 分核心上线 不支持直接返回
        if (Objects.equals(SecProfitDayShowEnum.UN_SUPPORT.getValue(), useSecProfitDayFlag)) {
            SecProfitDayInfoVO secProfitDayInfoVO = new SecProfitDayInfoVO();
            secProfitDayInfoVO.setDateSupport(useSecProfitDayFlag.toString());
            return secProfitDayInfoVO;
        }

        // 计算实时收益日期
        int realBizDate = commonService.calRealTimeProfitBizDate(accountBizDate);
        calRealTimeProfit = calRealTimeProfit && realBizDate != 0;

        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("accountBizDate", accountBizDate);
        params.put("realBizDate", realBizDate);
        params.put("calRealTimeProfit", calRealTimeProfit);
        params.put("useSecProfitDayFlag", useSecProfitDayFlag);


        boolean stockFilterSupportFlag = nodeConfigService.getStockFilterSupportFlag();
        String calFlag = CommonUtil.convert(params.get("calFlag"), String.class);

        // 支持个股筛选 + 存在筛选条件 即查全部
        boolean stockProfitSearchAll = stockFilterSupportFlag &&
                (PositionCalFlagEnum.START.getValue().equals(calFlag) || PositionCalFlagEnum.END.getValue().equals(calFlag));
        params.put("stockProfitSearchAll", stockProfitSearchAll);

        // 确定排序方式
        specifiSort(params);

        // 查询清算后的收益
        List<SecProfitDayDO> secProfitDayList = secProfitDayListServiceFacade.getSecSettleProfitDayList(params);
        // 其它账户级别收益
        Double cashProfit = secProfitServiceSettle.getCashProfitDay(params);
        // 总条数，用于分页
        Integer totalNum;

        // 清算后的持仓列表
        Map<String, Integer> holdPositions = holdPositionService.getHoldPositionStartDate(fundId);

        if (!calRealTimeProfit) {
            totalNum = secProfitServiceSettle.getSecProfitTotalNum(params);
            return buildSecPorifit(secProfitDayList, holdPositions, cashProfit, totalNum, params);
        }

        List<SecProfitDayDO> secRealTimeProfitDayList = secProfitDayListServiceFacade.getSecRealTimeProfitDayList(params);
        secProfitDayList.addAll(secRealTimeProfitDayList);

        // 清算收益和当日实时收益合并
        secProfitDayList = new ArrayList<>(secProfitDayList.stream()
                .collect(
                        Collectors.toMap(
                                s -> s.getStkCode() + "-" + s.getMarket(),
                                Function.identity(),
                                (o1, o2) -> {
                                    o1.setProfit(ArithUtil.add(o1.getProfit(), o2.getProfit()));
                                    if(stockFilterSupportFlag) {
                                        //打开个股筛选开关   以实时收益的持仓标识为准
                                        o1.setHoldFlag(o2.getHoldFlag());
                                    }
                                    return o1;
                                }
                        )
                )
                .values());

        totalNum = secProfitDayList.size();
        return buildSecPorifit(secProfitDayList, holdPositions, cashProfit, totalNum, params);
    }

    /**
     * 指定具体的查询排序方式
     *
     * @param params
     */
    private void specifiSort(Map<String, Object> params) {
        // profitType-类型：收益额1 收益率2
        String profitType = CommonUtil.convert(params.get("profitType"), String.class);
        // orderFlag-排序方式：升序1 降序2
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);
        Boolean calRealTimeProfit = CommonUtil.convert(params.get("calRealTimeProfit"), Boolean.class);
        Boolean stockProfitSearchAll = CommonUtil.convert(params.get("stockProfitSearchAll"), Boolean.class);

        if (BooleanUtils.isTrue(calRealTimeProfit) || stockProfitSearchAll) {
            // 如果查询实时收益，需要全量查询，无法在查询数据库时直接分页查询
            //如果支持个股筛选 且传入了筛选项，需要全量查询
            return;
        }

        // 分页查询：外层top3通过topSize控制；详情页分页通过pageNo+pageSize控制
        Integer topSize = CommonUtil.convert(params.get("topSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);

        if (topSize != null) {
            pageNo = 1;
            pageSize = topSize;
        }
        if (pageNo != null && pageSize != null) {
            Integer startNum = pageSize * (pageNo - 1);
            // 最多查询1000条
            pageSize = pageSize > MAX_PAGE_SIZE ? MAX_PAGE_SIZE : pageSize;
            params.put("startNum", startNum);
            params.put("pageSize", pageSize);
        }

        String sort = "";
        if ("1".equals(profitType)) {
            if ("1".equals(orderFlag)) {
                sort = "order by profit asc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by profit desc";
            }
        } else if ("2".equals(profitType)) {
            if ("1".equals(orderFlag)) {
                sort = "order by profitRate asc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by profitRate desc";
            }
        } else {
            throw new RuntimeException("参数错误:" + profitType);
        }
        sort = sort + ",market,stkcode";

        if (StringUtils.isNotBlank(sort)) {
            params.put("sort", sort);
        }
    }

    /**
     * 按照收益额/收益率排序
     *
     * @param secProfitDayList 个股日收益明细列表
     * @param cashProfit       账户收益
     * @param totalNum         总条数
     * @param params
     * @return
     */
    private SecProfitDayInfoVO buildSecPorifit(List<SecProfitDayDO> secProfitDayList, Map<String, Integer> holdPositions,
                                               Double cashProfit, Integer totalNum, Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String profitType = CommonUtil.convert(params.get("profitType"), String.class);
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);
        Boolean calRealTimeProfit = CommonUtil.convert(params.get("calRealTimeProfit"), Boolean.class);
        Integer topSize = CommonUtil.convert(params.get("topSize"), Integer.class);
        String useSecProfitDayFlag = CommonUtil.convert(params.get("useSecProfitDayFlag"), String.class);
        String calFlag = CommonUtil.convert(params.get("calFlag"), String.class);
        Boolean stockProfitSearchAll = CommonUtil.convert(params.get("stockProfitSearchAll"), Boolean.class);

        SecProfitDayInfoVO secProfitDayInfoVO = new SecProfitDayInfoVO(fundId, totalNum);
        secProfitDayInfoVO.setDateSupport(useSecProfitDayFlag);

        if (cashProfit != null) {
            secProfitDayInfoVO.setCashProfit(ArithUtil.round(cashProfit, 2));
        }

        if (CollectionUtils.isEmpty(secProfitDayList)) {
            return secProfitDayInfoVO;
        }

        List<SecProfitDayVO> secProfitDayVOs = secProfitDayList.stream()
                .map(SecProfitDayVO::of)
                .peek(s -> {
                    if (StringUtils.isEmpty(s.getStkCode()) && StringUtils.isEmpty(s.getMarket())) {
                        return;
                    }
                    // 判断是否持仓-0-清仓；1持仓。若还持仓，则跳转到getHoldPositionMergeTradeList，清仓跳转到getClearPositionMergeTradeList
                    if (s.getHoldFlag() == null) {
                        String alertTo830StkCode = bseCodeAlterService.getCodeAlterReverse(s.getStkCode(), s.getMarket());
                        String key = s.getMarket() + "-" + s.getStkCode();
                        String key830 = s.getMarket() + "-" + StringUtils.trim(alertTo830StkCode);
                        if (holdPositions.containsKey(key) || holdPositions.containsKey(key830)) {
                            s.setHoldFlag(1);
                        } else {
                            s.setHoldFlag(0);
                        }
                    }
                    // 设置名称
                    s.setStkName(stockService.getStkName(s.getStkCode(), s.getMarket()));
                    // 行业
                    String publishName = tpseStSechyreDao.getPublishName(s.getStkCode());
                    if (StringUtils.isBlank(publishName)) {
                        publishName = tpseStSechyreDao.getDefaultPublishName();
                    }
                    s.setPublishName(publishName);
                    //北交所 行情查询兼容
                    s.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(s.getStkCode(), s.getMarket()));
                })
                .filter(s -> {
                    if(stockProfitSearchAll) {
                        return (s.getHoldFlag() == 1 && PositionCalFlagEnum.START.getValue().equals(calFlag)) ||
                                (s.getHoldFlag() == 0 && PositionCalFlagEnum.END.getValue().equals(calFlag));
                    }
                    return true;
                })
                .collect(Collectors.toList());


        if (BooleanUtils.isTrue(calRealTimeProfit) || stockProfitSearchAll) {
            // 只有查询实时收益时才需要在此处排序，其它非实时情况已在数据库查询时排序(默认收益额降序)；
            Comparator<SecProfitDayVO> comparator = Comparator.comparing(SecProfitDayVO::getProfit, Comparator.nullsFirst(Double::compareTo)).reversed();
            if ("1".equals(profitType) && "1".equals(orderFlag)) {
                comparator = Comparator.comparing(SecProfitDayVO::getProfit, Comparator.nullsLast(Double::compareTo));
            } else if ("2".equals(profitType) && "1".equals(orderFlag)) {
                // 只有日历明细详情页支持按收益率排序
                comparator = Comparator.comparing(SecProfitDayVO::getProfitRate, Comparator.nullsLast(Double::compareTo))
                        .thenComparing(SecProfitDayVO::getProfit);
            } else if ("2".equals(profitType) && "2".equals(orderFlag)) {
                comparator = Comparator.comparing(SecProfitDayVO::getProfitRate, Comparator.nullsFirst(Double::compareTo))
                        .thenComparing(SecProfitDayVO::getProfit).reversed();
            }
            //  相同值情况再按照市场标的排序
            comparator = comparator.thenComparing(SecProfitDayVO::getMarket, Comparator.nullsLast(String::compareTo))
                    .thenComparing(SecProfitDayVO::getStkCode, Comparator.nullsLast(String::compareTo));

            secProfitDayVOs = secProfitDayVOs.stream()
                    .sorted(comparator)
                    .collect(Collectors.toList());
            // 分页
            if (topSize != null && secProfitDayVOs.size() > topSize) {
                secProfitDayVOs = secProfitDayVOs.subList(0, topSize);
            } else if (secProfitDayVOs.size() > MAX_PAGE_SIZE) {
                secProfitDayVOs.subList(0, MAX_PAGE_SIZE);
            }
        }

        secProfitDayInfoVO.setSecProfitDays(secProfitDayVOs);
        if(stockProfitSearchAll) {
            secProfitDayInfoVO.setTotalNum(secProfitDayVOs.size());
        }
        return secProfitDayInfoVO;
    }

    public List<CustCalenderVO> GetATradeDate(Map<String, Object> params) {
        List<CustCalenderVO> calenderVOS = new ArrayList<>();
        //先查询redis缓存
        String redisKey = RedisKeyEnum.TRADE_CUST_CALENDER.getPrefix() + params.get("dateFrom") + "&" + params.get("dateTo");
        String jsonStr = redisProxy.get(redisKey);
        if (StringUtils.isNotBlank(jsonStr)) {
            calenderVOS.addAll(JSON.parseArray(jsonStr, CustCalenderVO.class));
            return calenderVOS;
        }
        //redis查询不到，查询日历表
        List<CustCalenderDO> calenderDOList = custCalenderDao.getByBizDateRange(params)
                .stream()
                .sorted(Comparator.comparing(CustCalenderDO::getScal))
                .collect(Collectors.toList());
        if (calenderDOList != null && calenderDOList.size() > 0) {
            calenderDOList.forEach(item -> {
                CustCalenderVO calenderVO = new CustCalenderVO();
                calenderVO.setTradeDate(DateUtil.dateToStr(item.getScal(), "yyyyMMdd"));
                //isom字段标志是否开市，1：开市；0：休市
                calenderVO.setTradeDayIs(ConstantEnum.CalenderIsom.IS.getKey().equals(item.getIsom()));
                if (ConstantEnum.CalenderIsom.NO.getKey().equals(item.getIsom())) {
                    calenderVO.setHoliday(ConstantEnum.FestivalEnum.getByKey(item.getHoliday()).getDesc());
                }
                calenderVOS.add(calenderVO);
            });
            redisProxy.setex(redisKey, RedisKeyEnum.TRADE_CUST_CALENDER.getExpire(), JSON.toJSONString(calenderVOS));
        }
        return calenderVOS;
    }
}
