package com.eastmoney.accessor.dao.fundia;

import com.eastmoney.accessor.mapper.fundia.FundInvestMapper;
import com.eastmoney.common.entity.fundia.CombAsset;
import com.eastmoney.common.entity.fundia.CombProfit;
import com.eastmoney.common.entity.fundia.InvestFeeDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class FundInvestDaoImpl implements FundInvestDao {
    @Autowired
    private FundInvestMapper mapper;

    @Override
    public List<CombAsset> queryCombAsset(Map<String, Object> params) {
        return mapper.queryCombAsset(params);
    }

    @Override
    public List<CombProfit> queryCombProfit(Map<String, Object> params) {
        return mapper.queryCombProfit(params);
    }

    /**
     * 查询投顾服务费
     */
    @Override
    public Map<String, Double> queryInvestFee(Map<String, Object> params) {
        return mapper.queryInvestFee(params)
                .stream()
                .collect(Collectors.toMap(InvestFeeDetail::getGroupCode, InvestFeeDetail::getFeeAmt, (a, b) -> a));
    }

}
