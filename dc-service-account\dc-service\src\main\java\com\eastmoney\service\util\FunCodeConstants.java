package com.eastmoney.service.util;

/**
 * Created by sunyuncai on 2016/3/10.
 */
public class FunCodeConstants {
    public static final String GET_BANK_TRAN_REC = "getBankTranRec"; //是	转帐流水
    public static final String GET_ORDER_REC = "getOrderRec"; //是	委托流水(普通)
    public static final String GET_ORDER_REC_HGT = "getOrderRecHGT"; //是	委托流水(沪港通)
    public static final String GET_TRADE_REC = "getTradeRec"; //是	成交流水(普通)
    public static final String GET_TRADE_REC_HGT = "getTradeRecHGT"; //是	成交流水(沪港通)
    public static final String GET_BIND_CHANGE_LIST = "getBindChangeList"; //是 获取绑定变化清单
    public static final String GET_DELIVERY_ORDER_SUM = "getDeliveryOrderSum"; //否	交割单汇总

    public static final String GET_SYNC_STATUS = "getSyncStatus"; //否	获取同步状态
    public static final String GET_PROFIT_INFO = "getProfitInfo"; //资产总收益
    public static final String GET_PROFIT_SECTION_LIST = "getProfitSectionList"; //资产分片收益
    public static final String GET_PROFIT_DAY_TREND = "getProfitDayTrend"; //日收益走势
    public static final String GET_PROFIT_DAY_TREND_CUSTOMIZE = "getProfitDayTrendCustomize"; //自定义区间日收益走势
    public static final String GET_PROFIT_RATE_DAY_LIST = "getProfitRateDayList"; //日收益率
    public static final String GET_PROFIT_RATE_RANK_INFO = "getProfitRateRankInfo"; //收益率排名信息
    public static final String GET_STOCK_LIST = "getStockList"; //获取码表信息
    public static final String GET_REALTIME_PROFIT_INFO = "getRealTimeProfitInfo";//获取当日收益率，去除港股通
    public static final String GET_SHARE_STKASSET_PROFIT_INFO = "getShareStkAssetProfitInfo";

    public static final String GET_PROFIT_RATE_DAY_LIST_CUSTOMIZE = "getProfitRateDayListCustomize";//自定义区间查询收益率列表
    public static final String GET_INVEST_PROFIT_RATE_LIST = "getInvestProfitRateList";//优优投顾-我的收益走势

    public static final String GET_ZHFX_SETTLE_STATUS = "getZhfxSettleStatus";//获取账户分析清算状态

    public static final String GET_IS_POSITION_STK = "getIsPositionStk"; //获取是否用证券持仓
    public static final String GET_HGT_RATE_CHANGE = "getHgtRateChange";//港股通汇率波动提示

    public static final String GET_ACCT_DIAGNOSE = "getAcctDiagnose";//获取账户诊断报表

    /**
     * 获取账单中的资产走势
     */
    public static final String GET_ASSET_BILL_TREND = "getBillAssetTrend";
}
