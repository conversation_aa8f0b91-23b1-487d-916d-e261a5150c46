package com.eastmoney.service.service.asset.concretesection;

import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.entity.cal.ProfitSection;
import com.eastmoney.service.service.asset.AbstractAssetSectionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/13-14:33.
 *
 * <AUTHOR>
 */
@Service("assetSectionServiceNotCross")
public class AssetSectionServiceNotCrossImpl extends AbstractAssetSectionService {

    @Override
    protected AssetHis getAssetStart(Map<String, Object> params, AssetHis assetNew) {
        ProfitSection profitSection = profitSectionCacheService.getProfitSection(params);
        if (profitSection == null) {
            return null;
        }
        Integer unitAssetStartDate = profitSection.getIndexDate();
        Integer assetStartDate = assetNew.getStartDate();
        if (unitAssetStartDate > assetStartDate) {
            params.put("startDate", unitAssetStartDate);
            int preStartDate = Integer.valueOf(tradeDateDao.getPreMarketDay(unitAssetStartDate));
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("bizDate", preStartDate);
            requestParams.put("fundId", params.get("fundId"));
            AssetHis assetStart = assetServiceSettle.getAsset(requestParams);
            if (assetStart != null) {
                return assetStart;
            } else {
                return new AssetHis();
            }
        }
        return new AssetHis();
    }
}
