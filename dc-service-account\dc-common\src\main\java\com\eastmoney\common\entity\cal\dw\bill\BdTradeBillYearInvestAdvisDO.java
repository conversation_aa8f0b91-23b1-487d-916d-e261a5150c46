package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 投顾指标
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
public class BdTradeBillYearInvestAdvisDO {
    // 2024年投顾服务天数
    private Long tgYearSignDays;
    // 2024年用户签约投顾组合数（人工+智能组合）
    private Long tgYearSignCnt;
    // 2024年组合盈利卖出笔数-金证
    private Long tgProfitCnt;
    // 2024年用户跟投笔数-金证
    private Long tgFollowCnt;
    // 2024年用户跟投组合盈利（普通账户）-金证
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tgFollowProfit;

    public Long getTgYearSignDays() {
        return tgYearSignDays;
    }

    public void setTgYearSignDays(Long tgYearSignDays) {
        this.tgYearSignDays = tgYearSignDays;
    }

    public Long getTgYearSignCnt() {
        return tgYearSignCnt;
    }

    public void setTgYearSignCnt(Long tgYearSignCnt) {
        this.tgYearSignCnt = tgYearSignCnt;
    }

    public Long getTgProfitCnt() {
        return tgProfitCnt;
    }

    public void setTgProfitCnt(Long tgProfitCnt) {
        this.tgProfitCnt = tgProfitCnt;
    }

    public Long getTgFollowCnt() {
        return tgFollowCnt;
    }

    public void setTgFollowCnt(Long tgFollowCnt) {
        this.tgFollowCnt = tgFollowCnt;
    }

    public BigDecimal getTgFollowProfit() {
        return tgFollowProfit;
    }

    public void setTgFollowProfit(BigDecimal tgFollowProfit) {
        this.tgFollowProfit = tgFollowProfit;
    }
}
