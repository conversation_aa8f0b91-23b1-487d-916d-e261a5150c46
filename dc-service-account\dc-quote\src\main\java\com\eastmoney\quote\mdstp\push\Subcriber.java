package com.eastmoney.quote.mdstp.push;

import com.eastmoney.quote.mdstp.model.QuoteTypeEnum;
import com.eastmoney.quote.mdstp.pull.conf.QuoteConstant;
import com.eastmoney.quote.mdstp.serializer.QtDecoderRunner;
import com.eastmoney.quote.mdstp.serializer.QtDecoderWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zeromq.ZMQ;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created on 16-10-24
 *
 * <AUTHOR>
 */
public class Subcriber implements Runnable {

    public static final byte[] FILETER = new byte[]{QuoteTypeEnum.QuoteHSSnapshot.getValue()};
    private static Logger LOG = LoggerFactory.getLogger(Subcriber.class);

    private final String quoteType;
    private final String host;
    private final int port;
    private ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

    public Subcriber(String quoteType, String host, int port) {
        this.quoteType = quoteType;
        this.host = host;
        this.port = port;
    }

    public void init() {
        try (ZMQ.Context context = ZMQ.context(1)) {
            ZMQ.Socket subscriber = context.socket(ZMQ.SUB);
            String url = "tcp://" + host + ":" + port;
            subscriber.connect(url);
            LOG.info("Subscriber startup {}", url);
            subscriber.subscribe(FILETER);
            while (!Thread.currentThread().isInterrupted()) {
                byte[] recv = subscriber.recv(0);
                QtDecoderWorker.submit(new QtDecoderRunner(quoteType, recv));
            }
        } catch (Exception e) {
            LOG.error("连接失败...{}", e.getMessage(), e);
        } finally {
            executor.execute(new Runnable() {

                @Override
                public void run() {
                    try {
                        LOG.info("进入递归连接,host:{} port:{}", host, port);
                        TimeUnit.SECONDS.sleep(60);
                        LOG.info("发起重连操作,host:{} port:{}", host, port);

                        Subcriber.this.init();
                    } catch (InterruptedException e) {
                        LOG.error(e.getMessage(), e);
                    }
                }
            });
        }
    }

    @Override
    public void run() {
        while (true) {
            try {
                ZMQ.Context context = ZMQ.context(1);
                ZMQ.Socket socket = context.socket(ZMQ.SUB);
                //开启so_keepalive，tcp探活机制
                socket.setTCPKeepAlive(1);
                //关闭一个非活跃连接之前的最大重试次数
                socket.setTCPKeepAliveCount(5);
                //设置连接上如果没有数据发送的话，多久才发送
                socket.setTCPKeepAliveIdle(300);
                //前后两次探测之间的时间间隔，单位为妙
                socket.setTCPKeepAliveInterval(300);
                String url = "tcp://" + host + ":" + port;
                socket.connect(url);
                if (QuoteConstant.QUOTE_TYPE_HS.equals(quoteType)) {
                    socket.subscribe(new byte[]{QuoteTypeEnum.QuoteHSSnapshot.getValue()});
                } else if (QuoteConstant.QUOTE_TYPE_HK.equals(quoteType)) {
                    socket.subscribe(new byte[]{QuoteTypeEnum.QuoteHKSnapshot.getValue()});
                } else if (QuoteConstant.QUOTE_TYPE_HSHK.equals(quoteType)) {
                    socket.subscribe(new byte[]{QuoteTypeEnum.QuoteHSSnapshot.getValue()});
                    socket.subscribe(new byte[]{QuoteTypeEnum.QuoteHKSnapshot.getValue()});
                }

                socket.monitor("inproc://submoniter", ZMQ.EVENT_CONNECTED | ZMQ.EVENT_DISCONNECTED);
                ZMQ.Socket monitorSocket = context.socket(ZMQ.PAIR);
                monitorSocket.connect("inproc://submoniter");
                Thread thread = new Thread(() -> {
                    while (true) {
                        ZMQ.Event event = ZMQ.Event.recv(monitorSocket);
                        String eventDescribe = "";
                        switch (event.getEvent()) {
                            case ZMQ.EVENT_CONNECTED:
                                eventDescribe = "connected";
                                break;
                            case ZMQ.EVENT_DISCONNECTED:
                                eventDescribe = "disconnected";
                                break;
                            default:
                                break;
                        }
                        LOG.info(String.format("sub-monitor:%s %s", eventDescribe, event.getAddress()));
                    }
                });
                thread.start();

                while (true) {
                    byte[] recv = socket.recv(0);
                    QtDecoderWorker.submit(new QtDecoderRunner(quoteType, recv));
                }
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            }
        }
    }
}



