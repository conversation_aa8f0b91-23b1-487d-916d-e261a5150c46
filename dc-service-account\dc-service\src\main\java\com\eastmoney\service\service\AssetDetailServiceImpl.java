package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.tidb.AssetDetailDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.AssetDetailDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Service
public class AssetDetailServiceImpl implements AssetDetailService{
    @Autowired
    private AssetDetailDao assetDetailDao;

    @Override
    @RedisCache(keyGenerator = "'jzjy_fundasset_'+ #fundId +'_'+ #bizDate")
    public AssetDetailDO getAllFundAsset(Integer bizDate, Long fundId, String moneyType) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("bizDate", bizDate);
        queryParams.put("moneyType", moneyType);
        List<AssetDetailDO> fundAssetList = assetDetailDao.getAllFundAsset(queryParams);
        if(!CollectionUtils.isEmpty(fundAssetList)){
            return fundAssetList.get(0);
        }else{
            return new AssetDetailDO();
        }
    }
}
