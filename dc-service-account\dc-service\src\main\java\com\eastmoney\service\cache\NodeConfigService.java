package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.NodeConfigDao;
import com.eastmoney.accessor.enums.SecProfitDayShowEnum;
import com.eastmoney.accessor.enums.TrdIdEnum;
import com.eastmoney.common.entity.cal.NodeConfigDO;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/11/20
 */
@Service
public class NodeConfigService {
    public static final LocalTime SEVENTEEN_CLOCK = LocalTime.of(17, 0);
    public static final LocalTime NINE_CLOCK = LocalTime.of(9, 0);
    // 个股当日盈亏夜间是否展示标识
    private static final String SEC_PROFIT_SHOW_FLAG = "secProfitShowFlag";
    // 个股收益明细是否展示标识
    private static final String SEC_PROFIT_DETAIL_SHOW = "secProfitDetailShow";
    // 港股通假期收益是否展示标识
    private static final String HK_PROFIT_SHOW = "hkProfitShow";
    // 缓存配置 0 guava 1 redis
    private static final String REDIS_CACHE_FLAG = "redisCacheFlag";
    // 清仓记录缓存top大小
    private static final String CLEAR_POSITION_TOP_SIZE = "clearPositionTopSize";
    //核心配置
    private static final String SERVER_ID_STRING = "SERVER_ID_STRING";
    // 盘中实时红利税流水展示标识
    private static final String REALTIME_BONUS_LOGASSET_SHOW = "realTimeBonusLogassetShow";
    // redis缓存开始时间
    private static final String REDIS_CACHE_START_TIME = "redisCacheStartTime";
    // redis缓存结束时间
    private static final String REDIS_CACHE_END_TIME = "redisCacheEndTime";
    // 年账单收益实时计算标识
    private static final String YEARBILL_REALTIME_PROFIT_CAL_FLAG = "yearbillRealtimeProfitCalFlag";
    /**
     * 年度账单功能是否启动Redis缓存，true-是，false-否
     */
    private static final String YEARBILL_USE_REDIS_CACHE_FLAG = "yearbillUseRedisCacheFlag";

    //当日盈亏计算费用标识
    private static final String DAY_PROFIT_CAL_FEE_FLAG = "dayProfitCalFeeFlag";

    //非交易类型
    private static final String NO_TRADE_ID = "noTradeId";

    // redis操作耗时统计的阈值
    private static final String REDIS_QUERYTIME_THRESHOLD = "redisQueryTimeThreshold";

    // 资产-其他净流入是否包含OTC转入开关
    public static final String OTC_NET_TRANSFER_CAL_FLAG = "otcNetTransferCalFlag";

    // 港股通收益计算优化开关 - 柜台灰度上线，准上线全上会报错
    private static final String HGT_PROFIT_CAL_OPTIMIZE_FLAG = "hgtProfitCalOptimizeFlag";

    // 个股支持筛选开关(true为支持  false为不支持即老版本)
    private static final String STOCK_FILTER_SUPPORT_FLAG =  "stockFilterSupportFlag";

    private static Logger LOG = LoggerFactory.getLogger(NodeConfigService.class);
    @Autowired
    private NodeConfigDao nodeConfigDao;
    @Resource(name = "nodeConfigCache")
    private LoadingCache<String, Optional<String>> nodeConfigCache;

    @Bean(name = "nodeConfigCache")
    public LoadingCache<String, Optional<String>> nodeConfigCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(100)
                .refreshAfterWrite(3, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<String>>() {
                    @Override
                    public Optional<String> load(String key) throws Exception {
                        try {
                            Map<String, Object> map = new HashMap<>();
                            map.put("key", key);
                            List<NodeConfigDO> nodeConfig = nodeConfigDao.query(map);
                            if (CollectionUtils.isEmpty(nodeConfig) || nodeConfig.get(0) == null || StringUtils.isEmpty(nodeConfig.get(0).getValue())) {
                                return Optional.empty();
                            }

                            return Optional.of(nodeConfig.get(0).getValue());
                        } catch (Exception e) {
                            LOG.error(e.getMessage(), e);
                        }

                        return Optional.empty();
                    }
                });
    }

    public String getNodeConfig(String key) {
        try {
            return nodeConfigCache.get(key).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("通过guava获取配置失败", e);
        }
        return null;
    }

    public Set<String> getTSellBsFlagSet() {
        String tSellBsFlag = getNodeConfig("tSellBsFlag");
        if (StringUtils.isEmpty(tSellBsFlag)) {
            tSellBsFlag = "0S,0f,0g,0h,0i,0j,0r,1H,1J,2S,3S,4S,3n,i,S,f,g,h,j,r,1k,2J,4n";
        }
        return Arrays.stream(tSellBsFlag.split(","))
                .collect(Collectors.toSet());
    }

    public Set<String> getTBuyBsFlagSet() {
        String tBuyBsFlag = getNodeConfig("tBuyBsFlag");
        if (StringUtils.isEmpty(tBuyBsFlag)) {
            tBuyBsFlag = "0B,0a,0b,0c,0d,0e,0q,1G,1I,2B,3B,3m,a,b,B,c,d,e,q,1j,2I,4m";
        }
        return Arrays.stream(tBuyBsFlag.split(","))
                .collect(Collectors.toSet());
    }

    public Set<String> getTBuySellBsFlagSet() {
        Set<String> tBuyBsFlagSet = getTBuyBsFlagSet();
        Set<String> tSellBsFlagSet = getTSellBsFlagSet();
        tBuyBsFlagSet.addAll(tSellBsFlagSet);
        return tBuyBsFlagSet;
    }

    /**
     * 获取柜台清算后个股收益展示标识
     *
     * @return
     */
    public boolean getSecProfitShowFlag() {
        String secProfitShowFlagStr = getNodeConfig(SEC_PROFIT_SHOW_FLAG);
        if (StringUtils.isNotEmpty(secProfitShowFlagStr) && "false".equals(secProfitShowFlagStr)) {
            return false;
        }
        return true;
    }

    /**
     * 获取个股日收益明细展示标识
     *
     * @param bizDate
     * @param serverId
     * @return
     */
    public Integer getSecProfitDetailShowFlag(Integer bizDate, Integer serverId) {
        String startDate = getNodeConfig(SEC_PROFIT_DETAIL_SHOW + "_" + serverId);
        if (StringUtils.isEmpty(startDate)) {
            return SecProfitDayShowEnum.UN_SUPPORT.getValue();
        }
        if (bizDate >= Integer.parseInt(startDate)) {
            return SecProfitDayShowEnum.SUPPORT.getValue();
        } else {
            return SecProfitDayShowEnum.OUT_OF_RANGE.getValue();
        }
    }

    /**
     * 获取港股通假期收益展示标识
     *
     * @return
     */
    public Boolean getHkProfitShowFlag() {
        String showFlag = getNodeConfig(HK_PROFIT_SHOW);
        if (StringUtils.isNotEmpty(showFlag) && "false".equals(showFlag)) {
            return false;
        }
        return true;
    }

    /**
     * 查询核心配置
     *
     * @return
     */
    public List<Integer> getServerIds() {
        String serverIdString = getNodeConfig(SERVER_ID_STRING);
        if (StringUtils.isEmpty(serverIdString)) {
            serverIdString = "1,2,3,4,5,7,8,9";
        }
        List<String> columnTypeSet = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(serverIdString);
        if (CollectionUtils.isEmpty(columnTypeSet)) {
            return Collections.emptyList();
        }
        List<Integer> result = new ArrayList<>(columnTypeSet.size());
        for (String s : columnTypeSet) {
            result.add(Integer.parseInt(s));
        }
        return result;
    }

    /**
     * 获取盘中实时红利税流水展示标识，不配置默认为true-开启
     *
     * @return
     */
    public boolean getRealTimeBonusLogassetShowFlag(Integer serverId) {
        String showFlag = getNodeConfig(REALTIME_BONUS_LOGASSET_SHOW + "_" + serverId);
        if (StringUtils.isNotEmpty(showFlag) && "false".equals(showFlag)) {
            return false;
        }
        return true;
    }


    public String getRedisCacheFlag() {
        String redisCacheFlag = getNodeConfig(REDIS_CACHE_FLAG);
        if (StringUtils.isEmpty(redisCacheFlag)) {
            return "2";
        }
        return redisCacheFlag;
    }

    public int getClearPositionTOPSize() {
        String size = getNodeConfig(CLEAR_POSITION_TOP_SIZE);
        if (StringUtils.isEmpty(size)) {
            return 5;
        }
        return Integer.parseInt(size);
    }

    public LocalTime getRedisCacheStartTime() {
        String startTime = getNodeConfig(REDIS_CACHE_START_TIME);
        if (StringUtils.isEmpty(startTime)) {
            return NINE_CLOCK;
        }
        return LocalTime.parse(startTime, DateTimeFormatter.ISO_TIME);
    }

    public LocalTime getRedisCacheEndTime() {
        String endTime = getNodeConfig(REDIS_CACHE_END_TIME);
        if (StringUtils.isEmpty(endTime)) {
            return SEVENTEEN_CLOCK;
        }
        return LocalTime.parse(endTime, DateTimeFormatter.ISO_TIME);
    }

    /**
     * 获取年账单实时计算收益标识
     * 默认 false
     *
     * @return
     */
    public Boolean getYearBillProfitCalFlag() {
        String calFlag = getNodeConfig(YEARBILL_REALTIME_PROFIT_CAL_FLAG);
        if (StringUtils.isNotEmpty(calFlag) && "true".equals(calFlag)) {
            return true;
        }
        return false;
    }

    public boolean getDayProfitCalFeeFlag() {
        String value = getNodeConfig(DAY_PROFIT_CAL_FEE_FLAG);
        return StringUtils.isEmpty(value) || "true".equals(value);
    }

    public List<String> getNoTradeIdList() {
        List<String> noTradeIds = Arrays.asList(
                TrdIdEnum.PAYMENT.getValue(), TrdIdEnum.SG.getValue(), TrdIdEnum.ETF_SG.getValue(), TrdIdEnum.ETF_RG.getValue()
        );
        String value = getNodeConfig(NO_TRADE_ID);
        if (StringUtils.isNotEmpty(value)) {
            noTradeIds = Arrays.asList(value.split(","));
        }
        return noTradeIds;
    }

    /**
     * 获取Redis操作耗时统计的阈值
     * 默认10ms
     * @return
     */
    public int getRedisQueryTimeThreshold() {
        String threshold = getNodeConfig(REDIS_QUERYTIME_THRESHOLD);
        if (StringUtils.isEmpty(threshold)) {
            return 10;
        }
        return Integer.parseInt(threshold);
    }

    /**
     * 【资产不计算otc转入资金】开关，开关开启，说明功能启用，不计算otc转入资金
     */
    public boolean getOtcNetTransferCalFlag(Integer serverId) {
        String configKey = String.join("_", OTC_NET_TRANSFER_CAL_FLAG, serverId.toString());
        String calFlag = getNodeConfig(configKey);
        return "true".equals(calFlag);
    }

    /**
     * 获取年账单是否使用redis缓存，默认为false
     *
     * @return 是否开启
     */
    public boolean isYearBillUseRedisCache() {
        String calFlag = getNodeConfig(YEARBILL_USE_REDIS_CACHE_FLAG);
        return StringUtils.isNotEmpty(calFlag) && "true".equals(calFlag);
    }

    /**
     * 【港股通收益计算优化】开关，开关开启，说明功能启用，使用柜台最新逻辑计算收益
     */
    public boolean getHgtProfitCalOptimizeFlag(Integer serverId) {
        String configKey = String.join("_", HGT_PROFIT_CAL_OPTIMIZE_FLAG, serverId.toString());
        String calFlag = getNodeConfig(configKey);
        // 开关默认开启：如果查不到，则认为是开启
        return StringUtils.isEmpty(calFlag) || "1".equals(calFlag);
    }

    public boolean getStockFilterSupportFlag() {
        String calFlag = getNodeConfig(STOCK_FILTER_SUPPORT_FLAG);
        // 开关默认关闭：如果查不到，则认为是关闭
        return StringUtils.isNotEmpty(calFlag) && "1".equals(calFlag);
    }
}
