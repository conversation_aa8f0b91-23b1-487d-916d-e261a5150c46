package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.StockLayeredInfoDao;
import com.eastmoney.accessor.mapper.oracle.StockLayeredInfoMapper;
import com.eastmoney.accessor.mapper.tidb.TiStockLayeredInfoMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.StockLayeredInfo;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 15:59
 */
//@Conditional(ZhfxDataSourceCondition.class)
//@ZhfxDataSource("tidb")
@Service("stockLayeredInfoDao")
public class TiStockLayeredInfoDaoImpl extends BaseDao<TiStockLayeredInfoMapper, StockLayeredInfo, Integer> implements StockLayeredInfoDao {

    @Override
    public List<StockLayeredInfo> selectAll() {
        return getMapper().selectAll();
    }

}
