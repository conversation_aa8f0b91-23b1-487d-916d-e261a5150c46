<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiProfitRateContrastChartMapper">
    <resultMap id="BaseResultMap" type="as_profitRateContrastChart" >
        <result column="PROFIT_RATE" property="profitRate"/>
        <result column="RATIO" property="ratio"/>
        <result column="UNIT" property="unit"/>
    </resultMap>
    <sql id="All_Column">
        PROFIT_RATE, RATIO, UNIT
    </sql>
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="All_Column"/> FROM ATCENTER.PROFIT_RATE_CONTRAST_CHART
        <where>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
            <if test="fundIds != null">
                AND fundid IN
                <foreach item="item" index="index" collection="fundIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>