package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.entity.cal.IsInterceptedUserResult;
import com.eastmoney.service.cache.VipCustomersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description VIP用户处理相关接口 排除部分机构户、产品户
 * @date 2024/4/26 10:18
 */
@Action
@Component
public class VipCustomersAction {

    @Autowired
    private VipCustomersService vipCustomersService;

    @FunCodeMapping("isInterceptedUser")
    @RequestMapping("/isInterceptedUser")
    public IsInterceptedUserResult isInterceptedUser(Map<String, Object> params) {
        return vipCustomersService.isInterceptedUser(params);

    }

    @RequestMapping("/initInterceptedUser")
    public void initInterceptedUser(Map<String, Object> params) {
        vipCustomersService.initInterceptFundIdSet();
    }
}
