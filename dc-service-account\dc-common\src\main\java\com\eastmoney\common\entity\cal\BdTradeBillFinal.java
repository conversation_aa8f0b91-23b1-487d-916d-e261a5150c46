package com.eastmoney.common.entity.cal;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @program: BdTradeBillFinal
 * @description: 大数据神操作
 * @author: huang<PERSON><PERSON><PERSON>@eastmoney.com
 * @create: 2022/11/16
 */
public class BdTradeBillFinal {
    /**
     * 资金账号
      */
    private Long fundId;

    /**
     * indexKey
     */
    private Integer indexKey;

    /**
     * 逃顶成功次数
     */
    private Integer saleCeil;

    /**
     * 抄底成功次数
     */
    private Integer buyFloor;

    /**
     * 逃顶成功次数超越东财交易者的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal saleCeilPercent;

    /**
     * 抄底成功次数超越东财交易者的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal buyFloorPercent;

    /**
     * 逃顶最成功的个股名称
     */
    private String sNameSale;

    /**
     * 逃顶最成功的股票逃顶后10日跌幅
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal downPercent;

    /**
     * 抄底最成功的个股名称
     */
    private String sNameBuy;

    /**
     *  抄底最成功的股票抄底后10日涨幅
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal upPercent;

    /**
     * 打新次数
     */
    private Long subsNewCnt;

    /**
     * 打新次数超越东财交易者的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal subsNewPerc;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public Integer getSaleCeil() {
        return saleCeil;
    }

    public void setSaleCeil(Integer saleCeil) {
        this.saleCeil = saleCeil;
    }

    public Integer getBuyFloor() {
        return buyFloor;
    }

    public void setBuyFloor(Integer buyFloor) {
        this.buyFloor = buyFloor;
    }

    public BigDecimal getSaleCeilPercent() {
        return saleCeilPercent;
    }

    public void setSaleCeilPercent(BigDecimal saleCeilPercent) {
        this.saleCeilPercent = saleCeilPercent;
    }

    public BigDecimal getBuyFloorPercent() {
        return buyFloorPercent;
    }

    public void setBuyFloorPercent(BigDecimal buyFloorPercent) {
        this.buyFloorPercent = buyFloorPercent;
    }

    public String getsNameSale() {
        return sNameSale;
    }

    public void setsNameSale(String sNameSale) {
        this.sNameSale = sNameSale;
    }

    public BigDecimal getDownPercent() {
        return downPercent;
    }

    public void setDownPercent(BigDecimal downPercent) {
        this.downPercent = downPercent;
    }

    public String getsNameBuy() {
        return sNameBuy;
    }

    public void setsNameBuy(String sNameBuy) {
        this.sNameBuy = sNameBuy;
    }

    public BigDecimal getUpPercent() {
        return upPercent;
    }

    public void setUpPercent(BigDecimal upPercent) {
        this.upPercent = upPercent;
    }

    public Long getSubsNewCnt() {
        return subsNewCnt;
    }

    public void setSubsNewCnt(Long subsNewCnt) {
        this.subsNewCnt = subsNewCnt;
    }

    public BigDecimal getSubsNewPerc() {
        return subsNewPerc;
    }

    public void setSubsNewPerc(BigDecimal subsNewPerc) {
        this.subsNewPerc = subsNewPerc;
    }
}
