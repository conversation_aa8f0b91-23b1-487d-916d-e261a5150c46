package com.eastmoney.transport.hearbeat;

import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.ChannelUtil;
import com.eastmoney.transport.util.Constants;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/27
 * Time: 16:18
 */
public class HeartBeatHandler extends ChannelInboundHandlerAdapter {

    private static Logger LOG = LoggerFactory.getLogger(HeartBeatHandler.class);
    private volatile ScheduledFuture<?> heartBeat;
    private boolean isServer = true;
    private int heartBeatInterval;

    public HeartBeatHandler(boolean isServer, int heartBeatInterval) {
        this.isServer = isServer;
        this.heartBeatInterval = heartBeatInterval;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws InterruptedException {
        ctx.fireChannelActive();
        if (heartBeat == null) {
            LOG.info("{} 设置心跳发送器 {}", isServer == true ? "服务端" : "客户端", ChannelUtil.getChannelRoute(ctx.channel()));
            heartBeat = ctx.executor().scheduleAtFixedRate(new HeartBeatTask(ctx), 0, heartBeatInterval, TimeUnit.SECONDS);
        }
    }

    private class HeartBeatTask implements Runnable {
        private final ChannelHandlerContext ctx;

        public HeartBeatTask(final ChannelHandlerContext ctx) {
            this.ctx = ctx;
        }


        @Override
        public void run() {
            if (ctx.channel().isWritable()) {
                LOG.info("{}发送心跳信息 {}", isServer == true ? "服务端" : "客户端", ChannelUtil.getChannelRoute(ctx.channel()));
                Message message = new Message((byte) 0, 0, Constants.SOCKET_TYPE_HTBT_REQ, null);
                message.setRequestId(CommonUtil.generateUUID().getBytes());
                ctx.writeAndFlush(message);
            }
        }
    }


}

