package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.PositionSection;

import java.util.List;

/**
 * Created by h<PERSON>chengqi on 2016/8/3.
 */
public interface PositionSectionDao extends IBaseDao<PositionSection, Integer> {
    /**
     * 查询区间内清仓统计数据
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    List<PositionSection> getPositionProfitStatistics(Long fundId, Integer startDate, Integer endDate);
}
