package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.PositionSectionMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.PositionSection;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by huachengqi on 2016/8/3.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("positionSectionDao")
public class PositionSectionDaoImpl extends BaseDao<PositionSectionMapper, PositionSection, Integer> implements PositionSectionDao {

    @Override
    public List<PositionSection> getPositionProfitStatistics(Long fundId, Integer startDate, Integer endDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("fundId", fundId);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        return mapper.getPositionProfitStatistics(param);
    }
}
