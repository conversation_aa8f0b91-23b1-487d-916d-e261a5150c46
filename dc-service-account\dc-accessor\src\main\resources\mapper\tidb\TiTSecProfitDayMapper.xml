<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiTSecProfitDayMapper">

    <select id="getSecTProfitSection" resultType="com.eastmoney.common.entity.cal.TProfitBO">
        select
        totalttimes,
        totaltsuccess,
        totaltprofit,
        round(totaltsuccess/totalttimes,4) as totaltsuccessrate
        from
            (select
                count(1) as totalttimes,
                count(if(tprofit>0,1,null)) as totaltsuccess,
                sum(tprofit) as totaltprofit
            from atcenter.t_sec_profit_day use index(pk_lg_t_sec_profit_day)
            <where>
                <if test="startDate != null">
                    and bizdate  <![CDATA[>=]]>  #{startDate}
                </if>
                <if test="endDate != null">
                    and bizdate  <![CDATA[<=]]>  #{endDate}
                </if>
                <if test="fundId != null">
                    and fundid = #{fundId}
                </if>
                <if test="market != null">
                    and market = #{market}
                </if>
                <choose>
                    <when test="stkCodeList != null and stkCodeList.size() > 0">
                        AND stkCode IN
                        <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        <if test="stkCode != null">
                            and stkCode = #{stkCode}
                        </if>
                    </otherwise>
                </choose>
            </where>
            )res
    </select>

    <select id="getSecTProfitRankList" resultType="com.eastmoney.common.entity.cal.tProfitRank">
        select
        *
        from (
            select
            stkcode,
            market,
            count(1) as ttimes,
            count(if(tprofit>0,1,null)) as tsuccess,
            sum(tprofit) as tprofit,
            round(count(if(tprofit>0,1,null))/ count(1),4) as tsuccessrate
            from atcenter.t_sec_profit_day use index(pk_lg_t_sec_profit_day)
        <where>
            <if test="startDate != null">
                and bizdate  <![CDATA[>=]]>  #{startDate}
            </if>
            <if test="endDate != null">
                and bizdate  <![CDATA[<=]]>  #{endDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <if test="stkCode != null">
                and stkCode = #{stkCode}
            </if>
        </where>
        group by stkcode,market
        )t
        order by
        <choose>
            <when test='sortFlag == "1"'>
                t.ttimes
            </when>
            <when test='sortFlag == "2"'>
                t.tprofit
            </when>
            <when test='sortFlag == "3"'>
                t.tsuccess
            </when>
            <when test='sortFlag == "4"'>
                t.tsuccessrate
            </when>
            <otherwise>
                t.tprofit
            </otherwise>
        </choose>
        <if test='orderFlag == "2"'>
            desc
        </if>
        ,t.market,t.stkcode
        limit #{startNum},#{pageSize}
    </select>

    <select id="getTSecProfitDayList" resultType="com.eastmoney.common.entity.cal.TSecProfitDayDO">
        select bizdate,stkcode,market,tmatchqty,
        case when market in ('5','S') then tdifferencesHk
        else tdifferences end as tdifferences,
        tprofitHk,
        tprofit
        from atcenter.t_sec_profit_day use index(pk_lg_t_sec_profit_day)
        <where>
            <if test="bizDate != null">
                and bizdate = #{bizDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND stkCode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and stkCode = #{stkCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by bizdate desc,lastmatchtime desc
    </select>

    <select id="getTProfitDayList" resultType="com.eastmoney.common.entity.cal.TProfitDayDO">
        select bizdate, tprofit from atcenter.t_sec_profit_day use index(pk_lg_t_sec_profit_day)
        <where>
            <if test="startDate != null">
                and bizdate  <![CDATA[>=]]>  #{startDate}
            </if>
            <if test="endDate != null">
                and bizdate  <![CDATA[<=]]>  #{endDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND stkCode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and stkCode = #{stkCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by bizdate desc
        limit #{startNum},#{pageSize}
    </select>

    <select id="getTSecProfitDayListPC" resultType="com.eastmoney.common.entity.cal.TSecProfitDayDO">
        select bizdate,stkcode,market,tmatchqty,
        case when market in ('5','S') then tdifferencesHk
        else tdifferences end as tdifferences,
        tprofitHk,
        tprofit
        from atcenter.t_sec_profit_day use index(pk_lg_t_sec_profit_day)
        <where>
            <if test="startDate != null">
                and bizdate  <![CDATA[>=]]>  #{startDate}
            </if>
            <if test="endDate != null">
                and bizdate  <![CDATA[<=]]>  #{endDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND stkCode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and stkCode = #{stkCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by bizdate desc,market,stkCode
        <if test="startNum != null and pageSize != null">
            limit #{startNum},#{pageSize}
        </if>
    </select>

</mapper>