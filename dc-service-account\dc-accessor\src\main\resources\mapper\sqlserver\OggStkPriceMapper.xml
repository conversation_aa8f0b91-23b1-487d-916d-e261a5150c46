<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggStkPriceMapper">
    <select id="query" resultType="as_stkPrice">
        select b.market,
               b.stkcode,
               c.stkname,
               0                           as buycost,
               c.bondintr                  as bondintr,
               b.closeprice                as closeprice,
               b.openprice                 as openprice,
               b.lastprice                 as lastprice,
               c.stktype                   as stktype,
               d.i_by                      as lofmoneyflag,
               b.mtkcalflag                as mtkcalflag,
               e.stklevel,
               isnull(c.ticketprice, 0.00) as ticketprice,
               c.quitdate,
               c.trdid
        from run.dbo.stkprice b with (nolock)
                 left join run.dbo.stktrd c with (nolock)
                           on (b.market = c.market and b.stkcode = c.stkcode)
                 left join run.dbo.loftrd d with (nolock)
                           on (b.market = d.market and b.stkcode = d.stkcode)
                 left join run.dbo.stock e with (nolock)
                           on (b.market = e.market and b.stkcode = e.stkcode)
    </select>

</mapper>