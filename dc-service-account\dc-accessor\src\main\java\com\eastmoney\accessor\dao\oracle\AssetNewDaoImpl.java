package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.AssetNewMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.AssetNew;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2016/3/17
 *
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("assetNewDao")
public class AssetNewDaoImpl extends BaseDao<AssetNewMapper, AssetNew, Integer> implements AssetNewDao {

}
