package com.eastmoney.quote.app.conf;

import com.eastmoney.quote.app.util.ComputerAddressUtil;
import org.apache.commons.configuration.CompositeConfiguration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;

/**
 * Created by 1 on 15-7-7.
 */
public class Constant {
    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(Constant.class);

    public static  byte CONTENT_TYPE_HEART = 15;

    public static  byte CONTENT_TYPE_BUSINESS = 1;

    public static byte[] PACKET_TAIL= new byte[]{'\0','\0','\r','\n'};

    public static byte split = 100;



    ////
    public static String SERVER_IP;
    public static int SERVER_PORT;
    public static String CHARSET = "GBK";
    //#初始连接数
    public static int initialSize=10;
    //#允许最大连接数
    public static int maxActive=100;
    //#允许最大空闲连接数
    public static int maxIdle=50;
    //#允许最小空闲连接数
    public static int minIdle=10;

    //客户端ip和mac地址
    public static String IP_ADDRESS = null;
    public static String MAC_ADDRESS = null;

    static {
        CompositeConfiguration config = new CompositeConfiguration();
        try {
            config.addConfiguration(new PropertiesConfiguration("quote-config.properties"));
        } catch (ConfigurationException e) {
            LOG.error("quote-config.properties file err:" + e.getMessage(), e);
        }

        try {
            SERVER_IP = config.getString("server.ip");
            CHARSET = config.getString("charset",CHARSET);
            SERVER_PORT = Integer.parseInt(config.getString("server.port"));
            initialSize = Integer.parseInt(config.getString("initialSize"));
            maxActive = Integer.parseInt(config.getString("maxActive"));
            maxIdle = Integer.parseInt(config.getString("maxIdle"));
            minIdle = Integer.parseInt(config.getString("minIdle"));

            IP_ADDRESS = config.getString("ip_address");
            if (StringUtils.isBlank(IP_ADDRESS)) {
                IP_ADDRESS = ComputerAddressUtil.getIp();
            }
            MAC_ADDRESS = config.getString("mac_address");
            if(StringUtils.isBlank(MAC_ADDRESS)){
                MAC_ADDRESS = ComputerAddressUtil.getMacAddress();
            }

            StringBuffer sb = new StringBuffer("");
            sb.append(" SERVER_IP=" + SERVER_IP);
            sb.append(" SERVER_PORT=" + SERVER_PORT);
            sb.append(" initialSize=" + initialSize);
            sb.append(" maxActive=" + maxActive);
            sb.append(" maxIdle=" + maxIdle);
            sb.append(" minIdle=" + minIdle);
            sb.append("\n");
            sb.append("IP_ADDRESS="+IP_ADDRESS + "\n");
            sb.append("MAC_ADDRESS="+MAC_ADDRESS + "\n");
            LOG.info(sb.toString());
        } catch (Exception e) {
            LOG.error("counterservice.properties [server.ip server.port] err:" + e.getMessage(), e);
        }






    }

    public static void main(String[] args) {
        System.out.println(SERVER_IP);
    }
}
