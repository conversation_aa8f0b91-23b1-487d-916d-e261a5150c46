package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitDayMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitDay;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2016/7/19
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitDayDao")
public class TiProfitDayDaoImpl extends BaseDao<TiProfitDayMapper, ProfitDay, Long> implements ProfitDayDao {
    @Override
    public List getProfitStatList(Map<String, Object> params) {
        return mapper.getProfitStatList(params);
    }

    @Override
    public Double getSumProfit(Map<String, Object> params) {
        return mapper.getSumProfit(params);
    }

    @Override
    public Double getSumProfit(Long fundId, Integer startDate, Integer endDate) {
        Map<String,Object> param=new HashMap<>();
        param.put("fundId",fundId);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        return mapper.getSumProfit(param);
    }
}
