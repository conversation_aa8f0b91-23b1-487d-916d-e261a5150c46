package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.TradeDate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by robin on 2016/6/24.
 *
 * <AUTHOR>
 *
 * update on 2016/7/19
 * <AUTHOR>
 */
@Repository
public interface TiTradeDateMapper extends BaseMapper<Object,Integer> {

    /**
     * 查询今天是否交易日
     * @return
     */
    Integer todayIsMarket(Map<String, Object> params);

    /**
     * 取得上一个交易日
     * @return
     */
    String getPreMarketDay(Map<String, Object> params);

    /**
     * 取得下一个交易日
     * @param params
     * @return
     */
    String getNextMarketDay(Map<String, Object> params);

    //获取交易时间
    List<TradeDate> getTradeDateList(Map<String, Object> params);

    /**
     * 获取账户分析起始日至今交易时间
     *
     * @param params
     * @return
     */
    List<TradeDate> getAllTradeDateList(Map<String,Object> params);

    /**
     * 获取每个月最后一个交易日
     * @param params
     * @return
     */
    List<TradeDate> getLastTradeDateOfMonth(Map<String,Object> params);
}
