dependencies {
    implementation project(':dc-common')
    implementation project(':dc-accessor')
    implementation project(':dc-rpc')
    implementation project(':dc-quote')
}

def mainClass = "com.eastmoney.service.main.AccountMain"

jar {
    manifest {
        attributes 'Manifest-Version': '1.0'
        attributes 'Created-By': 'Gradle'
        attributes 'Main-Class': mainClass
        attributes 'Class-Path': ". config/ libs/" + configurations.runtimeClasspath.collect { it.name }.join(' libs/')
    }
    exclude('*.properties', '*/*.properties')
    exclude('*.yml')
    exclude('*.xml', '*/*.xml')
    exclude('*.sh', '*/*.sh')
}
