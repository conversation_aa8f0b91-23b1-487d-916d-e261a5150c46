package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.LogAsset;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
public interface LogAssetDao extends IBaseDao<LogAsset, Integer> {

    /**
     * 线上经纪人获取交割单的交易额和佣金
     * @param params
     * @return
     */
    List<Object> getLogAssetSum(Map<String, Object> params);

    List<LogAsset> getStkTradeList(Map<String, Object> params);

    List<LogAsset> getStkShiftList(Map<String, Object> params);

    /**
     * 获取区间资产变动-其他流入
     * @param params
     * @return
     */
    List<LogAsset> getOtherDetailList(Map<String, Object> params);

    /**
     * 获取positionsharechange 对应的交割单
     * @param params
     * @return
     */
    List<LogAsset> getTradeShiftList(Map<String, Object> params);

    /**
     * 查询 position_profit 的交割单以及交易税费(仅持仓)
     * @param params
     * @return
     */
    List<LogAsset> getHoldTradeShiftList(Map<String, Object> params);

    /**
     * 获取买卖交易记录
     * @param params
     * @return
     */
    List<LogAsset> getBSTradeList(Map<String, Object> params);

}
