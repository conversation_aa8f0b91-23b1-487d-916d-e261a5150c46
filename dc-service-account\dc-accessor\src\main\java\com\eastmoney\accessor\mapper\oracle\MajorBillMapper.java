package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.ProfitRateDay;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/31.
 */
public interface MajorBillMapper extends BaseMapper<MajorBill,Long> {
    List<MajorBill> getBestMonth(Map<String,Object> params);

    List<ProfitRateDay> selectMonthProfitRate(Map<String,Object> params);
}
