package com.eastmoney.service.cache;

import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.fundia.QueryCombFundAssetResponse;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.FundInvestService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class FundIaFundAssetService {
    private static final Logger LOG = LoggerFactory.getLogger(FundIaFundAssetService.class);
    @Resource(name = "fundIaFundAssetCache")
    private LoadingCache<Long, Optional<Double>> fundIaFundAssetCache;
    @Autowired
    private FundInvestService fundInvestService;

    @Bean(name = "fundIaFundAssetCache")
    public LoadingCache<Long, Optional<Double>> fundIaFundAssetCache() {
        LoadingCache<Long, Optional<Double>> fundIaFundAssetCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1000)
                .maximumSize(100000)
                .refreshAfterWrite(600, TimeUnit.SECONDS)
                .build(new CacheLoader<Long, Optional<Double>>() {
                    @Override
                    public Optional<Double> load(Long fundId) throws Exception {
                        Map<String, Object> params = new HashMap<>();
                        params.put("fundId", String.valueOf(fundId));
                        Map<String, QueryCombFundAssetResponse> investCombAssetMap = fundInvestService.getInvestCombAssetMap(params);
                        double totalAsset = 0.0;
                        if (investCombAssetMap != null && investCombAssetMap.size() > 0) {
                            totalAsset = investCombAssetMap.values().stream().mapToDouble(QueryCombFundAssetResponse::getTotalAsset).sum();
                        }
                        return Optional.of(totalAsset);
                    }
                });
        return fundIaFundAssetCache;
    }

    @RedisCache(keyGenerator = "'jzjy_fundiafundasset_' + #params.get('fundId')",expireSeconds = 600)
    public Double getFundIaFundAsset(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        try {
            return fundIaFundAssetCache.get(fundId).orElse(0.0);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取基金投顾资产失败", e);
        }
        return 0.0;
    }
}
