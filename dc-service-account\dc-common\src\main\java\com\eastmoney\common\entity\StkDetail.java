package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/4.
 */
public class StkDetail {
    public String market;
    public String stkCode;
    public Double buyCost;
    public Long stkQty;
    public Double bondIntr;
    public Double closePrice;
    public Double openPrice;
    public Double lastPrice;
    public String stkType;
    public Integer lofMoneyFlag;
    public String mtkCalFlag;
    public String stkLevel;

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Double getBuyCost() {
        return buyCost;
    }

    public void setBuyCost(Double buyCost) {
        this.buyCost = buyCost;
    }

    public Long getStkQty() {
        return stkQty;
    }

    public void setStkQty(Long stkQty) {
        this.stkQty = stkQty;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public Double getClosePrice() {
        return closePrice;
    }

    public void setClosePrice(Double closePrice) {
        this.closePrice = closePrice;
    }

    public Double getOpenPrice() {
        return openPrice;
    }

    public void setOpenPrice(Double openPrice) {
        this.openPrice = openPrice;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public Integer getLofMoneyFlag() {
        return lofMoneyFlag;
    }

    public void setLofMoneyFlag(Integer lofMoneyFlag) {
        this.lofMoneyFlag = lofMoneyFlag;
    }

    public String getMtkCalFlag() {
        return mtkCalFlag;
    }

    public void setMtkCalFlag(String mtkCalFlag) {
        this.mtkCalFlag = mtkCalFlag;
    }

    public String getStkLevel() {
        return stkLevel;
    }

    public void setStkLevel(String stkLevel) {
        this.stkLevel = stkLevel;
    }
}
