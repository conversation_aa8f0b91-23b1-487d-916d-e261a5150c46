package com.eastmoney.service.cache;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.dao.tidb.HgtExchangeRateDao;
import com.eastmoney.accessor.enums.ExchangeRateTypeEnum;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.HgtExchangeRate;
import com.eastmoney.common.entity.VO.ExchangeRateInfoVO;
import com.eastmoney.common.sysEnum.ConstantEnum;
import com.eastmoney.common.sysEnum.RedisKeyEnum;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.datacenter.redis.client.RedisProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-03-26 11:29
 * <p>
 * 港股通汇率缓存
 * 每隔1小时同步一次，写入redis
 */
@Service
public class ExchangeRateCache {
    private static Logger LOG = LoggerFactory.getLogger(ExchangeRateCache.class);

    @Resource
    private HgtExchangeRateDao hgtExchangeRateDao;

    @Resource
    private RedisProxy redisProxy;

    private final String LATEST_EXCHANGE_SUFFIX = "latest";

    /**
     * 从redis缓存查询指定日期的港股通汇率信息
     *
     * @param params
     * @return
     */
    public ExchangeRateInfoVO getExchangeRateInfoVO(Map<String, Object> params) {
        //获取请求参数bizDate
        Integer bizDate = CommonUtil.convert(params.get("bizDate"), Integer.class);
        String source = CommonUtil.convert(params.get("source"), String.class);
        ExchangeRateInfoVO infoVO = new ExchangeRateInfoVO();
        //查询bizDate日期汇率信息，先查询redis，查询不到在查询数据库
        String result = redisProxy.get(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix() + bizDate);
        if (StringUtils.isNotBlank(result)) {
            try {
                return JSON.parseObject(result, ExchangeRateInfoVO.class);
            } catch (Exception e) {
                infoVO.setBizDate(bizDate);
                return infoVO;
            }
        }
        //继续查询bizdate日期数据库汇率信息
        List<HgtExchangeRate> exchangeRates = hgtExchangeRateDao.getRateListByBizdate(params);
        if (exchangeRates != null && exchangeRates.size() > 0) {
            infoVO = transferExchangeRate(exchangeRates);
            redisProxy.setex(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix() + bizDate, RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getExpire().intValue(), JSON.toJSONString(infoVO));
            return infoVO;

        }
        if (ConstantEnum.ExchangeRateSource.TOperatePage.getKey().equals(source)) {
            String latestExchangeJsonStr = redisProxy.get(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix() + LATEST_EXCHANGE_SUFFIX);
            if (StringUtils.isNotBlank(latestExchangeJsonStr)) {
                try {
                    return JSON.parseObject(latestExchangeJsonStr, ExchangeRateInfoVO.class);
                } catch (Exception e) {
                    infoVO.setBizDate(bizDate);
                    return infoVO;
                }
            }
            List<HgtExchangeRate> exchangeList = hgtExchangeRateDao.getLatestRateList(params);
            if (exchangeList == null || exchangeList.size() == 0) {
                infoVO.setBizDate(bizDate);
            } else {
                infoVO = transferExchangeRate(exchangeList);
                redisProxy.setex(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix() + LATEST_EXCHANGE_SUFFIX, RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getExpire(), JSON.toJSONString(infoVO));
            }
            return infoVO;
        }
        //说明是功能说明页日期控件自主选择的，直接返回暂无数据
        infoVO.setBizDate(bizDate);
        return infoVO;
    }

    /**
     * 将数据库每日汇率信息转换成汇总对象
     *
     * @param exchangeRates
     * @return
     */
    private ExchangeRateInfoVO transferExchangeRate(List<HgtExchangeRate> exchangeRates) {
        ExchangeRateInfoVO rateInfoVO = new ExchangeRateInfoVO();
        rateInfoVO.setBizDate(exchangeRates.get(0).getBizDate());
        for (HgtExchangeRate rate : exchangeRates) {
            if (MarketEnum.SH_HK.getValue().equals(rate.getMarket())) {
                if (ExchangeRateTypeEnum.BUY_SETT.getType().equals(rate.getType())) {
                    rateInfoVO.setHgtBuySettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5, RoundingMode.HALF_UP)));
                }
                if (ExchangeRateTypeEnum.SELL_SETT.getType().equals(rate.getType())) {
                    rateInfoVO.setHgtSellSettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5, RoundingMode.HALF_UP)));
                }
            }
            if (MarketEnum.SZ_HK.getValue().equals(rate.getMarket())) {
                if (ExchangeRateTypeEnum.BUY_SETT.getType().equals(rate.getType())) {
                    rateInfoVO.setSgtBuySettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5, RoundingMode.HALF_UP)));
                }
                if (ExchangeRateTypeEnum.SELL_SETT.getType().equals(rate.getType())) {
                    rateInfoVO.setSgtSellSettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5, RoundingMode.HALF_UP)));
                }
            }
        }
        return rateInfoVO;
    }

//    @PostConstruct
//    public void init() {
//        //定义一个支持周期性执行的线程池
//        ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, r -> {
//            Thread thread = new Thread(r);
//            thread.setName("exchange-rate-cache-thread");
//            return thread;
//        });
//        //创建runnable接口，实现run方法为加载汇率到redis中，zset存储
//        Runnable command = () -> fetchExchangeRateToRedis();
//        //立即执行，每隔一个小时执行一次
//        scheduledExecutorService.scheduleWithFixedDelay(command, 0, 1, TimeUnit.HOURS);
//    }
//
//
//    public void fetchExchangeRateToRedis() {
//        LOG.info("开始刷新港股通汇率信息到redis");
//        Map<String, Object> params = new HashMap<>();
//        Map<Integer, List<HgtExchangeRate>> exchangeRateMap = hgtExchangeRateDao.getRateListByBizdate(params)
//                .stream()
//                .collect(Collectors.groupingBy(HgtExchangeRate::getBizDate));
//        Map<String, Double> rateMap = new HashMap<>();
//        for (Map.Entry<Integer, List<HgtExchangeRate>> entry : exchangeRateMap.entrySet()) {
//            Integer bizDate = entry.getKey();
//            List<HgtExchangeRate> exchangeRates = entry.getValue();
//            ExchangeRateInfoVO rateInfoVO = new ExchangeRateInfoVO();
//            rateInfoVO.setBizDate(bizDate);
//            for (HgtExchangeRate rate : exchangeRates) {
//                if (MarketEnum.SH_HK.getValue().equals(rate.getMarket())) {
//                    if (ExchangeRateTypeEnum.BUY_SETT.getType().equals(rate.getType())) {
//                        rateInfoVO.setHgtBuySettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5,RoundingMode.HALF_UP)));
//                    }
//                    if (ExchangeRateTypeEnum.SELL_SETT.getType().equals(rate.getType())) {
//                        rateInfoVO.setHgtSellSettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5,RoundingMode.HALF_UP)));
//                    }
//                }
//                if (MarketEnum.SZ_HK.getValue().equals(rate.getMarket())) {
//                    if (ExchangeRateTypeEnum.BUY_SETT.getType().equals(rate.getType())) {
//                        rateInfoVO.setSgtBuySettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5,RoundingMode.HALF_UP)));
//                    }
//                    if (ExchangeRateTypeEnum.SELL_SETT.getType().equals(rate.getType())) {
//                        rateInfoVO.setSgtSellSettRate(String.valueOf(BigDecimal.valueOf(rate.getExchangeRate()).setScale(5,RoundingMode.HALF_UP)));
//                    }
//                }
//            }
//            rateMap.put(JSON.toJSONString(rateInfoVO), Double.valueOf(bizDate));
//        }
//        redisProxy.zadd(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix(), rateMap);
//        LOG.info("港股通汇率信息刷新到redis完成，共放入【{}】条", rateMap.size());
//    }

//    /**
//     * 从redis缓存查询指定日期的港股通汇率信息
//     *
//     * @param params
//     * @return
//     */
//    public ExchangeRateInfoVO getExchangeRateInfoVO(Map<String, Object> params) {
//        //获取请求参数bizDate
//        Double bizDate = CommonUtil.convert(params.get("bizDate"), Double.class);
//        String source = CommonUtil.convert(params.get("source"), String.class);
//        String exchangeRateStr = "";
//        ExchangeRateInfoVO infoVO = new ExchangeRateInfoVO();
//        //redis查询bizDate日期缓存数据
//        List<String> data = redisProxy.zrangeByScore(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix(), bizDate, bizDate);
//        if (data != null && data.size() > 0) {
//            //查询到了，说明是历史数据或者已清算后数据
//            exchangeRateStr = data.get(0);
//        } else if (ConstantEnum.ExchangeRateSource.TOperatePage.getKey().equals(source)) {
//            //查询不到，界面内触发的查询，说明是盘中或者非交易日或者查询日期仍未清算同步到数据，查询redis最大的日期汇率返回
//            List<String> maxBizDateData = redisProxy.zrevrange(RedisKeyEnum.GGT_EXCHANGE_RATE_KEY.getPrefix(), 0, 0);
//            if (maxBizDateData != null && maxBizDateData.size() > 0) {
//                exchangeRateStr = maxBizDateData.get(0);
//            }
//        } else {
//            infoVO.setBizDate(CommonUtil.convert(params.get("bizDate"), Integer.class));
//        }
//        if (StringUtils.isNotBlank(exchangeRateStr)) {
//            infoVO = JSON.parseObject(exchangeRateStr, ExchangeRateInfoVO.class);
//        }
//        return infoVO;
//    }


}
