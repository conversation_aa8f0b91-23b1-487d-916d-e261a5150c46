<?xml version="1.0" encoding="UTF-8"?>
<!--
说明：default 表示默认数据源，仅能有一个(默认oracle)
      dbType  表示数据源的种类： oracle sqlServer mysql 等
      serverId 表示sqlServer数据库的机器编号
      flag 表示sqlServer数据库标记 实时库run 历史库history
      property 对应的连接池属性
-->
<datasources>
    <!--此节点为通用配置-->
    <commonConfig>
        <property name="maxWait" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="initialSize" value="1" />
        <property name="minIdle" value="0" />
        <property name="filters" value="stat"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="validationQuery" value="select getdate()" />
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
    </commonConfig>
    <!-- oracle -->
    <datasource id="oracleRead" class="com.alibaba.druid.pool.DruidDataSource" dbType="oracle" default="true">
        <!-- 开发库 -->
        <!--        <property name="url" value="********************************************"/>-->
        <!--        <property name="username" value="DEV_ADMIN"/>-->
        <!--        <property name="password" value="hnr46Ef1"/>-->

        <!--        &lt;!&ndash; 测试库 &ndash;&gt;-->
        <!--        <property name="url" value="*******************************************"/>-->
        <!--        <property name="username" value="T_READ"/>-->
        <!--        <property name="password" value="pjx2aQdf"/>-->

        <item name="url" value="*******************************************"/>
        <item name="username" value="T_ADMIN"/>
        <item name="password" value="tn6Y66YiuP"/>
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 仅建议支持游标的数据库打开此配置 性能提升很高-->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="100" />
    </datasource>

    <!-- tidb -->
    <datasource id="tidbRead" class="com.eastmoney.accessor.datasource.enhance.LoadBalanceDruidDataSource" dbType="mysql" default="false">
        <property name="url" value="jdbc:mysql://{10.10.89.160:3390}/atcenter"/>
        <property name="username" value="zhfx"/>
        <property name="password" value="siq#Qaq!3^"/>
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <!--        &lt;!&ndash; 打开PSCache，并且指定每个连接上PSCache的大小 仅建议支持游标的数据库打开此配置 性能提升很高&ndash;&gt;-->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="100" />
    </datasource>

    <!-- oracle (OTC) -->
    <datasource id="oracleOtc" class="com.eastmoney.accessor.datasource.enhance.LoadBalanceDruidDataSource" dbType="oracle">
        <property name="url" value="*****************************************"/>
        <property name="username" value="otcww"/>
        <property name="password" value="dCotc_359"/>
        <property name="maxWait" value="5000"/>
        <!--<property name="url" value="********************************************"/>-->
        <!--<property name="username" value="emxztxkh"/>-->
        <!--<property name="password" value="tEfr23"/>-->
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 仅建议支持游标的数据库打开此配置 性能提升很高-->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="100" />
    </datasource>

    <datasource id="oracleOtcKGDB" class="com.eastmoney.accessor.datasource.enhance.LoadBalanceDruidDataSource" dbType="oracle">
        <!-- 自研OTC仿真线的数据库-->
        <property name="url" value="******************************************"/>
        <property name="username" value="otcww"/>
        <property name="password" value="dCotc_359"/>
        <property name="maxWait" value="5000"/>
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 仅建议支持游标的数据库打开此配置 性能提升很高-->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="100" />
    </datasource>

    <!-- oracle (FUNDIA) -->
    <datasource id="oracleFundIa" class="com.eastmoney.accessor.datasource.enhance.LoadBalanceDruidDataSource" dbType="oracle">
        <property name="url" value="jdbc:oracle:thin:@//{10.10.84.183:1521,10.10.84.183:1521}/FIQS"/>
        <property name="username" value="zhfx_r"/>
        <property name="password" value="AFiuRIzF0NPR"/>
        <property name="maxWait" value="5000"/>
        <property name="validationQuery" value="SELECT 'x' FROM DUAL" />
        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 仅建议支持游标的数据库打开此配置 性能提升很高-->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="100" />
    </datasource>

    <!-- sqlServer run库-->
    <datasource id="run2" class="com.alibaba.druid.pool.DruidDataSource" dbType="sqlServer" flag="run" serverId="2">
        <property name="url" value="*******************************************"/>
        <property name="username" value="cdkf"/>
        <property name="password" value="Cdkf@088~dongCai"/>
    </datasource>

    <!-- sqlServer run库-->
    <datasource id="run3" class="com.alibaba.druid.pool.DruidDataSource" dbType="sqlServer" flag="run" serverId="3">
        <property name="url" value="*******************************************"/>
        <property name="username" value="cdkf"/>
        <property name="password" value="Cdkf@088~dongCai"/>
    </datasource>

    <!-- sqlServer run库-->
    <datasource id="run9" class="com.alibaba.druid.pool.DruidDataSource" dbType="sqlServer" flag="run" serverId="9">
        <property name="url" value="*******************************************"/>
        <property name="username" value="write"/>
        <property name="password" value="xz@088~dongcai"/>
    </datasource>

</datasources>
