package com.eastmoney.common.entity;

import java.util.Date;

/**
 * choice码表
 * 具体字段类型说明参考
 * http://172.16.84.37:8180/embaseDictionary/ 搜索 CDSY_SECUCODE
 *
 * <AUTHOR>
 * @Date 2020/12/5
 */
public class ChoiceStock {
    /**
     * 证券品种内码
     */
    private String securityVarietyCode;
    /**
     * 证券代码
     */
    private String securityCode;
    /**
     * 证券类型编码
     */
    private String securityTypeCode;
    /**
     * 证券简称
     */
    private String securityShortName;
    /**
     * 交易市场编码
     */
    private String tradeMarketCode;
    /**
     * 上市状态
     */
    private String listingState;
    /**
     * 上市日期
     */
    private Date listingDate;
    /**
     * 截止日期
     */
    private Date endDate;
    /**
     * 计量货币
     */
    private String currency;
    /**
     * 证券拼音
     */
    private String securityPinyin;

    /**
     * 扩位简称
     *
     */
    private String expandNameAbbr;

    /**
     * 扩位简称拼音
     */
    private String expandNamePinyin;

    public String getSecurityVarietyCode() {
        return securityVarietyCode;
    }

    public void setSecurityVarietyCode(String securityVarietyCode) {
        this.securityVarietyCode = securityVarietyCode;
    }

    public String getSecurityCode() {
        return securityCode;
    }

    public void setSecurityCode(String securityCode) {
        this.securityCode = securityCode;
    }

    public String getSecurityTypeCode() {
        return securityTypeCode;
    }

    public void setSecurityTypeCode(String securityTypeCode) {
        this.securityTypeCode = securityTypeCode;
    }

    public String getSecurityShortName() {
        return securityShortName;
    }

    public void setSecurityShortName(String securityShortName) {
        this.securityShortName = securityShortName;
    }

    public String getTradeMarketCode() {
        return tradeMarketCode;
    }

    public void setTradeMarketCode(String tradeMarketCode) {
        this.tradeMarketCode = tradeMarketCode;
    }

    public String getListingState() {
        return listingState;
    }

    public void setListingState(String listingState) {
        this.listingState = listingState;
    }

    public Date getListingDate() {
        return listingDate;
    }

    public void setListingDate(Date listingDate) {
        this.listingDate = listingDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSecurityPinyin() {
        return securityPinyin;
    }

    public void setSecurityPinyin(String securityPinyin) {
        this.securityPinyin = securityPinyin;
    }

    public String getExpandNameAbbr() {
        return expandNameAbbr;
    }

    public void setExpandNameAbbr(String expandNameAbbr) {
        this.expandNameAbbr = expandNameAbbr;
    }

    public String getExpandNamePinyin() {
        return expandNamePinyin;
    }

    public void setExpandNamePinyin(String expandNamePinyin) {
        this.expandNamePinyin = expandNamePinyin;
    }
}
