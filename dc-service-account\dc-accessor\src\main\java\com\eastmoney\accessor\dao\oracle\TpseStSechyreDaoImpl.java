package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.TpseStSechyreMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.TpseStSechyre;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2017/3/28.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("tpseStSechyreDao")
public class TpseStSechyreDaoImpl extends BaseDao<TpseStSechyreMapper,TpseStSechyre,Integer> implements TpseStSechyreDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(TpseStSechyreDaoImpl.class);

    private final ConcurrentHashMap<String, String> tpseStSechyreMap = new ConcurrentHashMap<>();

    @Override
    public String getPublishName(String stkCode) {
        return tpseStSechyreMap.get(stkCode.trim());
    }

    @Override
    @PostConstruct
    public void loadTpseStSechyreMap() {
        LOGGER.info("------------------------正在加载行业类别信息信息，请等待-------------------------" + DateUtil.getCurDateTime());
        doLoadStockMap();
        LOGGER.info("------------------------行业类别信息加载完成-------------------------"  + DateUtil.getCurDateTime());
        Calendar date = Calendar.getInstance();
        date.set(date.get(Calendar.YEAR), date.get(Calendar.MONTH), date.get(Calendar.DATE), 6, 0, 0);
        Date now = new Date();
        if (now.after(date.getTime())) {
            date.add(Calendar.DATE, 1);
        }
        long diff = (date.getTime().getTime() - now.getTime()) / 1000;
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        executor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                doLoadStockMap();
            }
        }, diff, 24 * 60 * 60, TimeUnit.SECONDS);
    }

    private void doLoadStockMap() {
        List<TpseStSechyre> tpseStSechyreList = getMapper().getTpseStSechyreList();
        for (TpseStSechyre tpseStSechyres : tpseStSechyreList) {
            tpseStSechyreMap.put(StringUtils.trim(tpseStSechyres.getSecurityCode()) , StringUtils.trim(tpseStSechyres.getPublishName()));
        }
    }

}
