package com.eastmoney.service.service.profit.list;

import com.eastmoney.accessor.dao.oracle.MajorBillDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created on 2020/8/12-18:28.
 *
 * <AUTHOR>
 */
@Service("profitDayListServiceFacade")
public class ProfitDayListServiceFacade {
    private static final String CAL_REAL_TIME_PROFIT_RATE = "calRealTimeProfitRate";

    @Resource(name = "profitDayListServiceSettle")
    private ProfitDayListService profitDayListServiceSettle;
    @Resource(name = "profitDayListServiceRealTime")
    private ProfitDayListService profitDayListServiceRealTime;

    @Autowired
    private TradeDateDao tradeDateDao;

    @Autowired
    private MajorBillDao majorBillDao;

    /**
     * @param params startDate 开始时间 endDate 结束时间 fundId 资金账号
     * @return
     */
    public List<ProfitDay> getDayProfitList(Map<String, Object> params) {
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        List<ProfitDay> settleProfitDayList = profitDayListServiceSettle.getDayProfitList(params);
        int today = DateUtil.getCuryyyyMMddInteger();
        if (today >= startDate && today <= endDate) {
            int accountBizDate;
            if (!CollectionUtils.isEmpty(settleProfitDayList)) {
                accountBizDate = settleProfitDayList.get(settleProfitDayList.size() - 1).getBizDate();
            } else {
                //跨周期时，取不到收益，获取前一个交易日的日期作为清算日期
                accountBizDate = Integer.parseInt(tradeDateDao.getPreMarketDay(startDate));
            }
            params.put("accountBizDate", accountBizDate);
            List<ProfitDay> realTimeDayProfitList = profitDayListServiceRealTime.getDayProfitList(params);
            if (!CollectionUtils.isEmpty(realTimeDayProfitList)) {
                settleProfitDayList.addAll(realTimeDayProfitList);
            }
        }
        return settleProfitDayList;
    }

    /**
     * @param params
     * @return
     */
    public List<ProfitRateDay> getDayProfitRateList(Map<String, Object> params) {
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);

        //使用月收益率展示至今区间收益率列表
        Boolean calMonthProfitRate = CommonUtil.convert(params.getOrDefault("calMonthProfitRate", false), Boolean.class);
        if(calMonthProfitRate){
            return getMonthProfitRate(params,startDate);
        }

        Boolean calRealTimeProfitRate;
        calRealTimeProfitRate = CommonUtil.convert(params.get(CAL_REAL_TIME_PROFIT_RATE), Boolean.class);
        List<ProfitRateDay> profitRateDayList = profitDayListServiceSettle.getDayProfitRateList(params);
        if (BooleanUtils.isTrue(calRealTimeProfitRate)) {
            int accountBizDate;
            if (!CollectionUtils.isEmpty(profitRateDayList)) {
                accountBizDate = profitRateDayList.get(profitRateDayList.size() - 1).getBizDate();
            } else {
                //跨周期时，取不到收益率，获取前一个交易日的日期作为清算日期
                accountBizDate = Integer.parseInt(tradeDateDao.getPreMarketDay(startDate));
            }
            params.put("accountBizDate", accountBizDate);
            List<ProfitRateDay> dayProfitRateRealTime = profitDayListServiceRealTime.getDayProfitRateList(params);
            if (!CollectionUtils.isEmpty(dayProfitRateRealTime)) {
                profitRateDayList.addAll(dayProfitRateRealTime);
            }
        }
        // 对于本周、本月、本年第一天9:25之前的情况，返回空，前端展示：暂无数据
        if (profitRateDayList.size() > 0) {
            //如果需要区间上一个交易日的记录
            Integer beginIndex = CommonUtil.convert(params.get("beginIndex"), Integer.class);
            if (beginIndex != null && beginIndex == -1) {
                String lastTradeDate = tradeDateDao.getPreMarketDay(startDate);
                ProfitRateDay firstProfitRateDay = new ProfitRateDay();
                firstProfitRateDay.setBizDate(Integer.parseInt(lastTradeDate));
                profitRateDayList.add(0, firstProfitRateDay);
            }
        }
        return profitRateDayList;
    }

    /**
     * 查询至今区间已清算的月收益率
     * 计算实时收益率
     * 整合月收益率数据
     */
    public List<ProfitRateDay> getMonthProfitRate(Map<String, Object> params,Integer startDate){
        Map<Integer, List<ProfitRateDay>> resultMap;
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        Integer settleDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer endIndexKey = Integer.valueOf(endDate.toString().substring(0, 6));

        //查询区间已清算月收益率
        Map<Integer, List<ProfitRateDay>> monthBillMap = majorBillDao.selectMonthProfitRate(fundId, startDate, endDate);

        //查询实时收益率
        params.put("accountBizDate", settleDate);
        List<ProfitRateDay> dayProfitRateRealTime = profitDayListServiceRealTime.getDayProfitRateList(params);

        if(CollectionUtils.isEmpty(dayProfitRateRealTime)){
            resultMap = monthBillMap;
        }else{
            dayProfitRateRealTime = dayProfitRateRealTime.stream()
                    .sorted(Comparator.comparing(ProfitRateDay::getBizDate).reversed()).collect(Collectors.toList());

            if(monthBillMap.containsKey(endIndexKey)) {
                //实时收益率合并到月收益率
                ProfitRateDay monthProfitRate = monthBillMap.get(endIndexKey).get(0);
                monthProfitRate.setProfitRate(CommonUtil.multiply(monthProfitRate.getProfitRate(),
                        dayProfitRateRealTime.get(0).getProfitRate()));

                resultMap = monthBillMap;

            }else{
                //补充月收益率
                Integer indexDate = Integer.valueOf(dayProfitRateRealTime.get(0).getBizDate().toString().substring(0,6));
                dayProfitRateRealTime.get(0).setBizDate(indexDate);
                monthBillMap.put(indexDate, Collections.singletonList(dayProfitRateRealTime.get(0)));

                resultMap = monthBillMap;
            }
        }

        //如果需要区间上一个交易日的记录
        Integer beginIndex = CommonUtil.convert(params.get("beginIndex"), Integer.class);
        if (beginIndex != null && beginIndex == -1) {
            String lastTradeDate = DateUtil.addMonth(String.valueOf(startDate), DateUtil.yyyyMMdd, -1);
            ProfitRateDay firstProfitRateDay = new ProfitRateDay();
            firstProfitRateDay.setBizDate(Integer.valueOf(lastTradeDate.substring(0,6)));
            resultMap.put(0, Collections.singletonList(firstProfitRateDay));
        }

        return new ArrayList<>(resultMap.values()).stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
    }

    /**
     * 日收益额走势
     * @param params
     * @return
     */
    public List<ProfitDay> getDayProfitTrend(Map<String, Object> params) {
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer assetStartDate = CommonUtil.convert(params.get("assetStartDate"), Integer.class);
        Boolean calMonthProfit = CommonUtil.convert(params.getOrDefault(CommConstants.CAL_MONTH_PROFIT, false), Boolean.class);
        Boolean calRealTimeProfit = CommonUtil.convert(params.get(CommConstants.CAL_REAL_TIME_PROFIT), Boolean.class);

        if(BooleanUtils.isTrue(calMonthProfit)) {
            return getMonthProfit(params);
        }

        String lastTradeDate = tradeDateDao.getPreMarketDay(startDate);
        params.put("startDate", lastTradeDate);
        List<ProfitDay> profitDayList = profitDayListServiceSettle.getDayProfitList(params);

        /*-------------begin-------------计算实时收益-----------------------------*/
        if (BooleanUtils.isTrue(calRealTimeProfit)) {
            int accountBizDate;
            if (!CollectionUtils.isEmpty(profitDayList)) {
                accountBizDate = profitDayList.get(profitDayList.size() - 1).getBizDate();
            } else {
                //跨周期时，取不到日收益，获取前一个交易日的日期作为清算日期
                accountBizDate = Integer.parseInt(tradeDateDao.getPreMarketDay(startDate));
            }

            params.put("accountBizDate", accountBizDate);
            List<ProfitDay> dayProfitRealTime = profitDayListServiceRealTime.getDayProfitList(params);
            if (!CollectionUtils.isEmpty(dayProfitRealTime)) {
                profitDayList.addAll(dayProfitRealTime);
            }
        }
        /*-------------end-------------计算实时收益-----------------------------*/
        // 对于本周、本月、本年第一天9:25之前的情况，返回空，前端展示：暂无数据
        if (CollectionUtils.isEmpty(profitDayList) || (profitDayList.size() == 1 && profitDayList.get(0).getBizDate() < startDate)) {
            return Collections.emptyList();
        }

        //如果前端需要区间上一个交易日的记录
        Integer beginIndex = CommonUtil.convert(params.get("beginIndex"), Integer.class);
        if (beginIndex != null && beginIndex == -1 && Objects.equals(startDate, assetStartDate)) {
            ProfitDay firstProfitDay = new ProfitDay();
            firstProfitDay.setBizDate(Integer.parseInt(lastTradeDate));
            profitDayList.add(0, firstProfitDay);
        }
        return profitDayList;
    }

    /**
     * 查询至今区间已清算的月收益
     * 计算实时收益
     * 整合月收益数据
     */
    public List<ProfitDay> getMonthProfit(Map<String, Object> params){
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        Integer settleDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);

        //使用月收益展示至今区间收益列表
        List<ProfitDay> profitDayList = majorBillDao.selectMonthProfit(fundId, startDate, endDate);

        // 查询实时收益
        params.put("accountBizDate", settleDate);
        List<ProfitDay> dayProfitRealTimes = profitDayListServiceRealTime.getDayProfitList(params);
        if (!CollectionUtils.isEmpty(dayProfitRealTimes)) {
            dayProfitRealTimes = dayProfitRealTimes.stream()
                    .sorted(Comparator.comparing(ProfitDay::getBizDate).reversed())
                    .collect(Collectors.toList());
            ProfitDay dayProfitRealTime = dayProfitRealTimes.get(0);
            dayProfitRealTime.setBizDate(dayProfitRealTime.getBizDate() / 100);
            profitDayList.add(dayProfitRealTime);

            profitDayList = profitDayList.stream()
                    .collect(
                            Collectors.toMap(
                                    ProfitDay::getBizDate,
                                    Function.identity(),
                                    (o1, o2) -> {
                                        o1.setProfit(ArithUtil.add(o1.getProfit(), o2.getProfit()));
                                        return o1;
                                    }
                            )
                    )
                    .values()
                    .stream()
                    .sorted(Comparator.comparingInt(ProfitDay::getBizDate))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(profitDayList)) {
            return profitDayList;
        }

        //如果需要区间上一个交易日的记录
        Integer beginIndex = CommonUtil.convert(params.get("beginIndex"), Integer.class);
        if (beginIndex != null && beginIndex == -1) {
            String lastTradeDate = DateUtil.addMonth(String.valueOf(startDate), DateUtil.yyyyMMdd, -1);
            ProfitDay firstProfitDay = new ProfitDay();
            firstProfitDay.setBizDate(Integer.valueOf(lastTradeDate.substring(0,6)));
            profitDayList.add(0, firstProfitDay);
        }

        return profitDayList;
    }
}
