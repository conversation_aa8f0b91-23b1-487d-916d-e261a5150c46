package com.eastmoney.common.entity;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Created on 2016/3/1
 * 交割单
 *
 * <AUTHOR>
 */
public class LogAsset extends BaseEntity {
    private Integer operDate;/*操作日期, 物理日期 (委托日期)*/
    private Integer clearDate;/*清算日期, (成交日期)*/
    private Integer bizDate;/*交收日期, 表明该笔业务所属日期*/
    private Long sno;/*流水号*/
    private Long relativeSno;/*双向操作的关联流水号, (资金内部划转的划入流水号)*/
    private Long custId;/*客户代码*/
    private String custName;/*客户姓名*/
    private Long fundId;/*资金帐号*/
    private String moneyType;/*币种,货币代码 0人民币 1港币 2美元*/
    private String orgId;/*资金帐号营业部*/
    private String brhId;/*机构分支*/
    private String custKind;/*客户类别*/
    private String custGroup;/*客户分组*/
    private String fundKind;/*资金分类*/
    private String fundLevel;/*资金室号*/
    private String fundGroup;/*资金分组*/
    private Long digestId;/*摘要代码*/
    private String digestName;/*业务名称*/
    private Double fundEffect;/*资金发生金额*/
    private Double fundBal;/*资金本次余额*/
    private String secuId;/*股东代码*/
    private String market;/*交易市场*/
    private String bizType;/*业务大类*/
    private String stkCode;/*证券代码*/
    private String stkType;/*证券类别*/
    private String bankCode;/*银行代码*/
    private String trdBankCode;/*银证通银行代码*/
    private String bankBranch;/*银行支行*/
    private String bankNetPlace;/*银行网点*/
    private String stkName;/*证券名称*/
    private Long stkEffect;/*证券发生数, (过户数量)*/
    private Long stkBal;/*证券余额*/
    private String orderId;/*合同序号*/
    private String trdId;/*交易类型*/
    private Long orderQty;/*委托数量*/
    private Double orderPrice;/*委托价格*/
    private Double bondIntr;/*国债的预计利息*/
    private String orderDate;/*委托日期*/
    private String orderTime;/*委托时间*/
    private Long matchQty;/*成交数量*/
    private Double matchAmt;/*成交金额*/
    private String seat;/*席位代码*/
    private Long matchTimes;/*成交笔数*/
    private Double matchPrice;/*成交价格*/
    private Long matchTime;/*成交时间, (第一笔成交的时间)*/
    private String matchCode;/*实时成交号码, (存存取款流水号,第一笔成交号码)*/
    public String bsFlag;/*买卖类别(orderRec的)*/
    private Double feeFront;/*前台费用*/
    private String sourceType;/*发起方, '0' 本部发起 'B'银行发起 'S'券商发起*/
    private String bankId;/*银行帐号*/
    private Long agentId;/*代理人代码*/
    private Long operId;/*操作人代码*/
    private String operWay;/*操作方式*/
    private String operOrg;/*操作营业部代码*/
    private String operLevel;/*操作员级别*/
    private String netAddr;/*操作站点*/
    private Long chkOper;/*审核柜员 0证券交收流水 1补做交收流水*/
    private String checkFlag;/*复核标志*/
    private Long brokerId;/*经纪人*/
    private Long custMgrId;/*客户经理*/
    private Long fundUser0;/*客户其它分类0*/
    private Long fundUser1;/* 客户其它分类1*/
    private Double privilege;/*盈亏金额*/
    private String remark;/*备用*/
    private Long orderSno;/*委托号*/
    private String pathId;/*接口种类标识*/
    private String cancelFlag;/*冲销标志 '0' 未冲 '1' 已冲*/
    private String reportKind;/*资金报表分类*/
    private String creditId;/*融资品种标识*/
    private String creditFlag;/*融资开仓平仓强平*/
    private String prodCode;/* 产品编码 6位的证券代码 + 3位购回天数 + 3位期号*/
    private Double setTrate;/*结算汇率 沪港通业务*/
    private String postStr;
    private String shiftAmt;    //转入转出金额
    private Double settRate;//汇率

    private Double feeJsxf;/*净佣金*/
    private Double feeSxf;/*佣金*/
    private Double feeYhs;/*'0'-'印花税'*/
    private Double feeGhf;/*'1'-'过户费'*/
    private Double feeQsf;/*'2'-'清算费'*/
    private Double feeJygf;/*'3'-'交易规费'*/
    private Double feeJsf;/*'4'-'经手费'*/
    private Double feeZgf;/*'5'-'证管费'*/
    private Double feeQtf;/*'7'-'其他费'*/
    private Double feeOneYhs; //一级印花税
    private Double feeOneGhf;   //一级过户费
    private Double feeOneQsf;   //一级清算费
    private Double feeOneJygf;  //一级交易规费
    private Double feeOneJsf;  //一级经手费
    private Double feeOneZgf;  //一级证管费
    private Double feeOneQtf;  //一级证管费
    private Double feeOneFxj;  //一级证管费
    private Double feeTotal; //费用累计
    private String positionFlag;//建仓清仓标记  1为建仓  2为清仓
    /**
     *  买入成交：case when bsflag='0B' then matchprice*matchqty else 0 end
     */
    private Double buyMatchAmt;

    /**
     *  卖出成交：case when bsflag='0S' then matchprice*matchqty else 0 end
     */
    private Double saleMatchAmt;

    /**
     * 买入份额：case when bsflag='0B' then matchqty else 0 end
     */
    private Long buystkqty;
    /**
     * 卖出份额：case when bsflag='0S' then matchqty else 0 end
     */
    private Long salestkqty;
    public String getPositionFlag() {
        return positionFlag;
    }

    public void setPositionFlag(String positionFlag) {
        this.positionFlag = positionFlag;
    }

    public String getShiftAmt() {
        return shiftAmt;
    }

    public void setShiftAmt(String shiftAmt) {
        this.shiftAmt = shiftAmt;
    }

    public String getDigestName() {
        return digestName;
    }

    public void setDigestName(String digestName) {
        this.digestName = digestName;
    }

    public String getPostStr() {
        return postStr;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public Integer getOperDate() {
        return operDate;
    }

    public void setOperDate(Integer operDate) {
        this.operDate = operDate;
    }

    public Integer getClearDate() {
        return clearDate;
    }

    public void setClearDate(Integer clearDate) {
        this.clearDate = clearDate;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Long getSno() {
        return sno;
    }

    public void setSno(Long sno) {
        this.sno = sno;
    }

    public Long getRelativeSno() {
        return relativeSno;
    }

    public void setRelativeSno(Long relativeSno) {
        this.relativeSno = relativeSno;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = StringUtils.trim(custName);
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = StringUtils.trim(moneyType);
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = StringUtils.trim(orgId);
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = StringUtils.trim(brhId);
    }

    public String getCustKind() {
        return custKind;
    }

    public void setCustKind(String custKind) {
        this.custKind = StringUtils.trim(custKind);
    }

    public String getCustGroup() {
        return custGroup;
    }

    public void setCustGroup(String custGroup) {
        this.custGroup = StringUtils.trim(custGroup);
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = StringUtils.trim(fundKind);
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = StringUtils.trim(fundLevel);
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = StringUtils.trim(fundGroup);
    }

    public Long getDigestId() {
        return digestId;
    }

    public void setDigestId(Long digestId) {
        this.digestId = digestId;
    }

    public Double getFundEffect() {
        return fundEffect;
    }

    public void setFundEffect(Double fundEffect) {
        this.fundEffect = fundEffect;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = StringUtils.trim(secuId);
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = StringUtils.trim(market);
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = StringUtils.trim(bizType);
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = StringUtils.trim(stkCode);
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = StringUtils.trim(stkType);
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = StringUtils.trim(bankCode);
    }

    public String getTrdBankCode() {
        return trdBankCode;
    }

    public void setTrdBankCode(String trdBankCode) {
        this.trdBankCode = StringUtils.trim(trdBankCode);
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = StringUtils.trim(bankBranch);
    }

    public String getBankNetPlace() {
        return bankNetPlace;
    }

    public void setBankNetPlace(String bankNetPlace) {
        this.bankNetPlace = StringUtils.trim(bankNetPlace);
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = StringUtils.trim(stkName);
    }

    public Long getStkEffect() {
        return stkEffect;
    }

    public void setStkEffect(Long stkEffect) {
        this.stkEffect = stkEffect;
    }

    public Long getStkBal() {
        return stkBal;
    }

    public void setStkBal(Long stkBal) {
        this.stkBal = stkBal;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = StringUtils.trim(orderId);
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = StringUtils.trim(trdId);
    }

    public Long getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Long orderQty) {
        this.orderQty = orderQty;
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = StringUtils.trim(orderDate);
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = StringUtils.trim(orderTime);
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = StringUtils.trim(seat);
    }

    public Long getMatchTimes() {
        return matchTimes;
    }

    public void setMatchTimes(Long matchTimes) {
        this.matchTimes = matchTimes;
    }

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public Long getMatchTime() {
        return matchTime;
    }

    public void setMatchTime(Long matchTime) {
        this.matchTime = matchTime;
    }

    public String getMatchCode() {
        return matchCode;
    }

    public void setMatchCode(String matchCode) {
        this.matchCode = matchCode;
    }

    public Double getFeeJsxf() {
        return feeJsxf;
    }

    public void setFeeJsxf(Double feeJsxf) {
        this.feeJsxf = feeJsxf;
    }

    public Double getFeeSxf() {
        return feeSxf;
    }

    public void setFeeSxf(Double feeSxf) {
        this.feeSxf = feeSxf;
    }

    public Double getFeeYhs() {
        return feeYhs;
    }

    public void setFeeYhs(Double feeYhs) {
        this.feeYhs = feeYhs;
    }

    public Double getFeeGhf() {
        return feeGhf;
    }

    public void setFeeGhf(Double feeGhf) {
        this.feeGhf = feeGhf;
    }

    public Double getFeeQsf() {
        return feeQsf;
    }

    public void setFeeQsf(Double feeQsf) {
        this.feeQsf = feeQsf;
    }

    public Double getFeeJygf() {
        return feeJygf;
    }

    public void setFeeJygf(Double feeJygf) {
        this.feeJygf = feeJygf;
    }

    public Double getFeeJsf() {
        return feeJsf;
    }

    public void setFeeJsf(Double feeJsf) {
        this.feeJsf = feeJsf;
    }

    public Double getFeeZgf() {
        return feeZgf;
    }

    public void setFeeZgf(Double feeZgf) {
        this.feeZgf = feeZgf;
    }

    public Double getFeeQtf() {
        return feeQtf;
    }

    public void setFeeQtf(Double feeQtf) {
        this.feeQtf = feeQtf;
    }

    public Double getFeeOneYhs() {
        return feeOneYhs;
    }

    public void setFeeOneYhs(Double feeOneYhs) {
        this.feeOneYhs = feeOneYhs;
    }

    public Double getFeeOneGhf() {
        return feeOneGhf;
    }

    public void setFeeOneGhf(Double feeOneGhf) {
        this.feeOneGhf = feeOneGhf;
    }

    public Double getFeeOneQsf() {
        return feeOneQsf;
    }

    public void setFeeOneQsf(Double feeOneQsf) {
        this.feeOneQsf = feeOneQsf;
    }

    public Double getFeeOneJygf() {
        return feeOneJygf;
    }

    public void setFeeOneJygf(Double feeOneJygf) {
        this.feeOneJygf = feeOneJygf;
    }

    public Double getFeeOneJsf() {
        return feeOneJsf;
    }

    public void setFeeOneJsf(Double feeOneJsf) {
        this.feeOneJsf = feeOneJsf;
    }

    public Double getFeeOneZgf() {
        return feeOneZgf;
    }

    public void setFeeOneZgf(Double feeOneZgf) {
        this.feeOneZgf = feeOneZgf;
    }

    public Double getFeeOneQtf() {
        return feeOneQtf;
    }

    public void setFeeOneQtf(Double feeOneQtf) {
        this.feeOneQtf = feeOneQtf;
    }

    public Double getFeeOneFxj() {
        return feeOneFxj;
    }

    public void setFeeOneFxj(Double feeOneFxj) {
        this.feeOneFxj = feeOneFxj;
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag;
    }

    public Double getFeeFront() {
        return feeFront;
    }

    public void setFeeFront(Double feeFront) {
        this.feeFront = feeFront;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = StringUtils.trim(sourceType);
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = StringUtils.trim(bankId);
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = StringUtils.trim(operWay);
    }

    public String getOperOrg() {
        return operOrg;
    }

    public void setOperOrg(String operOrg) {
        this.operOrg = StringUtils.trim(operOrg);
    }

    public String getOperLevel() {
        return operLevel;
    }

    public void setOperLevel(String operLevel) {
        this.operLevel = StringUtils.trim(operLevel);
    }

    public String getNetAddr() {
        return netAddr;
    }

    public void setNetAddr(String netAddr) {
        this.netAddr = StringUtils.trim(netAddr);
    }

    public Long getChkOper() {
        return chkOper;
    }

    public void setChkOper(Long chkOper) {
        this.chkOper = chkOper;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = StringUtils.trim(checkFlag);
    }

    public Long getBrokerId() {
        return brokerId;
    }

    public void setBrokerId(Long brokerId) {
        this.brokerId = brokerId;
    }

    public Long getCustMgrId() {
        return custMgrId;
    }

    public void setCustMgrId(Long custMgrId) {
        this.custMgrId = custMgrId;
    }

    public Long getFundUser0() {
        return fundUser0;
    }

    public void setFundUser0(Long fundUser0) {
        this.fundUser0 = fundUser0;
    }

    public Long getFundUser1() {
        return fundUser1;
    }

    public void setFundUser1(Long fundUser1) {
        this.fundUser1 = fundUser1;
    }

    public Double getPrivilege() {
        return privilege;
    }

    public void setPrivilege(Double privilege) {
        this.privilege = privilege;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = StringUtils.trim(remark);
    }

    public Long getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Long orderSno) {
        this.orderSno = orderSno;
    }

    public String getPathId() {
        return pathId;
    }

    public void setPathId(String pathId) {
        this.pathId = StringUtils.trim(pathId);
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = StringUtils.trim(cancelFlag);
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = StringUtils.trim(reportKind);
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = StringUtils.trim(creditId);
    }

    public String getCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(String creditFlag) {
        this.creditFlag = StringUtils.trim(creditFlag);
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = StringUtils.trim(prodCode);
    }

    public Double getSetTrate() {
        return setTrate;
    }

    public void setSetTrate(Double setTrate) {
        this.setTrate = setTrate;
    }

    public Double getSettRate() {
        return settRate;
    }

    public void setSettRate(Double settRate) {
        this.settRate = settRate;
    }

    public void setFeeTotal(Double feeTotal){this.feeTotal = feeTotal;}

    public Double getFeeTotal(){return this.feeTotal;}

    public Double getBuyMatchAmt() {
        return buyMatchAmt;
    }

    public void setBuyMatchAmt(Double buyMatchAmt) {
        this.buyMatchAmt = buyMatchAmt;
    }

    public Double getSaleMatchAmt() {
        return saleMatchAmt;
    }

    public void setSaleMatchAmt(Double saleMatchAmt) {
        this.saleMatchAmt = saleMatchAmt;
    }

    public Long getBuystkqty() {
        return buystkqty;
    }

    public void setBuystkqty(Long buystkqty) {
        this.buystkqty = buystkqty;
    }

    public Long getSalestkqty() {
        return salestkqty;
    }

    public void setSalestkqty(Long salestkqty) {
        this.salestkqty = salestkqty;
    }

    public static LogAsset of(PosDBItem object) {
        LogAsset item = new LogAsset();
        if (Objects.isNull(object)) {
            return item;
        }

        item.setSecuId(object.getSecuId());
        item.setMarket(object.getMarket());
        item.setStkCode(object.getStkCode());
        item.setBuystkqty(object.getBuystkqty());
        item.setSalestkqty(object.getSalestkqty());
        item.setBuyMatchAmt(object.getBuyMatchAmt());
        item.setSaleMatchAmt(object.getSaleMatchAmt());
        return item;
    }
}
