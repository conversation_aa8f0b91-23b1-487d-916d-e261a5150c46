package test;

import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.service.Dispatcher;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by sunyuncai on 2016/11/9.
 */
public class TestDB {
    public static Map<Integer, Long> timeMap = new ConcurrentHashMap<>();
    public static void main(String[] args) throws Exception{
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int threads = availableProcessors;
        int requestNum = 1000;
        String funCode = "getRealTimePositionList";
        if (args != null && args.length >= 2) {
            threads = Integer.parseInt(args[0]);
            requestNum = Integer.parseInt(args[1]);
            funCode = args[2];
        }

        ExecutorService service = Executors.newFixedThreadPool(threads);
        Dispatcher dispatcher = SpringContextUtil.getBean(Dispatcher.class);
        Map<String,Object> map = new HashMap<>();
        map.put("fundId","540700265771");
        map.put("funCode",funCode);
        dispatcher.service(map);

    }
}
