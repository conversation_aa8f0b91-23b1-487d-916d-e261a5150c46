package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.StockCodeAlter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 */
@Repository
public interface StockCodeAlterMapper extends BaseMapper<StockCodeAlter, Integer> {

    /**
     * 查询更换过的股票代码
     * @param params
     * @return
     */
    public List<StockCodeAlter> getStockCodeAlterList(Map<String,Object> params);

}
