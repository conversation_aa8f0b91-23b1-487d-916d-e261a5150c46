package com.eastmoney.common.entity.cal.yearbill;


import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;
import com.eastmoney.common.util.ArithUtil;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Description 主账单——年账单
 * <AUTHOR>
 * @Date 2025/5/30 15:21
 */
public class MajorBillStrInfo  {

    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profitRate; //收益率
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profit; //收益额
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profitRankPercent; //收益额排名百分比
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tradeCount;  //交易次数
    private Integer bizDate;

    public BigDecimal getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(BigDecimal profitRate) {
        this.profitRate = profitRate;
    }

    public BigDecimal getProfit() {
        return profit;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

    public BigDecimal getProfitRankPercent() {
        return profitRankPercent;
    }

    public void setProfitRankPercent(BigDecimal profitRankPercent) {
        this.profitRankPercent = profitRankPercent;
    }

    public BigDecimal getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(BigDecimal tradeCount) {
        this.tradeCount = tradeCount;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public static MajorBillStrInfo buildStrInfo(MajorBill majorBill) {
        if (Objects.isNull(majorBill)) {
            return null;
        }
        MajorBillStrInfo majorBillStrInfo = new MajorBillStrInfo();
        majorBillStrInfo.setProfitRate(ArithUtil.toBigDecimal(majorBill.getProfitRate(), null));
        majorBillStrInfo.setProfit(ArithUtil.toBigDecimal(majorBill.getProfit(), null));
        majorBillStrInfo.setProfitRankPercent(ArithUtil.toBigDecimal(majorBill.getProfitRankPercent(), null));
        majorBillStrInfo.setTradeCount(ArithUtil.toBigDecimal(majorBill.getTradeCount(), null));
        majorBillStrInfo.setBizDate(majorBillStrInfo.getBizDate());
        return majorBillStrInfo;
    }
}
