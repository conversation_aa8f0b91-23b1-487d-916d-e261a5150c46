package com.eastmoney.transport.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * User: xww
 * Date: 14-7-2
 * Time: 上午9:13
 */
public class Constants {
    //连接
    public static final byte SOCKET_TYPE_KEY_REQ = 1;
    public static final byte SOCKET_TYPE_KEY_RES = 2;
    //心跳
    public static final byte SOCKET_TYPE_HTBT_REQ = 3;
    public static final byte SOCKET_TYPE_HTBT_RES = 4;
    //拉取
    public static final byte SOCKET_TYPE_PULL_REQ = 5;
    public static final byte SOCKET_TYPE_PULL_RES = 6;
    //推送
    public static final byte SOCKET_TYPE_PUSH_REQ = 7;
    public static final byte SOCKET_TYPE_PUSH_RES = 8;

    public static int SERVER_THREAD_POOL ;
    public static int CLIENT_THREAD_POOL ;
    public static int HEART_BEAT_INTERVAL;
    public static int DISCONNCT_INTERVAL;
    public static int REQ_NUM;
    public static int SERVER_SLEEP;
    public static int SEND_BYTE_SIZE;
    public static int COMPRESS_PACKAGE_LENGTH;

    //超时时间
    public static int TIMEOUT ;
    //断线重连时间
    public static int RECONNECT_INTERVAL ;
    static {
        InputStream in = null;
        try {
            in = Constants.class.getClassLoader().getResourceAsStream("rpc-config.properties");
            Properties ps = new Properties();
            ps.load(in);
            HEART_BEAT_INTERVAL = Integer.valueOf(ps.getProperty("heart_beat_interval", "5"));
            DISCONNCT_INTERVAL = Integer.valueOf(ps.getProperty("disconnct_interval", "50"));
            COMPRESS_PACKAGE_LENGTH = Integer.valueOf(ps.getProperty("compress_package_length", "10000"));

            in = Constants.class.getClassLoader().getResourceAsStream("rpc-client-config.properties");
            Properties p = new Properties();
            p.load(in);
            TIMEOUT = Integer.valueOf(p.getProperty("timeout", "1000"));
            CLIENT_THREAD_POOL = Integer.valueOf(p.getProperty("client_thread_pool", "10"));
            RECONNECT_INTERVAL = Integer.valueOf(p.getProperty("reconnect_interval", "30"));
            REQ_NUM = Integer.valueOf(p.getProperty("req_num", "10000"));
            SERVER_SLEEP = Integer.valueOf(p.getProperty("server_sleep", "1000"));
            SEND_BYTE_SIZE = Integer.valueOf(p.getProperty("send_byte_size", "0"));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(in != null)
                    in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {

    }
}
