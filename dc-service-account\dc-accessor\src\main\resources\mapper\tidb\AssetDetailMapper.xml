<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.AssetDetailMapper">

    <select id="getAllFundAsset" resultType="com.eastmoney.common.entity.cal.AssetDetailDO">
        select fundid,ifnull(fundasset,0)+ifnull(fundbuysale,0)+ifnull(fundcashpro,0) as asset
        from atcenter.asset_detail use index(pk_lg_assetdetail)
        <where>
            fundId = #{fundId}
            and BizDate = #{bizDate}
            and MoneyType = #{moneyType}
        </where>
    </select>

</mapper>