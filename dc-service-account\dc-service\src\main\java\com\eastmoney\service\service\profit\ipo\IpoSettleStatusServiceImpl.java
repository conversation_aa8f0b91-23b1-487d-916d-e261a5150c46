package com.eastmoney.service.service.profit.ipo;

import com.eastmoney.accessor.dao.tidb.IpoSettleStatDao;
import com.eastmoney.common.entity.cal.IPOProfitInfoBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IpoSettleStatusServiceImpl implements IpoSettleStatusService {
    @Autowired
    private IpoSettleStatDao ipoSettleStatDao;

    @Override
    public IPOProfitInfoBO getIpoSettleStatus(Integer serverId, String businessCode) {
        return ipoSettleStatDao.getIpoSettleStatus(serverId, businessCode);
    }
}
