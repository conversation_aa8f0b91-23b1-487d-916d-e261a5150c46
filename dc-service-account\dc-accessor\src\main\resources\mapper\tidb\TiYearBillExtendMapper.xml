<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiYearBillExtendMapper">

    <select id="getBestDay" resultType="com.eastmoney.common.entity.cal.ProfitDay">
        select profit,bizDate
        from atcenter.profit_day use index(PK_LG_PROFIT_DAY)
        where fundid = #{fundId} and bizDate between #{startDate} and #{endDate}
        order by profit desc limit 1
    </select>

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.YearBillExtend">
        select FUNDID
                ,serverId
                ,tradesCount
                ,tradesCountRankPercent
                ,mostTradeStkCode
                ,mostTradeMarket
                ,mostTradeStkName
                ,mostTradeStkTradeTimes
                ,mostHoldingStkCode
                ,mostHoldingMarket
                ,mostHoldingStkName
                ,mostHoldingStkHoldingDays
                ,TRADEWINDAYS
                ,TRADEWINRATERANKPERCENT
                ,PROFITBESTMONTH
                ,CLEARMOSTPROFITSECURITYCODE
                ,CLEARMOSTPROFITSECURITYMARKET
                ,CLEARMOSTLOSSSECURITYCODE
                ,CLEARMOSTLOSSSECURITYMARKET
        from atcenter.b_Year_bill_extend use index(PK_LG_B_Y_EXTEND_BILL)
        where fundid = #{fundId} and indexKey = #{indexKey}
    </select>

</mapper>
