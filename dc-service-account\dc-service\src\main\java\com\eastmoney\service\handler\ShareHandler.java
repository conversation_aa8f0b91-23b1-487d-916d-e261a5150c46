package com.eastmoney.service.handler;

import com.eastmoney.accessor.enums.StkTypeEnum;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.asset.AssetHisService;
import com.eastmoney.service.service.asset.base.AssetServiceRealTimeImpl;
import com.eastmoney.service.service.profit.base.ProfitServiceRealTimeImpl;
import com.eastmoney.service.service.profit.list.ProfitDayListServiceFacade;
import com.eastmoney.service.service.profit.realtime.ProfitRealTimeServiceFacade;
import com.eastmoney.service.service.profit.section.ProfitSectionService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.eastmoney.common.util.ArithUtil.*;

@Service
public class ShareHandler {
    @Resource(name = "profitRealTimeServiceFacade")
    private ProfitRealTimeServiceFacade profitRealTimeServiceFacade;
    @Resource(name = "profitServiceRealTime")
    private ProfitServiceRealTimeImpl profitServiceRealTime;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AssetNewService assetNewService;
    @Resource(name = "assetServiceRealTime")
    private AssetServiceRealTimeImpl assetServiceRealTime;
    @Autowired
    private StkAssetService stkAssetService;
    @Resource(name = "profitDayListServiceFacade")
    private ProfitDayListServiceFacade profitDayListServiceFacade;
    @Autowired
    private StockService stockService;
    @Autowired
    private AssetHisService assetHisService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    @Resource(name = "profitSectionServiceDay")
    private ProfitSectionService profitSectionServiceDay;

    /**
     * 柜台接口 获取当日收益率
     * 目前bizDate和startDate前端没有使用
     * fundId
     */
    public DayProfitBean getRealTimeProfitInfo(Map<String, Object> params) {

        DayProfitBean profitSection = new DayProfitBean();
        // 查询当日收益额、收益率
        params.put("calProfitRate", true);
        SectionProfitBean dayProfit = profitSectionServiceDay.getProfitSection(params);
        if (Objects.nonNull(dayProfit)) {
            profitSection.setProfit(dayProfit.getProfit());
            profitSection.setProfitRate(dayProfit.getProfitRate());
            return profitSection;
        } else {
            return null;
        }
    }

    public Object getShareStkAssetProfitInfo(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String moneyType = CommonUtil.convert(params.get("moneyType"), String.class);

        List<QryFund> qryFundList = assetServiceRealTime.getAssetRealTime(params, true);
        QryFund qryFund = !CollectionUtils.isEmpty(qryFundList)? qryFundList.get(0): new QryFund();

        params.put("startDate", DateUtil.getCuryyyyMMdd());
        params.put("endDate", DateUtil.getCuryyyyMMdd());
        List<ProfitDay> profitDay = profitDayListServiceFacade.getDayProfitList(params);
        if (!CollectionUtils.isEmpty(profitDay)) {
            qryFund.dayProfit = profitDay.stream()
                    .reduce(0.0, (profit, dayStkProfit) -> ArithUtil.add(profit, dayStkProfit.getProfit()), ArithUtil::add);
        }

        Map dayProfitResult = profitServiceRealTime.getDayProfitData(params, false, qryFund);
        List<DayStkProfit> dayStkProfitList = (List<DayStkProfit>) dayProfitResult.get("dayStkProfitList");
        Map<String, DayStkProfit> dayStkProfitMap = dayStkProfitList.stream().collect(Collectors.toMap(o -> getKey(o.getStkCode(), o.getMarket()), Function.identity(), (o1, o2) -> o2));

        List<PositionInfo> positionInfoList = (List<PositionInfo>) dayProfitResult.get("positionInfoList");
        Integer serverId = CommonUtil.convert(params.get("serverId"), Integer.class);
        //计算持仓收益
        stkAssetService.calPositionIncome(positionInfoList, moneyType, fundId, notNull(qryFund.getFundAllWithOtc()));
        for (PositionInfo pstInfo : positionInfoList) {
            qryFund.sumIncome = ArithUtil.add(qryFund.sumIncome, pstInfo.getIncome());
            // APPAGILE-115178 基金默认展示扩位简称，股票和债券维持短简称不变
            if (StkTypeEnum.isShowFullNameFundType(pstInfo.getStkType())) {
                pstInfo.setExpandNameAbbr(stockService.getExpandNameAbbr(pstInfo.stkCode, pstInfo.market));
            }
        }
        stkAssetService.setExtInfo(positionInfoList, fundId);
        if (!commonService.needCalDayProfit(String.valueOf(serverId))) {
            qryFund.setPositionInfoList(positionInfoList);
            return qryFund;
        }


        if (!CollectionUtils.isEmpty(dayStkProfitList)) {

            AssetNew assetNew = assetNewService.getAssetInfo(params);
            if (assetNew == null) {
                return qryFund;
            }
            Map<String, Double> stkOpenMktValMap = getStkOpenMktVal(fundId, assetNew.getBizDate(), Integer.valueOf(serverId));
            Iterator<PositionInfo> iter = positionInfoList.iterator();
            while (iter.hasNext()) {
                PositionInfo pstInfo = iter.next();
                if (!moneyType.equals(pstInfo.getMoneyType()) || pstInfo.isClear) {
                    iter.remove();
                    continue;
                }
                DayStkProfit dayStkProfit = dayStkProfitMap.get(getKey(pstInfo.getStkCode(), pstInfo.getMarket()));
                if (dayStkProfit != null) {
                    // 当日参考收益
                    pstInfo.setDayProfit(dayStkProfit.getProfit());
                    // 当日参考收益率
                    // 盈亏比例 = 当日盈亏 /（期初市值+当日买入金额）
                    Double stkOpenMktVal = stkOpenMktValMap.getOrDefault(getKey(pstInfo.getStkCode(), pstInfo.getMarket()),
                            stkOpenMktValMap.getOrDefault(getKey(bseCodeAlterService.getCodeAlterOrReverse(pstInfo.getStkCode(), pstInfo.getMarket())
                                    , pstInfo.getMarket()) , 0d));
                    //买入金额包含利息
                    Double dayProfitRateDeno = add(stkOpenMktVal, dayStkProfit.getBuyAmtWithIntr());
                    if (ArithUtil.eq(dayStkProfit.profit, 0d)) {
                        pstInfo.setDayProfitRate(0d);
                    } else if (dayProfitRateDeno > 0.0) {
                        pstInfo.setDayProfitRate(div(dayStkProfit.profit, dayProfitRateDeno));
                    }
                } else {
                    if (commonService.isStkUnsupported(pstInfo)) {
                        // 当日参考收益
                        pstInfo.setDayProfit(0.0);
                        // 当日参考收益率
                        pstInfo.setDayProfitRate(0.0);
                    }
                }
            }
        }
        qryFund.setPositionInfoList(positionInfoList);
        return qryFund;
    }

    private Map<String, Double> getStkOpenMktVal(Long fundId, Integer bizDate, Integer serverId) {
        //上一个交易日的持仓
        return assetHisService.getOpenMktVal(fundId, bizDate, serverId, false);
    }

    private String getKey(String stkCode, String market) {
        return stkCode.trim() + "-" + market.trim();
    }

}
