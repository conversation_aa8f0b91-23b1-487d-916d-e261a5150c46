<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.AccountTemporaryMapper">
    <select id="selectByCondition" resultType="com.eastmoney.common.entity.AccountTemporary">
        SELECT fundid, profit, profitrate, shiftout, shiftin, asset, bizdate,eutime,nvl(otcAsset,0)
        FROM ATCENTER.B_ACCOUNT_TEMPORARY t
        where
        FUNDID = #{fundId}
    </select>

</mapper>