package com.eastmoney.service.service.asset;

import com.eastmoney.accessor.dao.oracle.AssetHisDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.service.asset.base.AssetService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description
 * @auther pengjiejie
 * @create 2020-09-07 21:04
 */
public abstract class AbstractAssetSectionService implements AssetSectionService {
    @Resource(name = "assetServiceSettle")
    protected AssetService assetServiceSettle;
    @Resource(name = "assetServiceRealTime")
    protected AssetService assetServiceRealTime;
    @Autowired
    protected AssetHisDao assetHisDao;
    @Autowired
    protected TradeDateDao tradeDateDao;
    @Autowired
    protected ProfitSectionCacheService profitSectionCacheService;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    protected CoreConfigService coreConfigService;
    @Autowired
    private NodeConfigService nodeConfigService;

    @Override
    public AssetSection getAssetSection(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }
        params.put("accountBizDate", assetNew.getBizDate());
        AssetHis assetStart = getAssetStart(params, assetNew);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        if (startDate == null || startDate < assetNew.getStartDate()) {
            startDate = assetNew.getBizDate();
        }
        if (assetStart == null) {
            return null;
        }
        AssetHis assetRealTime = assetServiceRealTime.getAsset(params);
        AssetSection assetSection = generateAssetSection(assetStart, assetRealTime, assetNew);
        assetSection.setUnit(CommonUtil.convert(params.get("unit"), String.class));

        assetSection.setStartDate(startDate);
        Integer assetBizDate = assetRealTime.getBizDate() != null ? assetRealTime.getBizDate() :
                assetNew.getBizDate();
        assetSection.setBizDate(assetBizDate);
        return assetSection;
    }

    /**
     * 获取期初资产
     *
     * @param params
     * @return
     */
    protected abstract AssetHis getAssetStart(Map<String, Object> params, AssetHis assetNew);

    protected AssetSection generateAssetSection(AssetHis assetStart, AssetHis assetRealTime, AssetHis assetSettle) {
        AssetSection assetSection = new AssetSection();
        assetSection.setAsset(assetRealTime.getAsset());
        assetSection.setOtcAsset(assetRealTime.getOtcAsset());
        Double openAsset = assetStart.getAsset();
        if (openAsset == null) {
            openAsset = 0D;
        }
        assetSection.setOpenAsset(openAsset);
        Double openOtcAsset = assetStart.getOtcAsset();
        if (openOtcAsset == null) {
            openOtcAsset = 0D;
        }
        assetSection.setOpenOtcAsset(openOtcAsset);
        assetSection.setShiftInTotal(ArithUtil.add(assetRealTime.getShiftInTotal(),
                ArithUtil.sub(assetSettle.getShiftInTotal(), assetStart.getShiftInTotal()))
        );
        assetSection.setShiftOutTotal(ArithUtil.add(assetRealTime.getShiftOutTotal(),
                ArithUtil.sub(assetSettle.getShiftOutTotal(), assetStart.getShiftOutTotal()))
        );
        assetSection.setBankNetTransfer(ArithUtil.sub(
                assetSection.getShiftInTotal(), assetSection.getShiftOutTotal()
        ));
        assetSection.setNetTransfer(ArithUtil.sub(assetSettle.getNetTransfer(), assetStart.getNetTransfer()));
        assetSection.setOtherNetTransfer(ArithUtil.sub(assetSettle.getOtherNetTransfer(), assetStart.getOtherNetTransfer()));
        assetSection.setOtcNetTransfer(ArithUtil.sub(assetSettle.getOtcNetTransfer(), assetStart.getOtcNetTransfer()));

        // 增加serverId相关的开关
        Integer serverId = assetSettle.getServerId();
        if (nodeConfigService.getOtcNetTransferCalFlag(serverId)) {
            // 开关生效，则需要将 otherNetTransfer 字段去除 otcNetTransfer，也就是 netTransfer = otherNetTransfer
            // ((SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) OTHER_NET_TRANSFER,
            // ((OTC_SHIFT_IN_TOTAL + SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (OTC_SHIFT_OUT_TOTAL + SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) netTransfer
            assetSection.setNetTransfer(assetSection.getOtherNetTransfer());
        }

        return assetSection;
    }


}
