package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created by robin on 2016/7/18.
 * 收益日表
 * <AUTHOR>
 */
public class ProfitDay extends BaseEntityCal {

    private Long fundId;//资金帐号
    private Double profit;//今日收益
    private Double profitTotal;//总收益
    private Double profitRate;//今日收益率

    public Long getFundId() {
        return fundId;
    }

    public Double getProfit() {
        return profit;
    }

    public Double getProfitTotal() {
        return profitTotal;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public void setProfitTotal(Double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }
}
