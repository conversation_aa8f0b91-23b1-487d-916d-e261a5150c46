package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiSecProfitDayMapper;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 个股日收益明细Dao
 * <AUTHOR>
 * @create 2024/3/7
 */
@Service("secProfitDayDao")
public class SecProfitDayDaoImpl extends BaseDao<TiSecProfitDayMapper, SecProfitDayDO, Integer> implements SecProfitDayDao {

    @Override
    public List<SecProfitDayDO> getSecProfitDay(Map<String, Object> params) {
        return getMapper().getSecProfitDay(params);
    }

    @Override
    public List<SecProfitDayDO> getSecProfitDayByRange(Map<String, Object> params) {
        return getMapper().getSecProfitDayByRange(params);
    }

    @Override
    public Double getCashProfit(Map<String, Object> params) {
        return getMapper().getCashProfit(params);
    }

    @Override
    public Integer queryCount(Map<String, Object> params) {
        return getMapper().selectCountByCondition(params);
    }
}
