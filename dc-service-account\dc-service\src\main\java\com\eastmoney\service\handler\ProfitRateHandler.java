package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.ProfitRateDayDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.model.ProfitInfo;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.sysEnum.DimIndexEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.ProfitRateContrastCacheService;
import com.eastmoney.service.cache.ProfitRateSectionRankCacheService;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.service.profit.list.ProfitDayListServiceFacade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created on 2016/7/20.
 * 收益走势Handler
 */
@Service
public class ProfitRateHandler {
    private static final Integer OPEN_MARKET_TIME = 92500;
    private static final Logger LOG = LoggerFactory.getLogger(ProfitRateHandler.class);
    @Autowired
    protected ProfitRateDayDao profitRateDayDao;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;

    @Autowired
    private ProfitHandler profitHandler;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private ProfitRateContrastCacheService profitRateContrastCacheService;
    @Resource(name = "profitDayListServiceFacade")
    private ProfitDayListServiceFacade profitDayListServiceFacade;
    @Autowired
    private ProfitRateSectionRankCacheService profitRateSectionRankCacheService;

    /**
     * 日收益率列表
     * 该方法调用目前两种情况:
     * 第一种账户表现:1.截止日期到今天(手动计算当日收益率),传参数是unit
     * 另一种情况是账单:不需要计算当日收益率,不传参数unit,传参startDate、endDate
     */
    public List<ProfitRateDay> getProfitRateDayList(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        int today = DateUtil.getCuryyyyMMddInteger();
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        if (unit != null) {
            //确定账户分析数据计算到了哪天
            ProfitRateSection profitRateSection = getProfitRateSection(params);
            if (profitRateSection == null) {
                return null;
            }
            int startDate = profitRateSection.getIndexDate();
            int endDate = tradeDateDao.getNextMarketDay(profitRateSection.getBakBizDate());
            if (unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
                DateRange dateRange = CommonUtil.getDateRange(unit);
                //如果大于说明已经跨月或跨周，使用新的起止时间
                if (dateRange.getStartDate() > startDate) {
                    startDate = dateRange.getStartDate();
                    endDate = dateRange.getEndDate();
                }
            }
            //查询的开始时间不得小于首次拥有资产时间
            if (unit.equals(DateUnitEnum.ALL.getValue()) || startDate < assetNew.getStartDate()) {
                startDate = assetNew.getStartDate();

                //至今区间startDate小于设置的日期,使用月收益率  APPAGILE-80289
                if (unit.equals(DateUnitEnum.ALL.getValue()) && startDate < CommConstants.calProfitRateStartDate()) {
                    params.put("calMonthProfitRate", true);
                    params.put("settleDate", assetNew.getBizDate());
                }
            }
            if (today > endDate) {
                endDate = today;
            }
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            params.put("calRealTimeProfitRate", true);
        } else {
            Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);

            //查询的开始时间不得小于首次拥有资产时间
            startDate = startDate >= assetNew.getStartDate() ? startDate : assetNew.getStartDate();
            params.put("startDate", startDate);
        }
        return profitDayListServiceFacade.getDayProfitRateList(params);
    }


    /**
     * 1、日收益率列表
     * 查询日收益率数据
     * <p>
     * 2、月收益率列表
     * 2.1 首月：查询日收益率  firstMonthStartDay  firstMonthEndDay
     * 2.2 后续月：查询月收益率
     * 2.3 二者合并成月收益率列表
     */

    public List<ProfitRateDay> getInvestProfitRateList(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }


        int today = DateUtil.getCuryyyyMMddInteger();
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);

        if (startDate == null || endDate == null) {
            return null;
        }
        if (startDate > endDate) {
            return null;
        }
        //查询的开始时间不得小于首次拥有资产时间
        startDate = startDate >= assetNew.getStartDate() ? startDate : assetNew.getStartDate();
        params.put("startDate", startDate);
        //查询的截止时间小于等于当前时间
        endDate = endDate <= today ? endDate : today;
        if (endDate > assetNew.getBizDate()) {
            params.put("calRealTimeProfitRate", true);
        }

        //月收益率
        if (DimIndexEnum.MONTH.getFlag().equals(params.get("dimFlag"))) {
            params.put("settleDate", assetNew.getBizDate());
            List<ProfitRateDay> resultList = profitDayListServiceFacade.getMonthProfitRate(params, startDate);
            if (!CollectionUtils.isEmpty(resultList)) {
                //月收益率 顺序排序
                resultList = resultList.stream().sorted(Comparator.comparing(ProfitRateDay::getBizDate)).collect(Collectors.toList());
                //针对 startDate 非当月第一天,使用日收益率累积替换
                Integer curMonthFirstDay = DateUtil.getMonthFirstDay(startDate);
                if (!curMonthFirstDay.equals(startDate)) {
                    //查询首月最后一天
                    Integer firstMonth = startDate / 100;
                    Integer firstMonthEndDay = firstMonth * 100 + 31;
                    //首月收益率
                    Double profitRate = profitRateDayDao.getSectionProfitRate(fundId, startDate, firstMonthEndDay);
                    ProfitRateDay profitRateDay = new ProfitRateDay();
                    profitRateDay.setProfitRate(profitRate);
                    profitRateDay.setFundId(fundId);
                    profitRateDay.setBizDate(firstMonth);

                    for (int i = 0; i < resultList.size(); i++) {
                        if (Objects.equals(resultList.get(i).getBizDate(), firstMonth)) {
                            resultList.set(i, profitRateDay);
                            break;
                        }
                    }
                }
            }
            return resultList;
        } else {
            return profitDayListServiceFacade.getDayProfitRateList(params);
        }
    }

    /**
     * 日收益率列表
     * 账户表现页面：自定义区间查询,传参startDate、endDate
     *
     * @param params
     * @return
     */
    public List<ProfitRateDay> getProfitRateDayListCustomize(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        int today = DateUtil.getCuryyyyMMddInteger();
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);

        if (startDate == null || endDate == null) {
            return null;
        }
        if (startDate > endDate) {
            return null;
        }
        //查询的开始时间不得小于首次拥有资产时间
        startDate = startDate >= assetNew.getStartDate() ? startDate : assetNew.getStartDate();
        params.put("startDate", startDate);
        //查询的截止时间小于等于当前时间
        endDate = endDate <= today ? endDate : today;
        if (endDate > assetNew.getBizDate()) {
            params.put("calRealTimeProfitRate", true);
        }

        return profitDayListServiceFacade.getDayProfitRateList(params);
    }


    /**
     * 判断是否跨周，跨月
     *
     * @param params
     * @return true=跨月或跨周, false=未跨月,未跨周
     */
    private boolean startDateRange(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        if (unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
            ProfitSection profitSection = profitSectionCacheService.getProfitSection(params);
            if (profitSection == null) {
                //throw new CommonException(-1,"不存在收益片表数据");
                LOG.info(params.get("fundId") + ":不存在收益片表数据");
                return false;
            }
            Integer startDate = CommonUtil.getDateRange(unit).getStartDate();
            Integer time = CommonUtil.convert(DateUtil.getCurHHmmss(), Integer.class);
            Integer date = CommonUtil.convert(DateUtil.getCuryyyyMMdd(), Integer.class);
            //跨区间使用默认初始值
            //加入 date.equals(startDate) 条件，如果已经跨区间， 且当天是交易日的盘前，只有当天为该周期第一天才需要返回战胜率数据为0
            if (startDate > profitSection.getIndexDate()) {
                if (date.equals(startDate)) {
                    //区间第一天
                    //非交易日或盘前，返回true
                    if (!tradeDateDao.isMarket(date) || time <= OPEN_MARKET_TIME) {
                        params.put("updateTime", profitSection.getEuTime());
                        return true;
                    }
                } else {
                    //已经进入区间到第二天
                    //上一交易日小于startDate 说明区间第一日为非交易日
                    //当天是非交易日或交易日的盘前
                    if ((Integer.valueOf(tradeDateDao.getPreMarketDay(date)) < startDate && (!tradeDateDao.isMarket(date) || time <= OPEN_MARKET_TIME))) {
                        params.put("updateTime", profitSection.getEuTime());
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 收益率排名信息
     */
    public Map getProfitRateRankInfo(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>(8);
        //跨周或跨月并且是开盘前
        boolean flag = startDateRange(params);
        if (flag) {
            resultMap.put("profitRate", 0.0);
            resultMap.put("highAvg", 0.0);
            resultMap.put("allAvg", 0.0);
            resultMap.put("defeatRate", 0.0);
            resultMap.put("gainRate", 0.0);
            resultMap.put("lossRate", 0.0);
            resultMap.put("updateTime", params.get("updateTime"));
            resultMap.put("middleRate", 0.0);
            return resultMap;
        }

        //1.所有投资者收益率排名信息
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        ProfitRateContrast profitRateContrast = profitRateContrastCacheService.getProfitRateContrast(unit);
        //2.个人收益率排名信息
        ProfitRateSection profitRateSection = null;
        ProfitInfo profitInfo = null;
        double profitRate = 0.0;
        //计算实时的收益率
        profitInfo = profitHandler.getProfitInfo(params);
        if (profitInfo != null) {
            profitRateSection = new ProfitRateSection();
            profitRate = ArithUtil.round(profitInfo.getProfitRateTotal(), 4);
            params.put("profitRate", profitRate);
            Optional<Double> profitRateSectionRank = profitRateSectionRankCacheService.getProfitRateSectionRank(unit, profitRate);
            profitRateSection.setRankPercent(profitRateSectionRank.orElse(0d));
        }

        if (profitRateContrast != null) {
            resultMap.put("highAvg", profitRateContrast.getHighAvg());
            resultMap.put("allAvg", profitRateContrast.getAllAvg());
            double gainRate = ArithUtil.div(profitRateContrast.getGainNum() * 1d, profitRateContrast.getAllNum() * 1d, 4);
            double lossRate = ArithUtil.div(profitRateContrast.getLossNum() * 1d, profitRateContrast.getAllNum() * 1d, 4);
            resultMap.put("gainRate", gainRate);
            resultMap.put("lossRate", lossRate);
            //对应区间的收益率中位数
            resultMap.put("middleRate", profitRateContrast.getMiddleRate());
        }
        if (profitRateSection != null) {
            Double rankPercent = profitRateSection.getRankPercent();
            double defeatRate;
            if (rankPercent != null && rankPercent != -1) {
                defeatRate = ArithUtil.sub(1.0, profitRateSection.getRankPercent());
            } else {
                //若无排名信息，则默认战胜人数占比=亏损人数占比
                defeatRate = (Double) resultMap.get("lossRate");
            }
            Date updateTime = profitRateSection.getEuTime();
            if (profitInfo != null) {
                resultMap.put("profitRate", profitRate);
                updateTime = profitInfo.getUpdateTime();
            }
            params.put("rankPercent1", rankPercent);
            resultMap.put("defeatRate", defeatRate);
            resultMap.put("updateTime", updateTime);
        }
        return resultMap;
    }

    /**
     * 个人收益率排名分析
     *
     * @param params params
     * @return ProfitRateSection
     */
    private ProfitRateSection getProfitRateSection(Map<String, Object> params) {
        return profitSectionCacheService.getProfitRateSection(params);
    }

}
