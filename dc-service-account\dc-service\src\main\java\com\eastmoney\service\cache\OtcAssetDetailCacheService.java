package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.kgdb.OtcAssetDetailDao;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class OtcAssetDetailCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(OtcAssetDetailCacheService.class);
    @Resource(name = "kgdbOtcAssetDetailDao")
    private OtcAssetDetailDao kgdbOtcAssetDetailDao;

    @Resource(name = "otcAssetDetailCache")
    private LoadingCache<Long, Optional<Map<String, Double>>> otcAssetDetailCache;

    @Bean(name = "otcAssetDetailCache")
    public LoadingCache<Long, Optional<Map<String, Double>>> otcAssetDetailCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1000)
                .maximumSize(100000)
                .refreshAfterWrite(120, TimeUnit.SECONDS)
                .build(new CacheLoader<Long, Optional<Map<String, Double>>>() {
                    @Override
                    public Optional<Map<String, Double>> load(Long fundId) {
                        try {
                            Map<String, Object> params = new HashMap<>();
                            params.put("fundId", fundId);
                            Map<String, Double> kgdbOtcAssetDetail = kgdbOtcAssetDetailDao.getRealTimeOtcAsset(params);
                            return Optional.of(kgdbOtcAssetDetail);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.of(new HashMap<>());
                    }
                });
    }


    public Map<String, Double> getRealTimeOtcAsset(Long fundId) {
        try {
            return otcAssetDetailCache.get(fundId).orElse(new HashMap<>());
        } catch (ExecutionException e) {
            LOG.error("获取用户{}OTC资产失败", fundId, e);
        }
        return new HashMap<>();
    }
}
