package com.eastmoney.service.service.quote;


import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.accessor.enums.StkTypeEnum;
import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.common.entity.StkPrice;
import com.eastmoney.common.entity.Stock;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.quote.mdstp.container.QuoteContainer;
import com.eastmoney.quote.mdstp.model.Rec;
import com.eastmoney.service.cache.StkPriceCacheService;
import com.eastmoney.service.cache.TradeTimeCacheService;
import com.eastmoney.service.handler.FundAssetHandler;
import com.eastmoney.service.service.StockService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.eastmoney.common.util.ArithUtil.add;
import static com.eastmoney.common.util.ArithUtil.div;

/**
 * Created on 2020/8/22-10:04.
 *
 * <AUTHOR>
 */
@Service
public class QuoteServiceImpl implements QuoteService {
    public static final List<String> BOND_TYPE = Arrays.asList(
            StkTypeEnum.NATIONAL_DEBT.getValue(), StkTypeEnum.ENTERPRISE_BOND.getValue(),
            StkTypeEnum.COMPANY_BOND.getValue(), StkTypeEnum.EXCHANGEABLE_BOND.getValue(),
            StkTypeEnum.PRIVATE_PLACEMENT_BOND.getValue(), StkTypeEnum.ASSET_BACKED_SECURITY.getValue(),
            StkTypeEnum.CONVERTIBLE_PRIVATE_BOND.getValue(), StkTypeEnum.SUBORDINATED_BOND.getValue()
    );
    private static final double MIN_PRICE = 0.0001d;
    private static final int MAX_LOF_PRICE = 30;
    private static final String LOFTYPE_MONEY_OLD = "1";
    private static final String LOFTYPE_MONEY_NEW = "4";
    /** 沪市519开头的股票代码都是LOF，除了新式货币LOF和老式货币LOF之外，都需要将获取到的MDSTP行情除以100 */
    private static final String LOFCODE_SH_PREFIX = "519";
    private static final String STKT_SMZQ = "e";
    private static org.slf4j.Logger LOG = LoggerFactory.getLogger(FundAssetHandler.class);
    @Autowired
    private StockService stockService;

    @Autowired
    private TradeTimeCacheService tradeTimeCacheService;

    @Autowired
    private StkPriceCacheService stkPriceCacheService;

    @Autowired
    private BseCodeAlterService bseCodeAlterService;


    @Override
    public <T extends StkInfo> void setStkAssetQuote(List<T> stkInfoList, Boolean positionProfit) {
        //剔除持仓列表中stkcode为空的证券,否则获取个股行情
        if (stkInfoList == null || stkInfoList.isEmpty()) {
            return;
        }
        Iterator<T> it = stkInfoList.iterator();
        while (it.hasNext()) {
            T stkInfo = it.next();
            if (StringUtils.isBlank(stkInfo.getStkCode())) {
                it.remove();
                continue;
            }
            if (stkInfo.getStkPrice() == null || positionProfit) {
                //PCMP-10028770 【账户分析】北交所存量代码段改造  行情转换stkCode后 需要将stkCode再转回来 后续会用到
                String stkCode = stkInfo.getStkCode();
                //先都转为920去查
                bseCodeAlterService.transStkInfoCode(stkInfo, true);
                StkPrice stkPrice = getStkPrice(stkInfo, positionProfit);
                if (!stkPrice.isQtPrice) {
                    //再转为830去查
                    bseCodeAlterService.transStkInfoCode(stkInfo, false);
                    stkPrice = getStkPrice(stkInfo, positionProfit);
                }
                stkInfo.setStkCode(stkCode);
                stkInfo.setStkPrice(stkPrice);
            }
        }

        //深市可转债，发债转债共存价格处理
        for (StkInfo stkInfo : stkInfoList) {
            //深市可转债价格处理，如果是发债和转债并存，只计算发债(StkStatus=F 发债，StkStatus=Y 转债)
            if ("0".equals(stkInfo.getMarket()) && "8".equals(stkInfo.getStkType())) {
                Stock stock = stockService.getStock(stkInfo.getStkCode().trim(), stkInfo.getMarket());
                if (stock != null && "F".equals(stock.getStkStatus())) {
                    if (StringUtils.isBlank(stock.getLinkStk())) {
                        continue;
                    }
                    for (StkInfo temp : stkInfoList) {
                        if (temp.getStkCode().trim().equals(stock.getLinkStk().trim()) && "8".equals(temp.getStkType())
                                && temp.getMarket().equals(stkInfo.getMarket())) {
                            temp.getStkPrice().setPrice(0d);
                        }
                    }
                }
            }
        }
    }

    @Override
    public StkPrice getStkPrice(StkInfo stkInfo) {
        return getStkPrice(stkInfo, false);
    }

    @Override
    public StkPrice getStkPrice(StkInfo stkInfo, Boolean positionProfit) {
        StkPrice stkPrice = getQtPrice(stkInfo.stkCode, stkInfo.getMarket());
        double price = 0.0;
        if (stkPrice == null) {
            stkPrice = new StkPrice();
            if (stkInfo.lastPrice != null && stkInfo.lastPrice > MIN_PRICE) {
                price = stkInfo.lastPrice;
            } else if (stkInfo.openPrice != null && stkInfo.openPrice > MIN_PRICE) {
                price = stkInfo.openPrice;
            } else if (stkInfo.closePrice != null && stkInfo.closePrice > MIN_PRICE) {
                price = stkInfo.closePrice;
            }
            if (ArithUtil.eq(price, 0.0) && STKT_SMZQ.equals(stkInfo.getStkType()) && stkInfo.getTicketPrice() != null) {
                price = stkInfo.getTicketPrice();
            }
            stkPrice.setLastPrice(stkInfo.lastPrice);
            stkPrice.setClosePrice(stkInfo.closePrice);
        } else {
            price = stkPrice.getPrice();
        }
        //集合竞价期间，mdstp最新价使用虚拟匹配价，柜台计算资产收益使用昨收价，账户分析逻辑与柜台保持统一。
        //APPAGILE-120248
        if (tradeTimeCacheService.isBeforeMarketOpen(stkInfo.getMarket())) {
            price = stkPrice.getClosePrice();
        }

        // LOF大于30则除100
//        if (STKTEnum.STKT_LOF.getValue().equals(stkInfo.stkType) && price > MAX_LOF_PRICE) {
//            if(Objects.nonNull(stkInfo.getStkCode()) && !Objects.equals("160641", stkInfo.getStkCode().trim())) {
//                price = div(price, 100);
//                stkPrice.setClosePrice(div(stkPrice.getClosePrice(), 100));
//            }
//        }

        // 沪市519开头的股票代码都是LOF，除了新式货币LOF和老式货币LOF之外，都需要将获取到的MDSTP行情除以100
        if (MarketEnum.SH_A.getValue().equals(stkInfo.market) && LOFCODE_SH_PREFIX.equals(StringUtils.substring(stkInfo.getStkCode(), 0, 3))) {
            // 最新价
            price = div(price, 100);
            // 昨收价
            stkPrice.setClosePrice(div(stkPrice.getClosePrice(), 100));
        }

        //新式和旧式的货币基金的委托价格不一样, lids, 20120705, XQ-20120627-JZKJ-005
        if (STKTEnum.STKT_LOF.getValue().equals(stkInfo.stkType)) {
            if (LOFTYPE_MONEY_OLD.equals(stkInfo.lofMoneyFlag)) {
                price = 1.00;
            } else if (LOFTYPE_MONEY_NEW.equals(stkInfo.lofMoneyFlag)) {
                price = 0.01;
            }
        }

        if (STKTEnum.STKT_LOF.getValue().equals(stkInfo.stkType) && "F".equals(stkInfo.stkLevel) && MarketEnum.SZ_A.getValue().equals(stkInfo.market)) {
            price = stkInfo.closePrice;
        }

        //国债（1）、地方政府债券（1）、企业债券（2）、公司债券（C）、可交换债券（t） 不加应计利息  --APPAGILE-89983
        //新增：e-私募债券、j-可转换私募债、k-资产支持证券、l-次级债券  --APPAGILE-98395
        //计算持仓收益需加利息,因为参考成本profitCost包含利息 --APPAGILE-89983
        // 10032936 当日盈亏增加费用计算 防止choice码表有 但是核心一柜台码表没有导致stkType为空也加上了利息
        if (positionProfit || (!BOND_TYPE.contains(stkInfo.stkType) && StringUtils.isNotEmpty(stkInfo.stkType))) {
            price = add(price, stkInfo.bondIntr);
        }
        stkPrice.setPrice(price);
        stkPrice.setBondIntr(stkInfo.bondIntr);
        stkInfo.setStkPrice(stkPrice);
        return stkPrice;
    }

    /**
     * 获取行情价
     *
     * @param stkCode "000300.SH", "000001.SH", "399001.SZ", "399006.SZ", "399005.SZ","HKI|HSI"
     * @param market
     * @return
     */
    @Override
    public StkPrice getQtPrice(String stkCode, String market) {
        StkPrice stkPrice = null;
        double price = 0.00;
        //为适配 HS、HKI的行情，若stkCode开始为HS、HKI开头则特殊处理处类型
        boolean isHkIndex = false;
        if (ObjectUtils.isNotEmpty(stkCode) && (stkCode.startsWith("HKI") || stkCode.startsWith("HS"))) {
            isHkIndex = true;
            String[] split = stkCode.split("\\|");
            stkCode = split[split.length - 1];
        }
        Rec rec = QuoteContainer.getRec(stkCode, market);
        if (rec != null) {
            stkPrice = new StkPrice();
            double qtPrice;
            double qtClose;
            if (isHkIndex) {
                qtPrice = rec.getRealHkAndHSIndexSharePrice();
                qtClose = rec.getRealHkAndHSIndexShareClose();
            } else {
                qtPrice = rec.getRealAIndexSharePrice();
                qtClose = rec.getRealAIndexShareClose();
            }
            //上一个交易日的收盘价
            stkPrice.setClosePrice(qtClose);
            stkPrice.setLastPrice(qtPrice);
            stkPrice.setDwTime(rec.getDwTime());

            if (qtPrice > MIN_PRICE) {
                price = qtPrice;
            } else if (qtClose > MIN_PRICE) {
                price = qtClose;
            }
            if (ArithUtil.eq(price, 0.0)) {
                return null;
            }
            stkPrice.setPrice(price);
            stkPrice.isQtPrice = true;
        } else {
            LOG.warn("行情数据为空 stkCode=" + stkCode + " market=" + market);
        }
        return stkPrice;
    }

    @Override
    public List<StkPrice> getAllQuoteList(Map params) {
        String serverId = CommonUtil.convert(params.get("serverId"), String.class);
        if (StringUtils.isEmpty(serverId)) {
            serverId = "1";
        }
        Map<String, Stock> allStockMap = stockService.getAllStock();
        Set<String> allRecKey = QuoteContainer.getAllRecKey(allStockMap.keySet());
        Map<String, StkPrice> stkPriceMap = stkPriceCacheService.getStkPrice(serverId);
        return allRecKey.stream()
                .map(key -> {
                    String[] keyList = key.split("-");
                    String stkCode = keyList[0];
                    String market = keyList[1];
                    Stock stock = allStockMap.get(key);
                    StkPrice jzjyStkPrice = stkPriceMap.getOrDefault(StringUtils.join(market, "-", stkCode), new StkPrice());
                    StkPrice stkPrice = getStkPrice(new StkInfo(market, stkCode, stock.getStkType(), jzjyStkPrice.getBondIntr(), 1.0));
                    stkPrice.setStkCode(stkCode);
                    stkPrice.setMarket(market);
                    return stkPrice;
                })
                .collect(Collectors.toList());
    }
}
