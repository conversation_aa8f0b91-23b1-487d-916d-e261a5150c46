package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.AssetHis;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/7/18.
 */
@Repository
public interface TiAssetHisMapper extends BaseMapper<AssetHis, Long>{
    /**
     * 查询指定区间每一天的资产
     * @param params
     * @return
     */
    List<AssetHis> getAssetDayTrend(Map<String, Object> params);

    /**
     * 查询每个月最后一天的资产
     * @param params
     * @return
     */
    List<AssetHis> getMonthAssetDayTrend(Map<String, Object> params);
}
