package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.PositionProfitDao;
import com.eastmoney.accessor.dao.oracle.PositionSectionDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.accessor.enums.StkTypeEnum;
import com.eastmoney.accessor.service.MatchService;
import com.eastmoney.accessor.service.OggLogAssetService;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.sysEnum.PositionCalFlagEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.*;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.LogAssetService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.asset.base.AssetServiceRealTimeImpl;
import com.eastmoney.service.service.profit.base.ProfitServiceRealTimeImpl;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import com.eastmoney.service.util.BusinessUtil;
import com.eastmoney.transport.codec.MessageEncoder;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.eastmoney.common.util.ArithUtil.*;


/**
 * Created by sunyuncai on 2016/8/11.
 */
@Service
public class PositionHandler {
    private static final Logger LOG = LoggerFactory.getLogger(MessageEncoder.class);
    private static final List<String> POSITION_LOGASSET_CALINDEX = Arrays.asList("positionShareChange", "positionAmountChange");
    private static final Integer MAX_PAGE_SIZE = 1000;
    private static final String LOFTYPE_MONEY_OLD = "1";
    private static final String LOFTYPE_MONEY_NEW = "4";
    private static final List<Long> DIGESTID_LIST = Arrays.asList(220000L, 220188L, 220094L, 220098L, 221001L, 220189L, 220095L, 220099L);
    // 红利税流水
    private static final List<Long> BONUS_DIGESTID_LIST = Arrays.asList(140203L, 140204L, 140205L);
    //盘中交易记录
    private static final List<String> BSFLAG_LIST = Arrays.asList("0B", "0S", "3m", "3n", "B", "S", "0a", "a", "0f", "f",
            "0b", "b", "0g", "g", "0c", "c", "0h", "h", "0d", "d", "0i", "i", "0e", "e", "0j", "j", "0q", "q", "0r", "r",
            "1I", "1J", "2I", "2J", "1j", "1k", "4m", "4n", "4o", "4p");


    @Autowired
    private PositionProfitDao positionProfitDao;
    @Autowired
    private PositionProfitCacheService positionProfitCacheService;
    @Autowired
    private StockService stockService;
    @Autowired
    private PositionSectionDao positionSectionDao;
    @Autowired
    private PositionSectionCacheService positionSectionCacheService;
    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private LogAssetHandler logAssetHandler;
    @Autowired
    private LogAssetService logAssetService;
    @Autowired
    private OggLogAssetService oggLogAssetService;
    @Autowired
    private StkAssetService stkAssetService;
    @Autowired
    private MatchService matchService;
    @Autowired
    private DigestNameCache digestNameCache;
    @Resource(name = "assetServiceRealTime")
    private AssetServiceRealTimeImpl assetServiceRealTime;
    @Resource(name = "profitServiceRealTime")
    private ProfitServiceRealTimeImpl profitServiceRealTime;
    @Resource(name = "commonService")
    private CommonService commonService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;


    /**
     * 持仓盈亏分析-交易证券数，盈利证券数等
     * <p>
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params
     * @return
     */
    public Object getPositionSection(Map<String, Object> params) {
        List<PositionSection> positionSections = new ArrayList<>();
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        String unit = CommonUtil.convert(params, "unit", String.class);
        Integer startDate = CommonUtil.convert(params, "startDate", Integer.class);
        Integer endDate = CommonUtil.convert(params, "endDate", Integer.class);

        if (unit != null) {
            positionSections = positionSectionCacheService.getPositionSection(params);
        } else if (startDate != null && endDate != null) {
            if (startDate > endDate) {
                return positionSections;
            }

            positionSections = positionSectionDao.getPositionProfitStatistics(fundId, startDate, endDate);
        }

        if (positionSections != null && positionSections.size() == 1) {
            PositionSection positionSection = positionSections.get(0);
            // datacenter-3776  清仓次数维度
            calPositionSectionAvg(positionSection);

            if (unit != null) {
                int normalStartDate = CommonUtil.getDateRange(unit).getStartDate();
                if (unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
                    Map<String, Object> assetParam = new HashMap<>();
                    assetParam.put("fundId", fundId);
                    AssetNew assetNew = assetNewService.getAssetInfo(assetParam);

                    if (assetNew != null && assetNew.getBizDate() != null && normalStartDate > assetNew.getBizDate()) {
                        return new ArrayList<>();
                    }
                }

            }

        }

        return positionSections;
    }


    /**
     * 计算区间清仓平均盈利和平均亏损、平均持股天数、胜率
     *
     * @param positionSection
     */
    private void calPositionSectionAvg(PositionSection positionSection) {
        //初始化数据
        positionSection.setAvgLoss(0.0D);
        positionSection.setAvgGain(0.0D);
        positionSection.setAvgHolddays(0.0D);
        positionSection.setGainRate(0.0D);
        if (!Objects.equals(positionSection.getTradesNum(), 0L)) {
            positionSection.setAvgHolddays(div(positionSection.getTradesHolddays(), positionSection.getTradesNum()));
            positionSection.setGainRate(div(positionSection.getGainNum(), positionSection.getTradesNum()));
        }
        //平均盈利
        if (!Objects.equals(positionSection.getGainNum(), 0L)) {
            positionSection.setAvgGain(div(positionSection.getGainProfit(), positionSection.getGainNum()));
        }
        //平局亏损 平均亏损 = 亏损金额 / （清仓次数-盈利次数）
        Double lossProfitNum = ArithUtil.sub(positionSection.getTradesNum(), positionSection.getGainNum());
        if (!Objects.equals(lossProfitNum, 0.0D)) {
            positionSection.setAvgLoss(div(positionSection.getLossProfit(), lossProfitNum));
        }
    }

    /**
     * 旧版单个股票盈亏
     */
    private List<PositionProfit> getSinglePositionProfit(Map<String, Object> params) {
        //区间查询
        List<PositionProfit> list = positionProfitDao.getSinglePositionMergeProfit(params);
        for (PositionProfit positionProfit : list) {
            positionProfit.setStkName(stockService.getStkName(positionProfit.getStkCode(), positionProfit.getMarket()));
        }
        return list;
    }

    /**
     * 盈亏股票汇总
     */
    public List<PositionProfit> getPositionMergeList(Map<String, Object> params) {
        //确定周期起始时间
        setQueryTime(params);
        String startDate = CommonUtil.convert(params.get("startDate"), String.class);
        String endDate = CommonUtil.convert(params.get("endDate"), String.class);
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            LOG.info(params.get("fundId") + ":无法确定查询时间");
            return null;
        }

        //确定排序方式
        specifiSort(params);
        List<PositionProfit> list;
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        String profitFlag = CommonUtil.convert(params.get("profitFlag"), String.class);
        if (StringUtils.isNotEmpty(unit) && pageNo == 1 && pageSize == nodeConfigService.getClearPositionTOPSize() && StringUtils.isNotEmpty(profitFlag)) {
            list = positionProfitCacheService.getPositionMergeListA(params);
        } else {
            list = positionProfitCacheService.getPositionMergeList(params);
        }
        for (PositionProfit positionProfit : list) {
            positionProfit.setStktype(stockService.getStkType(positionProfit.getStkCode(), positionProfit.getMarket()));
            positionProfit.setStkName(stockService.getStkName(positionProfit.getStkCode(), positionProfit.getMarket()));
            // APPAGILE-115178 基金默认展示扩位简称，股票和债券维持短简称不变
            if (StkTypeEnum.isShowFullNameFundType(positionProfit.getStktype())) {
                positionProfit.setExpandNameAbbr(stockService.getExpandNameAbbr(positionProfit.getStkCode(), positionProfit.getMarket()));
            }
            positionProfit.setStartDate(Integer.valueOf(startDate));
            positionProfit.setEndDate(Integer.valueOf(endDate));
            positionProfit.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(positionProfit.getStkCode(), positionProfit.getMarket()));
        }
        return list;
    }


    /**
     * 区间盈亏清仓时间排序列表 支持单支股票维度 stkCode、market
     */
    public List<PositionProfit> getClearPositionList(Map<String, Object> params) {
        //确定周期起始时间
        //修改参数对象
        Map<String, Object> queryParams = new HashMap<>(params);
        setQueryTime(queryParams);
        String startDate = CommonUtil.convert(queryParams.get("startDate"), String.class);
        String endDate = CommonUtil.convert(queryParams.get("endDate"), String.class);
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            LOG.info(queryParams.get("fundId") + ":无法确定查询时间");
            return null;
        }
        //确定排序方式
        specifiSort(queryParams);
        Integer pageSize = CommonUtil.convert(queryParams.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(queryParams.get("pageNo"), Integer.class);
        Integer startNum = pageSize * (pageNo - 1);
        queryParams.put("startNum", startNum);
        queryParams.put("pageSize", pageSize);
        String stkCode = CommonUtil.convert(queryParams.get("stkCode"), String.class);
        String market = CommonUtil.convert(queryParams.get("market"), String.class);
        List<PositionProfit> list = new ArrayList<>();
        //获取 代码转换是否打开
        Boolean openBaseCodeAlter = Optional.ofNullable(CommonUtil.convert(queryParams.get("openBaseCodeAlter"), Boolean.class)).orElse(false);
        if(StringUtils.isNotBlank(stkCode) && StringUtils.isNotBlank(market) && market.equals(MarketEnum.BJ_A.getValue()) && openBaseCodeAlter) {
            //这里将830  920均查出来  然后进行查询
            bseCodeAlterService.getCodeAlterParams(queryParams);
        }
        list.addAll(positionProfitDao.getClearPositionProfitListByPage(queryParams));
        for (PositionProfit positionProfit : list) {
            //处理证券名称  证券类型
            positionProfit.setStktype(stockService.getStkType(positionProfit.getStkCode(), positionProfit.getMarket()));
            positionProfit.setStkName(stockService.getStkName(positionProfit.getStkCode(), positionProfit.getMarket()));
            // APPAGILE-115178 基金默认展示扩位简称，股票和债券维持短简称不变
            if (StkTypeEnum.isShowFullNameFundType(positionProfit.getStktype())) {
                positionProfit.setExpandNameAbbr(stockService.getExpandNameAbbr(positionProfit.getStkCode(), positionProfit.getMarket()));
            }
            //代码转换 830 B -> 920 B  用于前端进行行情跳转
            //代码转换 830 6 -> 920 B  用于前端进行行情跳转
            positionProfit.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(positionProfit.getStkCode(), positionProfit.getMarket()));

        }
        //使用summaryFlag时,补充个股明细及总次数总盈亏
        if (queryParams.containsKey("summaryFlag") && queryParams.containsKey("stkCode")) {
            addClearPositionSummary(queryParams, list);
        }
        return list;
    }

    /**
     * 时间/某只详情及汇总信息
     *
     * @param params
     * @return
     */
    public void addClearPositionSummary(Map<String, Object> params, List<PositionProfit> list) {
        PositionProfit positionProfitSummary= positionProfitDao.getClearPositionSummary(params);
        positionProfitSummary = Optional.ofNullable(positionProfitSummary).orElse(new PositionProfit());
        for (PositionProfit positionProfit : list) {
            Integer clearTimes = Optional.ofNullable(positionProfitSummary.getClearTimes()).orElse(0);
            Double profitTotal = Optional.ofNullable(positionProfitSummary.getProfitTotal()).orElse(0.0);
            positionProfit.setClearTimes(clearTimes);
            positionProfit.setProfitTotal(profitTotal);
        }
    }

    //指定具体的查询排序方式
    private void specifiSort(Map<String, Object> params) {
        String sortFlag = CommonUtil.convert(params.get("sortFlag"), String.class);
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);
        String profitFlag = CommonUtil.convert(params.get("profitFlag"), String.class);

        String sort = "";
        // 老版  盈亏字段排序 profitTotal
        if ("1".equals(sortFlag)) {
            if ("1".equals(profitFlag)) {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                }
            } else if ("2".equals(profitFlag)) {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                }
            } else {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                }
            }

        }

        // 老版  持仓天数排序 holdDays
        if ("2".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by holdDays asc,profitTotal desc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by holdDays desc,profitTotal desc,stkCode desc";
            }
        }

        // 老版  买卖次数排序 profitTotal
        if ("3".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by tradeTotal asc,profitTotal desc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by tradeTotal DESC,profitTotal desc,stkCode desc";
            }
        }

        //新版 基于清仓日期
        if (Objects.equals("4", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by ENDDATE ASC,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by ENDDATE DESC,stkCode desc";
            }
        }

        //新版 收益率
        if (Objects.equals("5", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by PROFIT_RATE asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by PROFIT_RATE desc,stkCode desc";
            }
        }

        //新版 持股天数     todo del  stkCode
        if (Objects.equals("6", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by HOLDDAYS asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by HOLDDAYS desc,stkCode desc";
            }
        }

        //新版 收益
        if (Objects.equals("7", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by PROFIT asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by PROFIT desc,stkCode desc";
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            params.put("sort", sort);
        }
    }

    //合并单只股票盈亏明细，买入，卖出统计，转入，转出统计
    public Object getPositionMergeTradeList(Map<String, Object> params) {
        String queryFlag = CommonUtil.convert(params.get("queryFlag"), String.class);
        if ("0".equals(queryFlag)) {
            return clearPositionAnalysis(params);
        } else if ("1".equals(queryFlag)) {
            return realTimePositionAnalysis(params);
        }
        return null;
    }

    /**
     * 清仓个股操作分析
     *
     * @param params
     * @return
     */
    public Object getClearPositionMergeTradeList(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        String startDate = CommonUtil.convert(params.get("startDate"), String.class);
        String endDate = CommonUtil.convert(params.get("endDate"), String.class);
        //修改参数对象
        Map<String, Object> queryParams = new HashMap<>(params);
        MergeTradeResult mergeTradeResult = null;
        if (!Objects.isNull(unit)) {
            //区间清仓
            mergeTradeResult = clearUnitPositionAnalysis(queryParams);
        } else if (!Objects.isNull(startDate) && !Objects.isNull(endDate)) {
            //单次清仓
            mergeTradeResult = clearSinglePositionAnalysis(queryParams);
        }
        return mergeTradeResult;
    }


    /**
     * 获取单支股票区间清仓记录
     *
     * @param params
     * @return
     */
    public Object getSectionStockClearPositionList(Map<String, Object> params) {
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        Integer startNum = pageSize * (pageNo - 1);
        params.put("startNum", startNum);
        params.put("pageSize", pageSize);
        return positionProfitDao.getClearPositionProfitListByPage(params);
    }

    /**
     * 清仓个股 - 交易操作分析页 区间
     *
     * @param params
     * @return
     */
    private MergeTradeResult clearUnitPositionAnalysis(Map<String, Object> params) {
        MergeTradeResult mergeTradeResult = new MergeTradeResult();
        // 查询区间
        setQueryTime(params);
        bseCodeAlterService.getCodeAlterParams(params);
        List<PositionProfit> mergePositionProfit = positionProfitDao.getSinglePositionMergeProfit(params);

        if (CollectionUtils.isEmpty(mergePositionProfit)) {
            return mergeTradeResult;
        }
        PositionProfit positionProfit = mergePositionProfit.get(0);
        String stkCode = positionProfit.getStkCode();
        String market = positionProfit.getMarket();
        positionProfit.setStktype(stockService.getStkType(stkCode, market));
        positionProfit.setStkName(stockService.getStkName(stkCode, market));
        positionProfit.setExpandNameAbbr(stockService.getExpandNameAbbr(stkCode, market));
        Double costTotal = add(positionProfit.getTotalBuy(), positionProfit.getTotalIn());
        if (costTotal == 0.0D) {
            positionProfit.setProfitRate(0.0D);
        } else {
            positionProfit.setProfitRate(div(positionProfit.getProfit(), costTotal));
        }
        //查询交割单使用最早建仓时间，最晚清仓时间
        params.put("startDate", positionProfit.getStartDate());
        params.put("endDate", positionProfit.getEndDate());
        mergeTradeResult.setStkProfits(mergePositionProfit);
        params.put("calIndexes", POSITION_LOGASSET_CALINDEX);
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        Integer startNum = pageSize * (pageNo - 1);
        params.put("startNum", startNum);
        params.put("pageSize", pageSize);
        List<LogAsset> tradeList = logAssetService.getTradeShiftList(params);
        //分组交割单 创建建仓、清仓标示
        handleStartEndFlag(tradeList);
        mergeTradeResult.setTotalLogAsset(tradeList);
        mergeTradeResult.setStkTrades(null);
        mergeTradeResult.setStkShifts(null);
        return mergeTradeResult;
    }


    /**
     * 清仓个股 - 交易操作分析页 单次
     *
     * @param params
     * @return
     */
    private MergeTradeResult clearSinglePositionAnalysis(Map<String, Object> params) {
        MergeTradeResult mergeTradeResult = new MergeTradeResult();
        bseCodeAlterService.getCodeAlterParams(params);
        List<PositionProfit> positionProfits = positionProfitDao.getSinglePositionProfit(params);
        for (PositionProfit positionProfit : positionProfits) {
            String stkCode = CommonUtil.convert(params.get("stkCode"), String.class);
            String market = CommonUtil.convert(params.get("market"), String.class);
            positionProfit.setStkName(stockService.getStkName(stkCode, market));
            positionProfit.setStktype(stockService.getStkType(stkCode, market));
            positionProfit.setExpandNameAbbr(stockService.getExpandNameAbbr(stkCode, market));
        }
        mergeTradeResult.setStkProfits(positionProfits);
        params.put("calIndexes", POSITION_LOGASSET_CALINDEX);
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        Integer startNum = pageSize * (pageNo - 1);
        params.put("startNum", startNum);
        params.put("pageSize", pageSize);
        List<LogAsset> tradeList = logAssetService.getTradeShiftList(params);
        if (!CollectionUtils.isEmpty(tradeList)) {
            LogAsset startLogAsset = tradeList.get(tradeList.size() - 1);
            LogAsset endLogAsset = tradeList.get(0);
            markPositionStartFlag(startLogAsset);
            markPositionEndFlag(endLogAsset);
        }
        mergeTradeResult.setStkShifts(null);
        mergeTradeResult.setStkTrades(null);
        mergeTradeResult.setTotalLogAsset(tradeList);
        return mergeTradeResult;
    }


    /**
     * 判断交割单是否为建仓的第一条交割单
     *
     * @param logAsset 某一条交割单
     */
    private void markPositionStartFlag(LogAsset logAsset) {
        // 建仓检查
        if (logAsset != null) {
            Long stkEffect = logAsset.getStkEffect();
            Long stkBal = logAsset.getStkBal();
            if (stkEffect == null) {
                return;
            }
            if (stkEffect > 0 && Objects.equals(stkEffect, stkBal)) {
                logAsset.setPositionFlag(PositionCalFlagEnum.START.getValue());
            }
        }
    }

    /**
     * 判断交割单是否为清仓的最后一条交割单
     *
     * @param logAsset 某一条交割单
     */
    private void markPositionEndFlag(LogAsset logAsset) {
        // 清仓检查
        if (logAsset != null) {
            Long stkEffect = logAsset.getStkEffect();
            Long stkBal = logAsset.getStkBal();
            if (stkEffect == null) {
                return;
            }
            if (stkEffect < 0 && Objects.equals(stkBal, 0L)) {
                logAsset.setPositionFlag(PositionCalFlagEnum.END.getValue());
            }
        }
    }

    //持仓个股 - 交易操作分析页
    private MergeTradeResult realTimePositionAnalysis(Map<String, Object> params) {
        MergeTradeResult mergeTradeResult = new MergeTradeResult();
        List<LogAsset> tradeList = logAssetHandler.getStkTradeList(params);
        List<LogAsset> shiftList = logAssetHandler.getStkShiftList(params);

        if (tradeList != null && tradeList.size() > 0) {
            mergeTradeResult.setStkTrades(tradeList);
        }
        if (shiftList != null && shiftList.size() > 0) {
            mergeTradeResult.setStkShifts(shiftList);
        }
        return mergeTradeResult;
    }


    //已卖出股票 - 交易操作分析页
    private MergeTradeResult clearPositionAnalysis(Map<String, Object> params) {
        MergeTradeResult mergeTradeResult = new MergeTradeResult();
        List<PositionProfit> positionProfits = getSinglePositionProfit(params);
        List<LogAsset> tradeList = new ArrayList<>();
        List<LogAsset> shiftList = new ArrayList<>();
        if (positionProfits != null && positionProfits.size() > 0) {
            mergeTradeResult.setStkProfits(positionProfits);
            Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
            PositionProfit positionProfit = positionProfits.get(0);
            //接口兼容
            positionProfit.setProfitTotal(positionProfit.getProfit());
            Map<String, Object> params2 = new HashMap<>(8);
            params2.put("fundId", fundId);
            params2.put("stkCode", positionProfit.getStkCode());
            params2.put("market", positionProfit.getMarket());
            params2.put("startDate", positionProfit.getStartDate());
            params2.put("endDate", positionProfit.getEndDate());

            params.put("clearStartDate", positionProfit.getStartDate());
            params.put("clearEndDate", positionProfit.getEndDate());
            tradeList = logAssetHandler.getStkTradeList(params2);
            shiftList = logAssetHandler.getStkShiftList(params2);
        }

        if (tradeList != null && tradeList.size() > 0) {
            mergeTradeResult.setStkTrades(tradeList);
        }
        if (shiftList != null && shiftList.size() > 0) {
            mergeTradeResult.setStkShifts(shiftList);
        }
        return mergeTradeResult;
    }

    //确定查询的开始，结束时间
    //入参支持：unit 或 startDate/endDate
    public void setQueryTime(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            LOG.info(params.get("fundId") + ":缺少asset_new表数据");
            return;
        }
        //区间查询
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        //自定义查询区间
        Integer startDate = CommonUtil.convert(params, "startDate", Integer.class);
        Integer endDate = CommonUtil.convert(params, "endDate", Integer.class);

        if (unit != null) {
            //取收益片表中的查询开始时间
            ProfitSection profitSection = profitSectionCacheService.getProfitSection(params);
            if (profitSection == null) {
                LOG.info(params.get("fundId") + ":缺少片收益率表数据");
                return;
            }
            startDate = profitSection.getIndexDate();
            endDate = tradeDateDao.getNextMarketDay(profitSection.getBakBizDate());

            if (unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
                DateRange dateRange = CommonUtil.getDateRange(unit);
                if (dateRange.getStartDate() > startDate) {
                    startDate = dateRange.getStartDate();
                    endDate = dateRange.getEndDate();
                }
            }
            //查询的开始时间不得小于首次拥有资产时间
            if (unit.equals(DateUnitEnum.ALL.getValue()) || startDate < assetNew.getStartDate()) {
                startDate = assetNew.getStartDate();
            }
            params.put("startDate", startDate);
            params.put("endDate", endDate);

        } else if (startDate != null && endDate != null) {
            Integer bizDate = assetNew.getBizDate();
            Integer initDate = assetNew.getStartDate();

            endDate = endDate <= bizDate ? endDate : bizDate;
            startDate = startDate >= initDate ? startDate : initDate;

            params.put("startDate", startDate);
            params.put("endDate", endDate);
        }
    }


    /**
     * 交割单建仓清仓处理
     *
     * @param resource
     * @return
     */
    private void handleStartEndFlag(List<LogAsset> resource) {
        Map<Integer, List<LogAsset>> reslut = new LinkedHashMap<>();
        for (LogAsset logAsset : resource) {
            Integer bizDate = logAsset.getBizDate();
            if (reslut.containsKey(bizDate)) {
                List<LogAsset> logAssets = reslut.get(bizDate);
                logAssets.add(logAsset);
            } else {
                List<LogAsset> logAssets = new ArrayList<>();
                logAssets.add(logAsset);
                reslut.put(bizDate, logAssets);
            }
        }
        if (CollectionUtils.isEmpty(reslut)) return;
        reslut.forEach((key, value) -> {
            LogAsset startLogAsset = value.get(value.size() - 1);
            LogAsset endLogAsset = value.get(0);
            markPositionStartFlag(startLogAsset);
            markPositionEndFlag(endLogAsset);
        });
    }

    /**
     * 持仓个股操作分析
     * datacenter-3776 新增
     * <p>
     * 总资产、当日收益、持仓列表、交易记录（历史、盘中）、positionProfit
     * #10032936 普通账户分析-当日盈亏增加费用：
     * 当日持仓盈亏比例 = 当日持仓盈亏 / （
     * (昨收价 + 应计利息）  *买入结算汇率*(持仓数量 - 当日买入总量 + 当日卖出数量 )
     * +买入清算金额*卖出结算汇率) ）
     *
     * @param params fundId, market, stkCode, pageSize, pageNo
     * @return
     */
    public MergeTradeResult getHoldPositionMergeTradeList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        String market = CommonUtil.convert(params, "market", String.class);
        String stkCode = CommonUtil.convert(params, "stkCode", String.class);
        Integer pageSize = CommonUtil.convert(params, "pageSize", Integer.class);
        Integer pageNo = CommonUtil.convert(params, "pageNo", Integer.class);
        Integer today = DateUtil.getCuryyyyMMddInteger();

        MergeTradeResult result = new MergeTradeResult();

        // 查询positionProfit表中的建仓信息
        Map<String, Object> positionParams = new HashMap<>();
        positionParams.put("fundId", fundId);
        positionParams.put("market", market);
        positionParams.put("stkCode", stkCode);
        bseCodeAlterService.getCodeAlterParams(positionParams);
        PositionProfit positionProfit = positionProfitDao.getHoldSinglePositionProfit(positionParams);
        if (positionProfit == null) {
            positionProfit = new PositionProfit();
            positionProfit.setMarket(market);
            positionProfit.setStartDate(today);
        }
        positionProfit.setStkCode(stkCode);
        // 持仓天数 feature-ACCTANAL-196 修改为自然日
        Integer holdDays = DateUtil.getDiffDay(positionProfit.getStartDate(), today).intValue();
        // 港股通实时持股天数置空
        if (holdDays == 0 && (MarketEnum.SH_HK.getValue().equals(market) || MarketEnum.SZ_HK.getValue().equals(market))) {
            holdDays = null;
        }
        positionProfit.setHoldDays(holdDays);

        if (pageNo == 1) {
            // 如果是第一页，则查询资产持仓等数据
            // 查询实时资产信息
            positionParams.put("moneyType", "0");
            List<QryFund> qryFundList = assetServiceRealTime.getAssetRealTime(positionParams, true);
            if (CollectionUtils.isEmpty(qryFundList)) {
                return result;
            }
            QryFund qryFund = qryFundList.get(0);
            // 获取实时收益和持仓
            Map dayProfitResult = profitServiceRealTime.getDayProfitData(positionParams, false, qryFund);
            List<DayStkProfit> dayStkProfitList = (List<DayStkProfit>) dayProfitResult.get("dayStkProfitList");
            List<PositionInfo> positionInfoList = (List<PositionInfo>) dayProfitResult.get("positionInfoList");

            Integer serverId = CommonUtil.convert(positionParams, "serverId", Integer.class);

            // 计算成本收益
            if (!CollectionUtils.isEmpty(positionInfoList)) {
                stkAssetService.calPositionIncome(positionInfoList, "0", fundId, notNull(qryFund.getFundAllWithOtc()));
            }
            if (!CollectionUtils.isEmpty(positionInfoList)) {
                PositionInfo positionInfo = positionInfoList.get(0);
                //以柜台返回的stkCode为主，从而进行返回
                positionProfit.setStkCode(positionInfo.getStkCode());
                positionProfit.setCostPrice(positionInfo.getCostPrice());
                positionProfit.setProfit(positionInfo.getIncome());
                positionProfit.setPrice(positionInfo.getPrice());
                positionProfit.setStkQty(positionInfo.getStkQty());
                if (qryFund.getFundAll() != null && qryFund.getFundAll() > 0) {
                    positionProfit.setPositionRatio(div(positionInfo.getMktVal(), add(qryFund.getFundAll(), qryFund.getOtcAsset())));
                }
                //成本价或现价是负数或者0  收益率为0
                if (le(notNull(positionInfo.getCostPrice()), 0.0) || le(notNull(positionInfo.getPrice()), 0.0)) {
                    positionProfit.setProfitRate(0.0);
                } else {
                    double tmp = ArithUtil.sub(positionInfo.getPrice(), positionInfo.getCostPrice());
                    positionProfit.setProfitRate(div(tmp, positionInfo.getCostPrice()));
                }

                if (commonService.needCalDayProfit(String.valueOf(serverId))) {
                    if (!CollectionUtils.isEmpty(dayStkProfitList)) {
                        DayStkProfit dayStkProfit = dayStkProfitList.get(0);
                        // 当日参考收益
                        positionProfit.setDayProfit(dayStkProfit.getProfit());
                        // 当日参考收益率
                        // 盈亏比例 = 当日盈亏 /（期初市值+当日买入金额）
                        AssetNew assetNew = assetNewService.getAssetInfo(params);
                        double stkOpenMktVal = 0.0;
                        if (assetNew != null) {
                            Map<String, Double> settRateMap = new HashMap<>();
                            Double settRate = Objects.isNull(dayStkProfit.getBuySettRate()) ? 1d : dayStkProfit.getBuySettRate();
                            settRateMap.put(market, settRate);

                            // 期初资产查询上一交易日
                            Integer openDate = Integer.valueOf(tradeDateDao.getPreMarketDay(today));
                            stkOpenMktVal = getStkOpenMktVal(fundId, openDate, market, stkCode, settRateMap, serverId);
                        }
                        //#10032936 普通账户分析-当日盈亏增加费用 买入金额包含利息和费用
                        Double dayProfitRateDeno = add(stkOpenMktVal, dayStkProfit.getBuyAmtWithIntr());
                        // 收益额为0时,将收益率置为0
                        if (ArithUtil.eq(dayStkProfit.profit, 0d)) {
                            positionProfit.setDayProfitRate(0d);
                        } else if (dayProfitRateDeno > 0.0) {
                            positionProfit.setDayProfitRate(div(dayStkProfit.profit, dayProfitRateDeno));
                        }
                    } else {
                        if (isStkUnsupported(positionInfo)) {
                            // 当日参考收益
                            positionProfit.setDayProfit(0.0);
                            // 当日参考收益率
                            positionProfit.setDayProfitRate(0.0);
                        }
                    }
                }
            }
        }
        // 获取交易记录
        positionParams.put("startDate", positionProfit.getStartDate());
        positionParams.put("endDate", today);
        positionParams.put("pageSize", pageSize);
        positionParams.put("pageNo", pageNo);
        positionParams.put("stkCode", stkCode);
        Integer startNum = pageSize * (pageNo - 1);
        positionParams.put("startNum", startNum);
        List<LogAsset> logAssets = getHoldLogAssetList(positionParams, positionProfit.getStartDate());
        if (!CollectionUtils.isEmpty(logAssets)) {
            result.setTotalLogAsset(logAssets);
        }

        // 扩位简称
        positionProfit.setExpandNameAbbr(stockService.getExpandNameAbbr(positionProfit.getStkCode(), market));
        positionProfit.setStktype(stockService.getStkType(positionProfit.getStkCode(), market));
        positionProfit.setStkName(stockService.getStkName(positionProfit.getStkCode(), market));
        result.setStkProfits(Collections.singletonList(positionProfit));
        return result;
    }

    private double getStkOpenMktVal(Long fundId, Integer bizDate, String market, String stkCode, Map<String, Double> settRateMap, Integer serverId) {
        DayProfitExtend dayProfitExtend = new DayProfitExtend.Builder().settRateMap(settRateMap).build();
        //上一个交易日的持仓
        return stkAssetService.getOpenMktVal(fundId, bizDate, "0", serverId, market, stkCode, dayProfitExtend);
    }

    private boolean isStkUnsupported(PositionInfo positionInfo) {
        // B股、港股通
        if (StringUtils.isBlank(positionInfo.getMarket()) ||
                MarketEnum.SH_B.getValue().equals(positionInfo.getMarket()) || MarketEnum.SZ_B.getValue().equals(positionInfo.getMarket())) {
            return false;
        }
        // L LOF基金，1 老式货币基金（盘中价格不变），4 新式货币基金（价格固定0.01）
        if (STKTEnum.STKT_LOF.getValue().equals(positionInfo.stkType) &&
                (LOFTYPE_MONEY_OLD.equals(positionInfo.lofMoneyFlag) || LOFTYPE_MONEY_NEW.equals(positionInfo.lofMoneyFlag))) {
            return false;
        }
        // 排除深市的 F 非交易开放基金
        return !STKTEnum.STKT_LOF.getValue().equals(positionInfo.stkType) || !"F".equals(positionInfo.stkLevel)
                || !MarketEnum.SZ_A.getValue().equals(positionInfo.market);
    }

    /**
     * 查询持仓的交易记录
     *
     * @param params    fundId, market, stkCode, pageSize, pageNo, serverId, stkCodeList(北交所)
     * @param startDate
     * @return
     */
    private List<LogAsset> getHoldLogAssetList(Map<String, Object> params, Integer startDate) {
        Integer pageSize = (Integer) params.get("pageSize");
        Integer pageNo = (Integer) params.get("pageNo");
        Long fundId = (Long) params.get("fundId");

        // 盘中交易数据
        List<Match> matchList = getRealTimeMatchList(params);
        List<LogAsset> realTimeMatchList = matchList.stream()
                .map(match -> holdMatchToLogAsset(fundId, match))
                .collect(Collectors.toList());
        // 盘中红利税流水
        List<LogAsset> bonusLogAssets = new ArrayList<>();
        // 上面调用getRealTimeMatchList()时，一定会将serverId放到params中
        Integer serverId = CommonUtil.convert(params, "serverId", Integer.class);
        boolean showFlag = nodeConfigService.getRealTimeBonusLogassetShowFlag(serverId);
        if (showFlag) {
            params.put("digestIdList", BONUS_DIGESTID_LIST);
            bonusLogAssets = oggLogAssetService.getRealTimeLogassetList(params);
            params.remove("digestIdList");
            bonusLogAssets.forEach(logAsset -> {
                if (logAsset.getDigestId() != null) {
                    logAsset.setDigestName(digestNameCache.getDigestName(logAsset.getDigestId()));
                }
                logAsset.setMatchAmt(Math.abs(notNull(logAsset.getFundEffect())));
                logAsset.setFeeTotal(0d);
            });
        }

        // (startIndex, endIndex]
        int startIndex = (pageNo - 1) * pageSize;
        List<LogAsset> realTimeAllLogAssetList = new ArrayList<>();
        realTimeAllLogAssetList.addAll(realTimeMatchList);
        realTimeAllLogAssetList.addAll(bonusLogAssets);
        // 按照matchtime降序，matchtime相同时按照sno降序
        Comparator<LogAsset> comparator = Comparator.comparing(LogAsset::getMatchTime, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(LogAsset::getSno, Comparator.nullsLast(Comparator.reverseOrder()));
        List<LogAsset> realTimeLogAssetList = realTimeAllLogAssetList.stream()
                .sorted(comparator)
                .collect(Collectors.toList())
                .stream()
                .skip(startIndex)
                .limit(pageSize)
                .collect(Collectors.toList());
        if (Objects.equals(startDate, DateUtil.getCuryyyyMMddInteger())) {
            // 如果开始日期是当天 则直接返回盘中数据
            return realTimeLogAssetList;
        }
        if (realTimeLogAssetList.size() == pageSize) {
            // 如果当日成交等于分页大小 则直接返回盘中数据
            return realTimeLogAssetList;
        } else {
            // 否则 要基于盘中交易总数和当前页盘中交易数量，重新计算新的分页参数
            startIndex = (pageNo - 1) * pageSize - (realTimeAllLogAssetList.size() - realTimeLogAssetList.size());
            int endIndex = startIndex + pageSize - realTimeLogAssetList.size();
            params.put("startIndex", startIndex);
            // 兼容tidb
            params.put("startNum", startIndex);
            params.put("endIndex", endIndex);
            // 兼容tidb
            params.put("pageSize", pageSize - realTimeLogAssetList.size());
            params.remove("pageNo");
        }
        List<LogAsset> logAssetList = new ArrayList<>(realTimeLogAssetList);
        params.put("calIndexes", POSITION_LOGASSET_CALINDEX);
        List<LogAsset> logAssets = logAssetService.getHoldTradeShiftList(params);
        logAssetList.addAll(logAssets);
        // 标记建仓
        markPositionStart(logAssetList, startDate);
        return logAssetList;
    }

    /**
     * 获取盘中实时成交记录
     *
     * @param params fundId, market, stkCode, pageSize, pageNo, serverId
     * @return
     */
    private List<Match> getRealTimeMatchList(Map<String, Object> params) {
        String market = (String) params.get("market");
        List<Match> matchList;
        if (BusinessUtil.isGgtMarket(market)) {
            // 查询沪港通历史成交表，还未交割的成交数据
            // 20210226 港股通不查询盘中成交记录
            matchList = new ArrayList<>();
        } else {
            params.put("bsFlagList", BSFLAG_LIST);
            matchList = matchService.getRealTimeMatchList(params);
        }
        return matchList;
    }

    /**
     * 标记非融券的建仓清仓记录
     *
     * @param allTradeList
     */
    private void markPositionStart(List<LogAsset> allTradeList, Integer startDate) {
        if (CollectionUtils.isEmpty(allTradeList)) {
            return;
        }
        LogAsset startLogAsset = allTradeList.get(allTradeList.size() - 1);
        // 建仓检查
        if (startLogAsset != null && startDate != null && startDate.equals(startLogAsset.getBizDate())) {
            Long stkEffect = startLogAsset.getStkEffect();
            Long stkBal = startLogAsset.getStkBal();
            if (stkEffect == null) {
                return;
            }
            if (stkEffect > 0 && Objects.equals(stkEffect, stkBal)) {
                startLogAsset.setPositionFlag(PositionCalFlagEnum.START.getValue());
            }
        }
    }


    private LogAsset holdMatchToLogAsset(Long fundId, Match match) {
        LogAsset logAsset = new LogAsset();
        logAsset.setFundId(fundId);
        logAsset.setBizDate(match.getTrdDate());
        Long digestId = null;
        String bsFlag = match.getBsFlag();
        if (bsFlag != null) {
            /*
            220188盘后定价买 3m
            220189盘后定价卖 3n
             */
            switch (bsFlag) {
                case "0B":
                case "B":
                case "0a":
                case "a":
                case "0b":
                case "b":
                case "0c":
                case "c":
                case "0d":
                case "d":
                case "0e":
                case "e":
                case "0q":
                case "q":
                case "1I":
                case "2I":
                case "1j":
                case "4m":
                    // 协商成交买入
                case "4o":
                    digestId = 220000L;
                    break;
                case "0S":
                case "S":
                case "0f":
                case "f":
                case "0g":
                case "g":
                case "0h":
                case "h":
                case "0i":
                case "i":
                case "0j":
                case "j":
                case "0r":
                case "r":
                case "1J":
                case "2J":
                case "1k":
                case "4n":
                    // 协商成交卖出
                case "4p":
                    digestId = 221001L;
                    break;
                case "3m":
                    digestId = 220188L;
                    break;
                case "3n":
                    digestId = 220189L;
                    break;
                case "2B":
                case "3B":
                    // 港股通买入
                    digestId = 220094L;
                    break;
                case "2S":
                case "3S":
                    // 港股通卖出
                    digestId = 220095L;
                    break;
                // TODO 盘中缺少权证买卖
                default:
            }
        }
        logAsset.setDigestId(digestId);
        if (digestId != null) {
            logAsset.setDigestName(digestNameCache.getDigestName(digestId));
        }
        logAsset.setMatchTime(match.getMatchTime());
        logAsset.setSno(match.getMatchSno());
        logAsset.setMatchAmt(match.getMatchAmt());
        logAsset.setMatchQty(match.getMatchQty());
        logAsset.setMatchPrice(match.getMatchPrice());
        logAsset.setMatchAmt(match.getMatchAmt());
        logAsset.setMoneyType(match.getMoneyType());
        logAsset.setStkCode(match.getStkCode());
        logAsset.setMarket(match.getMarket());
        return logAsset;
    }

    /**
     * 根据传入的开始日期、结束日期查询区间内的交易记录
     *
     * @param params
     * @return
     */
    public List<LogAsset> getBSTradeList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        Integer endDate = CommonUtil.convert(params, "endDate", Integer.class);

        List<LogAsset> tradeList = new ArrayList<>();
        //如果结束日期等于当前日期,查询盘中交易记录
        if (Objects.equals(endDate, DateUtil.getCuryyyyMMddInteger())) {
            List<Match> matchList = getRealTimeMatchList(params);
            List<LogAsset> realTimeLogAssetList = matchList.stream()
                    .map(match -> holdMatchToLogAsset(fundId, match))
                    .collect(Collectors.toList());
            tradeList.addAll(realTimeLogAssetList);
        }

        if (tradeList.size() >= MAX_PAGE_SIZE) {
            return tradeList;
        }
        params.put("digestIdList", DIGESTID_LIST);
        params.put("maxSize", MAX_PAGE_SIZE - tradeList.size());

        bseCodeAlterService.getCodeAlterParams(params);
        tradeList.addAll(logAssetService.getBSTradeList(params));


        return tradeList;
    }

    /**
     * APPAGILE-98832 已清仓股票支持搜索
     *
     * @param params fundId 资金账号
     * @return stkCode, market,spellId,stkName
     */
    public List<PositionProfitBO> getPositionProfitSearchData(Map<String, Object> params) {
        List<PositionProfitBO> positionProfitList = positionProfitDao.getClearPositionList(params);
        List<PositionProfitBO> codeAlterPositionProfitList = new ArrayList<>();
        HashSet<String> stkCodeList = positionProfitList.stream().map(PositionProfitBO::getStkCode).collect(Collectors.toCollection(HashSet::new));
        for (PositionProfitBO positionProfit : positionProfitList) {
            //处理证券名称  证券类型 证券市场名称
            positionProfit.setSpellId(stockService.getSpellId(positionProfit.getStkCode(), positionProfit.getMarket()));
            positionProfit.setStkName(stockService.getStkName(positionProfit.getStkCode(), positionProfit.getMarket()));
            positionProfit.setMarketName(MarketEnum.getMarketName(positionProfit.getMarket()));

            //搜索条件里  830和920都要有
            String alterStkCode = bseCodeAlterService.getCodeAlterOrReverse(positionProfit.getStkCode(), positionProfit.getMarket());
            //如果已经有转换后的代码在清仓记录里   就不加进去
            if(!stkCodeList.contains(alterStkCode)) {
                PositionProfitBO copyPositionProfit = new PositionProfitBO();
                copyPositionProfit.setStkCode(alterStkCode);
                copyPositionProfit.setMarket(positionProfit.getMarket());
                copyPositionProfit.setStkName(positionProfit.getStkName());
                copyPositionProfit.setSpellId(positionProfit.getSpellId());
                copyPositionProfit.setMarketName(positionProfit.getMarketName());
                codeAlterPositionProfitList.add(copyPositionProfit);
            }
        }
        positionProfitList.addAll(codeAlterPositionProfitList);
        return positionProfitList;
    }
}
