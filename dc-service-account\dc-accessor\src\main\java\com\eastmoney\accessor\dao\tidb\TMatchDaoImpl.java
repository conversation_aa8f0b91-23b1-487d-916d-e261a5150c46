package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiTMatchMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.Match;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/10 15:05
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("tMatchDao")
public class TMatchDaoImpl extends BaseDao<TiTMatchMapper, Match, Long> implements TMatchDao {
    @Override
    public List<Match> getTMatchList(Map<String, Object> params) {
        return getMapper().getTMatchList(params);
    }
}
