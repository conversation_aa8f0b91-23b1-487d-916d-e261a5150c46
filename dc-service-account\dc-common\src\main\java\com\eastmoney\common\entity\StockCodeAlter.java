package com.eastmoney.common.entity;

/**
 * <AUTHOR>
 * @create 2018-02-06 10:27
 */
public class StockCodeAlter extends BaseEntity {
    private String oldStkCode;  //原股票代码
    private String oldStkName;  //原股票名称
    private String stkCode;     //现股票代码
    private String stkName;     //现股票名称
    private Integer effectiveDate;  //首次生效交易日期
    private Integer del;    //逻辑删除状态

    public String getOldStkCode() {
        return oldStkCode;
    }

    public void setOldStkCode(String oldStkCode) {
        this.oldStkCode = oldStkCode;
    }

    public String getOldStkName() {
        return oldStkName;
    }

    public void setOldStkName(String oldStkName) {
        this.oldStkName = oldStkName;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Integer getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Integer effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getDel() {
        return del;
    }

    public void setDel(Integer del) {
        this.del = del;
    }
}
