package com.eastmoney.accessor.dao.oracle;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateStatMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateStat;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON>qi on 2016/7/27.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitRateStatDao")
public class ProfitRateStatDaoImpl extends BaseDao<ProfitRateStatMapper, ProfitRateStat, Long> implements ProfitRateStatDao {

}
