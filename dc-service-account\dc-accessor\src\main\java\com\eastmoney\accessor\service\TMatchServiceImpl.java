package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.tidb.TMatchDao;
import com.eastmoney.common.entity.Match;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/10 15:01
 */
@Service("tMatchService")
public class TMatchServiceImpl implements TMatchService {

    @Autowired
    private TMatchDao tMatchDao;

    @Override
    public List<Match> getTMatchList(Map<String, Object> params) {
        return tMatchDao.getTMatchList(params);
    }
}
