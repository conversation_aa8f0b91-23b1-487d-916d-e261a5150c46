package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

import java.util.Date;

public class ZhfxSettleStatus extends BaseEntity {
    private Integer status;
    private Date settleStartTime;
    private Date settleEndTime;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public ZhfxSettleStatus() {
    }

    public ZhfxSettleStatus(Integer status) {
        this.status = status;
    }

    public Date getSettleStartTime() {
        return settleStartTime;
    }

    public void setSettleStartTime(Date settleStartTime) {
        this.settleStartTime = settleStartTime;
    }

    public Date getSettleEndTime() {
        return settleEndTime;
    }

    public void setSettleEndTime(Date settleEndTime) {
        this.settleEndTime = settleEndTime;
    }
}
