package com.eastmoney.quote.mdstp.model;

import com.eastmoney.common.util.ArithUtil;

/**
 * Created by sunyuncai on 2016/11/1.
 */
public class Rec {
    // 最新价
    protected int dwPrice;
    // 前日收盘价(算昨涨跌幅)
    protected int dwClose;
    // 开盘价
    protected int dwOpen;

    //交易时间hhmmss
    protected int dwTime;

    public int getDwPrice() {
        return dwPrice;
    }

    public void setDwPrice(int dwPrice) {
        this.dwPrice = dwPrice;
    }

    public int getDwClose() {
        return dwClose;
    }

    public void setDwClose(int dwClose) {
        this.dwClose = dwClose;
    }

    public int getDwOpen() {
        return dwOpen;
    }

    public void setDwOpen(int dwOpen) {
        this.dwOpen = dwOpen;
    }

    public int getDwTime() {
        return dwTime;
    }

    public void setDwTime(int dwTime) {
        this.dwTime = dwTime;
    }


    //--------------------A股指数 start 缩放【1000】倍------------------
    // 获取A股指数实际 最新价
    public double getRealAIndexSharePrice() {
        return getDiv1000Price(dwPrice);
    }

    // 获取A股指数实际 收盘价
    public double getRealAIndexShareClose() {
        return getDiv1000Price(dwClose);
    }
    // 获取A股指数实际 开盘价
    public double getRealAIndexShareOpen() {
        return getDiv1000Price(dwOpen);
    }
    //--------------------A股指数 end 缩放【1000】倍------------------

    //--------------------港股指数 HS、HKI start 缩放【100】倍------------------
    // 获取港股指数实际 最新价
    public double getRealHkAndHSIndexSharePrice() {
        return getDiv100Price(dwPrice);
    }

    // 获取港股指数实际 收盘价
    public double getRealHkAndHSIndexShareClose() {
        return getDiv100Price(dwClose);
    }
    // 获取港股指数实际 开盘价
    public double getRealHkAndHSIndexShareOpen() {
        return getDiv100Price(dwOpen);
    }

    //--------------------港股指数 HS、HKI end 缩放【100】倍------------------

    private double getDiv1000Price(int price) {
        return ArithUtil.div(price, 1000);
    }

    private double getDiv100Price(int price) {
        return ArithUtil.div(price, 100);
    }

}
