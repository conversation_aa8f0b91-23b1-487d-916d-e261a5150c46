package com.eastmoney.service.rpc;

import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.service.model.ChannelData;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import org.slf4j.LoggerFactory;

/**
 * Created by sunyuncai on 2017/2/23.
 */
public class OtherTask implements Runnable {
    private static org.slf4j.Logger LOG = LoggerFactory.getLogger(OtherTask.class);
    private static MessageHandler messageHandler = SpringContextUtil.getBean(MessageHandler.class);
    private ChannelData channelData;

    public OtherTask(ChannelData channelData) {
        this.channelData = channelData;
    }

    @Override
    public void run() {
        try {
            Message message = channelData.getMessage();
            byte type = message.getType();
            //请求deskey
            if (type == Constants.SOCKET_TYPE_KEY_REQ) {
                messageHandler.replyDesKey(channelData.getCtx(), message);
            } else if (type == Constants.SOCKET_TYPE_HTBT_REQ) {
                //心跳
                messageHandler.replyHeartbeat(channelData.getCtx(), message);
            } else if (type == Constants.SOCKET_TYPE_HTBT_RES) {
                //返回心跳
                LOG.info("返回心跳");
            }
        } catch (Throwable t) {
            messageHandler.handleException(t, channelData);
        }
    }
}
