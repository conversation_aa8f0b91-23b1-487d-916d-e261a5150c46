package com.eastmoney.accessor.dao.kgdb;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
public class OtcInstNetDaoManager {
    @Autowired
    private OtcInstNetDao otcInstNetDao;

    @PostConstruct
    public void loadInstNetMap(){
        otcInstNetDao.loadInstNetMap();
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        Runnable run = () -> otcInstNetDao.loadInstNetMap();
        executor.scheduleWithFixedDelay(
                run, 10 * 60, 30 * 60, TimeUnit.SECONDS);
    }
}
