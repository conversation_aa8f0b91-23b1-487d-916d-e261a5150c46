package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.tProfitRank;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/30 11:11
 */
@Repository
public interface TiTSecProfitDayMapper extends BaseMapper<TSecProfitDayDO, Long> {
    List<tProfitRank> getSecTProfitRankList(Map<String, Object> params);

    TProfitBO getSecTProfitSection(Map<String, Object> params);

    List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params);

    List<TProfitDayDO> getTProfitDayList(Map<String, Object> params);

    List<TSecProfitDayDO> getTSecProfitDayListPC(Map<String, Object> params);
}
