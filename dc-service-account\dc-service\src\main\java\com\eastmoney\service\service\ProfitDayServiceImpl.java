package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.oracle.MajorBillDao;
import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.common.entity.cal.ProfitDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.eastmoney.accessor.enums.ProfitSectionStatEnum.*;

/**
 * 日收益serverice
 * <AUTHOR>
 * @create 2024/2/7
 */
@Service
public class ProfitDayServiceImpl implements ProfitDayService {

    @Autowired
    private ProfitDayDao profitDayDao;

    @Autowired
    private MajorBillDao majorBillDao;

    @Override
    public List<ProfitDay> queryProfitDayByUnit(Long fundId, Integer startDate, Integer endDate, String unit) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("startDate", startDate);
        queryParams.put("endDate", endDate);

        if (Objects.equals(unit, M.getType()) || Objects.equals(unit, Y.getType())) {
            return profitDayDao.query(queryParams);
        } else if (Objects.equals(unit, YS.getType())) {
            return majorBillDao.selectYearProfit(fundId, startDate, endDate);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProfitDay> queryProfitByBizDate(Long fundId, Integer bizDate) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("bizDate", bizDate);
        return profitDayDao.query(queryParams);
    }
}
