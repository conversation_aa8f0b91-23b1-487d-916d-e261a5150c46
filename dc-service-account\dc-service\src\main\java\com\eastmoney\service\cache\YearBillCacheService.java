package com.eastmoney.service.cache;

import com.eastmoney.common.annotation.RedisCache;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * 年账单数据缓存
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Service
public class YearBillCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(YearBillCacheService.class);
    @Resource(name = "yearBillExtendCache")
    private LoadingCache<String, Optional<byte[]>> yearBillExtendCache;

    @RedisCache(keyGenerator = "'jzjy_yearbill_' + #fundId + '_' + #indexKey")
    public byte[] getUserYearBillFromRedisCache(Long fundId, Integer indexKey, Integer startDate, Integer endDate) {
        return getUserYearBillFromMemoryCache(fundId, indexKey, startDate, endDate);
    }

    /**
     * 从内存缓存中获取年账单数据，如果缓存未命中，则直接从数据库中获取年账单数据
     *
     * @param fundId    资金账号
     * @param indexKey  年度
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 带反序列化的年账单数据
     */
    public byte[] getUserYearBillFromMemoryCache(Long fundId, Integer indexKey, Integer startDate, Integer endDate) {
        String key = StringUtils.joinWith("-", fundId, indexKey, startDate, endDate);
        try {
            return yearBillExtendCache.get(key).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("年账单缓存数据查询失败:{}", key, e);
        }
        return null;
    }
}
