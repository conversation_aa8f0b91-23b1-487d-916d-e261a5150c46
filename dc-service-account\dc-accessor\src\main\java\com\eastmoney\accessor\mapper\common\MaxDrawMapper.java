package com.eastmoney.accessor.mapper.common;

import com.eastmoney.common.entity.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by WuJunjie on 2016/11/04.
 */
public interface MaxDrawMapper {

    FundAssetSummary queryFundAssetSummary(Map<String, Object> params);

    Integer queryServerId(Map<String, Object> params);

    String queryFundidbbset(Map<String, Object> params);

    String queryOrgbbset(Map<String, Object> params);

    String queryMixedConfig(Map<String, Object> params);

    List<String> queryMoneyType(Map<String, Object> params);

    List<Double> getClearedAmt(Map<String, Object> params);

    Double getPunishRate(Map<String, Object> params);

    Integer getDayOfYear(Map<String, Object> params);

    HgtReferRate getHgtRefRate(Map<String, Object> params);

    HashMap getFundEffect(Map<String, Object> params);

    Integer querySysObjects(Map<String, Object> params);

    HashMap getCustintransInfo(Map<String, Object> params);

    FundPartAsset queryFundPartAsset(Map<String, Object> params);

    Long queryZZXQ(Map<String, Object> params);

    Double queryMarketValue(Map<String, Object> params);

    Double queryMKFrzamt(Map<String, Object> params);

    Double queryOfMkFrzamt(Map<String, Object> params);

    String queryReportKind(Map<String, Object> params);

    Double queryRqmcValue(Map<String, Object> params);

    Double queryFundAvl(Map<String, Object> params);

    List<StkDetail> queryStkAvl(Map<String, Object> params);

    Double queryRzrqValue(Map<String, Object> params);

    List<CreditContract> getCreditContract(Map<String, Object> params);

    Double queryFundCtrl(Map<String, Object> params);

    List queryFundAssetCurrent(Map<String, Object> params);

    List queryFundAssets(Map<String, Object> params);

    List queryStkAssets(Map<String, Object> params);

    List getCustomer(Map<String, Object> params);

    List getAssetDebt(Map<String, Object> params);

    List queryStkDetail(Map<String, Object> params);

    List getFundInfo(Map<String, Object> params);
}
