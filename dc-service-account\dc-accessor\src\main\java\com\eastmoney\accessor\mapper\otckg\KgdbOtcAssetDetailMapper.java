package com.eastmoney.accessor.mapper.otckg;

import com.eastmoney.common.entity.OtcAssetDetail;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON>feng on 2018/08/02
 */
@Repository
public interface KgdbOtcAssetDetailMapper {

    /**
     * 获取OTC资产
     */
    List<OtcAssetDetail> getRealTimeOtcAsset(Map<String, Object> params);

}
