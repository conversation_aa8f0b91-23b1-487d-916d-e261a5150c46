package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.AccountTemporaryMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.AccountTemporary;
import com.eastmoney.common.entity.DayProfitBean;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by h<PERSON><PERSON>qi on 2016/7/18.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("accountTemporaryDao")
public class AccountTemporaryDaoImpl extends BaseDao<AccountTemporaryMapper, AccountTemporary, Integer> implements AccountTemporaryDao {

    @Override
    public DayProfitBean getDayProfitBean(long fundId) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        List<AccountTemporary> accountTemporaries = super.query(params);
        DayProfitBean dayProfitBean = null;
        if (accountTemporaries.size() > 0) {
            AccountTemporary accountTemporary = accountTemporaries.get(0);
            dayProfitBean = new DayProfitBean();
            dayProfitBean.setEuTime(accountTemporary.getEuTime());
            dayProfitBean.setBizDate(accountTemporary.getBizDate());
            dayProfitBean.setAsset(accountTemporary.getAsset());
            dayProfitBean.setProfitRate(accountTemporary.getProfitRate());
            dayProfitBean.setProfit(accountTemporary.getProfit());
            dayProfitBean.setShiftIn(accountTemporary.getShiftIn());
            dayProfitBean.setShiftOut(accountTemporary.getShiftOut());
            dayProfitBean.setOtcAsset(accountTemporary.getOtcAsset());
        }
        return dayProfitBean;
    }
}
