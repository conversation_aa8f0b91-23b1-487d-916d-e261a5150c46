package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.sqlserver.OggVipCustomersDao;
import com.eastmoney.common.entity.cal.IsInterceptedUserResult;
import com.eastmoney.common.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/26 10:20
 */
@Service
public class VipCustomersService {

    private static Logger LOG = LoggerFactory.getLogger(VipCustomersService.class);

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private OggVipCustomersDao vipCustomersDao;


    private Set<Long> interceptFundIdsSet = new HashSet<>();

    @PostConstruct
    @Scheduled(cron = "0 0 6 * * ?")
    public void initInterceptFundIdSet() {
        Set<Long> interceptFundIdsNew = new HashSet<>();
        List<Integer> serverIds = nodeConfigService.getServerIds();
        Map<String, Object> params = new HashMap<>();
        for (Integer serverId : serverIds) {
            params.put("serverId", serverId);
            interceptFundIdsNew.addAll(vipCustomersDao.selectInterceptFundIds(params));
        }
        interceptFundIdsSet = interceptFundIdsNew;
        LOG.info("初始化拦截快订户非个人户资金账号完成：数量{}", interceptFundIdsSet.size());
    }

    public IsInterceptedUserResult isInterceptedUser(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        IsInterceptedUserResult isInterceptedUserResult = new IsInterceptedUserResult();
        if (interceptFundIdsSet.contains(fundId)) {
            isInterceptedUserResult.setIsIntercepted("1");
        } else {
            isInterceptedUserResult.setIsIntercepted("0");
        }
        return isInterceptedUserResult;
    }
}
