package com.eastmoney.service.util;

import com.eastmoney.common.model.DateRange;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class IndexKeyUtilTest {

    /**
     * 测试构造器
     */
    @Test
    void testConstructor() throws ClassNotFoundException, NoSuchMethodException {
        Class<?> clazz = Class.forName("com.eastmoney.service.util.IndexKeyUtil");
        Constructor<?> constructor = clazz.getDeclaredConstructor();
        constructor.setAccessible(true);

        assertThatThrownBy(constructor::newInstance).isInstanceOf(InvocationTargetException.class);
    }

    /**
     * 测试年
     */
    @Test
    void testYear() {
        final DateRange expectedResult = new DateRange(20250101, 20251231);

        final DateRange result = IndexKeyUtil.getDateRange("2025");

        assertThat(result).isEqualTo(expectedResult);
    }

    /**
     * 测试月
     */
    @Test
    void testMonth() {
        final DateRange expectedResult = new DateRange(20250201, 20250228);

        final DateRange result = IndexKeyUtil.getDateRange("202502");

        assertThat(result).isEqualTo(expectedResult);
    }

    /**
     * 测试本月
     */
    @Test
    void testCurrentMonth() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

        final DateRange expectedResult = new DateRange(Integer.parseInt(firstDayOfMonth.format(yyyyMMdd)), Integer.parseInt(today.format(yyyyMMdd)));

        final DateRange result = IndexKeyUtil.getDateRange(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));

        assertThat(result).isEqualTo(expectedResult);
    }

    /**
     * 测试异常值
     */
    @Test
    void testIllegal() {
        assertThatThrownBy(() -> IndexKeyUtil.getDateRange("20250201")).isInstanceOf(IllegalArgumentException.class);
        assertThatThrownBy(() -> IndexKeyUtil.getDateRange("abcd")).isInstanceOf(IllegalArgumentException.class);
    }
}
