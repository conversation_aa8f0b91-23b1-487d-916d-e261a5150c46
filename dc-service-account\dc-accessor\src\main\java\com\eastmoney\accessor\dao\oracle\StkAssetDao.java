package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.StkAsset;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/8
 *
 * <AUTHOR>
 */
public interface StkAssetDao extends IBaseDao<StkAsset, Integer> {

    List<StkAsset> getRealTimePosition(Map<String, Object> params);

    List<StkAsset> selectOne(Map<String, Object> param);
}
