package com.eastmoney.accessor.aspect;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

/**
 * Created on 2020/3/31-14:10.
 *
 * <AUTHOR>
 */
public class ZhfxDataSourceCondition implements Condition {
    private static final Logger LOG = LoggerFactory.getLogger(ZhfxDataSourceCondition.class);
    private static volatile String tidbFlag = "0";
    private static Properties props = null;

    static {
        try (InputStream in = ZhfxDataSourceCondition.class.getClassLoader().getResourceAsStream("application.yml")) {
            if(in != null){
                props = new Properties();
                props.load(in);

                tidbFlag = props.getProperty("tidb_flag");
                if (tidbFlag == null) {
                    tidbFlag = "0";
                }
            }

        } catch (IOException e) {
            LOG.error("加载tidb配置项 tidb_flag 失败", e);
        }
    }

    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        MultiValueMap<String, Object> zhfxDataSource = annotatedTypeMetadata.getAllAnnotationAttributes("com.eastmoney.common.annotation.ZhfxDataSource");
        List<Object> value = zhfxDataSource.get("value");
        if (!CollectionUtils.isEmpty(value)) {
            if ("tidb".equals(value.get(0)) && "1".equals(tidbFlag)) {
                return true;
            } else if ("oracle".equals(value.get(0)) && "0".equals(tidbFlag)) {
                return true;
            }
        }
        return false;

    }

}

