<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiAssetNewMapper">
    <resultMap id="BaseResultMap" type="as_assetNew">
        <result column="FUNDID" property="fundId"/>
        <result column="START_DATE" property="startDate"/>
        <result column="ASSET_INIT" property="assetInit"/>
        <result column="ASSET" property="asset"/>
        <result column="SHIFT_OUT_TOTAL" property="shiftOutTotal"/>
        <result column="SHIFT_IN_TOTAL" property="shiftInTotal"/>
        <result column="SHIFT_OUT" property="shiftOut"/>
        <result column="SHIFT_IN" property="shiftIn"/>
        <result column="MKTVAL" property="mktval"/>
        <result column="BIZDATE" property="bizDate"/>
        <result column="SERVERID" property="serverId"/>
        <result column="OTC_NET_TRANSFER" property="otcNetTransfer"/>
        <result column="OTC_ASSET" property="otcAsset"/>
        <result column="OTHER_NET_TRANSFER" property="otherNetTransfer"/>
        <result column="netTransfer" property="netTransfer"/>
        <result column="SERVERID" property="serverId"/>
        <result column="EUTIME" property="euTime"/>
    </resultMap>
    <sql id="All_Column">
        EUTIME, FUNDID, SERVERID, START_DATE, ASSET_INIT, ASSET, IFNULL(ABS(SHIFT_OUT_TOTAL),0) as SHIFT_OUT_TOTAL, SHIFT_IN_TOTAL, MKTVAL, BIZDATE, SERVERID, SHIFT_OUT, SHIFT_IN,
        (OTC_SHIFT_IN_TOTAL - OTC_SHIFT_OUT_TOTAL) OTC_NET_TRANSFER,IFNULL(OTC_ASSET,0) OTC_ASSET,
        ((SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) OTHER_NET_TRANSFER,
        ((OTC_SHIFT_IN_TOTAL + SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (OTC_SHIFT_OUT_TOTAL + SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) netTransfer
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="All_Column"/>
        FROM ATCENTER.ASSET_NEW use index (pk_lg_asset_new)
        <where>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
        </where>
        limit 1
    </select>

</mapper>