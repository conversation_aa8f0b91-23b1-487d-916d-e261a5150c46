package com.eastmoney.accessor.service;

import com.eastmoney.common.entity.LogAsset;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
public interface OggLogAssetService {

    /**
     * 银证划转、台账间划转记录
     * @param params
     * @return
     */
    List<LogAsset> selectTransferAmt(Map<String, Object> params);

    /**
     * 盘中交割单数据查询
     * @return
     */
    List<LogAsset> getRealTimeLogassetList(Map<String, Object> params);
}
