package com.eastmoney.service.service;

import com.eastmoney.common.entity.cal.ProfitRateDay;

import java.util.List;

/**
 * 日收益率service
 * <AUTHOR>
 * @create 2024/2/20
 */
public interface ProfitRateDayService {

    /**
     * 查询指定区间的收益数据
     * @param fundId
     * @param startDate
     * @param endDate
     * @param unit
     * @return
     */
    List<ProfitRateDay> queryProfitRateDayByUnit(Long fundId, Integer startDate, Integer endDate, String unit);

    /**
     * 查询指定日期收益率
     * @param fundId
     * @param bizDate
     * @return
     */
    List<ProfitRateDay> queryProfitRateByBizDate(Long fundId, Integer bizDate);
}
