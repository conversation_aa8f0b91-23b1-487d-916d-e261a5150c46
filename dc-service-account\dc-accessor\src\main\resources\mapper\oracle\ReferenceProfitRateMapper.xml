<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.ReferenceProfitRateMapper">


    <sql id="All_Column">
        timeSlot,indexKey,category,typeCode,typeName,profitRate,remark
    </sql>


    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.ReferenceProfitRate">
        select <include refid="All_Column"/>
        from atcenter.reference_profit_rate
        where indexKey = #{indexKey}
    </select>

</mapper>