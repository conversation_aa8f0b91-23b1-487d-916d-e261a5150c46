/*
 * Copyright 1999-2011 Alibaba Group.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.eastmoney.transport.exception;

import com.eastmoney.transport.client.MessageChannel;

import java.net.InetSocketAddress;

/**
 * <AUTHOR>
 * @export
 */
public class TimeoutException extends RemotingException {

    private static final long serialVersionUID = 3122966731958222692L;
    public TimeoutException(MessageChannel channel, String message){
        super(channel, message);
    }

}