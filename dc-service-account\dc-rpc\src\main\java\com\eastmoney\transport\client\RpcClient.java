package com.eastmoney.transport.client;


import com.alibaba.fastjson.JSON;
import com.eastmoney.common.exception.CommonException;
import com.eastmoney.common.sysEnum.ErrCodeEnum;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.model.RpcOutput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;


/**
 * Created by 1 on 15-7-7.
 */
public class RpcClient {
    private static final Logger logger = LoggerFactory.getLogger(RpcClient.class);
    public static <T> List<T> send(Map<String,Object> params, String groupName, Class<T> tClass) throws Exception {
        String nextReqId = CommonUtil.generateUUID();
        params.put("lastReqId",params.get("requestId"));
        params.remove("requestId");
        try {
            NettyClient nettyClient = NettyClientHolder.getNettyClient(groupName);
            ResponseFuture future = nettyClient.request(params, nextReqId);
            Message message = (Message) future.get();
            RpcOutput<T> rpcOutput = JSON.parseObject(message.getContent(),RpcOutput.class);
            if (rpcOutput.getErrCode() == 0) {
                rpcOutput.setData(JSON.parseArray(rpcOutput.getData().toString(), tClass));
            }  else {
                throw new CommonException(ErrCodeEnum.FAIL.getValue(),groupName + "服务返回失败 " + rpcOutput.getMsg());
            }
            return rpcOutput.getData();
        } catch (Throwable t) {
            logger.error("调用服务器失败 {} {}", nextReqId, t);
            if (t instanceof CommonException) {
                throw t;
            }
            throw new RuntimeException("调用服务器失败 nextReqId=" + nextReqId + " " + t.getMessage());
        }finally {
            params.remove("lastReqId");
            params.put("nextReqId", nextReqId);
        }
    }
}
