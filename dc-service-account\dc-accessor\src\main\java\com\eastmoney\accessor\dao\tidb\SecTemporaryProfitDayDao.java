package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.SecProfitDayDO;

import java.util.List;

/**
 * 盘后临时个股日收益明细Dao
 * <AUTHOR>
 * @create 2024/3/7
 */
public interface SecTemporaryProfitDayDao extends IBaseDao<SecProfitDayDO, Integer> {
    /**
     * 个股日收益明细查询
     * @param fundId
     * @return
     */
    List<SecProfitDayDO> getSecTemporaryProfitDay(Long fundId);

    List<SecProfitDayDO> getSecTemporaryProfitDay(Long fundId, List<String> markets);

}
