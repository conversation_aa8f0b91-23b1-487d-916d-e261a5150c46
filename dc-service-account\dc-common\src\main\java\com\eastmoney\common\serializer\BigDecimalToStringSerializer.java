package com.eastmoney.common.serializer;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * BigDecimal类型数据不使用科学计数法并转为String类型
 */
public class BigDecimalToStringSerializer implements ObjectSerializer {

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {

        if(Objects.nonNull(object)) {
            BigDecimal value = (BigDecimal) object;
            //去除数字后多余的0
            serializer.write(value.stripTrailingZeros().toPlainString());
        }
    }
}
