package com.eastmoney.service.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

/**
 * Created by sunyuncai on 2016/3/30.
 */
public class PropertiesUtil {
    private static Logger LOG = LoggerFactory.getLogger(PropertiesUtil.class);
    //配置文件的路径
    private static String configPath = null;
    private static Properties props = null;

    static {
        try( InputStream in = PropertiesUtil.class.getClassLoader().getResourceAsStream("application.yml")){
            props = new Properties();
            props.load(in);
            LOG.info("配置:" + readAllProperties());
        }catch (Exception e){
            LOG.error("",e);
        }
    }

    /**
     * 根据key值读取配置的值
     */
    public static String readValue(String key) {
        return props.getProperty(key);
    }
    /**
     * 根据key值读取配置的值
     */
    public static String readValue(String key,Object val) {
        String value = props.getProperty(key);
        if (value == null) {
            value = val + "";
        }
        return value;
    }

    /**
     * 读取properties的全部信息
     */
    public static Map<String, String> readAllProperties() throws Exception {
        //保存所有的键值
        Map<String, String> map = new HashMap<>();
        Enumeration en = props.propertyNames();
        while (en.hasMoreElements()) {
            String key = (String) en.nextElement();
            // 日志打印过滤密码
            if (StringUtils.equalsIgnoreCase(key, "password") || StringUtils.equalsIgnoreCase(key, "pwd")) {
                continue;
            }
            String Property = props.getProperty(key);
            map.put(key, Property);
        }
        return map;
    }

    /**
     * 设置某个key的值,并保存至文件。
     */
    public static void setValue(String key, String value) throws IOException {
        Properties prop = new Properties();
        try (
                InputStream fis = new FileInputStream(configPath);
                OutputStream fos = new FileOutputStream(configPath)
        ) {
            // 从输入流中读取属性列表（键和元素对）
            prop.load(fis);
            prop.setProperty(key, value);
            // 以适合使用 load 方法加载到 Properties 表中的格式，
            // 将此 Properties 表中的属性列表（键和元素对）写入输出流
            prop.store(fos, "last update");
        }
    }
}
