package com.eastmoney.service.handler;

import com.eastmoney.common.entity.LogAsset;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.DigestNameCache;
import com.eastmoney.service.service.LogAssetService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/4/21.
 */
@Service
public class LogAssetHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAssetHandler.class);
    @Autowired
    private LogAssetService logAssetService;
    @Autowired
    private DigestNameCache digestNameCache;
    @Autowired
    private PositionHandler positionHandler;

    public List<LogAsset> getStkTradeList(Map<String, Object> params) {
//        checkIsContainNewStkCode(params);
        List<LogAsset> list = logAssetService.getStkTradeList(params);
        return list;
    }

    public List<LogAsset> getStkShiftList(Map<String, Object> params) {
        //检查是否变更过股票代码
        //checkIsContainNewStkCode(params);
        List<LogAsset> logAssetList = logAssetService.getStkShiftList(params);
        for (LogAsset logAsset : logAssetList) {
            logAsset.setShiftAmt(logAsset.getMatchAmt() + "");
        }
        return logAssetList;
    }

    /**
     * 获取区间资产变动-其他流入的明细交割单
     *
     * @param params
     * @return
     */
    public List<LogAsset> getOtherChangeLogAsset(Map<String, Object> params) {
        // 设置查询时间区间
        if (params.containsKey("unit")) {
            positionHandler.setQueryTime(params);
            if ((!params.containsKey("startDate")) || (!params.containsKey("endDate"))) {
                LOGGER.info(params.get("fundId") + ":无法确定查询时间");
                return Collections.emptyList();
            }
        } else if ((!params.containsKey("startDate")) || (!params.containsKey("endDate"))) {
            throw new RuntimeException("缺少必要参数: unit or (startDate and endDate)");
        }
        // 设置排序
        specifiSort(params);
        List<LogAsset> logAssetList = logAssetService.getOtherDetailList(params);
        if (CollectionUtils.isEmpty(logAssetList)) {
            return logAssetList;
        }
        for (int i = 0; i < logAssetList.size(); i++) {
            LogAsset logAsset = logAssetList.get(i);
            if (logAsset == null) {
                continue;
            }
            Long digestId = logAsset.getDigestId();
            if (digestId == null) {
                continue;
            }
            String digestName = digestNameCache.getDigestName(digestId);
            logAsset.setDigestName(digestName);
        }
        return logAssetList;
    }

    /**
     * 指定具体的查询排序方式
     * sortFlag：1-日期 2-数量 3-金额
     * orderFlag：1-升序 2-倒叙
     */
    private void specifiSort(Map<String, Object> params) {
        String sortFlag = CommonUtil.convert(params.get("sortFlag"), String.class);
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);

        String sort = "";
        // 日期
        if ("1".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "ORDER BY a.BIZDATE ASC, a.SNO ASC";
            } else if ("2".equals(orderFlag)) {
                sort = "ORDER BY a.BIZDATE DESC, a.SNO DESC";
            }
        }

        // 数量
        if ("2".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "ORDER BY a.STKEFFECT ASC, a.BIZDATE DESC, a.SNO DESC";
            } else if ("2".equals(orderFlag)) {
                sort = "ORDER BY a.STKEFFECT DESC, a.BIZDATE DESC, a.SNO DESC";
            }
        }

        // 金额
        if ("3".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "ORDER BY a.FUNDEFFECT ASC, a.BIZDATE DESC, a.SNO DESC";
            } else if ("2".equals(orderFlag)) {
                sort = "ORDER BY a.FUNDEFFECT DESC, a.BIZDATE DESC, a.SNO DESC";
            }
        }
        if (StringUtils.isNotBlank(sort)) {
            params.put("sort", sort);
        }
    }
}
