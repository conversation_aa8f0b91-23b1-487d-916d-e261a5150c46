package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.accessor.service.MatchService;
import com.eastmoney.accessor.service.TMatchService;
import com.eastmoney.common.entity.BusinessTaskStatusDO;
import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.service.cache.BusinessTaskStatusCacheService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.TProfitService;
import com.eastmoney.service.service.stkasset.HoldPositionService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TOperationAnalyzeHandlerTest {

    @Mock
    private TProfitService mockTProfitService;
    @Mock
    private StockService mockStockService;
    @Mock
    private MatchService mockMatchService;

    @Mock
    private TradeDateDao mockTradeDateDao;
    @Mock
    private CoreConfigService mockCoreConfigService;
    @Mock
    private HoldPositionService mockHoldPositionService;
    @Mock
    private TMatchService mockTMatchService;
    @Mock
    private BusinessTaskStatusCacheService mockBusinessTaskStatusCacheService;
    @Mock
    private NodeConfigService mockNodeConfigService;

    @InjectMocks
    private TOperationAnalyzeHandler tOperationAnalyzeHandlerUnderTest;


    private static String tBuyBsFlag = "0B,0a,0b,0c,0d,0e,0q,1G,1I,2B,3B,3m,a,b,B,c,d,e,q,1j,2I,4m";
    private static String tSellBsFlag = "0S,0f,0g,0h,0i,0j,0r,1H,1J,2S,3S,4S,3n,i,S,f,g,h,j,r,1k,2J,4n";
    private static String buySellBsFlag = tBuyBsFlag + "," + tSellBsFlag;
    private static Set<String> buySellSet = Arrays.stream(buySellBsFlag.split(","))
            .collect(Collectors.toSet());

    private static Set<String> buySet = Arrays.stream(tBuyBsFlag.split(","))
            .collect(Collectors.toSet());


    @DisplayName("做T收益-个股汇总：测试常规返回")
    @Test
    void testGetTProfitStockList() {
        // Setup
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("orderFlag", "2");
        params.put("sortFlag", "2");
        params.put("startDate", ********);
        params.put("endDate", ********);
        params.put("startNum", 0);

        TProfitBO tProfitBO = new TProfitBO();
        tProfitRank tProfitRank = new tProfitRank();
        tProfitRank.setMarket("0");
        tProfitRank.setStkCode("000001");
        tProfitRank.setStkName("平安银行");
        tProfitRank.setTProfit(555.2);
        tProfitRank.setTSuccess(11);
        tProfitRank.setTSuccessRate(0.4074);
        tProfitRank.setTTimes(27);
        List<tProfitRank> tProfitRanks = new ArrayList<>();
        tProfitRanks.add(tProfitRank);
        tProfitBO.setEndDate("20240719");
        tProfitBO.setTProfitStockList(tProfitRanks);
        when(mockTProfitService.getSecTProfitRankList(params)).thenReturn(tProfitRanks);
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);

        BusinessTaskStatusDO businessTaskStatusDO = new BusinessTaskStatusDO();
        businessTaskStatusDO.setBusinessTaskHandle("tProfitDayCalHandle");
        businessTaskStatusDO.setExecuteStatus(0);
        businessTaskStatusDO.setBusinessTaskHandleName("做T收益计算handle");
        businessTaskStatusDO.setBizDate(20240719);
        when(mockBusinessTaskStatusCacheService.getBusinessTaskStatus(params,"tProfitDayCalHandle")).thenReturn(businessTaskStatusDO);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");

        TProfitBO result = tOperationAnalyzeHandlerUnderTest.getSecTProfitRankList(params);

    }

    @DisplayName("做T收益-个股汇总：测试返回空")
    @Test
    void testGetTProfitStockList_TSecProfitDayServiceReturnsNoItems() {
        // Setup
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("orderFlag", "2");
        params.put("sortFlag", "2");
        params.put("startDate", ********);
        params.put("endDate", ********);
        params.put("startNum", 0);
        when(mockTProfitService.getSecTProfitRankList(params)).thenReturn(Collections.emptyList());
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);

        BusinessTaskStatusDO businessTaskStatusDO = new BusinessTaskStatusDO();
        businessTaskStatusDO.setBusinessTaskHandle("tProfitDayCalHandle");
        businessTaskStatusDO.setExecuteStatus(0);
        businessTaskStatusDO.setBusinessTaskHandleName("做T收益计算handle");
        businessTaskStatusDO.setBizDate(20240719);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");

        final TProfitBO result = tOperationAnalyzeHandlerUnderTest.getSecTProfitRankList(params);

    }

    @DisplayName("做T收益-每日个股明细：清算完成时")
    @Test
    void testGetTProfitDetailList() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("bizDate", ********);
        Map<String, Integer> holdPositionMap = new HashMap<>();
        holdPositionMap.put("0-000008", 20240105);
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");
        when(mockHoldPositionService.getHoldPositionStartDate(************L)).thenReturn(holdPositionMap);
        String tBuyBsFlag = "0B,0a,0b,0c,0d,0e,0q,1G,1I,2B,3B,3m,a,b,B,c,d,e,q,1j,2I,4m";
        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(Arrays.stream(tBuyBsFlag.split(","))
                .collect(Collectors.toSet()));
        String buySellFlag = tBuyBsFlag + ",0S,0f,0g,0h,0i,0j,0r,1H,1J,2S,3S,4S,3n,i,S,f,g,h,j,r,1k,2J,4n";
        Set<String> buySellSet = Arrays.stream(buySellFlag.split(","))
                .collect(Collectors.toSet());
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);

        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);
        final TSecProfitDayDO tSecProfitDayDO = new TSecProfitDayDO();
        tSecProfitDayDO.setMoneyType("0");
        tSecProfitDayDO.setHoldFlag("0");
        tSecProfitDayDO.setLastMatchTime(9511569L);
        tSecProfitDayDO.setStkName("神州高铁");
        tSecProfitDayDO.setFundId(************L);
        tSecProfitDayDO.setStkCode("000008");
        tSecProfitDayDO.setMarket("0");
        tSecProfitDayDO.setTProfit(5.0);
        tSecProfitDayDO.setTDifferences(0.01);
        final List<TSecProfitDayDO> tSecProfitDayDOList = Arrays.asList(tSecProfitDayDO);
        params.put("bsFlagList", buySellSet);
        when(mockTProfitService.getTSecProfitDayList(params)).thenReturn(tSecProfitDayDOList);
        when(mockTMatchService.getTMatchList(params)).thenReturn(matches);
        when(mockStockService.getStkName("000008", "0")).thenReturn("神州高铁");

        // Run the test
        final List<TProfitDetail> result = tOperationAnalyzeHandlerUnderTest.getSecTDetailList(params);

    }

    @DisplayName("做T收益-每日个股明细：清算未完成时")
    @Test
    void testGetTProfitDetailListNotFinished() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("bizDate", ********);
        Map<String, Integer> holdPositionMap = new HashMap<>();
        holdPositionMap.put("0-000008", 20240105);
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");
        when(mockHoldPositionService.getHoldPositionStartDate(************L)).thenReturn(holdPositionMap);
        String tBuyBsFlag = "0B,0a,0b,0c,0d,0e,0q,1G,1I,2B,3B,3m,a,b,B,c,d,e,q,1j,2I,4m";
        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(Arrays.stream(tBuyBsFlag.split(","))
                .collect(Collectors.toSet()));

        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);

        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);
        final TSecProfitDayDO tSecProfitDayDO = new TSecProfitDayDO();
        tSecProfitDayDO.setMoneyType("0");
        tSecProfitDayDO.setHoldFlag("0");
        tSecProfitDayDO.setLastMatchTime(9511569L);
        tSecProfitDayDO.setStkName("神州高铁");
        tSecProfitDayDO.setFundId(************L);
        tSecProfitDayDO.setStkCode("000008");
        tSecProfitDayDO.setMarket("0");
        tSecProfitDayDO.setTProfit(5.0);
        tSecProfitDayDO.setTDifferences(0.01);
        final List<TSecProfitDayDO> tSecProfitDayDOList = Arrays.asList(tSecProfitDayDO);
        params.put("bsFlagList", buySellSet);
        when(mockTProfitService.getTSecProfitDayList(params)).thenReturn(tSecProfitDayDOList);
        when(mockTMatchService.getTMatchList(params)).thenReturn(matches);
        when(mockStockService.getStkName("000008", "0")).thenReturn("神州高铁");

        // Run the test
        final List<TProfitDetail> result = tOperationAnalyzeHandlerUnderTest.getSecTDetailList(params);

    }

    @DisplayName("做T收益-全量汇总：清算未完成时")
    @Test
    void testGetTotalTProfit() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("startDate", ********);
        params.put("endDate", ********);

        final TProfitBO tProfitBO = new TProfitBO();
        tProfitBO.setTotalTSuccess(124L);
        tProfitBO.setTotalTTimes(258L);
        tProfitBO.setTotalTSuccessRate(0.4806);
        tProfitBO.setTotalTProfit(1628073.94);
        tProfitBO.setEndDate("9511569");

        tProfitRank tProfitRank = new tProfitRank();
        tProfitRank.setMarket("0");
        tProfitRank.setStkCode("000001");
        tProfitRank.setStkName("平安银行");
        tProfitRank.setTProfit(555.2);
        tProfitRank.setTSuccess(11);
        tProfitRank.setTSuccessRate(0.4074);
        tProfitRank.setTTimes(27);
        ArrayList<tProfitRank> tProfitRanks = Lists.newArrayList(tProfitRank);

        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);

        when(mockTProfitService.getSecTProfitRankList(params)).thenReturn(tProfitRanks);
        when(mockTProfitService.getTProfitSection(params)).thenReturn(tProfitBO);
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);
        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(buySet);
        Map<String, Object> params2 = new HashMap<>();
        params2.putAll(params);
        params2.put("bsFlagList", buySellSet);
        when(mockMatchService.getRealTimeMatchList(params2)).thenReturn(matches);

        TProfitBO totalTProfit = tOperationAnalyzeHandlerUnderTest.getTProfitSection(params);

    }

    @DisplayName("做T收益-全量汇总：清算完成时")
    @Test
    void testGetTotalTProfitFinished() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("startDate", ********);
        params.put("endDate", ********);

        final TProfitBO tProfitBO = new TProfitBO();
        tProfitBO.setTotalTSuccess(124L);
        tProfitBO.setTotalTTimes(258L);
        tProfitBO.setTotalTSuccessRate(0.4806);
        tProfitBO.setTotalTProfit(1628073.94);
        tProfitBO.setEndDate("9511569");

        tProfitRank tProfitRank = new tProfitRank();
        tProfitRank.setMarket("0");
        tProfitRank.setStkCode("000001");
        tProfitRank.setStkName("平安银行");
        tProfitRank.setTProfit(555.2);
        tProfitRank.setTSuccess(11);
        tProfitRank.setTSuccessRate(0.4074);
        tProfitRank.setTTimes(27);
        ArrayList<tProfitRank> tProfitRanks = Lists.newArrayList(tProfitRank);

        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);

        when(mockTProfitService.getSecTProfitRankList(params)).thenReturn(tProfitRanks);
        when(mockTProfitService.getTProfitSection(params)).thenReturn(tProfitBO);
        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("20240719");
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);
        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(buySet);

        Map<String, Object> params2 = new HashMap<>(params);
        params2.put("bsFlagList", buySellSet);
        when(mockMatchService.getRealTimeMatchList(params2)).thenReturn(matches);

        TProfitBO result = tOperationAnalyzeHandlerUnderTest.getTProfitSection(params);
        assertThat(result).isEqualTo(tProfitBO);
    }

    @DisplayName("做T收益-每日汇总：清算未完成时")
    @Test
    void testGetTProfitDayList() {
        // Setup
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("startDate", ********);
        params.put("endDate", ********);
        params.put("startNum", 0);

        // Configure TProfitDayService.getTProfitDayList(...).
        final TProfitDayDO tProfitDayDO = new TProfitDayDO();
        tProfitDayDO.setBizDate(20240718);
        tProfitDayDO.setTProfit(-1.0);
        final List<TProfitDayDO> tProfitDayDOList = Lists.newArrayList(tProfitDayDO);
        when(mockTProfitService.getTProfitDayList(params)).thenReturn(tProfitDayDOList);

        // Configure TSecProfitDayService.getTProfitDayList(...).
        final TProfitDayDO tProfitDayDO1 = new TProfitDayDO();
        tProfitDayDO1.setBizDate(********);
        tProfitDayDO1.setTProfit(10.0);
        final List<TProfitDayDO> tProfitDayDOList1 =  Lists.newArrayList(tProfitDayDO1);
        when(mockTProfitService.getTProfitDayList(new HashMap<>())).thenReturn(tProfitDayDOList1);

        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("********");
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);

        // Configure MatchService.getRealTimeMatchList(...).
        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);

        Map<String, Object> params2 = new HashMap<>(params);
        params2.put("bsFlagList", buySellSet);
        when(mockMatchService.getRealTimeMatchList(params2)).thenReturn(matches);
        // Configure HgtMatchService.getRealTimeHgtMatchList(...).

        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(buySet);
        Map<String, Object> params3 = new HashMap<>(params2);
        params3.put("pageSize", 9);
        when(mockTProfitService.getTProfitDayList(params3)).thenReturn(tProfitDayDOList);

        // Run the test
        final List<TProfitDayDO> result = tOperationAnalyzeHandlerUnderTest.getTProfitDayList(params);

    }

    @DisplayName("做T收益-每日汇总：清算完成时")
    @Test
    void testGetTProfitDayListFinished() {
        // Setup
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("startDate", ********);
        params.put("endDate", ********);
        params.put("startNum", 0);

        // Configure TProfitDayService.getTProfitDayList(...).
        final TProfitDayDO tProfitDayDO = new TProfitDayDO();
        tProfitDayDO.setBizDate(20240718);
        tProfitDayDO.setTProfit(-1.0);
        final List<TProfitDayDO> tProfitDayDOList = Lists.newArrayList(tProfitDayDO);
        when(mockTProfitService.getTProfitDayList(params)).thenReturn(tProfitDayDOList);

        // Configure TSecProfitDayService.getTProfitDayList(...).
        final TProfitDayDO tProfitDayDO1 = new TProfitDayDO();
        tProfitDayDO1.setBizDate(********);
        tProfitDayDO1.setTProfit(10.0);
        final List<TProfitDayDO> tProfitDayDOList1 =  Lists.newArrayList(tProfitDayDO1);
        when(mockTProfitService.getTProfitDayList(new HashMap<>())).thenReturn(tProfitDayDOList1);

        when(mockCoreConfigService.getServerId(************L)).thenReturn(3);
        when(mockTradeDateDao.getPreMarketDay(********)).thenReturn("********");
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(buySellSet);

        // Configure MatchService.getRealTimeMatchList(...).
        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);
        match.setMarket("0");
        match.setStkCode("000008");
        match.setTrdDate(********);
        match.setMatchAmt(194.0);

        final Match match1 = new Match();
        match1.setBsFlag("S");
        match1.setMatchPrice(1.93);
        match1.setMatchQty(100L);
        match1.setMatchTime(9481829L);
        match1.setMarket("0");
        match1.setStkCode("000008");
        match1.setTrdDate(********);
        match1.setMatchAmt(193.0);
        ArrayList<Match> matches = new ArrayList<>();
        matches.add(match);
        matches.add(match1);

        Map<String, Object> params2 = new HashMap<>(params);
        params2.put("bsFlagList", buySellSet);
        when(mockMatchService.getRealTimeMatchList(params2)).thenReturn(matches);
        // Configure HgtMatchService.getRealTimeHgtMatchList(...).

        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(buySet);
        Map<String, Object> params3 = new HashMap<>(params2);
        params3.put("pageSize", 9);
        when(mockTProfitService.getTProfitDayList(params3)).thenReturn(tProfitDayDOList);

        // Run the test
        final List<TProfitDayDO> result = tOperationAnalyzeHandlerUnderTest.getTProfitDayList(params);

    }

    @DisplayName("做T收益-每日个股收益PC端：清算未完成时")
    @Test
    void testGetTProfitDayListPC() {
        // Setup
        // Setup
        Map<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("fundId", "************");
        params.put("unit", "M");
        params.put("startDate", ********);
        params.put("endDate", ********);
        params.put("startNum", 0);

        // Configure MatchService.getRealTimeMatchList(...).
        final Match match = new Match();
        match.setBondIntr(0.0);
        match.setBankCode("bankCode");
        match.setBankBranch("bankBranch");
        match.setBankNetPlace("bankNetPlace");
        match.setSourceType("sourceType");
        match.setRecNum(0L);
        match.setBankOrderId("bankOrderId");
        match.setBankId("bankId");
        match.setExtEffectAmt(0.0);
        match.setBankRtnFlag("bankRtnFlag");
        final List<Match> matches = Arrays.asList(match);
        when(mockMatchService.getRealTimeMatchList(new HashMap<>())).thenReturn(matches);

        // Configure HgtMatchService.getRealTimeHgtMatchList(...).
        final Match match1 = new Match();
        match1.setBondIntr(0.0);
        match1.setBankCode("bankCode");
        match1.setBankBranch("bankBranch");
        match1.setBankNetPlace("bankNetPlace");
        match1.setSourceType("sourceType");
        match1.setRecNum(0L);
        match1.setBankOrderId("bankOrderId");
        match1.setBankId("bankId");
        match1.setExtEffectAmt(0.0);
        match1.setBankRtnFlag("bankRtnFlag");
        final List<Match> matches1 = Arrays.asList(match1);
        // Configure TSecProfitDayService.getTSecProfitDayList(...).
        final TSecProfitDayDO tSecProfitDayDO = new TSecProfitDayDO();
        tSecProfitDayDO.setMoneyType("moneyType");
        tSecProfitDayDO.setHoldFlag("holdFlag");
        tSecProfitDayDO.setLastMatchTime(0L);
        tSecProfitDayDO.setStkName("stkName");
        tSecProfitDayDO.setFundId(0L);
        tSecProfitDayDO.setStkCode("stkCode");
        tSecProfitDayDO.setMarket("market");
        tSecProfitDayDO.setTProfit(0.0);
        tSecProfitDayDO.setTDifferences(0.0);
        final List<TSecProfitDayDO> tSecProfitDayDOList = Arrays.asList(tSecProfitDayDO);
        when(mockTProfitService.getTSecProfitDayList(new HashMap<>())).thenReturn(tSecProfitDayDOList);

        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockStockService.getStkName("stkCode", "market")).thenReturn("result");

        // Run the test
        final List<TSecProfitDayDO> result = tOperationAnalyzeHandlerUnderTest.getTProfitDayListPC(params);

        // Verify the results
    }

    @DisplayName("做T收益-每日买卖明细PC端：清算未完成时")
    @Test
    void testGetTProfitDetailListPC() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        final Match match = new Match();
        match.setBondIntr(0.0);
        match.setBankCode("bankCode");
        match.setBankBranch("bankBranch");
        match.setBankNetPlace("bankNetPlace");
        match.setSourceType("sourceType");
        match.setRecNum(0L);
        match.setBankOrderId("bankOrderId");
        match.setBankId("bankId");
        match.setExtEffectAmt(0.0);
        match.setBankRtnFlag("bankRtnFlag");
        final List<Match> expectedResult = Arrays.asList(match);
        when(mockCoreConfigService.getServerId(0L)).thenReturn(0);
        when(mockTradeDateDao.getPreMarketDay(0)).thenReturn("result");
        when(mockNodeConfigService.getTBuySellBsFlagSet()).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure MatchService.getRealTimeMatchList(...).
        final Match match1 = new Match();
        match1.setBondIntr(0.0);
        match1.setBankCode("bankCode");
        match1.setBankBranch("bankBranch");
        match1.setBankNetPlace("bankNetPlace");
        match1.setSourceType("sourceType");
        match1.setRecNum(0L);
        match1.setBankOrderId("bankOrderId");
        match1.setBankId("bankId");
        match1.setExtEffectAmt(0.0);
        match1.setBankRtnFlag("bankRtnFlag");
        final List<Match> matches = Arrays.asList(match1);
        when(mockMatchService.getRealTimeMatchList(new HashMap<>())).thenReturn(matches);

        // Configure HgtMatchService.getRealTimeHgtMatchList(...).
        final Match match2 = new Match();
        match2.setBondIntr(0.0);
        match2.setBankCode("bankCode");
        match2.setBankBranch("bankBranch");
        match2.setBankNetPlace("bankNetPlace");
        match2.setSourceType("sourceType");
        match2.setRecNum(0L);
        match2.setBankOrderId("bankOrderId");
        match2.setBankId("bankId");
        match2.setExtEffectAmt(0.0);
        match2.setBankRtnFlag("bankRtnFlag");
        final List<Match> matches1 = Arrays.asList(match2);

        when(mockNodeConfigService.getTBuyBsFlagSet()).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure TMatchService.getTMatchList(...).
        final Match match3 = new Match();
        match3.setBondIntr(0.0);
        match3.setBankCode("bankCode");
        match3.setBankBranch("bankBranch");
        match3.setBankNetPlace("bankNetPlace");
        match3.setSourceType("sourceType");
        match3.setRecNum(0L);
        match3.setBankOrderId("bankOrderId");
        match3.setBankId("bankId");
        match3.setExtEffectAmt(0.0);
        match3.setBankRtnFlag("bankRtnFlag");
        final List<Match> matches2 = Arrays.asList(match3);
        when(mockTMatchService.getTMatchList(new HashMap<>())).thenReturn(matches2);

        // Run the test
        final List<Match> result = tOperationAnalyzeHandlerUnderTest.getSecTDetailListPC(params);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

}
