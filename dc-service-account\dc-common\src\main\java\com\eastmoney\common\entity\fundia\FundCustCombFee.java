package com.eastmoney.common.entity.fundia;

public class FundCustCombFee {
    private String investId;
    private String groupCode;
    private String feeKind;
    private Double assignFee;
    private Double paidFee;
    private Double deductFee;
    private Double notAssignFee;
    private Double feeCalDate;
    private Double createUserId;
    private Double createUserName;
    private Double editUserId;
    private Double editUserName;

    public String getInvestId() {
        return investId;
    }

    public void setInvestId(String investId) {
        this.investId = investId;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getFeeKind() {
        return feeKind;
    }

    public void setFeeKind(String feeKind) {
        this.feeKind = feeKind;
    }

    public Double getAssignFee() {
        return assignFee;
    }

    public void setAssignFee(Double assignFee) {
        this.assignFee = assignFee;
    }

    public Double getPaidFee() {
        return paidFee;
    }

    public void setPaidFee(Double paidFee) {
        this.paidFee = paidFee;
    }

    public Double getDeductFee() {
        return deductFee;
    }

    public void setDeductFee(Double deductFee) {
        this.deductFee = deductFee;
    }

    public Double getNotAssignFee() {
        return notAssignFee;
    }

    public void setNotAssignFee(Double notAssignFee) {
        this.notAssignFee = notAssignFee;
    }

    public Double getFeeCalDate() {
        return feeCalDate;
    }

    public void setFeeCalDate(Double feeCalDate) {
        this.feeCalDate = feeCalDate;
    }

    public Double getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Double createUserId) {
        this.createUserId = createUserId;
    }

    public Double getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(Double createUserName) {
        this.createUserName = createUserName;
    }

    public Double getEditUserId() {
        return editUserId;
    }

    public void setEditUserId(Double editUserId) {
        this.editUserId = editUserId;
    }

    public Double getEditUserName() {
        return editUserName;
    }

    public void setEditUserName(Double editUserName) {
        this.editUserName = editUserName;
    }
}
