package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created by robin on 2016/10/17.
 * 非正常数据记录实体
 * <AUTHOR>
 */
public class ImproperInfo extends BaseEntityCal{

    private Long fundId;//资金账号

    private String function;//功能类型 值从FunctionEnum枚举类获取

    private String detail;//非正常数据原始详情

    private String remark;//备注

    private Integer status;//状态 0-未处理 1-已处理

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
