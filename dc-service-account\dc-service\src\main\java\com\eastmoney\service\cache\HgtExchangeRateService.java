package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.tidb.HgtExchangeRateDao;
import com.eastmoney.common.entity.HgtExchangeRate;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 港股通汇率波动
 *
 * <AUTHOR>
 * @create 2022/11/20
 */
@Service
public class HgtExchangeRateService {
    private static Logger LOG = LoggerFactory.getLogger(HgtExchangeRateService.class);
    private static final String SPLIT_FLAG = "-";

    @Resource(name = "hgtExchangeRateCache")
    private LoadingCache<String, Optional<Double>> hgtExchangeRateCache;

    @Resource(name = "hgtExchangeRateDao")
    private HgtExchangeRateDao hgtExchangeRateDao;

    @Bean(name = "hgtExchangeRateCache")
    public LoadingCache<String, Optional<Double>> hgtExchangeRateCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(3, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<Double>>() {
                    @Override
                    public Optional<Double> load(String key) throws Exception {
                        try {
                            String[] splitArray = key.split(SPLIT_FLAG);
                            String market = CommonUtil.convert(splitArray[0], String.class);
                            String type = CommonUtil.convert(splitArray[1], String.class);
                            Integer bizDate = CommonUtil.convert(splitArray[2], Integer.class);

                            Map<String, Object> params = new HashMap<>();
                            params.put("market", market);
                            params.put("type", type);
                            params.put("bizDate", bizDate);

                            List<HgtExchangeRate> hgtExchangeRateList = hgtExchangeRateDao.getLastestExchangeRate(params);
                            if (hgtExchangeRateList == null || hgtExchangeRateList.size() < 2 ||
                                   !bizDate.equals(hgtExchangeRateList.get(0).getBizDate())) {
                                return Optional.empty();
                            }

                            return Optional.ofNullable(calExchangeRateRatio(hgtExchangeRateList));
                        } catch (Exception e) {
                            LOG.error(e.getMessage(), e);
                        }

                        return Optional.empty();
                    }
                });
    }

    public Double getExchangeRateRatio(Map<String, Object> params) {
        try {
            String market = CommonUtil.convert(params.get("market"), String.class);
            String type = CommonUtil.convert(params.get("type"), String.class);
            Integer bizDate = CommonUtil.convert(params.get("bizDate"), Integer.class);

            return hgtExchangeRateCache.get(market + SPLIT_FLAG + type + SPLIT_FLAG + bizDate).orElse(null);
        } catch (Exception e) {
            LOG.error("通过guava获取深港通汇率失败", e);
        }

        return null;
    }

    /**
     * 计算最新两日的港股通买入结算汇率波动
     * @param hgtExchangeRateList
     * @return
     */
    private Double calExchangeRateRatio(List<HgtExchangeRate> hgtExchangeRateList) {
        HgtExchangeRate hgtExchangeRate = hgtExchangeRateList.get(0);
        HgtExchangeRate lastHgtExchangeRate = hgtExchangeRateList.get(1);
        Double rate = hgtExchangeRate.getExchangeRate();
        Double lastRate = lastHgtExchangeRate.getExchangeRate();
        if (rate == null || lastRate == null || lastRate == 0) {
            LOG.warn("港股通结算汇率异常，bizDate:{}-rate:{}, lastBizDate:{}-lastRate:{}", hgtExchangeRate.getBizDate(),
                    rate, lastHgtExchangeRate.getBizDate(), lastRate);
            return null;
        }

        return ArithUtil.div(Math.abs(ArithUtil.sub(rate, lastRate)), lastRate);
    }
}
