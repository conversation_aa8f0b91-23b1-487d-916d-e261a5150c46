package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.tProfitRank;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;

import java.util.List;
import java.util.Map;


public interface TSecProfitDayDao extends IBaseDao<TSecProfitDayDO, Long> {

    List<tProfitRank> getSecTProfitRankList(Map<String, Object> params);

    List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params);

    TProfitBO getSecTProfitSection(Map<String, Object> params);

    List<TProfitDayDO> getTProfitDayList(Map<String, Object> params);

    List<TSecProfitDayDO> getTSecProfitDayListPC(Map<String, Object> params);
}
