package com.eastmoney.quote.app.client;



import com.eastmoney.quote.app.serializer.Packet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by 1 on 15-7-7.
 */
public class RpcClient {

    private static final Logger logger = LoggerFactory .getLogger(RpcClient.class);
    private RpcConnFactInterface connectionFactory;

    public RpcClient() {
        this.connectionFactory = new PoolableRpcConnectionFactory();
    }

    /**
     * recycle
     * @param connection
     */
    private void recycle(RpcConnection connection) {
        if (null != connection && null != connectionFactory) {
            try {
                connectionFactory.recycle(connection);
            } catch (Throwable t) {
                logger.warn("recycle rpc connection fail!", t);
            }
        }
    }

    /**
     * get connection
     * @return
     * @throws Throwable
     */
    private RpcConnection getConnection() throws Throwable {
        return connectionFactory.getConnection();
    }

    public Packet send(Packet request) throws Exception {
        RpcConnection connection = null;
        Packet response = null;
        try {
            connection = getConnection();
            response = connection.sendRequest(request);
        } catch (Throwable t) {
            logger.error("send rpc request fail! type: {} {}",
                    request.getType(), t);
            throw new RuntimeException(t);
        } finally {
            recycle(connection);
        }
        return response;
    }

}
