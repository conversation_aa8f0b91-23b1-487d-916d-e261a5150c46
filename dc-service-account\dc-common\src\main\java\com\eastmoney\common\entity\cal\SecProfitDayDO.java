package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntity;

/**
 * 个股日收益明细
 * <AUTHOR>
 * @create 2024/3/5
 */
public class SecProfitDayDO extends BaseEntity {
    private Long fundId;//资金帐号
    private String market; //市场代码
    private String stkCode; //股票代码
    private Double profit;//个股日收益
    private Double profitRate;//个股日收益率
    private Integer holdFlag;//持仓标识
    private Double openValue;//昨收价

    public Integer getHoldFlag() {
        return holdFlag;
    }

    public void setHoldFlag(Integer holdFlag) {
        this.holdFlag = holdFlag;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Double getOpenValue() {
        return openValue;
    }

    public void setOpenValue(Double openValue) {
        this.openValue = openValue;
    }
}
