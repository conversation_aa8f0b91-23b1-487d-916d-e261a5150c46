<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiTMatchMapper">
    <select id="getTMatchList" resultType="as_match">
        select * from atcenter.t_match use index(idx_t_match)
        <where>
            <if test="bizDate != null">
                and trddate = #{bizDate}
            </if>
            <if test="serverId != null">
                and serverid = #{serverId}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND stkCode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and stkCode = #{stkCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by ordersno desc
    </select>
</mapper>