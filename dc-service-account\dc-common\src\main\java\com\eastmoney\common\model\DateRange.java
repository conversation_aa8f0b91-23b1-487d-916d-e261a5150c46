package com.eastmoney.common.model;

/**
 * Created by sunyuncai on 2016/7/20.
 */
public class DateRange {
    private Integer startDate;
    private Integer endDate;

    public DateRange(Integer startDate,Integer endDate){
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }

    /**
     * 重写object的hashCode
     * 目的：重写DateRange的equal配套重写
     * <AUTHOR>
     * @date 2016/8/25
     * @return
     */
    @Override
    public int hashCode(){
        return (startDate+""+endDate).hashCode();
    }

    /**
     * 重写equal方法,当对象的起止日期相等时，这两个对象完全相等
     * <AUTHOR>
     * @date 2016/8/25
     * @param obj
     * @return
     */
    @Override
    public boolean equals(Object obj) {
        if(obj instanceof DateRange){
            DateRange dr = (DateRange) obj;
            if(this.startDate.equals(dr.getStartDate())
                    && this.endDate.equals(dr.getEndDate())){
                return true;
            }
        }
        return false;
    }
}
