package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.ZhfxSettleStatusDao;
import com.eastmoney.common.entity.cal.ZhfxSettleStatus;
import com.eastmoney.common.sysEnum.ZhfxSettleStatusEnum;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class ZhfxSettleStatusService {
    private static Logger LOG = LoggerFactory.getLogger(ZhfxSettleStatusService.class);
    private static final Integer SERVERID_ALL = -1;
    @Resource(name = "zhfxSettleStatusCache")
    private LoadingCache<Integer, Optional<ZhfxSettleStatus>> zhfxSettleStatusCache;
    @Resource(name = "zhfxSettleStatusDao")
    private ZhfxSettleStatusDao zhfxSettleStatusDao;

    @Bean(name = "zhfxSettleStatusCache")
    public LoadingCache<Integer, Optional<ZhfxSettleStatus>> ZhfxSettleStatusCache() {
        LoadingCache<Integer, Optional<ZhfxSettleStatus>> zhfxSettleStatusCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1)
                .maximumSize(6)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<Integer, Optional<ZhfxSettleStatus>>() {
                    @Override
                    public Optional<ZhfxSettleStatus> load(Integer key) throws Exception {
                        ZhfxSettleStatus initStatus = new ZhfxSettleStatus(ZhfxSettleStatusEnum.INIT.getStatus());
                        try {
                            Map<String, Object> params = new HashMap<>();
                            params.put("serverId", key);
                            List<ZhfxSettleStatus> list = zhfxSettleStatusDao.query(params);
                            if(CollectionUtils.isEmpty(list)){
                                return Optional.of(initStatus);
                            }
                            ZhfxSettleStatus zhfxSettleStatus = list.get(0);
                            //如果清算完成，赋值清算结束时间为eutime
                            if(ZhfxSettleStatusEnum.INIT.getStatus().intValue() == zhfxSettleStatus.getStatus()){
                                zhfxSettleStatus.setSettleEndTime(zhfxSettleStatus.getEuTime());
                            }
                            zhfxSettleStatus.setSettleStartTime(zhfxSettleStatus.getEiTime());
                            return Optional.ofNullable(zhfxSettleStatus);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.of(initStatus);
                    }
                });
        return zhfxSettleStatusCache;
    }

    public ZhfxSettleStatus getZhfxSettleStatus() {
        ZhfxSettleStatus initStatus = new ZhfxSettleStatus(ZhfxSettleStatusEnum.INIT.getStatus());
        try {
            return zhfxSettleStatusCache.get(SERVERID_ALL).orElse(initStatus);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取清算状态失败", e);
        }
        return initStatus;
    }
}
