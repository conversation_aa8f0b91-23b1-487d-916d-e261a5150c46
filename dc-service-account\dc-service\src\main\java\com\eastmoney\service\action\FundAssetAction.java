package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.JsonSerializeFilter;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.QryFund;
import com.eastmoney.common.entity.RealAsset;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.FundAssetHandler;
import com.eastmoney.service.serializer.RealTimeAssetFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/5/25.
 */
@Action
@Component
@RequestMapping("")
public class FundAssetAction {

    @Autowired
    private FundAssetHandler fundAssetHandler;

    /**
     * 持仓分析 - 实时资产
     * 获取总资产 总可用 总市值 当日盈亏
     *
     * @param params
     * @return
     */
    @FunCodeMapping("getRealTimeAsset")
    @RequestMapping("/getRealTimeAsset")
    @JsonSerializeFilter(RealTimeAssetFilter.class)
    public QryFund getRealTimeAsset(Map params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId","moneyType"});
        return fundAssetHandler.getRealTimeAsset(params);
    }

    /**
     * 账户表现 - 总资产走势 APPAGILE-134721
     * @param params
     * @return
     */
    @FunCodeMapping("getAssetDayTrend")
    @RequestMapping("/getAssetDayTrend")
    public List<AssetDay> getAssetDayList(Map params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "unit"});
        return fundAssetHandler.getAssetDayTrend(params);
    }

    /**
     * 账户表现 - 自定义区间总资产走势 APPAGILE-134721
     * @param params
     * @return
     */
    @FunCodeMapping("getAssetDayTrendCustomize")
    @RequestMapping("/getAssetDayTrendCustomize")
    public List<AssetDay> getAssetDayTrendCustomize(Map params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId","startDate", "endDate"});
        return fundAssetHandler.getAssetDayTrendCustomize(params);
    }

    /**
     * 资产账户等级-查询实时资产
     * 实时资产 = 集中交易资产+理财资产+基金投顾资产
     *
     * @param params
     * @return
     */
    @FunCodeMapping("getOnlyRealTimeAsset")
    @RequestMapping("/getOnlyRealTimeAsset")
    @JsonSerializeFilter(RealTimeAssetFilter.class)
    public RealAsset getOnlyRealTimeAsset(Map params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "moneyType"});
        return fundAssetHandler.getOnlyRealTimeAsset(params);
    }
}
