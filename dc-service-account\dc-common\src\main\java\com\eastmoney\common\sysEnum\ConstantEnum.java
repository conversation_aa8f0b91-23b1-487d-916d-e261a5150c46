package com.eastmoney.common.sysEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-03-28 14:29
 * <p>
 * 接口常量枚举公共类
 */
public class ConstantEnum {

    public enum ExchangeRateSource {

        TOperatePage("out", "做t分析主界面跳转入口"),

        TOperateIntroduce("in", "做t功能说明也日期控件入口"),
        ;

        private final String key;

        private final String desc;

        ExchangeRateSource(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }

        public String getKey() {
            return key;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum CalenderIsom {

        IS(1L, "开市"),

        NO(0L, "不开市"),
        ;

        private final Long key;

        private final String desc;

        CalenderIsom(Long key, String desc) {
            this.key = key;
            this.desc = desc;
        }

        public Long getKey() {
            return key;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum FestivalEnum {

        CHUN_JIE("春节", "CHUNJIE"),
        YUAN_DAN("元旦", "YUANDAN"),
        CHU_XI("除夕", "CHUXI"),
        QING_MING("清明", "QINGMING"),
        LAO_DONG("劳动节", "LAODONG"),
        DUAN_WU("端午", "DUANWU"),
        ZHONG_QIU("中秋", "ZHONGQIU"),
        GUO_QING("国庆", "GUOQING"),
        REST("休", "REST"),
        ;

        private final String key;

        private final String desc;

        FestivalEnum(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }

        public String getKey() {
            return key;
        }

        public String getDesc() {
            return desc;
        }

        public static FestivalEnum getByKey(String key) {
            for (FestivalEnum festival : FestivalEnum.values()) {
                if (festival.getKey().equals(key)) {
                    return festival;
                }
            }
            return REST;
        }
    }
}
