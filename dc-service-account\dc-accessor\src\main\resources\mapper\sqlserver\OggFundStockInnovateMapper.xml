<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggFundStockInnovateMapper">
    <resultMap id="ProResult" type="com.eastmoney.common.entity.ProResult">
    </resultMap>
    <resultMap id="QryFund" type="com.eastmoney.common.entity.QryFund">
        <result column="bb_contamt" property="bbContamt"/>
    </resultMap>
    <resultMap id="StkInfo" type="com.eastmoney.common.entity.StkInfo">
    </resultMap>
    <resultMap id="PositionInfo" type="com.eastmoney.common.entity.PositionInfo">
    </resultMap>
    <resultMap id="DoubleMap" type="double">
    </resultMap>
    <resultMap id="MatchResult" type="com.eastmoney.common.entity.LogAsset">
    </resultMap>

    <resultMap type="com.eastmoney.common.entity.PosDBItem" id="DBDataMapOld">
        <result column="fundstktype" property="fundStkType"/>
        <result column="fundid" property="fundId"/>
        <result column="orgid" property="orgId"/>
        <!--        <result column="custid" property="custid"/>-->
        <result column="moneytype" property="moneyType"/>
        <result column="fundbal" property="fundBal"/>
        <result column="fundavl" property="fundAvl"/>
        <result column="fundseq" property="fundSeq"/>
        <result column="fundbuysale" property="fundBuySale"/>
        <!--        <result column="fundbrkbuy" property="fundbrkbuy"/>-->
        <result column="funduncomebuy" property="fundUncomeBuy"/>
        <result column="funduncomesale" property="fundUncomeSale"/>
        <result column="bb_contamt" property="bbContamt"/>
        <result column="realrqamt" property="realRqAmt"/>
        <result column="fundlastbal" property="fundLastBal"/>
        <result column="fundfrz" property="fundFrz"/>
        <result column="fundbuy" property="fundBuy"/>
        <result column="fundsale" property="fundSale"/>
        <result column="fundloan" property="fundLoan"/>
        <result column="xjbfundval" property="xjbFundVal"/>
<!--                <result column="fundunfrz" property="fundunfrz"/>-->
<!--        <result column="fundtrdunfrz" property="fundtrdunfrz"/>-->
        <!--        <result column="fundtrdfrz" property="fundtrdfrz"/>-->
        <!--        <result column="fundstandby" property="fundstandby"/>-->
        <!--        <result column="fundnightfrz" property="fundnightfrz"/>-->
        <!--        <result column="fundrealbuy" property="fundrealbuy"/>-->
        <!--        <result column="fundcashpro" property="fundcashpro"/>-->
        <!--        <result column="fundavlprefrz" property="fundavlprefrz"/>-->
        <!--        <result column="exchcashlmt" property="exchcashlmt"/>-->
        <!--        <result column="fd_fund" property="fd_fund"/>-->
        <!--        <result column="fd_fundasset" property="fd_fundasset"/>-->
        <!--        <result column="punishintr" property="punishintr"/>-->
        <!--        <result column="punishamt" property="punishamt"/>-->
        <!--        <result column="fundcash" property="fundcash"/>-->
        <!--        <result column="fundcheck" property="fundcheck"/>-->
        <!--        <result column="fundremote" property="fundremote"/>-->
        <!--        <result column="fundclearsale" property="fundclearsale"/>-->
        <!--        <result column="fundclearbuy" property="fundclearbuy"/>-->
        <!--        <result column="fundbbamt" property="fundbbamt"/>-->
        <!--        <result column="fundbbuncomeamt" property="fundbbuncomeamt"/>-->
        <!--        <result column="fundbbfrzamt" property="fundbbfrzamt"/>-->
        <!--        <result column="fundprein" property="fundprein"/>-->
        <!--        <result column="fundpreout" property="fundpreout"/>-->
        <!--        <result column="lastfundclear" property="lastfundclear"/>-->
        <result column="fundassetadjamt" property="fundAssetAdjamt"/>
        <result column="stkassetadjamt" property="stkAssetAdjamt"/>
        <!--        <result column="uncommit" property="uncommit"/>-->
        <!--        <result column="uncommitsale" property="uncommitsale"/>-->
        <result column="market" property="market"/>
        <result column="secuid" property="secuId"/>
        <result column="stkcode" property="stkCode"/>
        <result column="stktype" property="stkType"/>
        <!--        <result column="stkname" property="stkname"/>-->
        <result column="mtkcalflag" property="mtkCalFlag"/>
        <result column="bondintr" property="bondIntr"/>
        <result column="ticketprice" property="ticketPrice"/>
        <result column="stkbal" property="stkBal"/>
        <result column="stkavl" property="stkAvl"/>
        <result column="buycost" property="buyCost"/>
        <result column="stkqty" property="stkQty"/>
        <!--        <result column="costprice" property="costprice"/>-->
        <result column="stkuncomebuy" property="stkUnComeBuy"/>
        <result column="stkuncomesale" property="stkUnComeSale"/>
        <result column="mktval" property="mktVal"/>
        <result column="profitcost" property="profitCost"/>
        <!--        <result column="stkdecimal" property="stkdecimal"/>-->
        <!--        <result column="profitprice" property="profitprice"/>-->
        <result column="stkbuy" property="stkBuy"/>
        <result column="stksale" property="stkSale"/>
        <result column="stkfrz" property="stkFrz"/>
        <result column="closeprice" property="closePrice"/>
        <result column="openprice" property="openPrice"/>
        <result column="lastprice" property="lastPrice"/>
        <result column="stktrdfrz" property="stkTrdFrz"/>
        <result column="stktrdunfrz" property="stkTrdUnFrz"/>
        <result column="stkdiff" property="stkDiff"/>
        <result column="stkbuysale" property="stkBuySale"/>
        <result column="lofmoneyflag" property="lofMoneyFlag"/>
        <!--        <result column="priceflag" property="priceflag"/>-->
        <result column="stklevel" property="stkLevel"/>
        <!--        <result column="buyrate" property="buyrate"/>-->
        <!--        <result column="tinc_price_a" property="tinc_price_a"/>-->
        <!--        <result column="tinc_price_b" property="tinc_price_b"/>-->
        <!--        <result column="tinc_stkqty" property="tinc_stkqty"/>-->
        <!--        <result column="tinc_income" property="tinc_income"/>-->
        <!--        <result column="tinc_rate" property="tinc_rate"/>-->
        <!--        <result column="tinc_showflag" property="tinc_showflag"/>-->
        <result column="salestkqty" property="salestkqty"/>
        <result column="f7X24zzamt" property="f7X24zzAmt"/>
        <!--        <result column="trdid" property="trdid"/>-->
        <!--        <result column="stkright" property="stkright"/>-->
        <!--        <result column="stkfc" property="stkfc"/>-->
        <!--        <result column="incclosemktval" property="incclosemktval"/>-->
        <!--        <result column="lastprofitcost" property="lastprofitcost"/>-->
        <!--        <result column="yesterdayljyk" property="yesterdayljyk"/>-->
        <!--        <result column="fundnightlongfrz" property="fundnightlongfrz"/>-->
        <!--        <result column="tomfundcashbal" property="tomfundcashbal"/>-->
        <!--        <result column="fundbank" property="fundbank"/>-->
        <!--        <result column="njmczjzcflag" property="njmczjzcflag"/>-->
        <!--        <result column="stkfullname" property="stkfullname"/>-->
        <result column="poststr" property="postStr"/>
        <!--        <result column="fundavlnightprefrz" property="fundavlnightprefrz"/>-->
        <!--        <result column="bb_contamtnew" property="bb_contamtnew"/>-->
        <!--        <result column="bb_bondassetflag" property="bb_bondassetflag"/>-->
        <!--        <result column="has_punishrate" property="has_punishrate"/>-->
        <!--        <result column="punishrate" property="punishrate"/>-->
        <!--        <result column="fundcode" property="fundcode"/>-->
        <!--        <result column="fundtranout" property="fundtranout"/>-->
        <!--        <result column="resultid5" property="resultid5"/>-->
        <!--        <result column="resultid6" property="resultid6"/>-->
        <!--        <result column="avlamt" property="avlamt"/>-->
        <!--        <result column="bblimit" property="bblimit"/>-->
        <!--        <result column="dayofyear" property="dayofyear"/>-->
        <!--        <result column="fundeffect" property="fundeffect"/>-->
        <!--        <result column="ctrlflag" property="ctrlflag"/>-->
        <!--        <result column="branch1" property="branch1"/>-->
        <!--        <result column="branch2" property="branch2"/>-->
        <!--        <result column="branch3" property="branch3"/>-->
        <!--        <result column="adj" property="adj"/>-->
        <!--        <result column="resultid8" property="resultid8"/>-->
        <!--        <result column="changedate" property="changedate"/>-->
        <!--        <result column="islimitctrl" property="islimitctrl"/>-->
        <!--        <result column="resultid2" property="resultid2"/>-->
        <result column="maxdraw" property="maxDraw"/>
        <result column="fundall" property="fundAll"/>
        <result column="assetadjamt" property="assetAdjAmt"/>
        <!--        <result column="mixed1002080" property="mixed1002080"/>-->
        <result column="buystkqty" property="buystkqty"/>
        <result column="salestkqty" property="salestkqty"/>
        <result column="buymatchamt" property="buyMatchAmt"/>
        <result column="salematchamt" property="saleMatchAmt"/>
        <result column="settrate" property="settRate"/>
        <result column="salesettrate" property="saleSettRate"/>
        <result column="quitdate" property="quitDate"/>
    </resultMap>


    <resultMap type="com.eastmoney.common.entity.PosDBItem" id="DBDataMap">
        <result column="fundstktype" property="fundStkType"/>
        <result column="custid" property="custId"/>
        <result column="fundid" property="fundId"/>
        <result column="orgid" property="orgId"/>
        <!--        <result column="custid" property="custid"/>-->
        <result column="moneytype" property="moneyType"/>
        <result column="fundbal" property="fundBal"/>
        <result column="fundavl" property="fundAvl"/>
        <result column="fundseq" property="fundSeq"/>
        <result column="fundbuysale" property="fundBuySale"/>
        <!--        <result column="fundbrkbuy" property="fundbrkbuy"/>-->
        <result column="funduncomebuy" property="fundUncomeBuy"/>
        <result column="funduncomesale" property="fundUncomeSale"/>
        <result column="bb_contamt" property="bbContamt"/>
        <result column="realrqamt" property="realRqAmt"/>
        <result column="fundlastbal" property="fundLastBal"/>
        <result column="fundfrz" property="fundFrz"/>
        <result column="fundbuy" property="fundBuy"/>
        <result column="fundsale" property="fundSale"/>
        <result column="fundloan" property="fundLoan"/>
        <result column="xjbfundval" property="xjbFundVal"/>
<!--                <result column="fundunfrz" property="fundunfrz"/>-->
<!--        <result column="fundtrdunfrz" property="fundtrdunfrz"/>-->
        <!--        <result column="fundtrdfrz" property="fundtrdfrz"/>-->
        <!--        <result column="fundstandby" property="fundstandby"/>-->
        <!--        <result column="fundnightfrz" property="fundnightfrz"/>-->
        <!--        <result column="fundrealbuy" property="fundrealbuy"/>-->
        <!--        <result column="fundcashpro" property="fundcashpro"/>-->
        <!--        <result column="fundavlprefrz" property="fundavlprefrz"/>-->
        <!--        <result column="exchcashlmt" property="exchcashlmt"/>-->
        <!--        <result column="fd_fund" property="fd_fund"/>-->
        <!--        <result column="fd_fundasset" property="fd_fundasset"/>-->
        <!--        <result column="punishintr" property="punishintr"/>-->
        <!--        <result column="punishamt" property="punishamt"/>-->
        <!--        <result column="fundcash" property="fundcash"/>-->
        <!--        <result column="fundcheck" property="fundcheck"/>-->
        <!--        <result column="fundremote" property="fundremote"/>-->
        <!--        <result column="fundclearsale" property="fundclearsale"/>-->
        <!--        <result column="fundclearbuy" property="fundclearbuy"/>-->
        <!--        <result column="fundbbamt" property="fundbbamt"/>-->
        <!--        <result column="fundbbuncomeamt" property="fundbbuncomeamt"/>-->
        <!--        <result column="fundbbfrzamt" property="fundbbfrzamt"/>-->
        <!--        <result column="fundprein" property="fundprein"/>-->
        <!--        <result column="fundpreout" property="fundpreout"/>-->
        <!--        <result column="lastfundclear" property="lastfundclear"/>-->
         <result column="fundassetadjamt" property="fundAssetAdjamt"/>
         <result column="stkassetadjamt" property="stkAssetAdjamt"/>
        <!--        <result column="uncommit" property="uncommit"/>-->
        <!--        <result column="uncommitsale" property="uncommitsale"/>-->
        <result column="market" property="market"/>
        <result column="secuid" property="secuId"/>
        <result column="stkcode" property="stkCode"/>
        <result column="stktype" property="stkType"/>
        <!--        <result column="stkname" property="stkname"/>-->
        <result column="mtkcalflag" property="mtkCalFlag"/>
        <result column="bondintr" property="bondIntr"/>
        <result column="ticketprice" property="ticketPrice"/>
        <result column="stkbal" property="stkBal"/>
        <result column="stkavl" property="stkAvl"/>
        <result column="buycost" property="buyCost"/>
        <result column="stkqty" property="stkQty"/>
<!--        <result column="costprice" property="costprice"/>-->
        <result column="stkuncomebuy" property="stkUnComeBuy"/>
        <result column="stkuncomesale" property="stkUnComeSale"/>
        <result column="mktval" property="mktVal"/>
        <result column="profitcost" property="profitCost"/>
<!--        <result column="stkdecimal" property="stkdecimal"/>-->
<!--        <result column="profitprice" property="profitprice"/>-->
        <result column="stkbuy" property="stkBuy"/>
        <result column="stksale" property="stkSale"/>
        <result column="stkfrz" property="stkFrz"/>
        <result column="closeprice" property="closePrice"/>
        <result column="openprice" property="openPrice"/>
        <result column="lastprice" property="lastPrice"/>
        <result column="stktrdfrz" property="stkTrdFrz"/>
        <result column="stktrdunfrz" property="stkTrdUnFrz"/>
        <result column="stkdiff" property="stkDiff"/>
        <result column="stkbuysale" property="stkBuySale"/>
        <result column="lofmoneyflag" property="lofMoneyFlag"/>
        <!--        <result column="priceflag" property="priceflag"/>-->
        <result column="stklevel" property="stkLevel"/>
        <!--        <result column="buyrate" property="buyrate"/>-->
        <!--        <result column="tinc_price_a" property="tinc_price_a"/>-->
        <!--        <result column="tinc_price_b" property="tinc_price_b"/>-->
        <!--        <result column="tinc_stkqty" property="tinc_stkqty"/>-->
        <!--        <result column="tinc_income" property="tinc_income"/>-->
        <!--        <result column="tinc_rate" property="tinc_rate"/>-->
<!--        <result column="tinc_showflag" property="tinc_showflag"/>-->
        <result column="salestkqty" property="salestkqty"/>
        <result column="f7X24zzamt" property="f7X24zzAmt"/>
        <!--        <result column="trdid" property="trdid"/>-->
        <!--        <result column="stkright" property="stkright"/>-->
        <!--        <result column="stkfc" property="stkfc"/>-->
        <!--        <result column="incclosemktval" property="incclosemktval"/>-->
<!--        <result column="lastprofitcost" property="lastprofitcost"/>-->
        <!--        <result column="yesterdayljyk" property="yesterdayljyk"/>-->
        <!--        <result column="fundnightlongfrz" property="fundnightlongfrz"/>-->
        <!--        <result column="tomfundcashbal" property="tomfundcashbal"/>-->
        <!--        <result column="fundbank" property="fundbank"/>-->
        <!--        <result column="njmczjzcflag" property="njmczjzcflag"/>-->
        <!--        <result column="stkfullname" property="stkfullname"/>-->
        <result column="poststr" property="postStr"/>
        <!--        <result column="fundavlnightprefrz" property="fundavlnightprefrz"/>-->
<!--        <result column="bb_contamtnew" property="bb_contamtnew"/>-->
        <!--        <result column="bb_bondassetflag" property="bb_bondassetflag"/>-->
        <!--        <result column="has_punishrate" property="has_punishrate"/>-->
        <!--        <result column="punishrate" property="punishrate"/>-->
        <!--        <result column="fundcode" property="fundcode"/>-->
        <!--        <result column="fundtranout" property="fundtranout"/>-->
        <!--        <result column="resultid5" property="resultid5"/>-->
        <!--        <result column="resultid6" property="resultid6"/>-->
        <!--        <result column="avlamt" property="avlamt"/>-->
        <!--        <result column="bblimit" property="bblimit"/>-->
        <!--        <result column="dayofyear" property="dayofyear"/>-->
        <!--        <result column="fundeffect" property="fundeffect"/>-->
        <!--        <result column="ctrlflag" property="ctrlflag"/>-->
        <!--        <result column="branch1" property="branch1"/>-->
        <!--        <result column="branch2" property="branch2"/>-->
        <!--        <result column="branch3" property="branch3"/>-->
        <!--        <result column="adj" property="adj"/>-->
        <!--        <result column="resultid8" property="resultid8"/>-->
        <!--        <result column="changedate" property="changedate"/>-->
        <!--        <result column="islimitctrl" property="islimitctrl"/>-->
        <!--        <result column="resultid2" property="resultid2"/>-->
        <result column="maxdraw" property="maxDraw"/>
        <result column="fundall" property="fundAll"/>
        <result column="assetadjamt" property="assetAdjAmt"/>
<!--        <result column="mixed1002080" property="mixed1002080"/>-->
        <result column="buystkqty" property="buystkqty"/>
        <result column="salestkqty" property="salestkqty"/>
        <result column="buymatchamt" property="buyMatchAmt"/>
        <result column="salematchamt" property="saleMatchAmt"/>
        <result column="settrate" property="settRate"/>
        <result column="salesettrate" property="saleSettRate"/>
        <result column="quitdate" property="quitDate"/>
        <result column="totalbuyqty" property="totalBuyQty"/>
        <result column="totalbuyamt" property="totalBuyAmt"/>
        <result column="totalsaleqty" property="totalSaleQty"/>
        <result column="totalsaleamt" property="totalSaleAmt"/>
    </resultMap>

    <resultMap id="MapResult" type="java.util.HashMap">
    </resultMap>

    <select id="queryOld" resultMap="ProResult,DBDataMapOld,DBDataMapOld,DBDataMapOld,DBDataMapOld,DBDataMapOld" parameterType="map" statementType="CALLABLE" resultType="java.util.List">
        {call run.dbo.up_xzperqryfund_stk_maxdraw_notp(
                #{orgId, jdbcType=VARCHAR, mode=IN},
                #{custId, jdbcType=VARCHAR, mode=IN},
                #{fundId, jdbcType=VARCHAR, mode=IN},
                #{moneyType, jdbcType=VARCHAR, mode=IN},
                #{market, jdbcType=VARCHAR, mode=IN},
                #{secuId, jdbcType=VARCHAR, mode=IN},
                #{stkCode, jdbcType=VARCHAR, mode=IN},
                #{qryFlag, jdbcType=VARCHAR, mode=IN},
                #{postStr, jdbcType=VARCHAR, mode=IN},
                #{funcId, jdbcType=NUMERIC, mode=IN},
                #{bsFlag, jdbcType=VARCHAR, mode=IN},
                #{nCount, jdbcType=NUMERIC, mode=IN},
                #{cEmckykzlkg, jdbcType=VARCHAR, mode=IN},
                #{kqFlag, jdbcType=VARCHAR, mode=IN}
              )
            }
    </select>

    <select id="query" resultMap="ProResult,DBDataMap,DBDataMap,DBDataMap,DBDataMap,DBDataMap" parameterType="map" statementType="CALLABLE" resultType="java.util.List">
        {call run.dbo.up_xzperqryfund_stk_maxdraw_notpnew1(
                #{orgId, jdbcType=VARCHAR, mode=IN},
                #{custId, jdbcType=VARCHAR, mode=IN},
                #{fundId, jdbcType=VARCHAR, mode=IN},
                #{moneyType, jdbcType=VARCHAR, mode=IN},
                #{market, jdbcType=VARCHAR, mode=IN},
                #{secuId, jdbcType=VARCHAR, mode=IN},
                #{stkCode, jdbcType=VARCHAR, mode=IN},
                #{qryFlag, jdbcType=VARCHAR, mode=IN},
                #{postStr, jdbcType=VARCHAR, mode=IN},
                #{funcId, jdbcType=NUMERIC, mode=IN},
                #{bsFlag, jdbcType=VARCHAR, mode=IN},
                #{nCount, jdbcType=NUMERIC, mode=IN},
                #{cEmckykzlkg, jdbcType=VARCHAR, mode=IN},
                #{kqFlag, jdbcType=VARCHAR, mode=IN}
            )
        }
    </select>
</mapper>