package com.eastmoney.service.service.asset;

import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.CommonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Created on 2020/8/13-14:38.
 * 处理跨周期判断
 *
 * <AUTHOR>
 */
@Service("assetSectionServiceFacade")
public class AssetSectionServiceFacade {

    @Resource(name = "assetSectionServiceAll")
    private AssetSectionService assetSectionServiceAll;
    @Resource(name = "assetSectionServiceCross")
    private AssetSectionService assetSectionServiceCross;
    @Resource(name = "assetSectionServiceNotCross")
    private AssetSectionService assetSectionServiceNotCross;
    @Resource(name = "assetSectionServiceDay")
    private AssetSectionService assetSectionServiceDay;

    public AssetSection getAssetSection(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        AssetSectionService assetSectionService = null;
        if (DateUnitEnum.ALL.getValue().equals(unit)) {
            assetSectionService = assetSectionServiceAll;
        } else if (DateUnitEnum.MONTH.getValue().equals(unit)
                || DateUnitEnum.WEEK.getValue().equals(unit)
                || DateUnitEnum.YEAR.getValue().equals(unit)) {
            assetSectionService = assetSectionServiceCross;
        } else if (DateUnitEnum.PAST_HALF_YEAR.getValue().equals(unit)
                || DateUnitEnum.PAST_ONE_YEAR.getValue().equals(unit)) {
            assetSectionService = assetSectionServiceNotCross;
        } else if(DateUnitEnum.DAY.getValue().equals(unit)){
            assetSectionService = assetSectionServiceDay;
        }
        if (assetSectionService != null) {
            AssetSection assetSection = assetSectionService.getAssetSection(params);
            if (assetSection != null) {
                assetSection.setUnit(unit);
            }
            return assetSection;
        }
        return null;
    }
}
