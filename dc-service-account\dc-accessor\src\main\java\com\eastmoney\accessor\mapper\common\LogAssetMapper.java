package com.eastmoney.accessor.mapper.common;

import com.eastmoney.common.entity.LogAsset;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/1/24.
 */
public interface LogAssetMapper {

    /**
     * 查询历史交易数据
     * @param queryParam
     * @return
     */
    List<LogAsset> getMatchHis(Map<String, Object> queryParam);
    /**
     * 交割单查询
     */
    List<LogAsset> getPromptNote(Map<String, Object> queryParam);

    /**
     * 历史资金流水查询
     */
    List<LogAsset> getCheckNoteNew(Map<String, Object> queryParam);

    /**
     * 单日成交查询
     */
    List getMatchRun(Map<String, Object> queryParam);
    List getMatchRunVtis(Map<String, Object> queryParam);

    /**
     * 查询盘中银证划转、台账间划转记录
     *
     * @param queryParam
     * @return
     */
    List<LogAsset> selectTransferAmt(Map<String,Object> queryParam);

    /**
     * 盘中交割单数据查询
     * @param queryParam
     * @return
     */
    List<LogAsset> getRealTimeLogassetList(Map<String,Object> queryParam);
}
