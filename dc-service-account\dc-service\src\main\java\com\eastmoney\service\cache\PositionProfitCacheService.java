package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.PositionProfitDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.PositionProfit;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class PositionProfitCacheService {
    private static Logger LOG = LoggerFactory.getLogger(PositionProfitCacheService.class);
    private static final String splitFlag = "-";
    @Resource(name = "positionProfitCache")
    private LoadingCache<String, Optional<List<PositionProfit>>> positionProfitCache;
    @Autowired
    private PositionProfitDao positionProfitDao;


    @Bean(name = "positionProfitCache")
    public LoadingCache<String, Optional<List<PositionProfit>>> positionProfitCache() {
        LoadingCache<String, Optional<List<PositionProfit>>> positionProfitCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(100000)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<List<PositionProfit>>>() {
                    @Override
                    public Optional<List<PositionProfit>> load(String key) throws Exception {
                        List<PositionProfit> positionProfitList = null;
                        try {
                            Map<String, Object> param = keyToMap(key);
                            positionProfitList = positionProfitDao.getPositionMergeList(param);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(positionProfitList);
                    }
                });
        return positionProfitCache;
    }

    public List<PositionProfit> getPositionMergeList(Map<String, Object> params) {
        try {
            return positionProfitCache.get(getKey(params)).orElse(new ArrayList<>());
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取持仓收益失败", e);
        }
        return null;
    }

    @RedisCache(keyGenerator = "'jzjy_clearposition_' + #params.get('fundId') + '_' + #params.get('unit')+ '_' + #params.get('profitFlag')")
    public List<PositionProfit> getPositionMergeListA(Map<String, Object> params) {
        try {
            return positionProfitCache.get(getKey(params)).orElse(new ArrayList<>());
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取持仓收益失败", e);
        }
        return null;
    }

    private String getKey(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String startDate = CommonUtil.convert(params.get("startDate"), String.class);
        String endDate = CommonUtil.convert(params.get("endDate"), String.class);
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        String sortFlag = CommonUtil.convert(params.get("sortFlag"), String.class);
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);
        String profitFlag = CommonUtil.convert(params.get("profitFlag"), String.class);
        Integer startNum = pageSize * (pageNo - 1);

        return fundId + splitFlag +
                startDate + splitFlag +
                endDate + splitFlag +
                pageSize + splitFlag +
                pageNo + splitFlag +
                sortFlag + splitFlag +
                orderFlag + splitFlag +
                profitFlag + splitFlag +
                startNum;
    }

    private Map<String, Object> keyToMap(String key) {
        Map<String, Object> params = new HashMap<>();
        String[] splitArray = key.split(splitFlag);
        Long fundId = CommonUtil.convert(splitArray[0], Long.class);
        params.put("fundId", fundId);
        String startDate = CommonUtil.convert(splitArray[1], String.class);
        params.put("startDate", startDate);
        String endDate = CommonUtil.convert(splitArray[2], String.class);
        params.put("endDate", endDate);
        Integer pageSize = CommonUtil.convert(splitArray[3], Integer.class);
        params.put("pageSize", pageSize);
        Integer pageNo = CommonUtil.convert(splitArray[4], Integer.class);
        params.put("pageNo", pageNo);
        String sortFlag = CommonUtil.convert(splitArray[5], String.class);
        params.put("sortFlag", sortFlag);
        String orderFlag = CommonUtil.convert(splitArray[6], String.class);
        params.put("orderFlag", orderFlag);
        String profitFlag = CommonUtil.convert(splitArray[7], String.class);
        params.put("profitFlag", profitFlag);
        Integer startNum = CommonUtil.convert(splitArray[8], Integer.class);
        params.put("startNum", startNum);
        specifiSort(params);
        return params;
    }

    //指定具体的查询排序方式
    private void specifiSort(Map<String, Object> params) {
        String sortFlag = CommonUtil.convert(params.get("sortFlag"), String.class);
        String orderFlag = CommonUtil.convert(params.get("orderFlag"), String.class);
        String profitFlag = CommonUtil.convert(params.get("profitFlag"), String.class);

        String sort = "";
        // 老版  盈亏字段排序 profitTotal
        if ("1".equals(sortFlag)) {
            if ("1".equals(profitFlag)) {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                }
            } else if ("2".equals(profitFlag)) {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                }
            } else {
                if ("1".equals(orderFlag)) {
                    sort = "order by profitTotal ASC,holdDays ASC,stkCode desc";
                } else if ("2".equals(orderFlag)) {
                    sort = "order by profitTotal DESC,holdDays ASC,stkCode desc";
                }
            }

        }

        // 老版  持仓天数排序 holdDays
        if ("2".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by holdDays asc,profitTotal desc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by holdDays desc,profitTotal desc,stkCode desc";
            }
        }

        // 老版  买卖次数排序 profitTotal
        if ("3".equals(sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by tradeTotal asc,profitTotal desc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by tradeTotal DESC,profitTotal desc,stkCode desc";
            }
        }

        //新版 基于清仓日期
        if (Objects.equals("4", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by ENDDATE ASC,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by ENDDATE DESC,stkCode desc";
            }
        }

        //新版 收益率
        if (Objects.equals("5", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by PROFIT_RATE asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by PROFIT_RATE desc,stkCode desc";
            }
        }

        //新版 持股天数     todo del  stkCode
        if (Objects.equals("6", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by HOLDDAYS asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by HOLDDAYS desc,stkCode desc";
            }
        }

        //新版 收益
        if (Objects.equals("7", sortFlag)) {
            if ("1".equals(orderFlag)) {
                sort = "order by PROFIT asc,stkCode desc";
            } else if ("2".equals(orderFlag)) {
                sort = "order by PROFIT desc,stkCode desc";
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            params.put("sort", sort);
        }
    }
}
