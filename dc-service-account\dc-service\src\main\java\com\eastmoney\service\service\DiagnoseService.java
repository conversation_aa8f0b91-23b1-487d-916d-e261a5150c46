package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.oracle.PositionProfitDao;
import com.eastmoney.accessor.dao.tidb.DiagnoseAvgDao;
import com.eastmoney.accessor.dao.tidb.DiagnoseDao;
import com.eastmoney.accessor.enums.DiagnoseStockEnum;
import com.eastmoney.common.entity.PositionInfo;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.entity.cal.AcctDiagnoseBO.IndPct;
import com.eastmoney.common.entity.cal.AcctDiagnoseBO.StkTopLabel;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.service.asset.base.AssetServiceRealTimeImpl;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created on 2024-05-07
 * Description
 *
 * <AUTHOR>
 */
@Service
public class DiagnoseService {

    private static final Logger LOG = LoggerFactory.getLogger(DiagnoseService.class);

    @Autowired
    private AssetServiceRealTimeImpl assetServiceRealTime;
    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;
    @Autowired
    private PositionProfitDao positionProfitDao;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    DiagnoseDao diagnoseDao;
    @Autowired
    DiagnoseAvgDao diagnoseAvgDao;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;
    @Resource(name = "acctDiagnoseCache")
    private LoadingCache<Long, AcctDiagnoseBO> acctDiagnoseCache;

    @Bean(name = "acctDiagnoseCache")
    public LoadingCache<Long, AcctDiagnoseBO> acctDiagnoseCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1000)
                .maximumSize(10000)
                .refreshAfterWrite(3, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, AcctDiagnoseBO>() {
                    public AcctDiagnoseBO load(Long key) {
                        return queryDiagnoseProcess(key);
                    }
                });
    }

    public AcctDiagnoseBO getAcctUserDiagnose(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        AcctDiagnoseBO acctDiagnoseBO = new AcctDiagnoseBO();
        try {
            acctDiagnoseBO = acctDiagnoseCache.get(fundId);
            if(AcctDiagnoseBO.isAvailable(acctDiagnoseBO)) {
                //个人数据处理
                diagnoseProcessPersonal(acctDiagnoseBO, params);
                //实时股票处理
                extStockAbility(acctDiagnoseBO, params);
            }
            return acctDiagnoseBO;
        } catch (Exception e) {
            LOG.error("查询诊断表数据异常 fundId={}", fundId, e);
        }
        return acctDiagnoseBO;
    }

    public AcctDiagnoseBO getFiveDimensionDiagnose(Map<String, Object> params){
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        AcctDiagnoseBO acctDiagnoseBO = new AcctDiagnoseBO();
        try {
            acctDiagnoseBO = acctDiagnoseCache.get(fundId);
            return acctDiagnoseBO;
        } catch (Exception e) {
            LOG.error("查询诊断表数据异常 fundId={}", fundId, e);
        }
        return acctDiagnoseBO;
    }

    public AcctDiagnoseBO queryDiagnoseProcess(Long fundId) {
        AcctDiagnoseBO acctDiagnoseBO = new AcctDiagnoseBO();
        AcctDiagnoseDO acctDiagnoseDO = new AcctDiagnoseDO();
        AcctDiagnoseAvgDO acctDiagnoseAvgDO = new AcctDiagnoseAvgDO();
        Map<String, Object> params = new HashMap<>(1);
        params.put("fundId", fundId);
        List<AcctDiagnoseDO> result = diagnoseDao.query(params);
        List<AcctDiagnoseAvgDO> resultAvg = diagnoseAvgDao.query(params);
        if (!CollectionUtils.isEmpty(result)) {
            acctDiagnoseDO = result.get(0);
        }
        if (!CollectionUtils.isEmpty(resultAvg)) {
            acctDiagnoseAvgDO = resultAvg.get(0);
        }
        //通用数据处理
        diagnoseProcess(acctDiagnoseBO, acctDiagnoseDO, acctDiagnoseAvgDO);
        return acctDiagnoseBO;
    }

    /**
     * 诊断报告预处理,包含数据提供标签,清算状态,起止日期的处理
     * @param acctDiagnoseBO BO对象
     * @param acctDiagnoseDO DO对象
     * @param acctDiagnoseAvgDO  AVG DO对象
     */
    public void diagnoseProcess(AcctDiagnoseBO acctDiagnoseBO, AcctDiagnoseDO acctDiagnoseDO, AcctDiagnoseAvgDO acctDiagnoseAvgDO) {
        //DO to BO
        acctDiagnoseBO.combineOf(acctDiagnoseDO,acctDiagnoseAvgDO);
        //数据更新时间戳
        String bizTime = acctDiagnoseAvgDO.getBizTime();
        Integer todayOrYesterday = isTodayOrYesterday(bizTime);
        acctDiagnoseBO.setEndFlag(todayOrYesterday);
        acctDiagnoseBO.setBizTime(bizTime);
        //获取账户诊断清算完成时间
        Integer bizDate = Optional.ofNullable(acctDiagnoseAvgDO.getBizDate()).orElse(DateUtil.getCuryyyyMMddInteger());
        //设置起止时间(起止时间与账号解绑,适配空白状态场景下的返回)
        DateRange dateRange = CommonUtil.getDateRange(bizDate, DateUnitEnum.PAST_ONE_YEAR.getValue());
        acctDiagnoseBO.setStartDate(dateRange.getStartDate());
        acctDiagnoseBO.setEndDate(bizDate);
        //处理分数差值
        calScoreDiff(acctDiagnoseBO, acctDiagnoseDO);
        //行业配置字符串解析
        extInvestAbility(acctDiagnoseBO, acctDiagnoseDO);
    }

    public void diagnoseProcessPersonal(AcctDiagnoseBO acctDiagnoseBO, Map<String, Object> params) {
        //资产起始日,仅用于内部计算真实收益起始点
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if(Objects.nonNull(assetNew)){
            Integer assertStartDate = Math.max(assetNew.getStartDate(), acctDiagnoseBO.getStartDate());
            acctDiagnoseBO.setAssetStartDate(assertStartDate);
            //盈利能力额外处理
            extProfitAbility(acctDiagnoseBO, params);
            //择时能力额外处理
            extPickAbility(acctDiagnoseBO, params);
        }
    }

    /**
     * 盈利能力(沪深300指数及超越幅度由中台处理)
     *
     * @param acctDiagnoseBO acctDiagnoseBO
     * @param params params
     */
    public void extProfitAbility(AcctDiagnoseBO acctDiagnoseBO, Map<String, Object> params) {
        Map<String, Object> paramsCopy = new HashMap<>(params);
        paramsCopy.putIfAbsent("unit", DateUnitEnum.PAST_ONE_YEAR.getValue());
        //T-1日区间收益
        ProfitSection profitSection = profitSectionCacheService.getProfitSection(paramsCopy);
        ProfitRateSection profitRateSection = profitSectionCacheService.getProfitRateSection(paramsCopy);
        acctDiagnoseBO.setProfit(profitSection.getProfit());
        acctDiagnoseBO.setProfitRate(profitRateSection.getProfitRate());
    }

    /**
     * 股票能力
     *
     * @param acctDiagnoseBO acctDiagnoseBO
     * @param params params
     */
    public void extStockAbility(AcctDiagnoseBO acctDiagnoseBO, Map<String, Object> params) {
        //获取实时持仓
        List<PositionInfo> positionInfo = assetServiceRealTime.queryRealTimePositionSimple(params);
        positionInfo = positionInfo.stream().filter(o ->
                        Objects.nonNull(o) &&
                        Objects.nonNull(o.getIncome()) &&
                        Objects.nonNull(o.getPositionRate()) &&
                        Objects.nonNull(o.getMktVal()) &&
                        Objects.nonNull(o.getHoldDays()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(positionInfo))
            return ;
        List<StkTopLabel> stkTopLabels = new ArrayList<>();
        //盈利最大的股票
        PositionInfo topProfitStock = positionInfo.stream().max(Comparator.comparingDouble(PositionInfo::getIncome)).orElse(new PositionInfo());
        stkTopLabels.add(new StkTopLabel(topProfitStock.getStkName(), topProfitStock.getStkCode(),
                topProfitStock.getMarket(), DiagnoseStockEnum.TOP_PROFIT.getType()));
        //盈利最小的股票
        PositionInfo lowProfitStock = positionInfo.stream().min(Comparator.comparingDouble(PositionInfo::getIncome)).orElse(new PositionInfo());
        stkTopLabels.add(new StkTopLabel(lowProfitStock.getStkName(), lowProfitStock.getStkCode(),
                lowProfitStock.getMarket(), DiagnoseStockEnum.LOWEST_PROFIT.getType()));
        //仓位最大的股票
        PositionInfo topPositionStock = positionInfo.stream().max(Comparator.comparingDouble(PositionInfo::getPositionRate)).orElse(new PositionInfo());
        stkTopLabels.add(new StkTopLabel(topPositionStock.getStkName(), topPositionStock.getStkCode(),
                topPositionStock.getMarket(), DiagnoseStockEnum.TOP_POSITION.getType()));
        //市值最大的股票
        PositionInfo topMktValStock = positionInfo.stream().max(Comparator.comparingDouble(PositionInfo::getMktVal)).orElse(new PositionInfo());
        stkTopLabels.add(new StkTopLabel(topMktValStock.getStkName(), topMktValStock.getStkCode(),
                topMktValStock.getMarket(), DiagnoseStockEnum.TOP_MKTVAL.getType()));
        //持仓最久的股票
        PositionInfo topHoldDayStock = positionInfo.stream().max(Comparator.comparingDouble(PositionInfo::getHoldDays)).orElse(new PositionInfo());
        stkTopLabels.add(new StkTopLabel(topHoldDayStock.getStkName(), topHoldDayStock.getStkCode(),
                topHoldDayStock.getMarket(), DiagnoseStockEnum.TOP_HOLDDAYS.getType()));
        //去重判空
        HashSet<String> duplicateStock = new HashSet<>();
        ArrayList<StkTopLabel> stkTopResult = new ArrayList<>();
        for(StkTopLabel stk:stkTopLabels){
            if(Objects.isNull(stk) || StringUtils.isEmpty(stk.getStkCode()) || StringUtils.isEmpty(stk.getMarket())) {
                continue;
            }
            String key = stk.getStkCode() + "_" + stk.getMarket();
            if(!duplicateStock.contains(key)){
                duplicateStock.add(key);

                //北交所行情适配
                stk.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(stk.getStkCode(), stk.getMarket()));
                stkTopResult.add(stk);
            }
        }
        //赋值返回
        acctDiagnoseBO.setCurHoldStkList(stkTopResult);
    }

    /**
     * 择时能力
     *
     * @param acctDiagnoseBO acctDiagnoseBO
     * @param params params
     */
    public void extPickAbility(AcctDiagnoseBO acctDiagnoseBO, Map<String, Object> params) {
        Integer startDate = acctDiagnoseBO.getStartDate();
        Integer endDate = acctDiagnoseBO.getEndDate();
        Map<String, Object> paramsCopy = new HashMap<>(params);
        paramsCopy.putIfAbsent("startDate", startDate);
        paramsCopy.putIfAbsent("endDate", endDate);
        PositionProfit clearTimesWithAvgHoldDays = positionProfitDao.getDiagnosePositionSection(paramsCopy);
        Integer clearTimes = clearTimesWithAvgHoldDays.getClearTimes(); //无清仓则为0
        Integer holdDays = clearTimesWithAvgHoldDays.getHoldDays(); //无清仓则为null
        if (clearTimes >= CommConstants.MIN_CLEAR_TIMES) {
            Integer avgHoldDays = (int) Math.round(ArithUtil.div(holdDays, clearTimes));
            acctDiagnoseBO.setClearTimes(clearTimes);
            acctDiagnoseBO.setAvgHoldDays(avgHoldDays);
            acctDiagnoseBO.setInvestStyle(avgHoldDays <= CommConstants.MIN_AVG_HOLD_DAYS ? "S" : "L");
        }
    }

    /**
     * 行业配置能力
     * 约定格式：name,percent;name,percent;name,percent; 不定长
     */
    public void extInvestAbility(AcctDiagnoseBO acctDiagnoseBO, AcctDiagnoseDO acctDiagnoseDO) {
        List<IndPct> indPct = new ArrayList<>();
        String investLabels = acctDiagnoseDO.getPreferIndList();
        String[] investLabelSplit = Optional.ofNullable(investLabels).orElse("").split(";");
        for (String invest : investLabelSplit) {
            if (StringUtils.isEmpty(invest))
                continue;
            String[] investSplit = invest.split(",");
            if (investSplit.length >= 2) {
                String labelName = investSplit[0];
                Double labelPercent =  CommonUtil.convert(investSplit[1],Double.class);
                indPct.add(new IndPct(labelName, labelPercent));
            }
        }
        indPct.sort(Comparator.comparingDouble(IndPct::getPercent).reversed());
        if(!CollectionUtils.isEmpty(indPct)){
            acctDiagnoseBO.setPreferIndPctList(indPct);
        }
    }

    private void calScoreDiff(AcctDiagnoseBO acctDiagnoseBO, AcctDiagnoseDO acctDiagnoseDO) {
        Integer totalDiff = ArithUtil.sub(acctDiagnoseDO.getTotalScore(), acctDiagnoseDO.getTotalScorePre()).intValue();
        acctDiagnoseBO.setTotalScoreDiff(totalDiff);
        Integer profitDiff = ArithUtil.sub(acctDiagnoseDO.getProfitScore(), acctDiagnoseDO.getProfitScorePre()).intValue();
        acctDiagnoseBO.setProfitScoreDiff(profitDiff);
        Integer riskDiff = ArithUtil.sub(acctDiagnoseDO.getRiskScore(), acctDiagnoseDO.getRiskScorePre()).intValue();
        acctDiagnoseBO.setRiskScoreDiff(riskDiff);
        Integer holdDiff = ArithUtil.sub(acctDiagnoseDO.getHoldScore(), acctDiagnoseDO.getHoldScorePre()).intValue();
        acctDiagnoseBO.setHoldScoreDiff(holdDiff);
        Integer tradeDiff = ArithUtil.sub(acctDiagnoseDO.getTradeScore(), acctDiagnoseDO.getTradeScorePre()).intValue();
        acctDiagnoseBO.setTradeScoreDiff(tradeDiff);
        Integer allocateDiff = ArithUtil.sub(acctDiagnoseDO.getIndAllocScore(), acctDiagnoseDO.getIndAllocScorePre()).intValue();
        acctDiagnoseBO.setIndAllocScoreDiff(allocateDiff);
    }

    /**
     * 判断给定的日期是否是今天或昨天。
     *
     * @param date 需要判断的日期
     * @return 如果是昨天返回1，如果是今天返回2，否则返回3
     */
    public static Integer isTodayOrYesterday(String date) {
        if(StringUtils.isEmpty(date))
            return 3; // 不是今天或昨天
        //解析date
        Date checkDate = DateUtil.strToDate(date, DateUtil.yyyy_MM_dd_HH_mm_ss);
        // 定义日期格式
        String dateToCheckStr = DateUtil.dateToStr(checkDate, DateUtil.yyyyMMdd);
        String todayStr = DateUtil.getCuryyyyMMdd();
        String yesterdayStr = String.valueOf(DateUtil.getYesterdayDate());
        if (dateToCheckStr.equals(yesterdayStr)) {
            return 1; // 是昨天
        } else if(dateToCheckStr.equals(todayStr)){
            return 2; // 是今天
        }else {
            return 3; // 不是今天或昨天
        }
    }
}
