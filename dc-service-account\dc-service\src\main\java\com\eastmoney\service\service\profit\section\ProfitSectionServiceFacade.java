package com.eastmoney.service.service.profit.section;

import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.CommonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 处理跨周期判断
 * Created on 2020/8/12-15:53.
 *
 * <AUTHOR>
 */
@Service("profitSectionServiceFacade")
public class ProfitSectionServiceFacade {

    @Resource(name = "profitSectionServiceAll")
    private ProfitSectionService profitSectionServiceAll;
    @Resource(name = "profitSectionServiceCross")
    private ProfitSectionService profitSectionServiceCross;
    @Resource(name = "profitSectionServiceNotCross")
    private ProfitSectionService profitSectionServiceNotCross;
    @Resource(name = "profitSectionServiceCustom")
    private ProfitSectionService profitSectionServiceCustom;
    @Resource(name = "profitSectionServiceDay")
    private ProfitSectionService profitSectionServiceDay;

    /**
     * @param params fundId - 资金账号 unit:区间 最新清算日期
     * @return
     */
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        Integer startDate = CommonUtil.convert(params,"startDate", Integer.class);
        Integer endDate = CommonUtil.convert(params,"endDate", Integer.class);

        ProfitSectionService profitSectionService = null;
        if(unit != null){
            if (DateUnitEnum.ALL.getValue().equals(unit)) {
                profitSectionService = profitSectionServiceAll;
            } else if (DateUnitEnum.MONTH.getValue().equals(unit)
                    || DateUnitEnum.WEEK.getValue().equals(unit)
                    || DateUnitEnum.YEAR.getValue().equals(unit)) {
                profitSectionService = profitSectionServiceCross;
            } else if (DateUnitEnum.PAST_HALF_YEAR.getValue().equals(unit)
                    || DateUnitEnum.PAST_ONE_YEAR.getValue().equals(unit)) {
                profitSectionService = profitSectionServiceNotCross;
            } else if(DateUnitEnum.DAY.getValue().equals(unit)){
                profitSectionService = profitSectionServiceDay;
            }
        }else if(startDate != null && endDate != null){
            if(startDate > endDate){
                return null;
            }
            profitSectionService = profitSectionServiceCustom;
        }

        if (profitSectionService != null) {
            return profitSectionService.getProfitSection(params);
        }
        return null;
    }
}
