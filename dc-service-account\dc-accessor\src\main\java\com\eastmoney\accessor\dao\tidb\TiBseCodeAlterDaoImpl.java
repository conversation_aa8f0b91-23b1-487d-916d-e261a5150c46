package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiBseCodeAlterMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.BseCodeAlterDO;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/14 14:26
 */
@Service("bseCodeAlterDao")
@ZhfxDataSource("tidb")
@Conditional(ZhfxDataSourceCondition.class)
public class TiBseCodeAlterDaoImpl extends BaseDao<TiBseCodeAlterMapper,BseCodeAlterDO,Long>implements TiBseCodeAlterDao {

}
