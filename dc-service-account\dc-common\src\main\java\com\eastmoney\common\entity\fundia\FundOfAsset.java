package com.eastmoney.common.entity.fundia;

public class FundOfAsset {
    /**
     * 客户代码
     */
    private String custId;
    /**
     * 资金帐号
     */
    private String fundId;
    /**
     * 投顾账户
     */
    private String investId;
    /**
     * 基金帐号
     */
    private String fundAccount;
    /**
     * 基金交易帐号
     */
    private String fundTradeAccount;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金公司内码
     */
    private String taCode;
    /**
     * 基金余额
     */
    private Double ofBal;
    /**
     * 基金可用数
     */
    private Double ofAvl;
    /**
     * 基金证券市值
     */
    private Double ofMktValue;
    /**
     * 未到账份额资产
     */
    private Double unArriveAsset;
    /**
     * 未到账资金资产
     */
    private Double unArriveFund;
    /**
     * 冻结份额
     */
    private Double ofFrz;

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getFundId() {
        return fundId;
    }

    public void setFundId(String fundId) {
        this.fundId = fundId;
    }

    public String getInvestId() {
        return investId;
    }

    public void setInvestId(String investId) {
        this.investId = investId;
    }

    public String getFundAccount() {
        return fundAccount;
    }

    public void setFundAccount(String fundAccount) {
        this.fundAccount = fundAccount;
    }

    public String getFundTradeAccount() {
        return fundTradeAccount;
    }

    public void setFundTradeAccount(String fundTradeAccount) {
        this.fundTradeAccount = fundTradeAccount;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public Double getOfBal() {
        return ofBal;
    }

    public void setOfBal(Double ofBal) {
        this.ofBal = ofBal;
    }

    public Double getOfAvl() {
        return ofAvl;
    }

    public void setOfAvl(Double ofAvl) {
        this.ofAvl = ofAvl;
    }

    public Double getOfMktValue() {
        return ofMktValue;
    }

    public void setOfMktValue(Double ofMktValue) {
        this.ofMktValue = ofMktValue;
    }

    public Double getUnArriveAsset() {
        return unArriveAsset;
    }

    public void setUnArriveAsset(Double unArriveAsset) {
        this.unArriveAsset = unArriveAsset;
    }

    public Double getUnArriveFund() {
        return unArriveFund;
    }

    public void setUnArriveFund(Double unArriveFund) {
        this.unArriveFund = unArriveFund;
    }

    public Double getOfFrz() {
        return ofFrz;
    }

    public void setOfFrz(Double ofFrz) {
        this.ofFrz = ofFrz;
    }
}
