package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.AssetHis;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>qi on 2016/7/18.
 * @mender robin on 2016/10/09
 * @content 去掉冗余代码，各方法加上简短注释
 */
public interface AssetHisDao extends IBaseDao<AssetHis, Integer> {

    /**
     * 查询指定区间每一天的资产
     * @param params
     * @return
     */
    List<AssetHis> getAssetDayTrend(Map<String, Object> params);

    /**
     * 查询每个月最后一天的资产
     * @param params
     * @return
     */
    List<AssetHis> getMonthAssetDayTrend(Map<String, Object> params);
}
