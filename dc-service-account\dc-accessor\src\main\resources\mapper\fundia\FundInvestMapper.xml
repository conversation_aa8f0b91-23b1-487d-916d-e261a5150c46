<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.fundia.FundInvestMapper">
    <select id="queryCombAsset" resultType="com.eastmoney.common.entity.fundia.CombAsset">
        select a.custid,m.fundid,m.investid,a.fundcode,nvl(a.bal,'0.00') as bal,nvl(a.avl,'0.00') as avl,
               nvl(a.unarriveasset,'0.00') as unarriveasset,nvl(a.unarrivefund,'0.00') as unarrivefund,nvl(a.type,'1') as type,
               nvl(FUNDASSETADJAMT,'0.00') AS FUNDASSETADJAMT,nvl(STKASSETADJAMT,'0.00') AS STKASSETADJAMT,
        m.groupcode as combcode,g.groupname as combname
        FROM (select * from fundia.fund_user_group_manage  where fundid = #{fundId})  m
        join FUNDIA.FUND_GROUP_PROD G on g.groupcode = m.groupcode
        left join (select custid,fundid,INVESTID,fundcode,ofbal as bal,OFAVL as avl,
                          '0' as type,unarrivefund as unarrivefund,unarriveasset as unarriveasset,
                          '' AS FUNDASSETADJAMT,'' AS STKASSETADJAMT  from fundia.fund_ofasset
        union all
        select custid,fundid,INVESTID,'' as fundcode,fundbal as bal ,fundavl as avl,'1' as type,fundway as unarrivefund,
               0.00 as unarriveasset,to_char(FUNDASSETADJAMT) AS FUNDASSETADJAMT,to_char(STKASSETADJAMT) AS STKASSETADJAMT from fundia.fund_asset)
        a on  m.investid = a.investid and m.fundid = a.fundid
        <where>
            m.agreementstatus != '3'
        </where>
    </select>
    <!--查询投顾服务费
        FUNDIA.Fund_Comb_Feeclear_Log 每日计提服务费和抵扣费用的流水
        fundia.FUND_LOGASSET 查询资金流水中的费用，按日汇总累加获取
        -->
    <select id="queryInvestFee" resultType="com.eastmoney.common.entity.fundia.InvestFeeDetail">
        select a.groupcode, a.feeamt
        from (select t.investid,
                     t.groupcode,
                     nvl(t.feetotal, 0) as feeamt,
                     t.bizdate,
                     row_number() over(partition by t.INVESTID order by t.BIZDATE desc) rn
              from FUNDIA.FUND_CUST_ASSET_SUM t
              where t.fundid = #{fundId}
                and exists(select 1
                           from FUNDIA.FUND_USER_GROUP_MANAGE
                           where fundid = #{fundId}
                             and investid = t.investid
                             and AGREEMENTSTATUS <![CDATA[<>]]> '3')) a
        where a.rn = 1
    </select>

    <!--查询投顾系统运行状态-->
    <select id="querySystemStatus" resultType="com.eastmoney.common.entity.FundSysStatus">
        select lastdate,currentdate,status from fundia.fund_sys_status
    </select>

    <!--查询开盘后发起的费用对应的业务申请-->
    <select id="queryLatestBizApplyOrders" resultType="com.eastmoney.common.entity.fundia.FundInvestBizApplyOrderInfo">
        SELECT * FROM (
                          SELECT fl.BIZDATE ,fba.BUSITYPE,fba.REQRATE,fba.eid as bizeid,
                                 RANK() over(ORDER BY fl.BIZDATE DESC, fl.EITIME DESC) AS nums
                          FROM fundia.FUND_LOGASSET fl
                                   JOIN fundia.FUND_BUSINESS_APPLY fba ON fl.RELATIVESEID = fba.EID
                          WHERE fl.CREATEUSERID ='BIZBEFOREOPEN' AND fl.DIGESTID IN ('53000149','60000001','60000002','60000003','60000004','60000005')
                            AND fl.INVESTID = #{investId}
                      ) WHERE nums = 1
    </select>

    <!--查询组合当前收益-->
    <select id="queryCombProfit" resultType="com.eastmoney.common.entity.fundia.CombProfit">
        select nvl(profitrate,'0') as profitrate,nvl(totalprofit,'0') as totalprofit,nvl(profitamt,'0') as profitamt,combcode,bizdate,investid from (
        select r.profitrate,p.totalprofit,p.profitamt,m.groupcode as combcode ,p.bizdate,m.investid,
        rank() over(partition by m.INVESTID order by p.bizdate desc) nums from fundia.fund_user_group_manage m
        left join fundia.fund_cust_profit p on m.investid = p.investid and p.bizdate > m.signdate
        left join fundia.fund_cust_profitrate r on r.investid = p.investid and r.bizdate = p.bizdate
        <where>
            m.fundid =#{fundId}
            <if test="combcode != null">
                and m.groupcode = #{combcode}
            </if>
        </where>
        ) where nums = 1
    </select>
</mapper>