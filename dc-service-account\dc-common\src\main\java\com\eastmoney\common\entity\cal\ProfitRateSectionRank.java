package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created on 2021/1/15-10:57.
 *
 * <AUTHOR>
 */
public class ProfitRateSectionRank extends BaseEntityCal {
    private Double rankPercent;
    private Integer rankIndex;
    private String unit;
    private Double profitRate;

    public Double getRankPercent() {
        return rankPercent;
    }

    public void setRankPercent(Double rankPercent) {
        this.rankPercent = rankPercent;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }
}
