package com.eastmoney.transport.client;

import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.model.Response;
import com.eastmoney.transport.util.ChannelUtil;
import com.eastmoney.transport.util.Constants;
import com.eastmoney.transport.util.DesUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/27
 * Time: 15:26
 */
public class ClientHandler extends ChannelInboundHandlerAdapter {
    private static Logger LOG = LoggerFactory.getLogger(ClientHandler.class);
    private String desKey = null;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws InterruptedException {
        LOG.info("====客户端建立连接," + ChannelUtil.getChannelRoute(ctx.channel()));
        ctx.fireChannelActive();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        LOG.info("====客户端连接中断," + ChannelUtil.getChannelRoute(ctx.channel()));
        //关闭通道
        ctx.close();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        try {
            Message message = (Message) msg;
            byte type = message.getType();
            if (type == Constants.SOCKET_TYPE_PULL_RES || type == 0) {
                Response response = new Response();
                response.setId(new String(message.getRequestId()));
                //解密
                if (message.getCipher() == 1) {
                    String contentStr = new String(message.getContent());
                    contentStr = DesUtil.decrypt(contentStr, desKey);
                    message.setContent(contentStr.getBytes());
                }
                response.setMessage(message);
                DefaultFuture.received(response);
            } else if (type == Constants.SOCKET_TYPE_HTBT_RES) {

            } else if (type == Constants.SOCKET_TYPE_KEY_RES) {
                desKey = new String(message.getContent());
                LOG.info("连接返回desKey" + desKey);
            } else if (type == Constants.SOCKET_TYPE_HTBT_REQ) {
                LOG.info("服务端发来心跳 {}", ChannelUtil.getRemoteAddress(ctx.channel()));
                Message replyMessage = new Message();
                replyMessage.setType(Constants.SOCKET_TYPE_HTBT_RES);
                replyMessage.setRequestId(message.getRequestId());
                ctx.writeAndFlush(replyMessage);
            }
        } catch (Exception e) {
            LOG.error("", e);
        }
        ctx.fireChannelRead(msg);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        //异常后channel已为空
        LOG.error("通信异常{}", ChannelUtil.getChannelRoute(ctx.channel()), cause);
        //关闭通道
        ctx.close();
    }
}
