package com.eastmoney.accessor.dao.kgdb;

import com.eastmoney.accessor.mapper.otckg.OtcInstNetMapper;
import com.eastmoney.common.entity.OtcInstNet;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Service("otcInstNetDao")
public class OtcInstNetDaoImpl implements OtcInstNetDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(OtcInstNetDaoImpl.class);

    private final ConcurrentHashMap<String, OtcInstNet> allInstMap = new ConcurrentHashMap<>();

    @Resource(name = "otcInstNetMapper")
    private OtcInstNetMapper otcInstNetMapper;

    @Override
    public void loadInstNetMap() {
        LOGGER.info("------------------------正在加载otc基金净值信息，请等待-------------------------{}", DateUtil.getCurDateTime());
        doLoadFundNetMap();
    }

    private void doLoadFundNetMap() {
        List<OtcInstNet> otcInstNetList = otcInstNetMapper.getOtcInstNetList();
        long otcInstNetCount =  otcInstNetList.stream()
                .filter(otcInstNet -> otcInstNet.getInstCode() != null && otcInstNet.getLastNet() != null)
                .peek(otcInstNet -> allInstMap.put(StringUtils.trim(otcInstNet.getInstCode()), otcInstNet))
                .count();
        LOGGER.info("------------------------otc基金净值信息加载完成{}条-------------------------{}", otcInstNetCount, DateUtil.getCurDateTime());
    }

    @Override
    public Double getLastNet(String instCode) {
        OtcInstNet otcInstNet = allInstMap.get(instCode);
        if(otcInstNet == null){
            return 1.0;
        }
        return ArithUtil.div(otcInstNet.getLastNet(), 10000);
    }
}
