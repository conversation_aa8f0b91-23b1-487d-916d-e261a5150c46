package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.JsonSerializeFilter;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.ShareHandler;
import com.eastmoney.service.serializer.ShareStkAssetProfitFilter;
import com.eastmoney.service.util.FunCodeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Action
public class ShareAction {

    @Autowired
    private ShareHandler shareHandler;

    /**
     * 柜台分享页接口 - 获取当日收益率
     * 显示规则：当日盈亏时间段的显示规则:物理日期=交易日且物理时间>=9:25且集中交易系统状态=正常0，系统才显示当日盈亏。(数据库已经求出)
     * APPAGILE-88833 过滤港股通收益
     *
     * [更新]：交易日 92500~240000 展示当日盈亏 (APPAGILE-134973)
     *
     * @param params fundId, unit, count[100]
     * @return profitRate 收益率
     */
    @RequestMapping("/getRealTimeProfitInfo" )
    @FunCodeMapping(FunCodeConstants.GET_REALTIME_PROFIT_INFO)
    public Object getProfitInfo(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});

        return shareHandler.getRealTimeProfitInfo(params);
    }

    /**
     * [更新]：交易日 92500~240000 展示当日盈亏 (APPAGILE-134973)
     * @param params
     * @return
     */
    @RequestMapping("/getShareStkAssetProfitInfo")
    @FunCodeMapping(FunCodeConstants.GET_SHARE_STKASSET_PROFIT_INFO)
    @JsonSerializeFilter(ShareStkAssetProfitFilter.class)
    public Object getShareStkAssetProfitInfo(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params, new String[]{"fundId","moneyType"});
        return shareHandler.getShareStkAssetProfitInfo(params);
    }
}

