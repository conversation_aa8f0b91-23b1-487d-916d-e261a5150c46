<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.StockLayeredInfoMapper">
    <resultMap id="BaseResultMap" type="com.eastmoney.common.entity.StockLayeredInfo">
        <result column="STKCODE" property="stkCode"/>
        <result column="SUBLEVEL" property="subLevel"/>
        <result column="CHANGEDATE" property="changeDate"/>
        <result column="CHANGETIME" property="changeTime"/>
    </resultMap>

    <select id="selectAll" resultMap="BaseResultMap">
        select EID, EITIME, EUTIME, STKCODE, SUBLEVEL, CHANGEDATE, CHANGETIME
        from ATCENTER.STOCK_LAYERED_INFO
    </select>

</mapper>