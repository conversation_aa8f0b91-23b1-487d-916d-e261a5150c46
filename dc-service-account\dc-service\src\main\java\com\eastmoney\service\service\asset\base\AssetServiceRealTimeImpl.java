package com.eastmoney.service.service.asset.base;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.accessor.service.FundStockInnovateService;
import com.eastmoney.accessor.service.LogBankTranService;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.*;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.profit.base.ProfitService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.quote.QuoteService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import com.eastmoney.service.util.BusinessUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.eastmoney.common.util.ArithUtil.*;

/**
 * Created on 2020/8/13-14:31.
 * 处理总资产，otc资产，实时转入转出
 *
 * <AUTHOR>
 */
@Service("assetServiceRealTime")
public class AssetServiceRealTimeImpl implements AssetService {
    @Autowired
    private QuoteService quoteService;
    @Resource(name = "otcAssetDetailCacheService")
    private OtcAssetDetailCacheService otcAssetDetailCacheService;
    @Autowired
    private CommonService commonService;
    @Resource(name = "profitServiceTemp")
    private ProfitService profitServiceTemp;
    @Autowired
    private LogBankTranService logBankTranService;

    @Autowired
    private HgtReferRateService hgtReferRateService;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Autowired
    private FundIaFundAccountService fundIaFundAccountService;
    @Autowired
    private FundIaFundAssetService fundIaFundAssetService;
    @Autowired
    private StkPriceCacheService stkPriceCacheService;
    @Autowired
    private FundStockInnovateService fundStockInnovateService;
    @Autowired
    private StkAssetService stkAssetService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private CoreConfigService coreConfigService;


    /**
     * 根据实时资产，ogg银证转账，临时收益信息
     * 获取区间资产变动的asset, otcAsset, shiftInTotal shiftOutTotal字段
     *
     * @param params fundId
     * @return
     */
    @Override
    public AssetHis getAsset(Map<String, Object> params) {
        params.put("moneyType", "0");
        AssetHis assetRealTime = new AssetHis();
        //处理实时资产、转入和转出
        List<QryFund> qryFundList = getAssetRealTime(params, false);
        if (CollectionUtils.isEmpty(qryFundList)) {
            assetRealTime.setAsset(0d);
            assetRealTime.setOtcAsset(0d);
            assetRealTime.setMktval(0d);
            return assetRealTime;
        }
        QryFund qryFund = qryFundList.get(0);

        assetRealTime.setAsset(ArithUtil.add(assetRealTime.getAsset(), qryFund.fundAll));
        assetRealTime.setOtcAsset(qryFund.otcAsset);
        assetRealTime.setMktval(qryFund.stkMktVal);
        //将转入转出加上实时数据
        calShiftByRt(params, assetRealTime, qryFund);
        return assetRealTime;
    }

    /**
     * 获取总资产 总可用 总市值
     *
     * @param params fundId : 资金账号 moneyType : 币种  logAssetFlag:是否查询流水数据
     * @return
     */
    public List<QryFund> getAssetRealTime(Map<String, Object> params, Boolean logAssetFlag) {
        //OTC 资产
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Map<String, Double> otcAssetMap = otcAssetDetailCacheService.getRealTimeOtcAsset(fundId);

        if (logAssetFlag) {
            //funcId=1002082：查询资金、持仓、流水
            params.put("funcId", 1002080);
        } else {
            //funcId=1002082：查询资金和持仓
            params.put("funcId", 1002082);
        }

        // PCMP-10616646 【柜台配合】港股通当日收益计算优化 - 根据开关判断走新的存过还是走老的存过
        Integer serverId = coreConfigService.getServerId(fundId);
        PosDBItem item;
        if (nodeConfigService.getHgtProfitCalOptimizeFlag(serverId)) {
            //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notpnew1，这个存过不返回港股通交易记录
            item = fundStockInnovateService.getDBResultInfo(params);
        } else {
            //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notp
            item = fundStockInnovateService.getDBResultInfoOld(params);
        }

        // 资产信息
        List<QryFund> qryFundList = item.getQryFundList();
        // 持仓信息
        List<PositionInfo> stkInfoList = item.getStkInfoList();
        // PCMP-10028770 【账户分析】北交所存量代码段改造 处理持仓 920切换当日 内存中将老代码持仓改为新代码
        // 流水信息
        List<LogAsset> logAssetList = item.getLogAssetList();
        // 持仓信息用于计算当日盈亏
        List<PositionInfo> profitStkInfoList = item.getProfitStkInfoList();
        // 流水信息用于计算当日盈亏
        List<LogAsset> profitLogAssetList = item.getProfitLogAssetList();

        //APPAGILE-80736 fundia库基金投顾资产 + 基金投顾资金
        double fundIaAsset = 0.0;
        double fundIaAdjustFundAsset = 0.0;
        if (fundIaFundAccountService.getFundIaFundAccount(params)) {
            fundIaAsset = getFundIaAsset(params);
            fundIaAdjustFundAsset = logBankTranService.selectFundIaAdjustFundAsset(params);
        }
        for (QryFund qryFund : qryFundList) {
            Double otcAsset = otcAssetMap.get(qryFund.getMoneyType());
            if (otcAsset == null) {
                qryFund.setOtcAsset(0.0);
            } else {
                qryFund.setOtcAsset(otcAsset);
            }

            //ACCTANAL-106 实时otc资产round
            qryFund.otcAsset = round(add(qryFund.otcAsset, qryFund.xjbFundVal));
            qryFund.setFundIaAsset(fundIaAsset);

            //1K  	报价回购买入  要把非T+0交收的资金排除掉
            //由于股票是T+1交收，报价回购是T+0交收，且为非担保交收，
            //因此当日卖出股票的资金不能用来做报价回购
            //具体算法为：
            //可用于报价回购申报的金额 = min(fundavl, fundbal + fundbrkbuy)
            //三合一存储过程已返回：case when ((@p_bsflag='1K') and (b.fundavl > (b.fundbal + b.fundbrkbuy))) then (b.fundbal + b.fundbrkbuy) else b.fundavl end fundavl,

            // 7*24银证净转入 区分正负号
            qryFund.setFundEffect7X24(qryFund.f7X24zzAmt);
            if (Objects.nonNull(qryFund.f7X24zzAmt) && qryFund.f7X24zzAmt > 0) {
                qryFund.setFundEffect7X24ShiftIn(qryFund.f7X24zzAmt);
            } else {
                qryFund.setFundEffect7X24ShiftOut(qryFund.f7X24zzAmt);
            }

            //资产中增加 补偿资金资产（fundassetadjamt）和补偿证券资产（stkassetadjamt）
            qryFund.fundBal = add(qryFund.fundBal, qryFund.fundEffect7X24, fundIaAdjustFundAsset);
            // fundall = b.fundbal + b.fundbuysale - b.funduncomebuy + b.funduncomesale+ isnull(d.fundprein,0.00) - isnull(d.fundpreout,0.00)
            double r = add(qryFund.fundAll, qryFund.assetAdjAmt, fundIaAdjustFundAsset);
            qryFund.setFundAll(r);
            qryFund.openFundVal = qryFund.xjbFundVal;
            if (qryFund.fundAll != null && qryFund.xjbFundVal != null
                    && qryFund.fundAll < 0 && qryFund.xjbFundVal > 0) {
                double transferFundVal = add(qryFund.fundAll, qryFund.xjbFundVal) > 0 ? -qryFund.fundAll : qryFund.xjbFundVal;
                // 资金余额不需计入天天宝和资金差值
                // qryFund.fundAll = add(qryFund.fundAll, transferFundVal);
                qryFund.openFundVal = sub(qryFund.openFundVal, transferFundVal);
            }
        }

        // 查询stkPrice缓存
        stkInfoList = stkPriceCacheService.updateStkInfoList(fundId, stkInfoList);

        //处理可转债,盘中债券资产使用全价计算  --APPAGILE-89983
        quoteService.setStkAssetQuote(stkInfoList, true);

        //股份市值
        for (StkInfo stkInfo : stkInfoList) {

            //交易日的竞合竞价期间，股票价格取昨收价 APPAGILE-120248
            Double price = stkInfo.getStkPrice().getPrice();
            //市值
            double stkMktVal = 0.00;

            //如果有行情才加股票市值
            if (Objects.equals(stkInfo.mtkCalFlag, "1") || ((Objects.equals(stkInfo.mtkCalFlag, "0") || Objects.equals(stkInfo.mtkCalFlag, " ")) && STKTEnum.STKT_LOF.getValue().equals(stkInfo.stkType))) {
                //计算持仓份额
                stkInfo.stkQty = ArithUtil.add(stkInfo.stkBal, stkInfo.stkBuySale, stkInfo.stkUnComeBuy,
                        ArithUtil.mul(-1, stkInfo.stkUnComeSale)).longValue();
                stkMktVal = mul(stkInfo.stkQty, price);
                if (BusinessUtil.isGgtMarket(stkInfo.getMarket())) {
                    //港股市值根据汇率换算人民币市值
                    HgtReferRate hgtReferRateInfo = hgtReferRateService.getOggHgtReferRateService(fundId, stkInfo.getMarket());
                    Double hgtReferRate = Objects.nonNull(hgtReferRateInfo) ? hgtReferRateInfo.getSettRate() : 1d;
                    stkMktVal = mul(stkMktVal, hgtReferRate);
                }
                //ACCTANAL-89  持仓市值=0，且成本不为0的，取成本buycost;
                if (ArithUtil.eq(price, 0.0) && ArithUtil.ge(stkInfo.getBuyCost(), 0)) {
                    stkMktVal = stkInfo.getBuyCost();
                }
            }

            for (QryFund qryFund : qryFundList) {
                if (stkInfo.fundId.equals(qryFund.fundId) && stkInfo.moneyType.equals(qryFund.moneyType)) {
                    if (qryFund.fundMktVal == null) {
                        qryFund.fundMktVal = 0.0;
                    }
                    if (qryFund.stkMktVal == null) {
                        qryFund.stkMktVal = 0.0;
                    }
                    qryFund.fundMktVal = add(qryFund.fundMktVal, stkMktVal);
                    qryFund.stkMktVal = add(qryFund.stkMktVal, stkMktVal);
                    break;
                }
            }
        }

        for (QryFund qryFund : qryFundList) {
            if ("0".equals(qryFund.getMoneyType())) {
                //人民币帐户累加开放基金和信用市值
                //此处假定人民币为'0'，否则需要根据实际情况调整代码（将currency存在KCBP内存中供检索可以解决此项冲突问题 )
                //市值部分做round
                // openFundVal 已计入到otcAsset,无需重复计入 fundMktVal
                // qryFund.fundMktVal = add(qryFund.fundMktVal, qryFund.openFundVal);
                qryFund.fundAll = add(qryFund.fundAll, qryFund.creditSval);
            }


            //ACCTANAL-106 实时持仓信息-总资产round
            qryFund.fundAll = round(add(qryFund.fundMktVal, qryFund.fundAll, qryFund.bbContamt, qryFund.realRqAmt, fundIaAsset));
            //ACCTANAL-106总市值取round
            qryFund.fundMktVal = round(add(qryFund.fundMktVal, qryFund.bbContamt));
            qryFund.stkMktVal = round(qryFund.stkMktVal);
            // 新增包含otc的总资产
            qryFund.setFundAllWithOtc(add(qryFund.fundAll, qryFund.otcAsset));
            qryFund.setStkInfoList(stkInfoList);
            // 保存交易流水数据
            qryFund.setLogAssetList(logAssetList);
            // 保存计算当日收益数据集合
            qryFund.setProfitStkInfoList(profitStkInfoList);
            qryFund.setProfitLogAssetList(profitLogAssetList);
        }
        return qryFundList;
    }

    /**
     * 基金投顾柜台资产分为两部分 fund_asset中的资金资产和fund_ofasset中的基金资产。
     * Fundasset每个fundId可能对应多个INVESTID，账户分析取资金资产需要基于fundId对fundbal
     *
     * @param params
     * @return
     */
    private double getFundIaAsset(Map<String, Object> params) {
        return fundIaFundAssetService.getFundIaFundAsset(params);
    }

    /**
     * 根据临时转入转出、ogg转入转出设置
     *
     * @param params
     * @param assetRealTime
     */
    private void calShiftByRt(Map<String, Object> params, AssetHis assetRealTime, QryFund qryFund) {
        DayProfitBean profitDayTemp = profitServiceTemp.getProfitDay(params);
        int calRealTimeProfitBizDate = commonService.calRealTimeProfitBizDate(
                CommonUtil.convert(params.get("accountBizDate"), Integer.class));
        //临时收益计算完成（17点）~ 账户分析完成清算 || 跨天未清算已开盘通过临时收益获取T-1转入转出
        if (profitDayTemp != null) {
            assetRealTime.setShiftInTotal(ArithUtil.add(
                    assetRealTime.getShiftInTotal(), profitDayTemp.getShiftIn()));
            assetRealTime.setShiftOutTotal(ArithUtil.add(
                    assetRealTime.getShiftOutTotal(), profitDayTemp.getShiftOut()));
            assetRealTime.setStartDate(profitDayTemp.getBizDate());
            assetRealTime.setBizDate(profitDayTemp.getBizDate());
            if (calRealTimeProfitBizDate == profitDayTemp.getBizDate()) {
                // T+1 银证转账logbanktran柜台完成日切~账户分析完成清算
                params.put("nextBizDate", tradeDateDao.getNextMarketDay(calRealTimeProfitBizDate));
                List<LogBankTran> logBankTrans = logBankTranService.selectBySysDate(params);
                for (LogBankTran logBankTran : logBankTrans) {
                    double fundEffect = logBankTran.getFundEffect();
                    if (fundEffect > 0) {
                        assetRealTime.setShiftInTotal(ArithUtil.add(
                                assetRealTime.getShiftInTotal(), fundEffect));
                    } else {
                        assetRealTime.setShiftOutTotal(ArithUtil.sub(
                                assetRealTime.getShiftOutTotal(), fundEffect));
                    }
                }
                params.remove("nextBizDate");
            }
        }
        //盘中及盘后~临时收益 || 跨天未清算已开盘
        if (profitDayTemp == null || calRealTimeProfitBizDate > profitDayTemp.getBizDate()) {
            calShiftByLogBankTran(params, assetRealTime);
            assetRealTime.setBizDate(calRealTimeProfitBizDate);
        }

        if (qryFund != null) {
            assetRealTime.setShiftInTotal(ArithUtil.add(
                    assetRealTime.getShiftInTotal(), qryFund.getFundEffect7X24ShiftIn()));
            assetRealTime.setShiftOutTotal(ArithUtil.sub(
                    assetRealTime.getShiftOutTotal(), qryFund.getFundEffect7X24ShiftOut()));
        }
    }


    /**
     * 根据ogg实时银证转账logbanktran计算转入转出
     *
     * @param params
     * @param assetRealTime
     */
    private void calShiftByLogBankTran(Map<String, Object> params, AssetHis assetRealTime) {
        List<LogBankTran> logBankTrans = logBankTranService.query(params);
        //计算当日转入转出
        for (LogBankTran logBankTran : logBankTrans) {
            double fundEffect = logBankTran.getFundEffect();
            if (fundEffect > 0) {
                assetRealTime.setShiftInTotal(ArithUtil.add(
                        assetRealTime.getShiftInTotal(), fundEffect));
            } else {
                assetRealTime.setShiftOutTotal(ArithUtil.sub(
                        assetRealTime.getShiftOutTotal(), fundEffect));
            }
        }
    }

    /**
     * 查询实时持仓信息
     * 本方法为实时资产中持仓部分的抽离,无交易流水
     * 未计算用户资金相关资产,无法提供准确的仓位信息
     *
     * @param params
     * @return
     */
    public List<PositionInfo> queryRealTimePositionSimple(Map<String, Object> params) {
        //查询实时持仓
        Map<String, Object> paramsCopy = new HashMap<>(params);
        paramsCopy.putIfAbsent("moneyType", "0");
        paramsCopy.putIfAbsent("flag2", "0");
        paramsCopy.put("funcId", 1002082);

        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer serverId = coreConfigService.getServerId(fundId);
        PosDBItem item;
        if (nodeConfigService.getHgtProfitCalOptimizeFlag(serverId)) {
            //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notpnew1，这个存过不返回港股通交易记录
            item = fundStockInnovateService.getDBResultInfo(paramsCopy);
        } else {
            //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notp
            item = fundStockInnovateService.getDBResultInfoOld(paramsCopy);
        }
        List<PositionInfo> stkInfoList = item.getStkInfoList();

        //使用stkPrice缓存更新持股信息
        stkInfoList = stkPriceCacheService.updateStkInfoList(fundId, stkInfoList);
        //处理可转债,盘中债券资产使用全价计算
        quoteService.setStkAssetQuote(stkInfoList, true);
        //股份市值
        for (StkInfo stkInfo : stkInfoList) {
            //如果有行情才加股票市值
            if (Objects.equals(stkInfo.mtkCalFlag, "1") || ((Objects.equals(stkInfo.mtkCalFlag, "0") ||
                    Objects.equals(stkInfo.mtkCalFlag, " ")) && STKTEnum.STKT_LOF.getValue().equals(stkInfo.stkType))) {
                stkInfo.stkQty = ArithUtil.add(stkInfo.stkBal, stkInfo.stkBuySale, stkInfo.stkUnComeBuy,
                        ArithUtil.mul(-1, stkInfo.stkUnComeSale)).longValue();
            }
        }
        String moneyType = CommonUtil.convert(paramsCopy.get("moneyType"), String.class);
        //使用100代替用户总资产,计算仓位数值不提供真实价值,若需要提供仓位精确值参考 getRealTimeAsset
        stkAssetService.calPositionIncome(stkInfoList, moneyType, fundId, 100);
        stkAssetService.setExtInfo(stkInfoList, fundId);
        return stkInfoList;
    }

}
