package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.MajorBillMapper;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2017/3/31.
 */
//@ZhfxDataSource
//@Conditional(ZhfxDataSourceCondition.class)
//@Service("majorBillDao")
public class MajorBillDaoImpl extends BaseDao<MajorBillMapper,MajorBill,Long> implements MajorBillDao {

    @Override
    public List<MajorBill> getBestMonth(Map<String, Object> params) {
        return mapper.getBestMonth(params);
    }

    @Override
    public Map<Integer,List<ProfitRateDay>> selectMonthProfitRate(Long fundId, Integer startDate, Integer endDate) {
        Map<String,Object> params = new HashMap<>();
        params.put("fundId",fundId);
        params.put("startDate",startDate.toString().substring(0,6));
        params.put("endDate",endDate.toString().substring(0,6));
        return mapper.selectMonthProfitRate(params).stream().collect(Collectors.groupingBy(ProfitRateDay::getBizDate));
    }

    @Override
    public List<ProfitDay> selectMonthProfit(Long fundId, Integer startDate, Integer endDate) {
        return null;
    }

    @Override
    public List<ProfitDay> selectYearProfit(Long fundId, Integer startDate, Integer endDate) {
        return null;
    }

    @Override
    public List<ProfitRateDay> selectYearProfitRate(Long fundId, Integer startDate, Integer endDate) {
        return null;
    }

    @Override
    public List<MajorBillStrInfo> selectByYearBill(Map<String, Object> params) {
        return null;
    }
}
