package com.eastmoney.quote.mdstp.pull.client;

import com.eastmoney.quote.mdstp.pull.conf.QuoteConstant;
import com.eastmoney.quote.mdstp.pull.serializer.Packet;
import com.eastmoney.quote.mdstp.pull.serializer.RpcDecoder;
import com.eastmoney.quote.mdstp.pull.serializer.RpcEncoder;
import com.eastmoney.quote.mdstp.pull.serializer.RpcHkDecoder;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetSocketAddress;


/**
 * Created by 1 on 15-7-7.
 */
public class RpcConnection extends ChannelInboundHandlerAdapter implements RpcConnInterface {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(RpcConnection.class);

    //inetsocket address
    private InetSocketAddress inetAddr;

    //org.jboss.netty.channel.Channel
    private volatile Channel channel;

    //response
    private volatile Packet response;

    //exception
    private volatile Throwable exception;

    private boolean connected;
    private String quoteType;
    private  Bootstrap b;
    private  EventLoopGroup group;

    public RpcConnection(String quoteType,String host, int port) {
        this.quoteType = quoteType;
        this.inetAddr = new InetSocketAddress(host, port);
    }


    @Override
    public Packet sendRequest(Packet request) throws Throwable {
        if (!isConnected()) {
            throw new IllegalStateException("not connected");
        }
        ChannelFuture writeFuture = channel.writeAndFlush(request);
        if (!writeFuture.awaitUninterruptibly().isSuccess()) {
            close();
            throw writeFuture.cause();
        }
        waitForResponse();
        Throwable ex = exception;
        Packet resp = this.response;
        this.response = null;
        this.exception = null;

        if (null != ex) {
            close();
            throw ex;
        }
        return resp;
    }

    public void waitForResponse() {
        synchronized (channel) {
            try {
                channel.wait(2 * 60 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void connection() throws Throwable {



        if (connected) {
            return;
        }

        b = new Bootstrap();
        group = new NioEventLoopGroup();
        b.group(group)
                .channel(NioSocketChannel.class)
                .handler(new ChannelInitializer<SocketChannel>(){
                    @Override
                    public void initChannel(SocketChannel ch) throws Exception {
                        ChannelPipeline pipeline = ch.pipeline();
                        if (QuoteConstant.QUOTE_TYPE_HS.equals(RpcConnection.this.quoteType)) {
                            pipeline.addLast("decoder", new RpcDecoder());
                        } else {
                            pipeline.addLast("decoder", new RpcHkDecoder());
                        }
                        pipeline.addLast("encoder", new RpcEncoder());
                        pipeline.addLast("handler", RpcConnection.this);

                    }
                });
        ChannelFuture channelFuture = b.connect(inetAddr).sync();

        if (!channelFuture.awaitUninterruptibly().isSuccess()) {
            group.shutdownGracefully();
            throw channelFuture.cause();
        }
        channel = channelFuture.channel();
        connected = true;
        LOG.info("connect success !!!");

    }

    @Override
    public void close() throws Throwable {
        connected = false;
        if (null != channel) {
            channel.close().awaitUninterruptibly();
            this.exception = new IOException("channel closed!!!");
            synchronized (channel) {
                channel.notifyAll();
            }
            channel = null;
        }
        if(group!=null){
            group.shutdownGracefully();
            group = null;
            b = null;
        }
    }

    @Override
    public void channelRead(final ChannelHandlerContext ctx, Object message) throws Exception {
        try {
            ctx.fireChannelRead(message);
            Packet packet = (Packet)message;
            response = packet;
            synchronized (channel) {
                channel.notifyAll();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    public boolean isConnected() {
        return connected;
    }

    public boolean isClosed() {
        return (null == channel) || !channel.isOpen()
                || !channel.isActive() || !channel.isWritable();
    }


}
