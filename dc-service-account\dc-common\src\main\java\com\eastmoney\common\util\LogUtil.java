package com.eastmoney.common.util;

import com.alibaba.fastjson.JSON;
import com.eastmoney.common.model.LogBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 日志系统格式记录日志
 * User: sunyuncai
 * Date: 2015/10/23
 * Time: 14:28
 */
public class LogUtil {

    public static Logger LOG = LoggerFactory.getLogger(LogUtil.class);

    public static void info(LogBean logBean) {
        logBean.setO_time(DateUtil.dateToStr(new Date(), DateUtil.yyyy_MM_dd_HH_mm_ss_SSS));
        LOG.info(JSON.toJSONString(logBean));
    }

    public static void error(String msg) {
        LogBean logBean = create("initServer", msg, 0);
        logBean.setResult("-1");
        logBean.setS_ip(CommonUtil.getLocalIp());
        logBean.setO_time(DateUtil.dateToStr(new Date(), DateUtil.yyyy_MM_dd_HH_mm_ss_SSS));
        LOG.error(JSON.toJSONString(logBean));
    }

    public static void fatal(String action, String msg, String requestId, long spentTime) {
        LogBean logBean = create(action, msg, spentTime);
        logBean.setT_id(requestId);
        logBean.setResult("-1");
        logBean.setLog_type("fatal");
        LOG.error(JSON.toJSONString(logBean));
    }

    private static LogBean create(String action, String msg, long spentTime) {
        LogBean logBean = new LogBean();
        logBean.setAction(action);
        logBean.setError_msg(msg);
        logBean.setS_ip(CommonUtil.getLocalIp());
        logBean.setSpent_time(spentTime + "");
        logBean.setO_time(DateUtil.dateToStr(new Date(), DateUtil.yyyy_MM_dd_HH_mm_ss_SSS));
        return logBean;
    }
}
