package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiHgtExchangeRateMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.HgtExchangeRate;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/11/20
 */
@Service("hgtExchangeRateDao")
@ZhfxDataSource("tidb")
@Conditional(ZhfxDataSourceCondition.class)
public class TiHgtExchangeRateDaoImpl extends BaseDao<TiHgtExchangeRateMapper, HgtExchangeRate, String> implements HgtExchangeRateDao {
    @Override
    public List<HgtExchangeRate> getLastestExchangeRate(Map<String, Object> params) {
        return getMapper().getLastestExchangeRate(params);
    }

    @Override
    public List<HgtExchangeRate> getRateListByBizdate(Map<String, Object> params) {
        return getMapper().getRateListByBizdate(params);
    }

    @Override
    public List<HgtExchangeRate> getLatestRateList(Map<String, Object> params) {
        return getMapper().getLatestRateList(params);
    }
}
