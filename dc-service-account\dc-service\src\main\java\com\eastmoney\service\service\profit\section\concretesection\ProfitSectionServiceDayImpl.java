package com.eastmoney.service.service.profit.section.concretesection;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.sysEnum.ZhfxSettleStatusEnum;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.ZhfxSettleStatusService;
import com.eastmoney.service.service.ProfitDayService;
import com.eastmoney.service.service.ProfitRateDayService;
import com.eastmoney.service.service.profit.section.ProfitSectionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 当日区间-收益数据
 *
 * 0:00 - 9:25                        不计算收益
 * 9:25 - 17:00                       实时计算收益
 * 17:00 - 日收益额&日收益率清算完成前   使用临时收益
 * 日收益额&日收益率清算完成后 - 24:00   使用清算收益
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@Service("profitSectionServiceDay")
public class ProfitSectionServiceDayImpl implements ProfitSectionService {
    @Autowired
    protected AssetNewService assetNewService;
    @Resource(name = "profitSectionServiceRealTime")
    protected ProfitSectionService profitSectionServiceRealTime;
    @Autowired
    protected ProfitDayService profitDayService;
    @Autowired
    protected ProfitRateDayService profitRateDayService;
    @Autowired
    protected TradeDateDao tradeDateDao;
    @Autowired
    protected ZhfxSettleStatusService zhfxSettleStatusService;

    @Override
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            // 返回未清算标识,便于前端展示文案
            SectionProfitBean sectionProfitBean = new SectionProfitBean();
            sectionProfitBean.setNoInit(true);
            return sectionProfitBean;
        }
        Integer today = DateUtil.getCuryyyyMMddInteger();
        SectionProfitBean profitSectionSettle = new SectionProfitBean();

        // 非交易日不展示收益
        if (!tradeDateDao.isMarket(today)) {
            return null;
        }
        // 24点前清算完成
        if (Objects.equals(assetNew.getBizDate(), today)) {
            // 查询清算的日收益额和日收益率
            List<ProfitDay> profitDayList = profitDayService.queryProfitByBizDate(assetNew.getFundId(), today);
            List<ProfitRateDay> profitRateDayList = profitRateDayService.queryProfitRateByBizDate(assetNew.getFundId(), today);
            if (CollectionUtils.isNotEmpty(profitDayList) && CollectionUtils.isNotEmpty(profitRateDayList)) {
                profitSectionSettle.setProfit(profitDayList.get(0).getProfit());
                profitSectionSettle.setProfitRate(profitRateDayList.get(0).getProfitRate());
                profitSectionSettle.setEuTime(assetNew.getEuTime());
                profitSectionSettle.setIndexDate(today);
                return profitSectionSettle;
            }
        }

        // 根据清算日期判断是否计算实时收益
        ZhfxSettleStatus settleStatus = zhfxSettleStatusService.getZhfxSettleStatus();
        if (Objects.nonNull(settleStatus)
                && Objects.equals(settleStatus.getStatus(), ZhfxSettleStatusEnum.INIT.getStatus())
                && Objects.equals(settleStatus.getBizDate(), today)) {
            // 当日清算完成,使用清算日期
            params.put("accountBizDate", settleStatus.getBizDate());
        } else {
            // 未清算完成,使用上一交易日
            params.put("accountBizDate", Integer.parseInt(tradeDateDao.getPreMarketDay(today)));
        }
        params.put("startDate", today);

        // 查询实时收益
        profitSectionSettle = profitSectionServiceRealTime.getProfitSection(params);
        if (Objects.nonNull(profitSectionSettle)) {
            profitSectionSettle.setIndexDate(today);
        }

        return profitSectionSettle;
    }
}
