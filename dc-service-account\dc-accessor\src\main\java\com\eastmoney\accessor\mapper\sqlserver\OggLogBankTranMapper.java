package com.eastmoney.accessor.mapper.sqlserver;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.LogBankTran;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MsLogBankTranMapper
 * @Description
 * @date 2022/11/7
 */
@Repository
public interface OggLogBankTranMapper extends BaseMapper {

    List<LogBankTran> selectBySysDate(Map<String, Object> params);

    Double selectFundIaAdjustFundAsset(Map<String,Object> param);
}
