package com.eastmoney.quote.mdstp.starter;

import com.eastmoney.quote.mdstp.model.QuoteTypeEnum;
import com.eastmoney.quote.mdstp.pull.client.RpcClient;
import com.eastmoney.quote.mdstp.pull.conf.QuoteConstant;
import com.eastmoney.quote.mdstp.pull.serializer.Packet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by sunyuncai on 2016/10/31.
 */
public class PullStarter {
    private static final Logger LOG = LoggerFactory.getLogger(PullStarter.class);
    public static void start(String quoteType,String host,int port) {
        final RpcClient rpcClient = new RpcClient(quoteType,host,port);
        try {
            Packet packet = new Packet();
            if (QuoteConstant.QUOTE_TYPE_HS.equals(quoteType)) {
                packet.setType(QuoteTypeEnum.QuoteHSSnapshot.getValue());
            } else if (QuoteConstant.QUOTE_TYPE_HK.equals(quoteType)) {
                packet.setType(QuoteTypeEnum.QuoteHKSnapshot.getValue());
            }
            rpcClient.send(packet);
            LOG.info("服务启动首次加载mdstp行情成功!");
        } catch (Exception e) {
            LOG.error("服务启动首次加载mdstp行情失败!", e);
        }
    }

}
