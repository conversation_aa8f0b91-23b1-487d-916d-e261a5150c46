package com.eastmoney.service.util;

import com.eastmoney.common.util.LogUtil;
import com.eastmoney.service.exception.ChannelException;
import com.eastmoney.service.model.ChannelData;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;

/**
 * Created by sunyuncai on 2016/10/24.
 */
public class ExceptionUtil {
    private static final Integer ERROR_MESSAGE_LENGTH = 50;
    private static final String FUN_CODE = "funCode";
    public static void handle(Throwable t, ChannelData channelData) {
        String errorMessage = "";
        String action = "exception";
        String requestId = "";
        if (t instanceof ChannelException) {
            errorMessage = ((ChannelException) t).getErrMsg();
        } else {
            errorMessage = t.getMessage();
            if (errorMessage == null || errorMessage.length() < ERROR_MESSAGE_LENGTH) {
                errorMessage = getImportStackTrace(t);
            }
        }

        errorMessage += " info:" + channelData.getMessage();
        if(!StringUtils.isEmpty(channelData.getParams().get(FUN_CODE))) {
            action = channelData.getParams().get(FUN_CODE).toString();
        }
        requestId = new String(channelData.getMessage().getRequestId());
        LogUtil.fatal(action, errorMessage, requestId, 0L);

    }

    private static String getImportStackTrace(Throwable t) {
        if (t instanceof InvocationTargetException) {
            t = ((InvocationTargetException) t).getTargetException();
        }

        String errorMessage = getExceptionMessage(t);
        Throwable causeException = t.getCause();
        if (causeException != null) {
            errorMessage += getExceptionMessage(causeException);
        }
        return errorMessage;
    }

    private static String getExceptionMessage(Throwable t) {
        StringBuffer errorMessage = new StringBuffer();
        StackTraceElement[] stackTraceElements = t.getStackTrace();
        errorMessage.append(t.getClass().toString().replaceAll("class", "") + "：");
        errorMessage.append(t.getMessage() + "\n");
        for (int i = 0; i < stackTraceElements.length; i++) {
            errorMessage.append("\t" + stackTraceElements[i] + "\n");
            if ((i == stackTraceElements.length - 1)
                    || (stackTraceElements[i].toString().contains("com.eastmoney") && !stackTraceElements[i + 1].toString().contains("com.eastmoney"))) {
                break;
            }
        }
        return errorMessage.toString();
    }

}
