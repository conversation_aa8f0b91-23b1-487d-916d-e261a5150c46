package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.HgtTradeDayDO;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * Created by robin on 2016/6/24.
 *
 * <AUTHOR>
 * <p>
 * update on 2016/7/19
 * <AUTHOR>
 */
@Repository
public interface TiHgtTradeDateMapper extends BaseMapper<Object, Integer> {

    /**
     * 查询今天是否交易日
     *
     * @return
     */
    HgtTradeDayDO todayIsMarket(Map<String, Object> params);

    /**
     * 查询港股上一交易日是否大于 A股和港股上一相等交易日
     * @param params
     * @return
     */
    Integer calHkProfitFlag(Map<String, Object> params);
}
