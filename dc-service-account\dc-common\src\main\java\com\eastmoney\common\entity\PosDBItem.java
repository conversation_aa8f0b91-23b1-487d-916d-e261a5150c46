package com.eastmoney.common.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;

/**
 * 存过出参类型
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
public class PosDBItem {
    /**
     * 集合类别
     */
    public String fundStkType;

    @JSONField(serialize = false)
    public String custId;
    @JSONField(serialize = false)
    public String orgId;
    @JSONField(serialize = false)
    public String brhId;
    @JSONField(serialize = false)
    public String operWay;
    @JSONField(serialize = false)
    public String bankCode;
    @JSONField(serialize = false)
    public String status;
    @JSONField(serialize = false)
    public String fundName;
    @JSONField(serialize = false)
    public String idNo;
    @JSONField(serialize = false)
    public String idType;
    public long fundId;
    @JSONField(serialize = false)
    public int fundSeq;
    @JSONField(serialize = false)
    public Double fundFetch;
    //    @JSONField(serialize = false)
    public Double fundBuySale;
    @JSONField(serialize = false)
    public Double fundBrkBuy; //报价回购终止买入差额, lIds, ********, 报价回购
    //    @JSONField(serialize = false)
    public Double fundUncomeBuy;
    //    @JSONField(serialize = false)
    public Double fundUncomeSale;
    //    @JSONField(serialize = false)
    public Double fundBuy;
    //    @JSONField(serialize = false)
    public Double fundSale;
    //    @JSONField(serialize = false)
    public Double bbContamt;
    @JSONField(serialize = false)
    public Double fundLastBal;
    @JSONField(serialize = false)
    public Double fundAsset;
    //    @JSONField(serialize = false)
    public Double creditSval; // 买券卖券资金
    //    @JSONField(serialize = false)
    public Double openFundVal; //开放基金市值
    //    @JSONField(serialize = false)
    public Double realRqAmt;   //实时融券资产, add by lIds, 20111102, ***********-ZXJT-005
    @JSONField(serialize = false)
    public Double fundLoan;//add by fengjy 20100612
    //    @JSONField(serialize = false)
    public Double xjbFundVal; //SPB-2765,liyi,20150413,增加现金宝市值输出
    @JSONField(serialize = false)
    public Double fundPartAvl;    //二级资金可用，tanwj，港股通
    @JSONField(serialize = false)
    public Double avlAmt;

    public Double fundAssetAdjamt;//补偿资金资产
    public Double stkAssetAdjamt;//补偿证券资产

    public Double fundAll; // 总资产
    public Double fundMktVal; // 总市值

    public Double stkMktVal; // 证券市值
    public Double fundAvl; // 总可用
    public Double fundBal; // 资金余额
    public Double fundFrz; // 冻结金额
    public String moneyType;//货币种类
    public Double dayProfit; //当日盈亏
    public Double maxDraw;//可取金额
    public String fundType;
    public Double sumIncome;//持仓盈亏
    public Double otcAsset;//OTC资产
    public Double cashProVal;//现金宝资产---2020/3/26 存储过程更新，现金宝不再存在xjbFundVal字段
    @JSONField(serialize = false)
    public Double fundEffect7X24;
    @JSONField(serialize = false)
    public Double fundEffect7X24ShiftIn;//7*24银证转账转入
    @JSONField(serialize = false)
    public Double fundEffect7X24ShiftOut;//7*24银证转账转
    public Double fundIaAsset;//基金投顾总资产

    private List<PositionInfo> positionInfoList;
    private List<PositionInfo> stkInfoList;
    private List<LogAsset> logAssetList;

    // 持仓信息用于计算当日盈亏
    List<PositionInfo> profitStkInfoList;
    // 流水信息用于计算当日盈亏
    List<LogAsset> profitLogAssetList;

    /**
     * 仓位
     */
    private Double positionRate;
    /**
     * 总资产（包含otc）
     */
    private Double fundAllWithOtc;

    /**
     * 7*24银证转账（三合一存过）
     */
    @JSONField(serialize = false)
    public Double f7X24zzAmt;

    /**
     * 补偿资产 V1.0.9.0 （三合一存过）
     */
    public Double assetAdjAmt;

    public String market;//市场
    public String stkCode; //证券代码
    public String stkType;//证券类型
    public Double buyCost;//当前成本
    public Long stkQty;//证券数量
    public Double bondIntr; //债券应计利息
    public Double closePrice;
    public Double openPrice;
    public Double lastPrice;//最新价格
    public String lofMoneyFlag;
    public String mtkCalFlag;//市值计算标识
    public String stkLevel;
    public Double settRate;  //购买的费率
    public Double saleSettRate; //卖出费率
    public Double ticketPrice;  //私募
    private StkPrice stkPrice;
    private String expandNameAbbr; // 扩位简称
    public Long stkBal;//股份余额
    public Long stkAvl;//可用数量
    public Long stkUnComeBuy;//在途买入
    public Long stkUnComeSale;//在途卖出
    public Long stkBuySale;//股份实时买卖差额

    public String stkName;//证券名称

    public String postStr;//定位串
    public String secuId;//股东代码
    public Double costPrice;//成本价格
    public Double costPriceEx;//成本价格扩展
    public Double mktVal;//最新市值
    public Double profitCost;//参考成本
    public Double profitPrice;//参考成本价
    public Double stkBuy;//股份买入解冻
    public Double stkSale;//股份卖出冻结
    public Double stkFrz;//冻结数量
    public Double stkTrdFrz;//买入申赎差
    public Double stkTrdUnFrz;//申赎数量
    public Double stkDiff;//可申赎数量
    public String priceFlag;
    public Double income;//累计盈亏
    public Double proIncome;//参考盈亏
    public double profitRate;//盈亏比例
    public String publishName; //行业分类名称
    public Double price; //价格
    public boolean isClear = false;
    public Double dayProfitRate;//日收益率

    /**
     * 持仓开始日期
     */
    private Integer startDate;
    /**
     * 持仓天数
     */
    private Integer holdDays;
    /**
     * 标签
     */
    private List<String> labels;

    private Integer operDate;/*操作日期, 物理日期 (委托日期)*/
    private Integer clearDate;/*清算日期, (成交日期)*/
    private Integer bizDate;/*交收日期, 表明该笔业务所属日期*/
    private Long sno;/*流水号*/
    private Long relativeSno;/*双向操作的关联流水号, (资金内部划转的划入流水号)*/
    private String custName;/*客户姓名*/
    private String custKind;/*客户类别*/
    private String custGroup;/*客户分组*/
    private String fundKind;/*资金分类*/
    private String fundLevel;/*资金室号*/
    private String fundGroup;/*资金分组*/
    private Long digestId;/*摘要代码*/
    private String digestName;/*业务名称*/
    private Double fundEffect;/*资金发生金额*/
    private String bizType;/*业务大类*/
    private String trdBankCode;/*银证通银行代码*/
    private String bankBranch;/*银行支行*/
    private String bankNetPlace;/*银行网点*/
    private Long stkEffect;/*证券发生数, (过户数量)*/
    private String orderId;/*合同序号*/
    private String trdId;/*交易类型*/
    private Long orderQty;/*委托数量*/
    private Double orderPrice;/*委托价格*/
    private String orderDate;/*委托日期*/
    private String orderTime;/*委托时间*/
    private Long matchQty;/*成交数量*/
    private Double matchAmt;/*成交金额*/
    private String seat;/*席位代码*/
    private Long matchTimes;/*成交笔数*/
    private Double matchPrice;/*成交价格*/
    private Long matchTime;/*成交时间, (第一笔成交的时间)*/
    private String matchCode;/*实时成交号码, (存存取款流水号,第一笔成交号码)*/
    public String bsFlag;/*买卖类别(orderRec的)*/
    private Double feeFront;/*前台费用*/
    private String sourceType;/*发起方, '0' 本部发起 'B'银行发起 'S'券商发起*/
    private String bankId;/*银行帐号*/
    private Long agentId;/*代理人代码*/
    private Long operId;/*操作人代码*/
    private String operOrg;/*操作营业部代码*/
    private String operLevel;/*操作员级别*/
    private String netAddr;/*操作站点*/
    private Long chkOper;/*审核柜员 0证券交收流水 1补做交收流水*/
    private String checkFlag;/*复核标志*/
    private Long brokerId;/*经纪人*/
    private Long custMgrId;/*客户经理*/
    private Long fundUser0;/*客户其它分类0*/
    private Long fundUser1;/* 客户其它分类1*/
    private Double privilege;/*盈亏金额*/
    private String remark;/*备用*/
    private Long orderSno;/*委托号*/
    private String pathId;/*接口种类标识*/
    private String cancelFlag;/*冲销标志 '0' 未冲 '1' 已冲*/
    private String reportKind;/*资金报表分类*/
    private String creditId;/*融资品种标识*/
    private String creditFlag;/*融资开仓平仓强平*/
    private String prodCode;/* 产品编码 6位的证券代码 + 3位购回天数 + 3位期号*/
    private Double setTrate;/*结算汇率 沪港通业务*/
    private String shiftAmt;    //转入转出金额
    private Double feeJsxf;/*净佣金*/
    private Double feeSxf;/*佣金*/
    private Double feeYhs;/*'0'-'印花税'*/
    private Double feeGhf;/*'1'-'过户费'*/
    private Double feeQsf;/*'2'-'清算费'*/
    private Double feeJygf;/*'3'-'交易规费'*/
    private Double feeJsf;/*'4'-'经手费'*/
    private Double feeZgf;/*'5'-'证管费'*/
    private Double feeQtf;/*'7'-'其他费'*/
    private Double feeOneYhs; //一级印花税
    private Double feeOneGhf;   //一级过户费
    private Double feeOneQsf;   //一级清算费
    private Double feeOneJygf;  //一级交易规费
    private Double feeOneJsf;  //一级经手费
    private Double feeOneZgf;  //一级证管费
    private Double feeOneQtf;  //一级证管费
    private Double feeOneFxj;  //一级证管费
    private Double feeTotal; //费用累计
    private String positionFlag;//建仓清仓标记  1为建仓  2为清仓
    /**
     *  买入成交：case when bsflag='0B' then matchprice*matchqty else 0 end
     */
    private Double buyMatchAmt;

    /**
     *  卖出成交：case when bsflag='0S' then matchprice*matchqty else 0 end
     */
    private Double saleMatchAmt;

    /**
     * 买入份额：case when bsflag='0B' then matchqty else 0 end
     */
    private Long buystkqty;
    /**
     * 卖出份额：case when bsflag='0S' then matchqty else 0 end
     */
    private Long salestkqty;
    private Integer serverId;//服务器编号

    public Integer quitDate;//退市日期

    /******* PCMP-10616646 【柜台配合】港股通当日收益计算优化 start *******/
    public Long totalBuyQty;    // 当日累计买入数量，港股通收益使用
    public Double totalBuyAmt;  // 当日累计买入实时清算金额，港股通收益使用
    public Long totalSaleQty;   // 当日累计卖出数量，港股通收益使用
    public Double totalSaleAmt; // 当日累计卖出实时清算金额，港股通收益使用
    /******* PCMP-10616646 【柜台配合】港股通当日收益计算优化 end *******/

    public List<QryFund> qryFundList;//资产集合

    public String getFundStkType() {
        return fundStkType;
    }

    public void setFundStkType(String fundStkType) {
        this.fundStkType = fundStkType;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId;
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public long getFundId() {
        return fundId;
    }

    public void setFundId(long fundId) {
        this.fundId = fundId;
    }

    public int getFundSeq() {
        return fundSeq;
    }

    public void setFundSeq(int fundSeq) {
        this.fundSeq = fundSeq;
    }

    public Double getFundFetch() {
        return fundFetch;
    }

    public void setFundFetch(Double fundFetch) {
        this.fundFetch = fundFetch;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundBrkBuy() {
        return fundBrkBuy;
    }

    public void setFundBrkBuy(Double fundBrkBuy) {
        this.fundBrkBuy = fundBrkBuy;
    }

    public Double getFundUncomeBuy() {
        return fundUncomeBuy;
    }

    public void setFundUncomeBuy(Double fundUncomeBuy) {
        this.fundUncomeBuy = fundUncomeBuy;
    }

    public Double getFundUncomeSale() {
        return fundUncomeSale;
    }

    public void setFundUncomeSale(Double fundUncomeSale) {
        this.fundUncomeSale = fundUncomeSale;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getBbContamt() {
        return bbContamt;
    }

    public void setBbContamt(Double bbContamt) {
        this.bbContamt = bbContamt;
    }

    public Double getFundLastBal() {
        return fundLastBal;
    }

    public void setFundLastBal(Double fundLastBal) {
        this.fundLastBal = fundLastBal;
    }

    public Double getFundAsset() {
        return fundAsset;
    }

    public void setFundAsset(Double fundAsset) {
        this.fundAsset = fundAsset;
    }

    public Double getCreditSval() {
        return creditSval;
    }

    public void setCreditSval(Double creditSval) {
        this.creditSval = creditSval;
    }

    public Double getOpenFundVal() {
        return openFundVal;
    }

    public void setOpenFundVal(Double openFundVal) {
        this.openFundVal = openFundVal;
    }

    public Double getRealRqAmt() {
        return realRqAmt;
    }

    public void setRealRqAmt(Double realRqAmt) {
        this.realRqAmt = realRqAmt;
    }

    public Double getFundLoan() {
        return fundLoan;
    }

    public void setFundLoan(Double fundLoan) {
        this.fundLoan = fundLoan;
    }

    public Double getXjbFundVal() {
        return xjbFundVal;
    }

    public void setXjbFundVal(Double xjbFundVal) {
        this.xjbFundVal = xjbFundVal;
    }

    public Double getFundPartAvl() {
        return fundPartAvl;
    }

    public void setFundPartAvl(Double fundPartAvl) {
        this.fundPartAvl = fundPartAvl;
    }

    public Double getAvlAmt() {
        return avlAmt;
    }

    public void setAvlAmt(Double avlAmt) {
        this.avlAmt = avlAmt;
    }

    public Double getFundAssetAdjamt() {
        return fundAssetAdjamt;
    }

    public void setFundAssetAdjamt(Double fundAssetAdjamt) {
        this.fundAssetAdjamt = fundAssetAdjamt;
    }

    public Double getStkAssetAdjamt() {
        return stkAssetAdjamt;
    }

    public void setStkAssetAdjamt(Double stkAssetAdjamt) {
        this.stkAssetAdjamt = stkAssetAdjamt;
    }

    public Double getFundAll() {
        return fundAll;
    }

    public void setFundAll(Double fundAll) {
        this.fundAll = fundAll;
    }

    public Double getFundMktVal() {
        return fundMktVal;
    }

    public void setFundMktVal(Double fundMktVal) {
        this.fundMktVal = fundMktVal;
    }

    public Double getStkMktVal() {
        return stkMktVal;
    }

    public void setStkMktVal(Double stkMktVal) {
        this.stkMktVal = stkMktVal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundFrz() {
        return fundFrz;
    }

    public void setFundFrz(Double fundFrz) {
        this.fundFrz = fundFrz;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public Double getDayProfit() {
        return dayProfit;
    }

    public void setDayProfit(Double dayProfit) {
        this.dayProfit = dayProfit;
    }

    public Double getMaxDraw() {
        return maxDraw;
    }

    public void setMaxDraw(Double maxDraw) {
        this.maxDraw = maxDraw;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Double getSumIncome() {
        return sumIncome;
    }

    public void setSumIncome(Double sumIncome) {
        this.sumIncome = sumIncome;
    }

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public Double getCashProVal() {
        return cashProVal;
    }

    public void setCashProVal(Double cashProVal) {
        this.cashProVal = cashProVal;
    }

    public Double getFundEffect7X24() {
        return fundEffect7X24;
    }

    public void setFundEffect7X24(Double fundEffect7X24) {
        this.fundEffect7X24 = fundEffect7X24;
    }

    public Double getFundEffect7X24ShiftIn() {
        return fundEffect7X24ShiftIn;
    }

    public void setFundEffect7X24ShiftIn(Double fundEffect7X24ShiftIn) {
        this.fundEffect7X24ShiftIn = fundEffect7X24ShiftIn;
    }

    public Double getFundEffect7X24ShiftOut() {
        return fundEffect7X24ShiftOut;
    }

    public void setFundEffect7X24ShiftOut(Double fundEffect7X24ShiftOut) {
        this.fundEffect7X24ShiftOut = fundEffect7X24ShiftOut;
    }

    public Double getFundIaAsset() {
        return fundIaAsset;
    }

    public void setFundIaAsset(Double fundIaAsset) {
        this.fundIaAsset = fundIaAsset;
    }

    public List<PositionInfo> getPositionInfoList() {
        return positionInfoList;
    }

    public void setPositionInfoList(List<PositionInfo> positionInfoList) {
        this.positionInfoList = positionInfoList;
    }

    public List<PositionInfo> getStkInfoList() {
        return stkInfoList;
    }

    public void setStkInfoList(List<PositionInfo> stkInfoList) {
        this.stkInfoList = stkInfoList;
    }

    public List<LogAsset> getLogAssetList() {
        return logAssetList;
    }

    public void setLogAssetList(List<LogAsset> logAssetList) {
        this.logAssetList = logAssetList;
    }

    public List<PositionInfo> getProfitStkInfoList() {
        return profitStkInfoList;
    }

    public void setProfitStkInfoList(List<PositionInfo> profitStkInfoList) {
        this.profitStkInfoList = profitStkInfoList;
    }

    public List<LogAsset> getProfitLogAssetList() {
        return profitLogAssetList;
    }

    public void setProfitLogAssetList(List<LogAsset> profitLogAssetList) {
        this.profitLogAssetList = profitLogAssetList;
    }

    public Double getPositionRate() {
        return positionRate;
    }

    public void setPositionRate(Double positionRate) {
        this.positionRate = positionRate;
    }

    public Double getFundAllWithOtc() {
        return fundAllWithOtc;
    }

    public void setFundAllWithOtc(Double fundAllWithOtc) {
        this.fundAllWithOtc = fundAllWithOtc;
    }

    public Double getF7X24zzAmt() {
        return f7X24zzAmt;
    }

    public void setF7X24zzAmt(Double f7X24zzAmt) {
        this.f7X24zzAmt = f7X24zzAmt;
    }

    public Double getAssetAdjAmt() {
        return assetAdjAmt;
    }

    public void setAssetAdjAmt(Double assetAdjAmt) {
        this.assetAdjAmt = assetAdjAmt;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public Double getBuyCost() {
        return buyCost;
    }

    public void setBuyCost(Double buyCost) {
        this.buyCost = buyCost;
    }

    public Long getStkQty() {
        return stkQty;
    }

    public void setStkQty(Long stkQty) {
        this.stkQty = stkQty;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public Double getClosePrice() {
        return closePrice;
    }

    public void setClosePrice(Double closePrice) {
        this.closePrice = closePrice;
    }

    public Double getOpenPrice() {
        return openPrice;
    }

    public void setOpenPrice(Double openPrice) {
        this.openPrice = openPrice;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getLofMoneyFlag() {
        return lofMoneyFlag;
    }

    public void setLofMoneyFlag(String lofMoneyFlag) {
        this.lofMoneyFlag = lofMoneyFlag;
    }

    public String getMtkCalFlag() {
        return mtkCalFlag;
    }

    public void setMtkCalFlag(String mtkCalFlag) {
        this.mtkCalFlag = mtkCalFlag;
    }

    public String getStkLevel() {
        return stkLevel;
    }

    public void setStkLevel(String stkLevel) {
        this.stkLevel = stkLevel;
    }

    public Double getSettRate() {
        return settRate;
    }

    public void setSettRate(Double settRate) {
        this.settRate = settRate;
    }

    public Double getSaleSettRate() {
        return saleSettRate;
    }

    public void setSaleSettRate(Double saleSettRate) {
        this.saleSettRate = saleSettRate;
    }

    public Double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(Double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public StkPrice getStkPrice() {
        return stkPrice;
    }

    public void setStkPrice(StkPrice stkPrice) {
        this.stkPrice = stkPrice;
    }

    public String getExpandNameAbbr() {
        return expandNameAbbr;
    }

    public void setExpandNameAbbr(String expandNameAbbr) {
        this.expandNameAbbr = expandNameAbbr;
    }

    public Long getStkBal() {
        return stkBal;
    }

    public void setStkBal(Long stkBal) {
        this.stkBal = stkBal;
    }

    public Long getStkAvl() {
        return stkAvl;
    }

    public void setStkAvl(Long stkAvl) {
        this.stkAvl = stkAvl;
    }

    public Long getStkUnComeBuy() {
        return stkUnComeBuy;
    }

    public void setStkUnComeBuy(Long stkUnComeBuy) {
        this.stkUnComeBuy = stkUnComeBuy;
    }

    public Long getStkUnComeSale() {
        return stkUnComeSale;
    }

    public void setStkUnComeSale(Long stkUnComeSale) {
        this.stkUnComeSale = stkUnComeSale;
    }

    public Long getStkBuySale() {
        return stkBuySale;
    }

    public void setStkBuySale(Long stkBuySale) {
        this.stkBuySale = stkBuySale;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getPostStr() {
        return postStr;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public Double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    }

    public Double getCostPriceEx() {
        return costPriceEx;
    }

    public void setCostPriceEx(Double costPriceEx) {
        this.costPriceEx = costPriceEx;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    public Double getProfitCost() {
        return profitCost;
    }

    public void setProfitCost(Double profitCost) {
        this.profitCost = profitCost;
    }

    public Double getProfitPrice() {
        return profitPrice;
    }

    public void setProfitPrice(Double profitPrice) {
        this.profitPrice = profitPrice;
    }

    public Double getStkBuy() {
        return stkBuy;
    }

    public void setStkBuy(Double stkBuy) {
        this.stkBuy = stkBuy;
    }

    public Double getStkSale() {
        return stkSale;
    }

    public void setStkSale(Double stkSale) {
        this.stkSale = stkSale;
    }

    public Double getStkFrz() {
        return stkFrz;
    }

    public void setStkFrz(Double stkFrz) {
        this.stkFrz = stkFrz;
    }

    public Double getStkTrdFrz() {
        return stkTrdFrz;
    }

    public void setStkTrdFrz(Double stkTrdFrz) {
        this.stkTrdFrz = stkTrdFrz;
    }

    public Double getStkTrdUnFrz() {
        return stkTrdUnFrz;
    }

    public void setStkTrdUnFrz(Double stkTrdUnFrz) {
        this.stkTrdUnFrz = stkTrdUnFrz;
    }

    public Double getStkDiff() {
        return stkDiff;
    }

    public void setStkDiff(Double stkDiff) {
        this.stkDiff = stkDiff;
    }

    public String getPriceFlag() {
        return priceFlag;
    }

    public void setPriceFlag(String priceFlag) {
        this.priceFlag = priceFlag;
    }

    public Double getIncome() {
        return income;
    }

    public void setIncome(Double income) {
        this.income = income;
    }

    public Double getProIncome() {
        return proIncome;
    }

    public void setProIncome(Double proIncome) {
        this.proIncome = proIncome;
    }

    public double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(double profitRate) {
        this.profitRate = profitRate;
    }

    public String getPublishName() {
        return publishName;
    }

    public void setPublishName(String publishName) {
        this.publishName = publishName;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public boolean isClear() {
        return isClear;
    }

    public void setClear(boolean clear) {
        isClear = clear;
    }

    public Double getDayProfitRate() {
        return dayProfitRate;
    }

    public void setDayProfitRate(Double dayProfitRate) {
        this.dayProfitRate = dayProfitRate;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(Integer holdDays) {
        this.holdDays = holdDays;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    public Integer getOperDate() {
        return operDate;
    }

    public void setOperDate(Integer operDate) {
        this.operDate = operDate;
    }

    public Integer getClearDate() {
        return clearDate;
    }

    public void setClearDate(Integer clearDate) {
        this.clearDate = clearDate;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Long getSno() {
        return sno;
    }

    public void setSno(Long sno) {
        this.sno = sno;
    }

    public Long getRelativeSno() {
        return relativeSno;
    }

    public void setRelativeSno(Long relativeSno) {
        this.relativeSno = relativeSno;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustKind() {
        return custKind;
    }

    public void setCustKind(String custKind) {
        this.custKind = custKind;
    }

    public String getCustGroup() {
        return custGroup;
    }

    public void setCustGroup(String custGroup) {
        this.custGroup = custGroup;
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind;
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel;
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup;
    }

    public Long getDigestId() {
        return digestId;
    }

    public void setDigestId(Long digestId) {
        this.digestId = digestId;
    }

    public String getDigestName() {
        return digestName;
    }

    public void setDigestName(String digestName) {
        this.digestName = digestName;
    }

    public Double getFundEffect() {
        return fundEffect;
    }

    public void setFundEffect(Double fundEffect) {
        this.fundEffect = fundEffect;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getTrdBankCode() {
        return trdBankCode;
    }

    public void setTrdBankCode(String trdBankCode) {
        this.trdBankCode = trdBankCode;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getBankNetPlace() {
        return bankNetPlace;
    }

    public void setBankNetPlace(String bankNetPlace) {
        this.bankNetPlace = bankNetPlace;
    }

    public Long getStkEffect() {
        return stkEffect;
    }

    public void setStkEffect(Long stkEffect) {
        this.stkEffect = stkEffect;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = trdId;
    }

    public Long getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Long orderQty) {
        this.orderQty = orderQty;
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat;
    }

    public Long getMatchTimes() {
        return matchTimes;
    }

    public void setMatchTimes(Long matchTimes) {
        this.matchTimes = matchTimes;
    }

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public Long getMatchTime() {
        return matchTime;
    }

    public void setMatchTime(Long matchTime) {
        this.matchTime = matchTime;
    }

    public String getMatchCode() {
        return matchCode;
    }

    public void setMatchCode(String matchCode) {
        this.matchCode = matchCode;
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag;
    }

    public Double getFeeFront() {
        return feeFront;
    }

    public void setFeeFront(Double feeFront) {
        this.feeFront = feeFront;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getOperOrg() {
        return operOrg;
    }

    public void setOperOrg(String operOrg) {
        this.operOrg = operOrg;
    }

    public String getOperLevel() {
        return operLevel;
    }

    public void setOperLevel(String operLevel) {
        this.operLevel = operLevel;
    }

    public String getNetAddr() {
        return netAddr;
    }

    public void setNetAddr(String netAddr) {
        this.netAddr = netAddr;
    }

    public Long getChkOper() {
        return chkOper;
    }

    public void setChkOper(Long chkOper) {
        this.chkOper = chkOper;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public Long getBrokerId() {
        return brokerId;
    }

    public void setBrokerId(Long brokerId) {
        this.brokerId = brokerId;
    }

    public Long getCustMgrId() {
        return custMgrId;
    }

    public void setCustMgrId(Long custMgrId) {
        this.custMgrId = custMgrId;
    }

    public Long getFundUser0() {
        return fundUser0;
    }

    public void setFundUser0(Long fundUser0) {
        this.fundUser0 = fundUser0;
    }

    public Long getFundUser1() {
        return fundUser1;
    }

    public void setFundUser1(Long fundUser1) {
        this.fundUser1 = fundUser1;
    }

    public Double getPrivilege() {
        return privilege;
    }

    public void setPrivilege(Double privilege) {
        this.privilege = privilege;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Long orderSno) {
        this.orderSno = orderSno;
    }

    public String getPathId() {
        return pathId;
    }

    public void setPathId(String pathId) {
        this.pathId = pathId;
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = reportKind;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(String creditFlag) {
        this.creditFlag = creditFlag;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public Double getSetTrate() {
        return setTrate;
    }

    public void setSetTrate(Double setTrate) {
        this.setTrate = setTrate;
    }

    public String getShiftAmt() {
        return shiftAmt;
    }

    public void setShiftAmt(String shiftAmt) {
        this.shiftAmt = shiftAmt;
    }

    public Double getFeeJsxf() {
        return feeJsxf;
    }

    public void setFeeJsxf(Double feeJsxf) {
        this.feeJsxf = feeJsxf;
    }

    public Double getFeeSxf() {
        return feeSxf;
    }

    public void setFeeSxf(Double feeSxf) {
        this.feeSxf = feeSxf;
    }

    public Double getFeeYhs() {
        return feeYhs;
    }

    public void setFeeYhs(Double feeYhs) {
        this.feeYhs = feeYhs;
    }

    public Double getFeeGhf() {
        return feeGhf;
    }

    public void setFeeGhf(Double feeGhf) {
        this.feeGhf = feeGhf;
    }

    public Double getFeeQsf() {
        return feeQsf;
    }

    public void setFeeQsf(Double feeQsf) {
        this.feeQsf = feeQsf;
    }

    public Double getFeeJygf() {
        return feeJygf;
    }

    public void setFeeJygf(Double feeJygf) {
        this.feeJygf = feeJygf;
    }

    public Double getFeeJsf() {
        return feeJsf;
    }

    public void setFeeJsf(Double feeJsf) {
        this.feeJsf = feeJsf;
    }

    public Double getFeeZgf() {
        return feeZgf;
    }

    public void setFeeZgf(Double feeZgf) {
        this.feeZgf = feeZgf;
    }

    public Double getFeeQtf() {
        return feeQtf;
    }

    public void setFeeQtf(Double feeQtf) {
        this.feeQtf = feeQtf;
    }

    public Double getFeeOneYhs() {
        return feeOneYhs;
    }

    public void setFeeOneYhs(Double feeOneYhs) {
        this.feeOneYhs = feeOneYhs;
    }

    public Double getFeeOneGhf() {
        return feeOneGhf;
    }

    public void setFeeOneGhf(Double feeOneGhf) {
        this.feeOneGhf = feeOneGhf;
    }

    public Double getFeeOneQsf() {
        return feeOneQsf;
    }

    public void setFeeOneQsf(Double feeOneQsf) {
        this.feeOneQsf = feeOneQsf;
    }

    public Double getFeeOneJygf() {
        return feeOneJygf;
    }

    public void setFeeOneJygf(Double feeOneJygf) {
        this.feeOneJygf = feeOneJygf;
    }

    public Double getFeeOneJsf() {
        return feeOneJsf;
    }

    public void setFeeOneJsf(Double feeOneJsf) {
        this.feeOneJsf = feeOneJsf;
    }

    public Double getFeeOneZgf() {
        return feeOneZgf;
    }

    public void setFeeOneZgf(Double feeOneZgf) {
        this.feeOneZgf = feeOneZgf;
    }

    public Double getFeeOneQtf() {
        return feeOneQtf;
    }

    public void setFeeOneQtf(Double feeOneQtf) {
        this.feeOneQtf = feeOneQtf;
    }

    public Double getFeeOneFxj() {
        return feeOneFxj;
    }

    public void setFeeOneFxj(Double feeOneFxj) {
        this.feeOneFxj = feeOneFxj;
    }

    public Double getFeeTotal() {
        return feeTotal;
    }

    public void setFeeTotal(Double feeTotal) {
        this.feeTotal = feeTotal;
    }

    public String getPositionFlag() {
        return positionFlag;
    }

    public void setPositionFlag(String positionFlag) {
        this.positionFlag = positionFlag;
    }

    public Double getBuyMatchAmt() {
        return buyMatchAmt;
    }

    public void setBuyMatchAmt(Double buyMatchAmt) {
        this.buyMatchAmt = buyMatchAmt;
    }

    public Double getSaleMatchAmt() {
        return saleMatchAmt;
    }

    public void setSaleMatchAmt(Double saleMatchAmt) {
        this.saleMatchAmt = saleMatchAmt;
    }

    public Long getBuystkqty() {
        return buystkqty;
    }

    public void setBuystkqty(Long buystkqty) {
        this.buystkqty = buystkqty;
    }

    public Long getSalestkqty() {
        return salestkqty;
    }

    public void setSalestkqty(Long salestkqty) {
        this.salestkqty = salestkqty;
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public Integer getQuitDate() {
        return quitDate;
    }

    public void setQuitDate(Integer quitDate) {
        this.quitDate = quitDate;
    }

    public Long getTotalBuyQty() {
        return totalBuyQty;
    }

    public void setTotalBuyQty(Long totalBuyQty) {
        this.totalBuyQty = totalBuyQty;
    }

    public Double getTotalBuyAmt() {
        return totalBuyAmt;
    }

    public void setTotalBuyAmt(Double totalBuyAmt) {
        this.totalBuyAmt = totalBuyAmt;
    }

    public Long getTotalSaleQty() {
        return totalSaleQty;
    }

    public void setTotalSaleQty(Long totalSaleQty) {
        this.totalSaleQty = totalSaleQty;
    }

    public Double getTotalSaleAmt() {
        return totalSaleAmt;
    }

    public void setTotalSaleAmt(Double totalSaleAmt) {
        this.totalSaleAmt = totalSaleAmt;
    }

    public List<QryFund> getQryFundList() {
        return qryFundList;
    }

    public void setQryFundList(List<QryFund> qryFundList) {
        this.qryFundList = qryFundList;
    }

    public static List typeConvert(List<PosDBItem> source, Class type){
        List target = new ArrayList<>();

        for (PosDBItem object : source) {
            Object item = new Object();
            if (type == QryFund.class) {
                item = QryFund.of(object);
            } else if (type == PositionInfo.class) {
                item = PositionInfo.of(object);
            } else if (type == LogAsset.class) {
                item = LogAsset.of(object);
            }
            target.add(item);
        }

        return target;
    }

}
