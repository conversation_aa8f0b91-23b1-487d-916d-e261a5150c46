<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiTradeDateMapper">
    <!-- 判断今日是否为交易日,(根据沪市日期匹配)  -->
    <select id="todayIsMarket" useCache="false" resultType="int">
        <if test="bizdate != null">
            SELECT COUNT(1) FROM ATCENTER.TRADE_DAY WHERE TDATE = #{bizdate} AND MARKET ='SH'
        </if>
        <if test="bizdate == null">
            SELECT COUNT(1) FROM ATCENTER.TRADE_DAY WHERE TDATE = DATE_FORMAT(CURDATE(),'%Y%m%d') AND MARKET ='SH'
        </if>
    </select>

    <!-- 取得上一个交易日,(根据沪市日期匹配)  -->
    <select id="getPreMarketDay" useCache="false" resultType="String">
        <if test="bizdate != null">
            select TRADEDATE from ATCENTER.TRADE_DAY where TDATE <![CDATA[<]]> #{bizdate} and MARKET = 'SH' order by TDATE desc limit 1
        </if>
        <if test="bizdate == null">
            select TRADEDATE from ATCENTER.TRADE_DAY where TDATE <![CDATA[<]]> DATE_FORMAT(CURDATE(),'%Y%m%d') and MARKET = 'SH' order by TDATE desc limit 1
        </if>

    </select>

    <select id="getNextMarketDay" useCache="false" resultType="String">
        <if test="bizdate != null">
            select TRADEDATE from ATCENTER.TRADE_DAY where TDATE <![CDATA[>]]> #{bizdate} and MARKET = 'SH' order by TDATE limit 1
        </if>
        <if test="bizdate == null">
            select TRADEDATE from ATCENTER.TRADE_DAY where TDATE <![CDATA[>]]> DATE_FORMAT(CURDATE(),'%Y%m%d') and MARKET = 'SH' order by TDATE limit 1
        </if>
    </select>

    <select id="getTradeDateList" resultType="com.eastmoney.common.entity.TradeDate">
        SELECT TDATE as tradeDate FROM ATCENTER.TRADE_DAY WHERE DATE_FORMAT(TDATE,'%Y%m') = #{tradeDate} and MARKET = 'SH' order by TDATE ASC
    </select>

    <select id="getAllTradeDateList" resultType="com.eastmoney.common.entity.TradeDate">
        SELECT TDATE as tradeDate FROM ATCENTER.TRADE_DAY WHERE
        TDATE <![CDATA[>=]]> #{startDate}
        and MARKET = 'SH'  order by TRADEDATE ASC
    </select>

    <select id="getLastTradeDateOfMonth" resultType="com.eastmoney.common.entity.TradeDate">
        select TDATE as tradeDate from (
             SELECT TDATE, ROW_NUMBER() over(PARTITION by left(TDATE, 6) order by TDATE desc) as rn
             from atcenter.TRADE_DAY
             where TDATE BETWEEN #{startDate} and #{endDate} and TRADEMARKETCODE = '069001001'
         ) r where r.rn = 1
    </select>

</mapper>