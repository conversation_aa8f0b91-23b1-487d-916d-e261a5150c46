package com.eastmoney.service.service.profit.hgt;

/**
 * <AUTHOR>
 * @description 港股通假期收益计算服务
 * @date 2025/3/27 15:09
 */
public interface HgtHolidayProfitService {

    /**
     * 获取港股通节假日收益，全异常捕获
     * <p>
     * 防弹方法
     *
     * @param fundId         资金账号
     * @param accountBizDate 账户分析清算日期
     * @return 节假日收益值，非包装类型，避免带来NULL歧义
     */
    double getHgtHolidayProfitWithTryCatch(final long fundId, final int accountBizDate);

    /**
     * 获取港股通节假日收益
     *
     * @param fundId         资金账号
     * @param accountBizDate 账户分析清算日期
     * @return 节假日收益值，非包装类型，避免带来NULL歧义
     */
    double getHgtHolidayProfit(final long fundId, final int accountBizDate);
}
