/*
 * Copyright 1999-2011 Alibaba Group.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.eastmoney.transport.model;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Request.
 * 
 * <AUTHOR>
 */
public class Request {
    
    public static final String HEARTBEAT_EVENT = null;
    
    private static final AtomicLong INVOKE_ID = new AtomicLong(0);

    private final long id;

    private String  version;

    private Object  data;

    public Request() {
        this.id = newId();
    }

    public Request(long id){
        this.id = id;
    }

    public long getId() {
        return id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }


    private static long newId() {
        // getAndIncrement()增长到MAX_VALUE时，再增长会变为MIN_VALUE，负数也可以做为ID
        return INVOKE_ID.getAndIncrement();
    }

    @Override
    public String toString() {
        return "Request [id=" + id + ", version=" + version + "" +
                ", data=" + (data == this ? "this" : safeToString(data)) + "]";
    }

    private static String safeToString(Object data) {
        if (data == null) return null;
        String dataStr;
        try {
            dataStr = data.toString();
        } catch (Throwable e) {
            dataStr = "<Fail toString of " + data.getClass() + ", cause: " + "";
        }
        return dataStr;
    }
}
