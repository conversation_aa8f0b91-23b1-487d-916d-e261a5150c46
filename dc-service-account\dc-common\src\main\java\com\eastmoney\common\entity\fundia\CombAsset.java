package com.eastmoney.common.entity.fundia;

public class CombAsset {
    private Long fundId;
    private String investId;
    private Double bal;
    private Double avl;
    private Double unArriveAsset;
    private Double unArriveFund;
    private String type;
    private String combCode;
    private String combName;
    private String fundCode;
    private Double fundAssetAdjAmt;
    private Double stkAssetAdjAmt;


    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getInvestId() {
        return investId;
    }

    public void setInvestId(String investId) {
        this.investId = investId;
    }

    public Double getBal() {
        return bal;
    }

    public void setBal(Double bal) {
        this.bal = bal;
    }

    public Double getAvl() {
        return avl;
    }

    public void setAvl(Double avl) {
        this.avl = avl;
    }

    public Double getUnArriveAsset() {
        return unArriveAsset;
    }

    public void setUnArriveAsset(Double unArriveAsset) {
        this.unArriveAsset = unArriveAsset;
    }

    public Double getUnArriveFund() {
        return unArriveFund;
    }

    public void setUnArriveFund(Double unArriveFund) {
        this.unArriveFund = unArriveFund;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCombCode() {
        return combCode;
    }

    public void setCombCode(String combCode) {
        this.combCode = combCode;
    }

    public String getCombName() {
        return combName;
    }

    public void setCombName(String combName) {
        this.combName = combName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public Double getFundAssetAdjAmt() {
        return fundAssetAdjAmt;
    }

    public void setFundAssetAdjAmt(Double fundAssetAdjAmt) {
        this.fundAssetAdjAmt = fundAssetAdjAmt;
    }

    public Double getStkAssetAdjAmt() {
        return stkAssetAdjAmt;
    }

    public void setStkAssetAdjAmt(Double stkAssetAdjAmt) {
        this.stkAssetAdjAmt = stkAssetAdjAmt;
    }
}
