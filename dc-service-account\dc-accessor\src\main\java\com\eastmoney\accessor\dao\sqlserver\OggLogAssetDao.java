package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.common.entity.LogAsset;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/1/24.
 */
public interface OggLogAssetDao {
    List<LogAsset> selectTransferAmt(Map<String, Object> params);

    /**
     * 盘中交割单数据查询
     * @param params
     * @return
     */
    List<LogAsset> getRealTimeLogassetList(Map<String, Object> params);
}
