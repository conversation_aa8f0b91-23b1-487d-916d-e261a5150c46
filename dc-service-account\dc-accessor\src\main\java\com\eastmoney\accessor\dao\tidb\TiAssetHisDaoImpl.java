package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.AssetHisDao;
import com.eastmoney.accessor.mapper.tidb.TiAssetHisMapper;
import com.eastmoney.common.entity.cal.AssetHis;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by huachengqi on 2016/7/18.
 */
//@Conditional(ZhfxDataSourceCondition.class)
@Service("assetHisDao")
public class TiAssetHisDaoImpl extends BaseDao<TiAssetHisMapper, AssetHis, Integer> implements AssetHisDao {

    @Override
    public List<AssetHis> getAssetDayTrend(Map<String, Object> params) {
        return getMapper().getAssetDayTrend(params);
    }

    @Override
    public List<AssetHis> getMonthAssetDayTrend(Map<String, Object> params) {
        return getMapper().getMonthAssetDayTrend(params);
    }
}
