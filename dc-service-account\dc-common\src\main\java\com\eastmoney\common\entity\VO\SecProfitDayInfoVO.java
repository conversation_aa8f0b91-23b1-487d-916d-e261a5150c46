package com.eastmoney.common.entity.VO;


import java.util.ArrayList;
import java.util.List;

/**
 * 个股日收益列表
 * <AUTHOR>
 * @create 2024/3/13
 */
public class SecProfitDayInfoVO {

    /**
     * 资金帐号
     */
    private Long fundId;

    /**
     * 账户级别收益
     */
    private Double cashProfit;

    /**
     * 个股明细列表总条数
     */
    private Integer totalNum;

    /**
     * 日期是否支持: 0 支持但显示日期较远、1 支持并展示个股明细、2 不支持不显示明细模块
     */
    private String dateSupport;

    /**
     * 个股明细列表
     */
    private List<SecProfitDayVO> secProfitDays = new ArrayList<>();

    public SecProfitDayInfoVO(Long fundId, Integer totalNum) {
        this.fundId = fundId;
        this.totalNum = totalNum;
    }

    public SecProfitDayInfoVO() {
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Double getCashProfit() {
        return cashProfit;
    }

    public void setCashProfit(Double cashProfit) {
        this.cashProfit = cashProfit;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public List<SecProfitDayVO> getSecProfitDays() {
        return secProfitDays;
    }

    public void setSecProfitDays(List<SecProfitDayVO> secProfitDays) {
        this.secProfitDays = secProfitDays;
    }

    public String getDateSupport() {
        return dateSupport;
    }

    public void setDateSupport(String dateSupport) {
        this.dateSupport = dateSupport;
    }
}
