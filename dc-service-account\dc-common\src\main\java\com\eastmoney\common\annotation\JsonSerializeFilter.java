package com.eastmoney.common.annotation;

import com.eastmoney.common.serializer.AbstractSerializeFilter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by sunyuncai on 2016/6/18.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface JsonSerializeFilter {
    Class<? extends AbstractSerializeFilter> value();

}
