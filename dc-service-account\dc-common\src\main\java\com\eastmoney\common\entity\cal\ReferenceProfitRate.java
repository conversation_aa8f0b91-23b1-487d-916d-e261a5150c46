package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

/**
 * 收益率参考表
 * <p/>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/22.
 */
public class ReferenceProfitRate extends BaseEntity {
    private String timeSlot; //时间槽类型
    private Integer indexKey; //索引日期
    private Integer category; //参考收益率种类
    private Integer typeCode; //参考收益率类型代码
    private String typeName; //参考收益率名称
    private Double profitRate; //参考收益率
    private String remark; //备注信息

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ReferenceProfitRate() {
    }

    public ReferenceProfitRate(String timeSlot, Integer indexKey, Integer category, Integer typeCode,
                               String typeName,Double profitRate,String remark){
        this.timeSlot = timeSlot;
        this.indexKey = indexKey;
        this.category = category;
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.profitRate = profitRate;
        this.remark = remark;
    }

}
