package com.eastmoney.test;

import com.eastmoney.common.entity.cal.MergeTradeResult;
import com.eastmoney.service.handler.PositionHandler;
import com.eastmoney.service.main.AccountMain;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * Created on 2023/12/7
 * Description
 *
 * <AUTHOR>
 */

@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class PositionHandleTest {

    private static Logger LOG = LoggerFactory.getLogger(AccountMain.class);

    @InjectMocks
    PositionHandler positionHandler;


    private Map<String, Object> params = new HashMap<>();


    @BeforeEach
    public void init() {
        params.put("pageSize", "20");
        params.put("pageNo", "1");
    }

    /**
     * 测试持仓查询,同时采集数据库清算结果与接口实时计算结果
     */
    @Test
    public void getHoldingPositionProfitTest() {
        //设计入参
        params.put("fundId", "************");
        params.put("stkCode", "000725");
        params.put("market", '0');
        //接口调用
        MergeTradeResult holdPositionMergeTradeList = positionHandler.getHoldPositionMergeTradeList(params);
        //查询清算服务离线计算的交易税费
        Double tradeTaxFee = holdPositionMergeTradeList.getStkProfits().get(0).getTradeTaxFee();
        Assertions.assertNotNull(tradeTaxFee);
    }

    /**
     * 测试清仓查询,入参类型为Date类型，同时采集数据库清算结果与接口实时计算结果
     */
    @Test
    public void getClearPositionProfitOnDateCaseTest() {
        params.put("fundId", "************");
        params.put("stkCode", "601279");
        params.put("market", '1');
        params.put("startDate", "20220712");
        params.put("endDate", "20220725");
        //接口调用
        MergeTradeResult clearPositionMergeTradeList = (MergeTradeResult) positionHandler.getClearPositionMergeTradeList(params);
        //查询清算服务离线计算的交易税费
        Double tradeTaxFee = clearPositionMergeTradeList.getStkProfits().get(0).getTradeTaxFee();
        Assertions.assertNotNull(tradeTaxFee);
    }

    /**
     * 测试清仓查询,入参类型为unit类型，同时采集数据库清算结果与接口实时计算结果
     */
    @Test
    public void getClearPositionProfitOnUnitCaseTest() {
        params.put("fundId", "540700210737");
        params.put("unit", "Y");
        params.put("stkCode", "605577");
        params.put("market", '1');
        //接口调用
        MergeTradeResult clearPositionMergeTradeList = (MergeTradeResult) positionHandler.getClearPositionMergeTradeList(params);
        //查询清算服务离线计算的交易税费
        Double tradeTaxFee = clearPositionMergeTradeList.getStkProfits().get(0).getTradeTaxFee();
        Assertions.assertNotNull(tradeTaxFee);
    }
}
