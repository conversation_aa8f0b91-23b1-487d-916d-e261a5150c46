package com.eastmoney.accessor.dao.oracle;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateContrastMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateContrast;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by xiaoyongyong on 2016/7/20.
 * ProfitRateContrastServiceImpl
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitRateContrastDao")
public class ProfitRateContrastDaoImpl extends BaseDao<ProfitRateContrastMapper, ProfitRateContrast, Long> implements ProfitRateContrastDao {

}
