package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntity;

/**
 * 板块账单表
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/31.
 */
public class PublishBill extends BaseEntity {

    private Long fundId; //资金账号
    private Integer indexKey; //时间槽索引key，年:yyyy,月：yyyymm
    private String timeSlot; //时间槽类型，年:y，月:m
    private String publishCode; //板块编号
    private String publishName; //板块名
    private Integer rankIndex; //排名序号

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public String getPublishCode() {
        return publishCode;
    }

    public void setPublishCode(String publishCode) {
        this.publishCode = publishCode;
    }

    public String getPublishName() {
        return publishName;
    }

    public void setPublishName(String publishName) {
        this.publishName = publishName;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }
}
