package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.oracle.UserStatDao;
import com.eastmoney.accessor.dao.oracle.WholeStatDao;
import com.eastmoney.accessor.enums.StatTypeEnum;
import com.eastmoney.common.entity.cal.UserStat;
import com.eastmoney.common.entity.cal.WholeStat;
import com.eastmoney.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018-03-26 13:41
 **/
@Service
public class PersonalComputerHandler {

    @Autowired
    private UserStatDao userStatDao;
    @Autowired
    private WholeStatDao wholeStatDao;

    @Autowired
    private TradeDateDao tradeDateDao;

    //个人与整体仓位数据的汇总
    public Map getPositionsRate(Map<String, Object> params) {
        Map resultMap = new HashMap();
        //整体数据
        String nowDate = DateUtil.getCuryyyyMMdd();
        String bizDate = tradeDateDao.getPreMarketDay(Integer.valueOf(nowDate));
        params.put("bizDate", bizDate);
        List<WholeStat> wholeStatList = getWholeStatRate(params);
        if (CollectionUtils.isEmpty(wholeStatList)) {
            //再次重试
            bizDate = tradeDateDao.getPreMarketDay(Integer.valueOf(bizDate));
            params.put("bizDate", bizDate);
            wholeStatList = getWholeStatRate(params);
        }

        //个人仓位数据
        List<UserStat> userStatList = getUserStatRate(params);
        String userPositionRank = null;
        String wholePosition = null;
        if (!CollectionUtils.isEmpty(userStatList)) {
            UserStat userStat = userStatList.get(0);
            userPositionRank = userStat.getIndexResult();
        }

        if (!CollectionUtils.isEmpty(wholeStatList)) {
            WholeStat wholeStat = wholeStatList.get(0);
            wholePosition = wholeStat.getIndexResult();
        }
        resultMap.put("fundId", params.get("fundId"));
        resultMap.put("bizDate", params.get("bizDate"));
        resultMap.put("userPositionRank", userPositionRank);
        resultMap.put("wholePosition", wholePosition);
        return resultMap;
    }

    //获取个人仓位数据
    public List<UserStat> getUserStatRate(Map<String, Object> params) {
        params.put("indexType", StatTypeEnum.B_USER_STAT.getType());
        return userStatDao.query(params);
    }

    //获取所有用户的平均仓位
    public List<WholeStat> getWholeStatRate(Map<String, Object> params) {
        params.put("indexType", StatTypeEnum.B_WHOLE_STAT.getType());
        return wholeStatDao.query(params);
    }
}
