package com.eastmoney.quote.app.client;

import org.apache.commons.pool.impl.GenericObjectPool;

import java.util.concurrent.CountDownLatch;

/**
 * Created by 1 on 15-7-8.
 */
public class InitPoolWorker implements Runnable {
    private int id;
    private CountDownLatch countDownLatch;
    private GenericObjectPool<RpcConnection> pool;

    public InitPoolWorker(int id, final CountDownLatch countDownLatch,GenericObjectPool<RpcConnection> pool) {
        this.id = id;
        this.countDownLatch = countDownLatch;
        this.pool=pool;
    }

    @Override
    public void run() {
        try {
            try {
                pool.addObject();
            } catch (Exception e) {
                e.printStackTrace();
            }
            countDownLatch.countDown();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}


