package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.tidb.CustCalenderDao;
import com.eastmoney.accessor.enums.SecProfitDayShowEnum;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.VO.SecProfitDayInfoVO;
import com.eastmoney.common.entity.VO.SecProfitDayVO;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.datacenter.redis.client.RedisProxy;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.ProfitRateContrastChartCacheService;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.ProfitDayService;
import com.eastmoney.service.service.ProfitRateDayService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.profit.base.SecProfitServiceSettleImpl;
import com.eastmoney.service.service.profit.list.ProfitDayListService;
import com.eastmoney.service.service.profit.list.ProfitDayListServiceFacade;
import com.eastmoney.service.service.profit.list.SecProfitDayListServiceFacade;
import com.eastmoney.service.service.profit.realtime.ProfitRealTimeServiceFacade;
import com.eastmoney.service.service.profit.section.ProfitSectionServiceFacade;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.HoldPositionService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProfitHandlerTest {

    @Mock
    private ProfitDayDao mockProfitDayDao;
    @Mock
    private ProfitDayService mockProfitDayService;
    @Mock
    private ProfitRateDayService mockProfitRateDayService;
    @Mock
    private ProfitRateContrastChartCacheService mockProfitRateContrastChartCacheService;
    @Mock
    private ProfitDayListServiceFacade mockProfitDayListServiceFacade;
    @Mock
    private ProfitSectionServiceFacade mockProfitSectionServiceFacade;
    @Mock
    private ProfitRealTimeServiceFacade mockProfitRealTimeServiceFacade;
    @Mock
    private CommonService mockCommonService;
    @Mock
    private AssetNewService mockAssetNewService;
    @Mock
    private ProfitDayListService mockProfitDayListServiceRealTime;
    @Mock
    private ProfitSectionCacheService mockProfitSectionCacheService;
    @Mock
    private TradeDateDao mockTradeDateDao;
    @Mock
    private SecProfitDayListServiceFacade mockSecProfitDayListServiceFacade;
    @Mock
    private SecProfitServiceSettleImpl mockSecProfitServiceSettle;
    @Mock
    private StockService mockStockService;
    @Mock
    private HoldPositionService mockHoldPositionService;
    @Mock
    private TpseStSechyreDao mockTpseStSechyreDao;
    @Mock
    private CoreConfigService mockCoreConfigService;
    @Mock
    private NodeConfigService mockNodeConfigService;
    @Mock
    private BseCodeAlterService mockBseCodeAlterService;
    @Mock
    private CustCalenderDao mockCustCalenderDao;
    @Mock
    private RedisProxy mockRedisProxy;

    @InjectMocks
    private ProfitHandler profitHandler;


    @Test
    void testGetSecProfitSectionStatList_UnitYS_WithStockProfitSearchAll() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", 1L);
        params.put("indexKey", 2024);
        params.put("unit", "YS");
        params.put("totalNumFlag", "1");
        params.put("profitType", "1");
        params.put("orderFlag", "2");
        params.put("cashProfitFlag", "1");
        params.put("topSize", "5");
        params.put("calFlag", "1");


        // 构造资产信息
        AssetNew assetNew = new AssetNew();
        assetNew.setBizDate(20250707);  // 确保大于indexKey
        when(mockAssetNewService.getAssetInfo(params)).thenReturn(assetNew);

        // 构造服务器ID
        when(mockCoreConfigService.getServerId(1L)).thenReturn(1001);

        // 构造收益明细支持状态
        when(mockNodeConfigService.getSecProfitDetailShowFlag(20240000, 1001))
                .thenReturn(SecProfitDayShowEnum.SUPPORT.getValue());

        // 构造实时收益计算标志
        when(mockCommonService.calRealTimeProfitBizDate(20250707)).thenReturn(20250708);

        //打开个股筛选开关
        when(mockNodeConfigService.getStockFilterSupportFlag()).thenReturn(true);

        // 构造清算收益数据
        List<SecProfitDayDO> settleProfitList = new ArrayList<>();
        SecProfitDayDO profitDayDO = new SecProfitDayDO();
        profitDayDO.setStkCode("000001");
        profitDayDO.setMarket("SZ");
        profitDayDO.setProfit(100.0);
        settleProfitList.add(profitDayDO);
        when(mockSecProfitDayListServiceFacade.getSecSettleProfitDayList(params)).thenReturn(settleProfitList);

        // 构造现金收益
        when(mockSecProfitServiceSettle.getCashProfitDay(params)).thenReturn(50.0);

        // 构造持仓信息
        Map<String, Integer> holdPositions = new HashMap<>();
        holdPositions.put("SZ-000001", 20230101);
        when(mockHoldPositionService.getHoldPositionStartDate(1L)).thenReturn(holdPositions);

        // 构造股票名称
        when(mockStockService.getStkName("000001", "SZ")).thenReturn("平安银行");

        // 构造行业信息
        when(mockTpseStSechyreDao.getPublishName("000001")).thenReturn("");
        when(mockTpseStSechyreDao.getDefaultPublishName()).thenReturn("未知行业");

        // 构造北交所代码
        when(mockBseCodeAlterService.getCodeAlterXsbAndBjs("000001", "SZ")).thenReturn("XSB001");

        // 执行测试
        SecProfitDayInfoVO result = profitHandler.getSecProfitSectionStatList(params);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getFundId().longValue());
        assertEquals("1", result.getDateSupport());
        assertEquals(50.0, result.getCashProfit(), 0.001);
        assertNotNull(result.getSecProfitDays());
        assertEquals(1, result.getSecProfitDays().size());

        SecProfitDayVO vo = result.getSecProfitDays().get(0);
        assertEquals("000001", vo.getStkCode());
        assertEquals("SZ", vo.getMarket());
        assertEquals("平安银行", vo.getStkName());
        assertEquals("未知行业", vo.getPublishName());
        assertEquals("XSB001", vo.getCorResCode());
        assertEquals(100.0, vo.getProfit(), 0.001);
        assertEquals(1, vo.getHoldFlag().intValue());
    }

    @Test
    void testGetSecProfitSectionStatList_UnsupportedDetail() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", 1L);
        params.put("indexKey", 20250707);
        params.put("unit", "M");
        params.put("totalNumFlag", "1");
        params.put("profitType", "1");
        params.put("orderFlag", "2");
        params.put("cashProfitFlag", "1");

        // 构造资产信息
        AssetNew assetNew = new AssetNew();
        assetNew.setBizDate(20250707);
        when(mockAssetNewService.getAssetInfo(params)).thenReturn(assetNew);

        // 构造服务器ID
        when(mockCoreConfigService.getServerId(1L)).thenReturn(1001);

        // 不支持收益明细
        when(mockNodeConfigService.getSecProfitDetailShowFlag(20250707, 1001))
                .thenReturn(SecProfitDayShowEnum.UN_SUPPORT.getValue());

        // 执行测试
        SecProfitDayInfoVO result = profitHandler.getSecProfitSectionStatList(params);

        // 验证结果
        assertNotNull(result);
        assertEquals("2", result.getDateSupport());
        assertNull(result.getCashProfit());
        assertNotNull(result.getSecProfitDays());
        assertTrue(result.getSecProfitDays().isEmpty());
    }

    @Test
    void testGetSecProfitSectionStatList_WithRealTimeProfit() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", 1L);
        params.put("indexKey", 202507);
        params.put("unit", "Y");
        params.put("totalNumFlag", "1");
        params.put("profitType", "2");
        params.put("orderFlag", "2");
        params.put("cashProfitFlag", "1");
        params.put("calFlag", "1");
        params.put("topSize", 1);

        // 构造资产信息
        AssetNew assetNew = new AssetNew();
        assetNew.setBizDate(20250707);  // 等于indexKey
        when(mockAssetNewService.getAssetInfo(params)).thenReturn(assetNew);

        // 构造服务器ID
        when(mockCoreConfigService.getServerId(1L)).thenReturn(1001);

        // 构造收益明细支持状态
        when(mockNodeConfigService.getSecProfitDetailShowFlag(20250700, 1001))
                .thenReturn(SecProfitDayShowEnum.SUPPORT.getValue());

        // 构造实时收益计算标志
        when(mockCommonService.calRealTimeProfitBizDate(20250707)).thenReturn(20250708);

        //打开个股筛选开关
        when(mockNodeConfigService.getStockFilterSupportFlag()).thenReturn(true);

        // 构造清算收益数据
        List<SecProfitDayDO> settleProfitList = new ArrayList<>();
        SecProfitDayDO settleProfit = new SecProfitDayDO();
        settleProfit.setStkCode("000001");
        settleProfit.setMarket("SZ");
        settleProfit.setProfit(100.0);
        settleProfitList.add(settleProfit);
        when(mockSecProfitDayListServiceFacade.getSecSettleProfitDayList(params)).thenReturn(settleProfitList);

        // 构造实时收益数据
        List<SecProfitDayDO> realTimeProfitList = new ArrayList<>();
        SecProfitDayDO realTimeProfit = new SecProfitDayDO();
        realTimeProfit.setStkCode("000001");
        realTimeProfit.setMarket("SZ");
        realTimeProfit.setProfit(50.0);
        realTimeProfit.setHoldFlag(1);

        SecProfitDayDO realTimeProfit2 = new SecProfitDayDO();
        realTimeProfit2.setStkCode("000002");
        realTimeProfit2.setMarket("SZ");
        realTimeProfit2.setProfit(50.0);
        realTimeProfit2.setHoldFlag(1);

        realTimeProfitList.add(realTimeProfit);
        realTimeProfitList.add(realTimeProfit2);
        when(mockSecProfitDayListServiceFacade.getSecRealTimeProfitDayList(params)).thenReturn(realTimeProfitList);

        // 构造现金收益
        when(mockSecProfitServiceSettle.getCashProfitDay(params)).thenReturn(50.0);

        // 构造持仓信息
        Map<String, Integer> holdPositions = new HashMap<>();
        holdPositions.put("SZ-000002", 20250707);
        when(mockHoldPositionService.getHoldPositionStartDate(1L)).thenReturn(holdPositions);

        // 构造股票名称
        when(mockStockService.getStkName("000001", "SZ")).thenReturn("平安银行");
        when(mockStockService.getStkName("000002", "SZ")).thenReturn("建设银行");

        // 构造行业信息
        when(mockTpseStSechyreDao.getPublishName("000001")).thenReturn("金融");
        when(mockTpseStSechyreDao.getPublishName("000002")).thenReturn("金融");

        // 构造北交所代码
        when(mockBseCodeAlterService.getCodeAlterXsbAndBjs("000001", "SZ")).thenReturn("XSB001");
        when(mockBseCodeAlterService.getCodeAlterXsbAndBjs("000002", "SZ")).thenReturn("XSB002");

        // 执行测试
        SecProfitDayInfoVO result = profitHandler.getSecProfitSectionStatList(params);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getFundId().longValue());
        assertEquals("1", result.getDateSupport());
        assertEquals(50.0, result.getCashProfit(), 0.001);
        assertNotNull(result.getSecProfitDays());
        assertEquals(1, result.getSecProfitDays().size());

        SecProfitDayVO vo = result.getSecProfitDays().get(0);
        assertEquals("000001", vo.getStkCode());
        assertEquals("SZ", vo.getMarket());
        assertEquals("平安银行", vo.getStkName());
        assertEquals("金融", vo.getPublishName());
        assertEquals("XSB001", vo.getCorResCode());
        assertEquals(150.0, vo.getProfit(), 0.001);  // 合并后的收益
        assertEquals(1, vo.getHoldFlag().intValue());
    }

    @Test
    void testGetSecProfitSectionStatList_CloseProfitSearchAll() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", 1L);
        params.put("indexKey", 2024);
        params.put("unit", "YS");
        params.put("totalNumFlag", "1");
        params.put("profitType", "2");
        params.put("orderFlag", "1");
        params.put("cashProfitFlag", "1");
        params.put("calFlag", "1");
        params.put("topSize", 1);


        // 构造资产信息
        AssetNew assetNew = new AssetNew();
        assetNew.setBizDate(20250707);  // 确保大于indexKey
        when(mockAssetNewService.getAssetInfo(params)).thenReturn(assetNew);

        // 构造服务器ID
        when(mockCoreConfigService.getServerId(1L)).thenReturn(1001);

        // 构造收益明细支持状态
        when(mockNodeConfigService.getSecProfitDetailShowFlag(20240000, 1001))
                .thenReturn(SecProfitDayShowEnum.SUPPORT.getValue());

        // 构造实时收益计算标志
        when(mockCommonService.calRealTimeProfitBizDate(20250707)).thenReturn(20250708);

        //打开个股筛选开关
        when(mockNodeConfigService.getStockFilterSupportFlag()).thenReturn(false);

        // 构造清算收益数据
        List<SecProfitDayDO> settleProfitList = new ArrayList<>();
        SecProfitDayDO profitDayDO = new SecProfitDayDO();
        profitDayDO.setStkCode("000001");
        profitDayDO.setMarket("SZ");
        profitDayDO.setProfit(100.0);
        settleProfitList.add(profitDayDO);
        when(mockSecProfitDayListServiceFacade.getSecSettleProfitDayList(params)).thenReturn(settleProfitList);

        // 构造现金收益
        when(mockSecProfitServiceSettle.getCashProfitDay(params)).thenReturn(50.0);

        // 构造持仓信息
        Map<String, Integer> holdPositions = new HashMap<>();
        holdPositions.put("SZ-000001", 20230101);
        when(mockHoldPositionService.getHoldPositionStartDate(1L)).thenReturn(holdPositions);

        // 构造股票名称
        when(mockStockService.getStkName("000001", "SZ")).thenReturn("平安银行");

        // 构造行业信息
        when(mockTpseStSechyreDao.getPublishName("000001")).thenReturn("");
        when(mockTpseStSechyreDao.getDefaultPublishName()).thenReturn("未知行业");

        // 构造北交所代码
        when(mockBseCodeAlterService.getCodeAlterXsbAndBjs("000001", "SZ")).thenReturn("XSB001");

        // 执行测试
        SecProfitDayInfoVO result = profitHandler.getSecProfitSectionStatList(params);

        params.put("orderFlag", "2");
        profitHandler.getSecProfitSectionStatList(params);

        params.put("profitType", "1");
        params.put("orderFlag", "1");
        profitHandler.getSecProfitSectionStatList(params);

        params.put("profitType", "1");
        params.put("orderFlag", "2");
        profitHandler.getSecProfitSectionStatList(params);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getFundId().longValue());
        assertEquals("1", result.getDateSupport());
        assertEquals(50.0, result.getCashProfit(), 0.001);
        assertNotNull(result.getSecProfitDays());
        assertEquals(1, result.getSecProfitDays().size());

        SecProfitDayVO vo = result.getSecProfitDays().get(0);
        assertEquals("000001", vo.getStkCode());
        assertEquals("SZ", vo.getMarket());
        assertEquals("平安银行", vo.getStkName());
        assertEquals("未知行业", vo.getPublishName());
        assertEquals("XSB001", vo.getCorResCode());
        assertEquals(100.0, vo.getProfit(), 0.001);
        assertEquals(1, vo.getHoldFlag().intValue());
    }
}
