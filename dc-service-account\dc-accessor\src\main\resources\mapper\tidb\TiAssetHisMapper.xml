<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiAssetHisMapper">
    <resultMap id="BaseResultMap" type="as_assetHis" >
        <result column="FUNDID" property="fundId"/>
        <result column="ASSET" property="asset"/>
        <result column="SHIFT_OUT" property="shiftOut"/>
        <result column="SHIFT_IN" property="shiftIn"/>
        <result column="MKTVAL" property="mktval"/>
        <result column="BIZDATE" property="bizDate"/>
        <result column="START_DATE" property="startDate"/>
        <result column="ASSET_INIT" property="assetInit"/>
        <result column="SHIFT_OUT_TOTAL" property="shiftOutTotal"/>
        <result column="SHIFT_IN_TOTAL" property="shiftInTotal"/>
        <result column="PROFIT_TOTAL" property="profitTotal"/>
        <result column="OTC_NET_TRANSFER" property="otcNetTransfer"/>
        <result column="OTHER_NET_TRANSFER" property="otherNetTransfer"/>
        <result column="netTransfer" property="netTransfer"/>
        <result column="OTC_ASSET" property="otcAsset"/>
    </resultMap>
    <sql id="All_Column">
        ASSET, SHIFT_OUT, SHIFT_IN, MKTVAL, BIZDATE,IFNULL(ABS(SHIFT_OUT_TOTAL),0) as SHIFT_OUT_TOTAL,SHIFT_IN_TOTAL,PROFIT_TOTAL,
        (OTC_SHIFT_IN_TOTAL - OTC_SHIFT_OUT_TOTAL) OTC_NET_TRANSFER,IFNULL(OTC_ASSET,0) OTC_ASSET,
        ((SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) OTHER_NET_TRANSFER,
        ((OTC_SHIFT_IN_TOTAL + SHARE_SHIFT_IN_TOTAL + OTHER_SHIFT_IN_TOTAL) - (OTC_SHIFT_OUT_TOTAL + SHARE_SHIFT_OUT_TOTAL + OTHER_SHIFT_OUT_TOTAL)) netTransfer
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT  <include refid="All_Column"/> FROM ATCENTER.ASSET_HIS t use index(pk_lg_asset_his_new)
        <where>
            FUNDID = #{fundId}
            <if test="startDate != null">
                AND BIZDATE BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="bizDate != null">
                AND BIZDATE = #{bizDate}
            </if>
        </where>
    </select>

    <select id="getAssetDayTrend" resultMap="BaseResultMap">
        SELECT FUNDID, BIZDATE, MKTVAL, ASSET, OTC_ASSET
        FROM ATCENTER.ASSET_HIS use index(pk_lg_asset_his_new)
        <where>
            FUNDID = #{fundId}
            <if test="startDate != null and endDate != null">
                AND BIZDATE BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
    </select>

    <select id="getMonthAssetDayTrend" resultMap="BaseResultMap">
        SELECT FUNDID, left(BIZDATE,6) BIZDATE, MKTVAL, ASSET, OTC_ASSET
        FROM ATCENTER.ASSET_HIS use index(pk_lg_asset_his_new)
        <where>
            FUNDID = #{fundId}
            <if test="tradeDateList != null and tradeDateList.size() > 0">
                AND BIZDATE IN
                <foreach item="item" index="index" collection="tradeDateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                AND BIZDATE BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
    </select>

</mapper>