package com.eastmoney.service.action;

import com.eastmoney.common.annotation.*;
import com.eastmoney.common.entity.LogAsset;
import com.eastmoney.common.entity.ProfitSectionStat;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.entity.cal.MergeTradeResult;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.*;
import com.eastmoney.service.serializer.ProfitSectionStatFilter;
import com.eastmoney.service.util.FunCodeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/19.
 * 账户表现action
 */
@Action
@Component
public class AccountAnalyzeAction {
    private static final String[] OTHER_CHANGE_LOGASSET_PARAMS = new String[]{"fundId", "pageNo", "pageSize", "sortFlag", "orderFlag"};
    private static final String[] HOLD_POSITION_TRADE_PARAMS = new String[]{"fundId", "pageNo", "pageSize", "market", "stkCode"};
    private static final String[] PROFIT_SECTION_STAT_PARAMS = new String[]{"fundId", "indexKey", "unit", "profitType"};
    @Autowired
    private ProfitHandler profitHandler;
    @Autowired
    private ProfitRateHandler profitRateHandler;
    @Autowired
    private AssetHandler assetHandler;
    @Autowired
    private PositionHandler positionHandler;
    @Autowired
    private LogAssetHandler logAssetHandler;

    /**
     * 账户表现 - 总收益
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params fundId, unit, count[100]
     * @return 总收益、总收益率、更新时间
     */
    @RequestMapping("/getProfitInfo")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_INFO)
    public Object getProfitInfo(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }
        return profitHandler.getProfitInfo(params);
    }

    /**
     * 账户表现 - 日收益率折线图(固定区间查询)
     * startDate小于设定的值时至今区间收益率走势图可展示月收益率 APPAGILE-80289
     *
     * @param params fundId, unit(账户表现页面), endDate、startDate(账单页面), beginIndex:[-1表示需要区间的上一个交易日]
     * @return 日收益率列表
     */
    @RequestMapping("/getProfitRateDayList")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_RATE_DAY_LIST)
    public List<ProfitRateDay> getProfitRateDayList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }
        return profitRateHandler.getProfitRateDayList(params);
    }


    /**
     * 账户表现 - 日收益率折线图(自定义区间查询)
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params
     * @return
     */
    @RequestMapping("/getProfitRateDayListCustomize")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_RATE_DAY_LIST_CUSTOMIZE)
    public List<ProfitRateDay> getProfitRateDayListCustomize(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "startDate", "endDate"});

        return profitRateHandler.getProfitRateDayListCustomize(params);
    }

    /**
     * APPAGILE-141388 优优投顾专属接口：账户表现 - 日/月 收益率折线图(自定义区间查询)
     *
     * @param params
     * @return
     */
    @RequestMapping("/getInvestProfitRateList")
    @FunCodeMapping(FunCodeConstants.GET_INVEST_PROFIT_RATE_LIST)
    public List<ProfitRateDay> getInvestProfitRateList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "startDate", "endDate", "dimFlag"});
        return profitRateHandler.getInvestProfitRateList(params);
    }


    /**
     * 账户表现 - 日收益折线图 APPAGILE-134721
     * startDate小于设定的值时至今区间收益走势图可展示月收益
     *
     * @param params fundId, unit, beginIndex:[-1表示需要区间的上一个交易日]
     * @return 日收益列表
     */
    @RequestMapping("/getProfitDayTrend")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_DAY_TREND)
    public List<ProfitDay> getProfitDayTrend(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "unit"});
        return profitHandler.getProfitDayTrend(params);
    }

    /**
     * 账户表现 - 自定义区间日收益折线图 APPAGILE-134721
     *
     * @param params fundId, startDate, endDate, beginIndex:[-1表示需要区间的上一个交易日]
     * @return 日收益列表
     */
    @RequestMapping("/getProfitDayTrendCustomize")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_DAY_TREND_CUSTOMIZE)
    public List<ProfitDay> getProfitDayTrendCustomize(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "startDate", "endDate"});
        return profitHandler.getProfitDayTrendCustomize(params);
    }


    /**
     * 账户表现 - 总资产变动
     *
     * @param params fundId, unit, bizDate, moneyType
     * @return 区间总资产变动
     */
    @RequestMapping("/getAssetSection")
    @FunCodeMapping("getAssetSection")
    public AssetSection getAssetSection(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "unit"});
        return assetHandler.getAssetSection(params);
    }

    //    @RequestMapping("/getOtherChangeDetails")
    @FunCodeMapping("getOtherChangeDetails")
    public List<LogAsset> getOtherChangeDetails(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, OTHER_CHANGE_LOGASSET_PARAMS);
        return logAssetHandler.getOtherChangeLogAsset(params);
    }

    /**
     * 账户表现 - 收益明细
     *
     * @param params fundId, startDate, endDate
     * @return 收益明细列表
     */
    @RequestMapping("/getProfitDayList")
    @FunCodeMapping("getProfitDayList")
    //todo @Deprecated("该方法貌似已经弃用了")
    public Object getProfitDayList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "startDate", "endDate"});
        return profitHandler.getProfitDayList(params);
    }


    /**
     * 账户表现 - 收益明细 选择全年
     *
     * @param params fundId, bizYear, unit, startDate, endDate
     * @return 年/月/周收益列表
     */
    @RequestMapping("/getProfitStatList")
    @FunCodeMapping("getProfitStatList")
    //todo @Deprecated("该方法貌似已经弃用了")
    public Object getProfitStatList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "bizYear"});
        return profitHandler.getProfitStatList(params);
    }

    /**
     * 账户表现 - 已清仓股票盈亏
     * 持仓盈亏分析-交易证券数，盈利证券数等
     * <p>
     * 新增已清仓股票盈亏数据 APPAGILE-52898
     * 支持自定义区间查询 APPAGILE-79949
     * 因股息红利扣税问题，实时统计计算指定资金账号的持仓盈亏 ACCTANAL-25X
     *
     * @param params fundId, unit/startDate、endDate
     * @return 持仓盈亏分析
     */
    @RequestMapping("/getPositionSection")
    @FunCodeMapping("getPositionSection")
    public Object getPositionSection(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }
        return positionHandler.getPositionSection(params);
    }

    /**
     * 该方法支持新版股票汇总，
     * <p>
     * 账户表现 - 已清仓股票盈亏  （支持新版按照股票汇总 新增清仓次数）
     * <p>
     * 2021/3/2 修改港股通的查询日期字段
     * <p>
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params fundId, startDate, endDate, pageSize, sort, sortFlag, unit/starDate、endDate, pageNo, orderFlag, profitFlag
     * @return 盈亏股票汇总
     */
    @FunCodeMapping("getPositionMergeList")
    @RequestMapping("/getPositionMergeList")
    public Object getPositionMergeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "pageNo", "pageSize", "sortFlag", "orderFlag"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }

        return positionHandler.getPositionMergeList(params);
    }

    /**
     * 账户表现 - 已清仓股票分析 - 按股票汇总 （按照时间、支持单支股票维度 stkCode、market）
     * datacenter-3776 新增
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     * <p>
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params fundId,  pageSize,  sortFlag, unit, pageNo, orderFlag, profitFlag  ---支持单支股票(stkCode、market)
     *               查询个股时支持入参 summaryFlag 查询汇总信息
     * @return 盈亏股票汇总
     */
    @FunCodeMapping("getClearPositionList")
    @RequestMapping("/getClearPositionList")
    public Object getClearPositionList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "pageNo", "pageSize", "sortFlag", "orderFlag"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }
        return positionHandler.getClearPositionList(params);
    }

    /**
     * 账户表现 - 已清仓股票盈亏(个股清仓记录) （单支股票维度 stkCode、market）
     * datacenter-3776 新增
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     * <p>
     * 支持自定义区间查询 APPAGILE-79949
     *
     * @param params fundId,  pageSize,  sortFlag, unit, pageNo, orderFlag, profitFlag  ---支持单支股票(stkCode、market)
     *               查询个股时支持入参 summaryFlag 查询汇总信息
     * @return 盈亏股票汇总
     */
    @FunCodeMapping("getClearPositionListB")
    @RequestMapping("/getClearPositionListB")
    public Object getClearPositionListB(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "pageNo", "pageSize", "sortFlag", "orderFlag", "stkCode", "market"});
        String unit = CommonUtil.convert(params, "unit", String.class);
        if (unit == null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
        }
         //用于打开代码转换
        params.put("openBaseCodeAlter", true);
        return positionHandler.getClearPositionList(params);
    }

    /**
     * 账户表现 - 已清仓股票盈亏- 选择一支股票查看详情
     * <p>
     * 合并单只股票盈亏明细，买入，卖出统计，转入，转出统计
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     *
     * @param params fundId, market, queryFlag, endDate, stkCode, clearEndDate, clearStartDate, startDate
     * @return
     */
    @MyDeprecated("该方法未来弃用，使用getClearPositionMergeTradeList替换")
    @FunCodeMapping("getPositionMergeTradeList")
    public Object getPositionMergeTradeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "startDate", "endDate", "queryFlag"});
        return positionHandler.getPositionMergeTradeList(params);
    }


    /**
     * 新版  账户表现-已清仓股票-个股操作分析
     * datacenter-3776 新增
     * <p>
     * 合并单只股票盈亏明细，买入，卖出统计，转入，转出统计
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     *
     * @param params fundId, market, stkCode, , unit区间信息   单只股票开始 startDate, endDate
     * @return
     */
    @FunCodeMapping("getClearPositionMergeTradeList")
    @RequestMapping("/getClearPositionMergeTradeList")
    public Object getClearPositionMergeTradeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "pageNo", "pageSize"});
        return positionHandler.getClearPositionMergeTradeList(params);
    }


    /**
     * 新增 单只股票区间 全部清仓记录
     * datacenter-3776 新增
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     *
     * @param params fundId, market, stkCode, startDate, endDate, pageNo, pageSize
     * @return
     */
    @FunCodeMapping("getSectionStockClearPositionList")
    @RequestMapping("/getSectionStockClearPositionList")
    @MyDeprecated("该方法未来弃用")
    public Object getSectionStockClearPositionList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "startDate", "endDate", "pageNo", "pageSize"});
        return positionHandler.getSectionStockClearPositionList(params);
    }

    /**
     * 持仓分析-持仓个股操作分析
     * datacenter-3776 新增
     * <p>
     * 合并单只股票盈亏明细，买入，卖出统计，转入，转出统计
     * <p>
     * 2021/3/2 修改港股通的查询日期和返回日期字段
     *
     * @param params fundId, market, stkCode, pageSize, pageNo
     * @return
     */
    @FunCodeMapping("getHoldPositionMergeTradeList")
    @RequestMapping("/getHoldPositionMergeTradeList")
    public MergeTradeResult getHoldPositionMergeTradeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, HOLD_POSITION_TRADE_PARAMS);
        return positionHandler.getHoldPositionMergeTradeList(params);
    }

    /**
     * 账户表现-盈亏日历
     * 2021/4/12 APPAGILE-70926
     *
     * @param params fundId, indexKey, unit, profitType
     * @return
     */
    @FunCodeMapping("getProfitSectionStatList")
    @RequestMapping("/getProfitSectionStatList")
    @JsonSerializeFilter(ProfitSectionStatFilter.class)
    public ProfitSectionStat getProfitSectionStatList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, PROFIT_SECTION_STAT_PARAMS);
        return profitHandler.getProfitSectionStatList(params);
    }

    /**
     * 盈亏日历-个股日收益明细查询 APPAGILE-135268
     * @param params
     * @return
     */
    @FunCodeMapping("getSecProfitSectionStatList")
    @RequestMapping("/getSecProfitSectionStatList")
    public Object getSecProfitSectionStatList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "indexKey", "unit", "profitType", "orderFlag"});
        return profitHandler.getSecProfitSectionStatList(params);
    }

    //买入，卖出统计 (目前未用到，监控在用)
    @FunCodeMapping("getStkTradeList")
    public Object getStkTradeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "startDate", "endDate"});
        return logAssetHandler.getStkTradeList(params);
    }

    //转入，转出统计 (目前未用到)
    @FunCodeMapping("getStkShiftList")
    public Object getStkShiftList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "startDate", "endDate"});
        return logAssetHandler.getStkShiftList(params);
    }

    /**
     * 个股操作分析-行情买卖点标识
     * APPAGILE-73382 新增
     *
     * @return
     */
    @FunCodeMapping("getBSTradeList")
    @RequestMapping("/getBSTradeList")
    public Object getBSTradeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "startDate", "endDate"});
        return positionHandler.getBSTradeList(params);
    }

    /**
     * APPAGILE-98832 已清仓股票支持搜索
     *
     * @param params fundId
     * @return stkCode, market, spellId, stkName
     */
    @RequestMapping("/getPositionProfitSearchData")
    @FunCodeMapping("getPositionProfitSearchData")
    public Object getPositionProfitSearchData(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        return positionHandler.getPositionProfitSearchData(params);
    }


    @FunCodeMapping("GetATradeDate")
    @RequestMapping("/GetATradeDate")
    public Object GetATradeDate(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"dateTo","dateFrom"});
        return profitHandler.GetATradeDate(params);
    }
}
