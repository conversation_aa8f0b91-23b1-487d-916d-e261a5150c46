package com.eastmoney.service.http;

import java.util.List;

/**
 * Created by Administrator on 2016/3/8.
 */
public class HttpResult {
    private Object data = new Object();

    /**
     * 对接内部网关：0:成功，其它:失败
     */
    private Integer status = 0;

    /**
     * 对接内部网关：失败时的错误信息
     */
    private String message;



    public HttpResult() {

    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getSize() {
        int size = 0;
        if (data != null && (data instanceof List)) {
            size = ((List) data).size();
        }
        return size;
    }
}
