<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiStockMapper">
    <select id="getStock" resultType="com.eastmoney.common.entity.Stock">
        SELECT  market,stkCode,stkName,linkStk,stktype,stkstatus,stkfc,spellId,stkFullName as expandNameAbbr
        FROM  ATCENTER.STOCK
        where  STKCODE = #{stkCode} AND MARKET = #{market} limit 1
    </select>

    <select id="queryAllStock" resultType="com.eastmoney.common.entity.Stock">
        select stkcode,market,stkname,stktype,stkfc,spellId,stkFullName as expandNameAbbr
        from ATCENTER.stock
        where serverid = 1
        union
        select stkcode,market,stkname,stktype,stkfc,spellId,stkFullName as expandNameAbbr
        from ATCENTER.stock
        where market in ('5','S')
    </select>
</mapper>