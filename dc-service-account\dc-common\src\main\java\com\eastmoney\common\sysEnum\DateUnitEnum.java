package com.eastmoney.common.sysEnum;

/**
 * Created by robin on 2016/7/11.
 * 时间单位枚举值 字符最长长度 3 与oracle atcenter.PROFIT_STAT和atcenter.PROFIT_SECTION的UNIT字段保持一致
 * <AUTHOR>
 */
public enum DateUnitEnum {

    DAY("D"),//天-今天
    WEEK("W"),//周-本周
    MONTH("M"),//月-本月
    QUARTER("Q"),//季-本季
    YEAR("Y"),//年-本年
    PAST_ONE_MONTH("P1M"),//近一月
    PAST_TWO_MONTH("P2M"),//近两月
    PAST_THREE_MONTH("P3M"),//近三月
    PAST_FOUR_MONTH("P4M"),//近四月
    PAST_FIVE_MONTH("P5M"),//近五月
    PAST_HALF_YEAR("PHY"),//近半年
    PAST_SEVEN_MONTH("P7M"),//近七月
    PAST_EIGHT_MONTH("P8M"),//近八月
    PAST_NINE_MONTH("P9M"),//近九月
    PAST_TEN_MONTH("P10M"),//近十月
    PAST_ELEVEN_MONTH("P11M"),//近十一月
    PAST_ONE_YEAR("P1Y"),//近一年
    ALL("ALL");//从开户到现在

    private String unit;
    DateUnitEnum(String unit){
        this.unit = unit;
    }

    public String getValue(){
        return this.unit;
    }

    public static DateUnitEnum getEnum(String value){
        switch (value){
            case "D":
                return DAY;
            case "W":
                return WEEK;
            case "M":
                return MONTH;
            case "Q":
                return QUARTER;
            case "Y":
                return YEAR;
            case "P1M":
                return PAST_ONE_MONTH;
            case "P2M":
                return PAST_TWO_MONTH;
            case "P3M":
                return PAST_THREE_MONTH;
            case "P4M":
                return PAST_FOUR_MONTH;
            case "P5M":
                return PAST_FIVE_MONTH;
            case "PHY":
                return PAST_HALF_YEAR;
            case "P7M":
                return PAST_SEVEN_MONTH;
            case "P8M":
                return PAST_EIGHT_MONTH;
            case "P9M":
                return PAST_NINE_MONTH;
            case "P10M":
                return PAST_TEN_MONTH;
            case "P11M":
                return PAST_ELEVEN_MONTH;
            case "P1Y":
                return PAST_ONE_YEAR;
            case "ALL":
                return ALL;
            default:
                throw new RuntimeException("not found unit[" + value+"]");
        }
    }

    /*YESTERDAY("YD"),//昨日
    TODAY("D"),//今日
    THIS_WEEK("W"),//本周
    THIS_MONTH("M"),//本月
    THIS_QUARTER("Q"),//本季度
    THIS_YEAR("Y"),//本年*/
}
