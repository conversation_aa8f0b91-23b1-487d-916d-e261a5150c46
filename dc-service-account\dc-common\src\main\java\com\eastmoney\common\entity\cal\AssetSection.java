package com.eastmoney.common.entity.cal;

/**
 * Created by sunyuncai on 2017/3/21.
 */
public class AssetSection {
    private Long fundId;
    private String unit;
    private double openAsset;//期初总资产
    private double asset;//最新总资产
    public double shiftOutTotal;//累计转出
    public double shiftInTotal;//累计转入
    public double otcShiftOutTotal;    //OTC累计转出金额
    public double otcShiftInTotal;     //OTC累计转入金额
    public double shareShiftOutTotal;  //股份累计转出金额
    public double shareShiftInTotal;   //股份累计转入金额
    public double otherShiftOutTotal;  //其他累计转出金额
    public double otherShiftInTotal;   //其他累计转入金额
    public double profit;//账户盈亏
    private Integer bizDate;
    private Integer startDate;

    private Double otcNetTransfer;  //OTC净转入
    private Double otherNetTransfer;    //其他净转入
    private Double bankNetTransfer;    //银证净转入
    private Double netTransfer;    //其他净转入
    private Double otcAsset;    //OTC 资产
    private Double openOtcAsset;    //期初 OTC 资产

    public Double getOpenOtcAsset() {
        return openOtcAsset;
    }

    public void setOpenOtcAsset(Double openOtcAsset) {
        this.openOtcAsset = openOtcAsset;
    }

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public Double getNetTransfer() {
        return netTransfer;
    }

    public void setNetTransfer(Double netTransfer) {
        this.netTransfer = netTransfer;
    }

    public Double getOtcNetTransfer() {
        return otcNetTransfer;
    }

    public void setOtcNetTransfer(Double otcNetTransfer) {
        this.otcNetTransfer = otcNetTransfer;
    }

    public Double getOtherNetTransfer() {
        return otherNetTransfer;
    }

    public void setOtherNetTransfer(Double otherNetTransfer) {
        this.otherNetTransfer = otherNetTransfer;
    }

    public Double getBankNetTransfer() {
        return bankNetTransfer;
    }

    public void setBankNetTransfer(Double bankNetTransfer) {
        this.bankNetTransfer = bankNetTransfer;
    }

    public double getOtcShiftOutTotal() {
        return otcShiftOutTotal;
    }

    public void setOtcShiftOutTotal(double otcShiftOutTotal) {
        this.otcShiftOutTotal = otcShiftOutTotal;
    }

    public double getOtcShiftInTotal() {
        return otcShiftInTotal;
    }

    public void setOtcShiftInTotal(double otcShiftInTotal) {
        this.otcShiftInTotal = otcShiftInTotal;
    }

    public double getShareShiftOutTotal() {
        return shareShiftOutTotal;
    }

    public void setShareShiftOutTotal(double shareShiftOutTotal) {
        this.shareShiftOutTotal = shareShiftOutTotal;
    }

    public double getShareShiftInTotal() {
        return shareShiftInTotal;
    }

    public void setShareShiftInTotal(double shareShiftInTotal) {
        this.shareShiftInTotal = shareShiftInTotal;
    }

    public double getOtherShiftOutTotal() {
        return otherShiftOutTotal;
    }

    public void setOtherShiftOutTotal(double otherShiftOutTotal) {
        this.otherShiftOutTotal = otherShiftOutTotal;
    }

    public double getOtherShiftInTotal() {
        return otherShiftInTotal;
    }

    public void setOtherShiftInTotal(double otherShiftInTotal) {
        this.otherShiftInTotal = otherShiftInTotal;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public double getOpenAsset() {
        return openAsset;
    }

    public void setOpenAsset(double openAsset) {
        this.openAsset = openAsset;
    }

    public double getAsset() {
        return asset;
    }

    public void setAsset(double asset) {
        this.asset = asset;
    }

    public double getShiftOutTotal() {
        return shiftOutTotal;
    }

    public void setShiftOutTotal(double shiftOutTotal) {
        this.shiftOutTotal = shiftOutTotal;
    }

    public double getShiftInTotal() {
        return shiftInTotal;
    }

    public void setShiftInTotal(double shiftInTotal) {
        this.shiftInTotal = shiftInTotal;
    }

    public double getProfit() {
        return profit;
    }

    public void setProfit(double profit) {
        this.profit = profit;
    }
}

