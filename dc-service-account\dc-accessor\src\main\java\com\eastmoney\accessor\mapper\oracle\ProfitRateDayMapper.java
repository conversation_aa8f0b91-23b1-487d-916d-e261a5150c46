package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.accessor.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * Created by xiaoyongyong on 2016/7/19.
 */
@Repository
public interface ProfitRateDayMapper extends BaseMapper<ProfitRateDay, Long> {

    Double getSectionProfitRate(Map<String,Object> param);
}
