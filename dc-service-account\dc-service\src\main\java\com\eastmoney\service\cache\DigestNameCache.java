package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.CalDigestDao;
import com.eastmoney.common.entity.cal.CalDigestDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/20 13:01
 */
@Service
public class DigestNameCache {
    private static Logger LOG = LoggerFactory.getLogger(DigestNameCache.class);

    @Autowired
    private CalDigestDao calDigestDao;

    private Map<Long, String> digestIdToNameMap = new HashMap<>();

    public String getDigestName(Long digestId) {
        Objects.requireNonNull(digestId, "查询digestName缓存 digestId不能为空");
        return this.digestIdToNameMap.get(digestId);
    }

    @PostConstruct
    public void init() {
        ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("digestName-cache-flush-thread");
            return thread;
        });
        Runnable runnable = this::flushData;
        scheduledExecutorService.scheduleWithFixedDelay(runnable, 0, 1, TimeUnit.HOURS);
    }

    public void flushData() {
        LOG.info("开始刷新digestName缓存数据...");
        try {
            Map<String, Object> params = new HashMap<>();
            List<CalDigestDO> calDigestDOList = calDigestDao.getDigestIdAndName(params);
            if (CollectionUtils.isEmpty(calDigestDOList)) {
                return;
            }
            Map<Long, String> newDigestIdToNameMap = new HashMap<>();
            for (int i = 0; i < calDigestDOList.size(); i++) {
                CalDigestDO calDigestDO = calDigestDOList.get(i);
                if (calDigestDO == null) {
                    continue;
                }
                Long digestId = calDigestDO.getDigestId();
                String digestName = calDigestDO.getDigestName();
                if (digestId == null || digestName == null || "".equals(digestName)) {
                    continue;
                }
                newDigestIdToNameMap.put(digestId, digestName);
            }
            this.digestIdToNameMap = newDigestIdToNameMap;
            LOG.info("digestName数据加载完成，总数为{}...", newDigestIdToNameMap.size());
        } catch (Exception e) {
            LOG.info("digestName数据加载异常...", e);
        }
    }

}
