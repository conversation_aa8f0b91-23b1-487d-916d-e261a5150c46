package com.eastmoney.common.serializer;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.eastmoney.common.model.Output;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by sunyuncai on 2017/2/6.
 */
public abstract class AbstractSerializeFilter implements PropertyPreFilter{
    protected final List<String> includes = new ArrayList<>();

    public AbstractSerializeFilter() {
        includes.addAll(genIncludes());
    }
    @Override
    public boolean apply(JSONSerializer serializer, Object object, String name) {
        if (object instanceof Output) {
            return true;
        }
        return includes.contains(name);
    }

    protected abstract Collection<String> genIncludes();
}
