package com.eastmoney.common.entity;


import java.util.Date;

/**
 * @Description
 * @auther $djs
 * @dateTime 2020-10-23 10:21
 * @content 港股交易日DO
 */
public class HgtTradeDayDO {
    /**
     *日期
     */
    private Date ppDate;
    /**
     *交易日期
     */
    private String tDate;
    /**
     *交易市场[内部]
     */
    private String texch;
    /**
     *品种
     */
    private String typeCode;
    /**
     *市场代码
     */
    private String tradeMarketCode;
    /**
     *竞拍开始时间
     */
    private String auctionOpAm;
    /**
     *竞拍结束时间
     */
    private String auctionCoAm;
    /**
     *上午开市时间
     */
    private String openTradeAm;
    /**
     *上午闭市时间
     */
    private String closeTradeAm;
    /**
     *下午开市时间
     */
    private String openTradePm;
    /**
     *下午闭市时间
     */
    private String closeTradePm;
    /**
     *收盘竞价开始时间
     */
    private String auctionOpPm;
    /**
     *收盘竞价结束时间
     */
    private String auctionCoPm;


    public Date getPpDate() {
        return ppDate;
    }

    public void setPpDate(Date ppDate) {
        this.ppDate = ppDate;
    }

    public String gettDate() {
        return tDate;
    }

    public void settDate(String tDate) {
        this.tDate = tDate;
    }

    public String getTexch() {
        return texch;
    }

    public void setTexch(String texch) {
        this.texch = texch;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTradeMarketCode() {
        return tradeMarketCode;
    }

    public void setTradeMarketCode(String tradeMarketCode) {
        this.tradeMarketCode = tradeMarketCode;
    }

    public String getAuctionOpAm() {
        return auctionOpAm;
    }

    public void setAuctionOpAm(String auctionOpAm) {
        this.auctionOpAm = auctionOpAm;
    }

    public String getAuctionCoAm() {
        return auctionCoAm;
    }

    public void setAuctionCoAm(String auctionCoAm) {
        this.auctionCoAm = auctionCoAm;
    }

    public String getOpenTradeAm() {
        return openTradeAm;
    }

    public void setOpenTradeAm(String openTradeAm) {
        this.openTradeAm = openTradeAm;
    }

    public String getCloseTradeAm() {
        return closeTradeAm;
    }

    public void setCloseTradeAm(String closeTradeAm) {
        this.closeTradeAm = closeTradeAm;
    }

    public String getOpenTradePm() {
        return openTradePm;
    }

    public void setOpenTradePm(String openTradePm) {
        this.openTradePm = openTradePm;
    }

    public String getCloseTradePm() {
        return closeTradePm;
    }

    public void setCloseTradePm(String closeTradePm) {
        this.closeTradePm = closeTradePm;
    }

    public String getAuctionOpPm() {
        return auctionOpPm;
    }

    public void setAuctionOpPm(String auctionOpPm) {
        this.auctionOpPm = auctionOpPm;
    }

    public String getAuctionCoPm() {
        return auctionCoPm;
    }

    public void setAuctionCoPm(String auctionCoPm) {
        this.auctionCoPm = auctionCoPm;
    }


    @Override
    public String toString() {
        return "HgtTradeDayDO{" +
                "ppDate=" + ppDate +
                ", tDate='" + tDate + '\'' +
                ", texch='" + texch + '\'' +
                ", typeCode='" + typeCode + '\'' +
                ", tradeMarketCode='" + tradeMarketCode + '\'' +
                ", auctionOpAm='" + auctionOpAm + '\'' +
                ", auctionCoAm='" + auctionCoAm + '\'' +
                ", openTradeAm='" + openTradeAm + '\'' +
                ", closeTradeAm='" + closeTradeAm + '\'' +
                ", openTradePm='" + openTradePm + '\'' +
                ", closeTradePm='" + closeTradePm + '\'' +
                ", auctionOpPm='" + auctionOpPm + '\'' +
                ", auctionCoPm='" + auctionCoPm + '\'' +
                '}';
    }

}
