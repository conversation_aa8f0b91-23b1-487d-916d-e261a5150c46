package com.eastmoney.accessor.util;

import com.eastmoney.common.entity.ProResult;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
public class StoreProcedureUtil {
    private static final String SUCCESS_CODE = "0";

    /**
     * 获取sqlServer存储过程结果List<List<?>>
     * 最外面的List表明有几个结果集，里面的List表明该结果集有多少条数据。
     * 约定第一个为存储过程执行状态结果，包括errorcode,errormsg，后面的list为存储过程执行数据集合
     *
     * @param result
     * @return
     */
    public static List<List<?>> getMsStoreProcedureList(List<List<?>> result) {
        List<ProResult> proResults = (List<ProResult>) result.get(0);
        if (CollectionUtils.isEmpty(proResults)) {
            return null;
        }
        ProResult proResult = proResults.get(0);
        String errorCode = proResult.getErrorCode();
        if (Objects.isNull(errorCode) || !errorCode.equals(SUCCESS_CODE)) {
            return null;
        }

        return result;
    }
}
