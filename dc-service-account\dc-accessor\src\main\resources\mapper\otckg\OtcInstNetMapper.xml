<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.otckg.OtcInstNetMapper">
    <select id="getOtcInstNetList"  resultType="com.eastmoney.common.entity.OtcInstNet">
        SELECT  t2.INST_CODE instCode, NVL(t1.LAST_NET, 10000) AS lastNet, t1.NET_DATE netDate FROM otcts.OTC_INST_EXT_INFO t1
               LEFT JOIN otcts.OTC_INST_BASE_INFO t2 ON t1.INST_SNO = t2.INST_SNO
    </select>
</mapper>