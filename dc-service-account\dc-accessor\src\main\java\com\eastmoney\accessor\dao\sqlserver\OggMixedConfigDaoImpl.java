package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggMixedConfigMapper;
import com.eastmoney.accessor.model.MixedConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created by Administrator on 2017/2/6.
 */
@Service
@SqlServerTarget()
public class OggMixedConfigDaoImpl implements OggMixedConfigDao {

    @Autowired
    private OggMixedConfigMapper oggMixedConfigMapper;

    @Override
    public MixedConfig getMixedConfig(Map<String,Object> params) {
        return oggMixedConfigMapper.getMixedConfig(params);
    }
}
