package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 盘后临时个股日收益明细mapper
 * <AUTHOR>
 * @create 2024/3/7
 */
@Repository
public interface TiTemporarySecProfitDayMapper extends BaseMapper<SecProfitDayDO,Integer> {

    /**
     * 个股日收益明细查询
     * @param params
     * @return
     */
    List<SecProfitDayDO> getSecTemporaryProfitDay(Map<String, Object> params);

}
