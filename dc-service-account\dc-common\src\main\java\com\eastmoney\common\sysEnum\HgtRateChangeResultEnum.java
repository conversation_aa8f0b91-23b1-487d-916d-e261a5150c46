package com.eastmoney.common.sysEnum;

/**
 * <AUTHOR>
 * @create 2022/11/20
 */
public enum HgtRateChangeResultEnum {
    // 汇率波动超过阈值
    RATE_EXCEED_RANGE(0),
    // 正在清算中
    SETTLING(1),
    // 没有港股通持仓
    NO_SKTASSET(2),
    // 汇率波动不超过阈值
    RATE_IN_RANGE(3);

    private Integer value;

    HgtRateChangeResultEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
