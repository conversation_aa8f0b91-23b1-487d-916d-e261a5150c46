package com.eastmoney.accessor.enums;

/**
 * Created on 2018/3/7.
 * <AUTHOR>
 * 指标类型枚举类
 */
public enum StatTypeEnum {
    B_USER_STAT("userPosition","用户当日仓位排名"),
    B_WHOLE_STAT("wholePosition","所有用户的当日均仓位");

    private String type;// 类型
    private String chName;//中文名称

    StatTypeEnum(String type, String chName) {
        this.type = type;
        this.chName = chName;
    }

    public String getType() {
        return type;
    }

    public String getChName() {
        return chName;
    }
}
