package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.AbilityBill;
import com.eastmoney.common.entity.cal.AcctDiagnoseBO;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.MajorBillYear;
import com.eastmoney.common.entity.cal.PublishBill;
import com.eastmoney.common.entity.cal.ReferenceProfitRate;
import com.eastmoney.common.entity.cal.StockBill;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.AssetHandler;
import com.eastmoney.service.handler.BillHandler;
import com.eastmoney.service.util.FunCodeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/4/1.
 */
@Action
@Component
public class BillAction {
    @Autowired
    private BillHandler billHandler;
    @Autowired
    private AssetHandler assetHandler;

    /**
     * 账单 - 最新总资产
     * @param params fundId
     *
     * @return 最新资产
     */
    @RequestMapping("/getAssetInfo")
    @FunCodeMapping("getAssetInfo")
    public AssetNew getAssetInfo(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        return assetHandler.getAssetInfo(params);
    }

    /**
     *  账单 - 基本信息
     */
    @RequestMapping("/getMajorBill")
    @FunCodeMapping("getMajorBill")
    public List<MajorBill> getMajorBill(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getMajorBill(params);
    }

    /**
     *  账单 - 股票盈亏排名
     */
    @RequestMapping("/getStockBill")
    @FunCodeMapping("getStockBill")
    public List<StockBill> getStockBill(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getStockBill(params);
    }

    /**
     *  账单 - 业绩比较
     */
    @RequestMapping("/getReferenceProfitRateList")
    @FunCodeMapping("getReferenceProfitRateList")
    public List<ReferenceProfitRate> getReferenceProfitRateList(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"indexKey"});
        return billHandler.getReferenceProfitRateList(params);
    }

    /**
     * 账单- 投资偏好
     */
    @RequestMapping("/getInvestLabelList")
    @FunCodeMapping("getInvestLabelList")
    public List<PublishBill> getInvestLabelList(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey","count"});
        return billHandler.getInvestLabelList(params);
    }

    /**
     * 账单 - 五维分析
     */
    @RequestMapping("/getAbilityBillList")
    @FunCodeMapping("getAbilityBillList")
    public List<AbilityBill> getAbilityBillList(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getAbilityBillList(params);
    }

    /**
     *  年度账单基本信息
     */
    @RequestMapping("/getYearMajorBill")
    @FunCodeMapping("getYearMajorBill")
    public MajorBillYear getYearMajorBill(Map<String,Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getYearMajorBill(params);
    }

    /**
     * 历史区间资产变动信息
     * @param params
     * @return
     */
    @RequestMapping("/getAssetSectionBill")
    @FunCodeMapping("getAssetSectionBill")
    public AssetSection getAssetSectionBill(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","startDate","endDate"});
        return billHandler.getAssetSectionBill(params);
    }


    /**
     * 2021~2024年度账单信息
     * 年终账单
     *
     * @param
     * @return
     */
    @FunCodeMapping("getUserYearBill")
    @RequestMapping("/getUserYearBill")
    public YearBillExtend getYearBillExtend(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getUserYearBill(params);
    }

    /**
     * 2023半年度账单信息
     * fundId 资金账号
     * indexKey 2023
     * @param
     * @return
     */
    @FunCodeMapping("getUserHalfYearBill")
    @RequestMapping("/getUserHalfYearBill")
    public YearBillExtend getUserHalfYearBillExtend(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId","indexKey"});
        return billHandler.getUserHalfYearBill(params);
    }



    @FunCodeMapping(FunCodeConstants.GET_ZHFX_SETTLE_STATUS)
    public Object getZhfxSettleStatus(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        return billHandler.getZhfxSettleStatus(params);
    }
    /**
     * ACCTANAL-34 港股通汇率波动提示
     * @param params
     * @return
     */
    @RequestMapping("/getHgtRateChange")
    @FunCodeMapping(FunCodeConstants.GET_HGT_RATE_CHANGE)
    public Object getHgtRateChange(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        return billHandler.getHgtRateChange(params);
    }

    /**
     * 用户近一年诊断账单
     *
     * @param params
     * @return
     */
    @FunCodeMapping(FunCodeConstants.GET_ACCT_DIAGNOSE)
    @RequestMapping("/getAcctDiagnose")
    public AcctDiagnoseBO getAcctDiagnose(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        return billHandler.getAcctDiagnose(params);
    }

    /**
     * 获取账单中的资产走势
     *
     * @param params fundId, indexKey
     * @return 日资产
     */
    @FunCodeMapping(FunCodeConstants.GET_ASSET_BILL_TREND)
    @RequestMapping("/getBillAssetTrend")
    public List<AssetDay> getBillAssetTrend(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "indexKey"});
        return billHandler.getBillAssetTrend(params);
    }
}
