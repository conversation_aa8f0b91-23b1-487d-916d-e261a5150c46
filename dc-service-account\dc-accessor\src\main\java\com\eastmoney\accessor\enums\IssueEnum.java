package com.eastmoney.accessor.enums;

/**
 * Created on 2016/11/03
 * 市场类别
 *
 * <AUTHOR>
 */
public enum IssueEnum {

    MKT_SHANGHAIA("1"),//沪A
    MKT_SHENZHENA("0"),//深A
    MKT_HGT("5");//沪港通
    private String unit;

    IssueEnum(String unit) {
        this.unit = unit;
    }

    public String getValue() {
        return this.unit;
    }

    public static IssueEnum getEnum(String value) {
        switch (value) {
            case "1":
                return MKT_SHANGHAIA;
            case "0":
                return MKT_SHENZHENA;
            case "5":
                return MKT_HGT;
            default:
                throw new RuntimeException("not found Issue[" + value + "]");

        }
    }

}
