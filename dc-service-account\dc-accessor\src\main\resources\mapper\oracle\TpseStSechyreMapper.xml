<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.TpseStSechyreMapper">

    <sql id="All_Column">
        EID,EITIME,EUTIME,HANGYE,SECURITYCODE,COMPANYCODE,EMINDEXCODE,PUBLISHNAME
    </sql>
    <select id="getTpseStSechyreList" resultType="com.eastmoney.common.entity.TpseStSechyre">
        SELECT  <include refid="All_Column"/> FROM atcenter.TPSE_ST_SECHYRE
        where hangye in('东财一级行业','港交所一级行业')
        <if test="stkCode != null">and securitycode = #{stkCode}</if>
    </select>

</mapper>