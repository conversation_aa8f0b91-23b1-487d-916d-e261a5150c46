package com.eastmoney.service.cache;

import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class LocalAlterCacheInitializer {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocalAlterCacheInitializer.class);

    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    @PostConstruct
    private void initStart(){
        LOGGER.info("------------------------正在加载 ***本地缓存*** 信息，请等待-------------------------" + DateUtil.getCurDateTime());

        bseCodeAlterService.doLoadInfo();
        LOGGER.info("------------------------加载 <代码变更> 完成-------------------------" + DateUtil.getCurDateTime());

        LOGGER.info("------------------------加载 ***本地缓存*** 全部完成-------------------------" + DateUtil.getCurDateTime());
    }

    @Scheduled(cron = "0 0 6 * * *")
    private void scheduledCodeAlterRefresh(){
        LOGGER.info("------------------------定时加载 *代码变更* 信息，请等待-------------------------" + DateUtil.getCurDateTime());
        bseCodeAlterService.doLoadInfo();
        LOGGER.info("------------------------加载 *代码变更* 完成-------------------------" + DateUtil.getCurDateTime());
    }

    public void flushDate() {
        LOGGER.info("------------------------手动刷新 *代码变更* 信息，请等待-------------------------" + DateUtil.getCurDateTime());
        bseCodeAlterService.doLoadInfo();
        LOGGER.info("------------------------加载 *代码变更* 完成-------------------------" + DateUtil.getCurDateTime());
    }
}
