package com.eastmoney.common.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.entity.cal.BdTradeBillFinal;
import com.eastmoney.common.entity.cal.BdTradeBillPrate;
import com.eastmoney.common.entity.cal.IPOTradeBill;
import com.eastmoney.common.entity.cal.dw.bill.*;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

public class YearBillExtend {
    private Long fundId; //资金账号
    private Integer serverId;
    private Integer indexKey; //时间槽索引key，年:yyyy,月：yyyyMM
    private MajorBillStrInfo majorBill; //年账单-收益额收益率
    private BdTradeBillMaxProfitDO bestDay;//收益最高的一天

//    private List<MonthBillBO> monthBill;//月度表现
//    private Double profitRateDefeatMarketIndex;// 全年收益率超越大盘指数
//    private Double profitRateMarketIndex;// 大盘指数收益率
    private Long tradesCount; // 全年交易次数
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tradesCountRankPercent; //全年交易次数排名百分比
    private String mostTradeStkCode; //全年交易次数最多的股票证券代码
    private String mostTradeMarket; //全年交易次数最多的股票市场
    private String mostTradeStkName; //全年交易次数最多的股票名称
    private Long mostTradeStkTradeTimes; // 全年交易次数最多的股票交易次数
    private String mostHoldingStkCode;//全年持有天数最长的股票证券代码
    private String mostHoldingMarket; //全年持有天数最长的股票市场bdTradeBillPrate
    private String mostHoldingStkName; //全年持有最多的股票名称
    private Integer mostHoldingStkHoldingDays;//全年持有天数最长的股票持有天数

//    private StockBill earnMostStock;//赚钱最多股票
//    private StockBill lossMostStock;//赚钱最多股票
//    private Long earnStockCount;
//    private Long lossStockCount;
//    private List<PublishBill> topPublishBill;//最爱投资的行业 TOP3
    private IPOTradeBill ipoTradeBill;//年度打新账单
//    private BdUserTradeBillDO bdUserTradeBill;// 大数据账单

    /** ------------------------------------------------ 2022 *****------------------- **/

    /**
     * 是否有证券持仓
     */
    private Boolean hasPositionStk;

    /**
     * 日收益率大于沪深200日涨幅的天数
     */
    private Long tradeWinDays;

    /**
     * 操盘胜率
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tradeWinRate;

    /**
     * 操盘胜率超过东财普通用户的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal tradeWinRateRankPercent;

    /**
     * 月收益大于0的月份数
     */
    private Long profitMonthCount;

    /**
     * 月收益大于0的月份数超越东财普通账户的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profitMonthCountRankPercent;

    /**
     * 月收益金额最大的月份的收益额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profitBestMonth;

    /**
     * 全年已清仓证券盈利证券数
     */
    private Long clearProfitSecurityNum;

    /**
     * 全年已清仓证券盈利最多的证券名称
     */
    private String clearMostProfitSecurityName;

    /**
     * 全年已清仓证券盈利最多的证券代码
     */
    private String clearMostProfitSecurityCode;

    /**
     * 全年已清仓证券盈利最多的证券市场
     */
    private String clearMostProfitSecurityMarket;

    /**
     * 全年已清仓证券盈利最多的收益额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal clearMostProfit;
    /**
     * 全年已清仓证券亏损证券数
     */
    private Long clearLossSecurityNum;

    /**
     * 全年已清仓证券亏损最多的证券名称
     */
    private String clearMostLossSecurityName;

    /**
     * 全年已经清仓证券亏损最多的证券代码
     */
    private String clearMostLossSecurityCode;

    /**
     * 全年已清仓证券亏损最多的证券市场
     */
    private String clearMostLossSecurityMarket;

    /**
     * 全年已清仓证券亏损最多的亏损金额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal clearMostLoss;

    /**
     * 大数据神操作
     */
    private BdTradeBillFinal bdTradeBillFinal;

    /**
     * 大数据振幅
     */
    private BdTradeBillPrate bdTradeBillPrate;


    /** -------------------------2023------------------- **/
    /**
     * 基础信息
     */
    private BdTradeBillBaseInfoDO baseInfo;
    /**
     * 首次交易
     */
    private BdTradeBillFirstDayDO firstDay;
    /**
     * 赚钱最多的产品的最后一次清仓日期
     */
    private Integer clearMostProfitLDate;
    /**
     * 单日交易次数最多
     */
    private BdTradeBillMostTradeDO mostTrdDay;
    /**
     * 月收益金额最高的月份
     */
    private Integer profitBestMonthDate;
    /**
     * 累计清仓总次数
     */
    private Long clearNum;
    /**
     * 获利清仓总次数
     */
    private Long clearProfitNum;
    /**
     * 清仓盈利率
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal clearProfitRate;

    /**
     * 清仓股平均持股天数
     */
    private Long avgHoldDays;

    /**
     * 热门概念板块
     */
    private BdTradeBillConceptSectorsDO conceptSectors;
    /**
     * 扭亏为盈的人数占普通交易用户的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal turnEarnPerc;

    /** -------------------------2024------------------- **/
    /**
     * 最高日收益
     */
    private BdTradeBillMaxProfitDO maxProfit;
    /**
     * 单笔最大清仓获利&清仓总计
     */
    private BdTradeBillYearClearProfitDO clearProfit;

    /**
     * 牛市指标
     */
    private BdTradeBillYearBullProfitDO bullProfitInfo;
    private BdTradeBillYearBullTradeDO bullTradeInfo;
    private BdTradeBillYearHoldDO holdInfo;
    private BdTradeBillYearTProfitDO tProfitInfo;

    /**
     * 投顾指标
     */
    private BdTradeBillYearInvestAdvisDO investAdvisInfo;

    /** ----------------------------------------------- **/

    /**
     * 行情回顾页
     */
    private BdTradeBillYearReviewDO reviewInfo;


    /** ----------------------------------------------- **/


    public Long getAvgHoldDays() {
        return avgHoldDays;
    }

    public void setAvgHoldDays(Long avgHoldDays) {
        this.avgHoldDays = avgHoldDays;
    }


    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public MajorBillStrInfo getMajorBill() {
        return majorBill;
    }

    public void setMajorBill(MajorBillStrInfo majorBill) {
        this.majorBill = majorBill;
    }

    public BdTradeBillMaxProfitDO getBestDay() {
        return bestDay;
    }

    public void setBestDay(BdTradeBillMaxProfitDO bestDay) {
        this.bestDay = bestDay;
    }

    public Long getTradesCount() {
        return tradesCount;
    }

    public void setTradesCount(Long tradesCount) {
        this.tradesCount = tradesCount;
    }

    public BigDecimal getTradesCountRankPercent() {
        return tradesCountRankPercent;
    }

    public void setTradesCountRankPercent(BigDecimal tradesCountRankPercent) {
        this.tradesCountRankPercent = tradesCountRankPercent;
    }

    public String getMostTradeStkCode() {
        return mostTradeStkCode;
    }

    public void setMostTradeStkCode(String mostTradeStkCode) {
        this.mostTradeStkCode = mostTradeStkCode;
    }

    public String getMostTradeMarket() {
        return mostTradeMarket;
    }

    public void setMostTradeMarket(String mostTradeMarket) {
        this.mostTradeMarket = mostTradeMarket;
    }

    public String getMostTradeStkName() {
        return mostTradeStkName;
    }

    public void setMostTradeStkName(String mostTradeStkName) {
        this.mostTradeStkName = mostTradeStkName;
    }

    public Long getMostTradeStkTradeTimes() {
        return mostTradeStkTradeTimes;
    }

    public void setMostTradeStkTradeTimes(Long mostTradeStkTradeTimes) {
        this.mostTradeStkTradeTimes = mostTradeStkTradeTimes;
    }

    public String getMostHoldingStkCode() {
        return mostHoldingStkCode;
    }

    public void setMostHoldingStkCode(String mostHoldingStkCode) {
        this.mostHoldingStkCode = mostHoldingStkCode;
    }

    public String getMostHoldingMarket() {
        return mostHoldingMarket;
    }

    public void setMostHoldingMarket(String mostHoldingMarket) {
        this.mostHoldingMarket = mostHoldingMarket;
    }

    public String getMostHoldingStkName() {
        return mostHoldingStkName;
    }

    public void setMostHoldingStkName(String mostHoldingStkName) {
        this.mostHoldingStkName = mostHoldingStkName;
    }

    public Integer getMostHoldingStkHoldingDays() {
        return mostHoldingStkHoldingDays;
    }

    public void setMostHoldingStkHoldingDays(Integer mostHoldingStkHoldingDays) {
        this.mostHoldingStkHoldingDays = mostHoldingStkHoldingDays;
    }

    public IPOTradeBill getIpoTradeBill() {
        return ipoTradeBill;
    }

    public void setIpoTradeBill(IPOTradeBill ipoTradeBill) {
        this.ipoTradeBill = ipoTradeBill;
    }

    public Boolean getHasPositionStk() {
        return hasPositionStk;
    }

    public void setHasPositionStk(Boolean hasPositionStk) {
        this.hasPositionStk = hasPositionStk;
    }

    public Long getTradeWinDays() {
        return tradeWinDays;
    }

    public void setTradeWinDays(Long tradeWinDays) {
        this.tradeWinDays = tradeWinDays;
    }

    public BigDecimal getTradeWinRate() {
        return tradeWinRate;
    }

    public void setTradeWinRate(BigDecimal tradeWinRate) {
        this.tradeWinRate = tradeWinRate;
    }

    public BigDecimal getTradeWinRateRankPercent() {
        return tradeWinRateRankPercent;
    }

    public void setTradeWinRateRankPercent(BigDecimal tradeWinRateRankPercent) {
        this.tradeWinRateRankPercent = tradeWinRateRankPercent;
    }

    public Long getProfitMonthCount() {
        return profitMonthCount;
    }

    public void setProfitMonthCount(Long profitMonthCount) {
        this.profitMonthCount = profitMonthCount;
    }

    public BigDecimal getProfitMonthCountRankPercent() {
        return profitMonthCountRankPercent;
    }

    public void setProfitMonthCountRankPercent(BigDecimal profitMonthCountRankPercent) {
        this.profitMonthCountRankPercent = profitMonthCountRankPercent;
    }

    public BigDecimal getProfitBestMonth() {
        return profitBestMonth;
    }

    public void setProfitBestMonth(BigDecimal profitBestMonth) {
        this.profitBestMonth = profitBestMonth;
    }

    public Long getClearProfitSecurityNum() {
        return clearProfitSecurityNum;
    }

    public void setClearProfitSecurityNum(Long clearProfitSecurityNum) {
        this.clearProfitSecurityNum = clearProfitSecurityNum;
    }

    public String getClearMostProfitSecurityName() {
        return clearMostProfitSecurityName;
    }

    public void setClearMostProfitSecurityName(String clearMostProfitSecurityName) {
        this.clearMostProfitSecurityName = clearMostProfitSecurityName;
    }

    public BigDecimal getClearMostProfit() {
        return clearMostProfit;
    }

    public void setClearMostProfit(BigDecimal clearMostProfit) {
        this.clearMostProfit = clearMostProfit;
    }

    public Long getClearLossSecurityNum() {
        return clearLossSecurityNum;
    }

    public void setClearLossSecurityNum(Long clearLossSecurityNum) {
        this.clearLossSecurityNum = clearLossSecurityNum;
    }

    public String getClearMostLossSecurityName() {
        return clearMostLossSecurityName;
    }

    public void setClearMostLossSecurityName(String clearMostLossSecurityName) {
        this.clearMostLossSecurityName = clearMostLossSecurityName;
    }

    public BigDecimal getClearMostLoss() {
        return clearMostLoss;
    }

    public void setClearMostLoss(BigDecimal clearMostLoss) {
        this.clearMostLoss = clearMostLoss;
    }

    public BdTradeBillFinal getBdTradeBillFinal() {
        return bdTradeBillFinal;
    }

    public void setBdTradeBillFinal(BdTradeBillFinal bdTradeBillFinal) {
        this.bdTradeBillFinal = bdTradeBillFinal;
    }

    public BdTradeBillPrate getBdTradeBillPrate() {
        return bdTradeBillPrate;
    }

    public void setBdTradeBillPrate(BdTradeBillPrate bdTradeBillPrate) {
        this.bdTradeBillPrate = bdTradeBillPrate;
    }

    public String getClearMostProfitSecurityCode() {
        return clearMostProfitSecurityCode;
    }

    public void setClearMostProfitSecurityCode(String clearMostProfitSecurityCode) {
        this.clearMostProfitSecurityCode = clearMostProfitSecurityCode;
    }

    public String getClearMostProfitSecurityMarket() {
        return clearMostProfitSecurityMarket;
    }

    public void setClearMostProfitSecurityMarket(String clearMostProfitSecurityMarket) {
        this.clearMostProfitSecurityMarket = clearMostProfitSecurityMarket;
    }

    public String getClearMostLossSecurityCode() {
        return clearMostLossSecurityCode;
    }

    public void setClearMostLossSecurityCode(String clearMostLossSecurityCode) {
        this.clearMostLossSecurityCode = clearMostLossSecurityCode;
    }

    public String getClearMostLossSecurityMarket() {
        return clearMostLossSecurityMarket;
    }

    public void setClearMostLossSecurityMarket(String clearMostLossSecurityMarket) {
        this.clearMostLossSecurityMarket = clearMostLossSecurityMarket;
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public BdTradeBillBaseInfoDO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BdTradeBillBaseInfoDO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public BdTradeBillFirstDayDO getFirstDay() {
        return firstDay;
    }

    public void setFirstDay(BdTradeBillFirstDayDO firstDay) {
        this.firstDay = firstDay;
    }

    public Integer getClearMostProfitLDate() {
        return clearMostProfitLDate;
    }

    public void setClearMostProfitLDate(Integer clearMostProfitLDate) {
        this.clearMostProfitLDate = clearMostProfitLDate;
    }

    public BdTradeBillMostTradeDO getMostTrdDay() {
        return mostTrdDay;
    }

    public void setMostTrdDay(BdTradeBillMostTradeDO mostTrdDay) {
        this.mostTrdDay = mostTrdDay;
    }

    public Integer getProfitBestMonthDate() {
        return profitBestMonthDate;
    }

    public void setProfitBestMonthDate(Integer profitBestMonthDate) {
        this.profitBestMonthDate = profitBestMonthDate;
    }

    public Long getClearNum() {
        return clearNum;
    }

    public void setClearNum(Long clearNum) {
        this.clearNum = clearNum;
    }

    public Long getClearProfitNum() {
        return clearProfitNum;
    }

    public void setClearProfitNum(Long clearProfitNum) {
        this.clearProfitNum = clearProfitNum;
    }

    public BigDecimal getClearProfitRate() {
        return clearProfitRate;
    }

    public void setClearProfitRate(BigDecimal clearProfitRate) {
        this.clearProfitRate = clearProfitRate;
    }

    public BdTradeBillConceptSectorsDO getConceptSectors() {
        return conceptSectors;
    }

    public void setConceptSectors(BdTradeBillConceptSectorsDO conceptSectors) {
        this.conceptSectors = conceptSectors;
    }

    public BigDecimal getTurnEarnPerc() {
        return turnEarnPerc;
    }

    public void setTurnEarnPerc(BigDecimal turnEarnPerc) {
        this.turnEarnPerc = turnEarnPerc;
    }

    public BdTradeBillMaxProfitDO getMaxProfit() {
        return maxProfit;
    }

    public void setMaxProfit(BdTradeBillMaxProfitDO maxProfit) {
        this.maxProfit = maxProfit;
    }

    public BdTradeBillYearClearProfitDO getClearProfit() {
        return clearProfit;
    }

    public void setClearProfit(BdTradeBillYearClearProfitDO clearProfit) {
        this.clearProfit = clearProfit;
    }

    public BdTradeBillYearBullProfitDO getBullProfitInfo() {
        return bullProfitInfo;
    }

    public void setBullProfitInfo(BdTradeBillYearBullProfitDO bullProfitInfo) {
        this.bullProfitInfo = bullProfitInfo;
    }

    public BdTradeBillYearBullTradeDO getBullTradeInfo() {
        return bullTradeInfo;
    }

    public void setBullTradeInfo(BdTradeBillYearBullTradeDO bullTradeInfo) {
        this.bullTradeInfo = bullTradeInfo;
    }

    public BdTradeBillYearHoldDO getHoldInfo() {
        return holdInfo;
    }

    public void setHoldInfo(BdTradeBillYearHoldDO holdInfo) {
        this.holdInfo = holdInfo;
    }

    public BdTradeBillYearTProfitDO gettProfitInfo() {
        return tProfitInfo;
    }

    public void settProfitInfo(BdTradeBillYearTProfitDO tProfitInfo) {
        this.tProfitInfo = tProfitInfo;
    }

    public BdTradeBillYearInvestAdvisDO getInvestAdvisInfo() {
        return investAdvisInfo;
    }

    public void setInvestAdvisInfo(BdTradeBillYearInvestAdvisDO investAdvisInfo) {
        this.investAdvisInfo = investAdvisInfo;
    }

    public BdTradeBillYearReviewDO getReviewInfo() {
        return reviewInfo;
    }

    public void setReviewInfo(BdTradeBillYearReviewDO reviewInfo) {
        this.reviewInfo = reviewInfo;
    }
}
