package com.eastmoney.common.entity.VO;


import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.common.util.ArithUtil;

/**
 * 个股日收益明细
 * <AUTHOR>
 * @create 2024/3/5
 */
public class SecProfitDayVO {
    private Long fundId;//资金帐号
    private String market; //市场代码
    private String stkCode; //股票代码
    private String stkName; //股票名称
    private String publishName; // 所属行业
    private Double profit;//个股日收益
    private Double profitRate;//个股日收益率
    /**
     * 是否持仓-0-清仓；1持仓
     */
    private Integer holdFlag;

    /**
     * 转换代码  830 -> 920
     * @return
     */
    private String corResCode;

    private SecProfitDayVO(Builder builder) {
        setFundId(builder.fundId);
        setMarket(builder.market);
        setStkCode(builder.stkCode);
        setStkName(builder.stkName);
        setPublishName(builder.pulishName);
        setProfit(builder.profit);
        setProfitRate(builder.profitRate);
        setHoldFlag(builder.holdFlag);
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getPublishName() {
        return publishName;
    }

    public void setPublishName(String publishName) {
        this.publishName = publishName;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Integer getHoldFlag() {
        return holdFlag;
    }

    public void setHoldFlag(Integer holdFlag) {
        this.holdFlag = holdFlag;
    }

    public String getCorResCode() {
        return corResCode;
    }

    public void setCorResCode(String corResCode) {
        this.corResCode = corResCode;
    }

    /**
     * DO 转 VO
     * @param secProfitDayDO
     * @return
     */
    public static SecProfitDayVO of(SecProfitDayDO secProfitDayDO) {
        Double profitRate = null;
        if (secProfitDayDO.getProfitRate() != null) {
            profitRate = ArithUtil.round(secProfitDayDO.getProfitRate(), 4);
        }

        return new Builder()
                .market(secProfitDayDO.getMarket())
                .stkCode(secProfitDayDO.getStkCode())
                .profit(ArithUtil.round(secProfitDayDO.getProfit(), 2))
                .profitRate(profitRate)
                .holdFlag(secProfitDayDO.getHoldFlag())
                .build();
    }


    public static final class Builder {
        private Long fundId;
        private String market;
        private String stkCode;
        private String stkName;
        private String pulishName;
        private Double profit;
        private Double profitRate;
        private Integer holdFlag;

        public Builder() {
        }

        public Builder fundId(Long val) {
            fundId = val;
            return this;
        }

        public Builder market(String val) {
            market = val;
            return this;
        }

        public Builder stkCode(String val) {
            stkCode = val;
            return this;
        }

        public Builder stkName(String val) {
            stkName = val;
            return this;
        }

        public Builder pulishName(String val) {
            pulishName = val;
            return this;
        }

        public Builder profit(Double val) {
            profit = val;
            return this;
        }

        public Builder profitRate(Double val) {
            profitRate = val;
            return this;
        }

        public Builder holdFlag(Integer val) {
            holdFlag = val;
            return this;
        }

        public SecProfitDayVO build() {
            return new SecProfitDayVO(this);
        }
    }
}
