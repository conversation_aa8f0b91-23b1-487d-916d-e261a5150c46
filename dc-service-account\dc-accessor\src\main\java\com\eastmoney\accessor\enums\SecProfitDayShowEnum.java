package com.eastmoney.accessor.enums;

/**
 * 各核心个股收益明细对外展示枚举类
 * <AUTHOR>
 * @date 2024/9/18
 */
public enum SecProfitDayShowEnum {
    OUT_OF_RANGE(0, "支持但显示日期较远"),
    SUPPORT(1, "支持并展示个股明细"),
    UN_SUPPORT(2, "不支持不显示明细模块"),
    ;

    private final Integer value;
    private final String name;

    SecProfitDayShowEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
