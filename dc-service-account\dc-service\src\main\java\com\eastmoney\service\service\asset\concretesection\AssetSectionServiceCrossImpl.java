package com.eastmoney.service.service.asset.concretesection;

import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.asset.AbstractAssetSectionService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created on 2020/8/13-14:33.
 *
 * <AUTHOR>
 */
@Service("assetSectionServiceCross")
public class AssetSectionServiceCrossImpl extends AbstractAssetSectionService {

    @Override
    protected AssetHis getAssetStart(Map<String, Object> params, AssetHis assetNew) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        Integer unitStartDate = CommonUtil.getDateRange(unit).getStartDate();
        Integer assetStartDate = assetNew.getStartDate();
        if (unitStartDate > assetStartDate) {
            params.put("startDate", unitStartDate);
            Integer preStartDate = Integer.valueOf(tradeDateDao.getPreMarketDay(unitStartDate));
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("bizDate", preStartDate);
            requestParams.put("fundId", params.get("fundId"));
            AssetHis assetStart = assetServiceSettle.getAsset(requestParams);
            if (assetStart != null) {
                return assetStart;
            } else {
                // ACCTANAL-35 节假日跨区间情况特殊处理
                AssetHis realTimeAsset = assetServiceRealTime.getAsset(params);
                // 节假日和周末暂不支持7*24银证转账
                realTimeAsset.setShiftInTotal(ArithUtil.add(realTimeAsset.getShiftInTotal(), assetNew.getShiftInTotal()));
                realTimeAsset.setShiftOutTotal(ArithUtil.add(realTimeAsset.getShiftOutTotal(), assetNew.getShiftOutTotal()));
                realTimeAsset.setNetTransfer(assetNew.getNetTransfer());
                realTimeAsset.setOtherNetTransfer(assetNew.getOtherNetTransfer());
                realTimeAsset.setOtcNetTransfer(assetNew.getOtcNetTransfer());
                return realTimeAsset;
            }
        }
        return new AssetHis();
    }
}
