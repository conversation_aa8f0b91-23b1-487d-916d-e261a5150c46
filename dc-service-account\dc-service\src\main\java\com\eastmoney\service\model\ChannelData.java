package com.eastmoney.service.model;

import com.eastmoney.transport.model.Message;
import io.netty.channel.ChannelHandlerContext;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/3/4.
 */
public class ChannelData {
    private final ChannelHandlerContext ctx;
    private final Message message;
    private volatile Map<String,Object> params = new HashMap<String,Object>();
    //请求入队时间戳
    private long requestTimestamp = System.currentTimeMillis();

    public ChannelData(ChannelHandlerContext ctx,Message message, Map<String,Object> params) {
        this.ctx = ctx;
        this.message = message;
        this.params = params;
    }
    public ChannelData(ChannelHandlerContext ctx,Message message) {
        this.ctx = ctx;
        this.message = message;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public Message getMessage() {
        return message;
    }

    public long getRequestTimestamp() {
        return requestTimestamp;
    }
}
