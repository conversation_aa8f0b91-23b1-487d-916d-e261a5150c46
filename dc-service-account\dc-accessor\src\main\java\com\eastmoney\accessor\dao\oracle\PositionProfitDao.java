
package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.PositionProfit;
import com.eastmoney.common.entity.cal.PositionProfitBO;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/8/3
 *
 * <AUTHOR>
 * PositionProfitService
 */
public interface PositionProfitDao extends IBaseDao<PositionProfit, Long> {

    /**
     * 老版股票汇总
     * @param params
     * @return
     */
    List<PositionProfit> getPositionMergeList(Map<String, Object> params);

    /**
     * 单支股票收益汇总信息
     * @param params
     * @return
     */
    List<PositionProfit> getSinglePositionMergeProfit(Map<String, Object> params);

    /**
     * 获取持仓记录
     *
     * @param params fundId, market, stkCode
     * @return
     */
    PositionProfit getHoldSinglePositionProfit(Map<String, Object> params);

    /**
     * 分页查询清仓记录---支持单支股票
     * @param params
     * @return
     */
    List<PositionProfit> getClearPositionProfitListByPage(Map<String, Object> params);

    /**
     * 获取单支单次清仓收益记录
     * @param params
     * @return
     */
    List<PositionProfit> getSinglePositionProfit(Map<String, Object> params);

    /**
     * 查询持仓数据
     *
     * @param params
     * @return
     */
    List<PositionProfit> getOpenPositionList(Map<String, Object> params);

    /**
     * APPAGILE-98832 已清仓股票支持搜索
     * @param params fundId 资金账号
     * @return stkCode, market
     */
    List<PositionProfitBO> getClearPositionList(Map<String, Object> params);

    /**
     * APPAGILE-138118 清仓股票总次数,总盈亏信息
     * @param params fundid,stkcode
     * @return
     */
    PositionProfit getClearPositionSummary(Map<String,Object> params);

    /**
     * 清仓股票总次数,总持仓天数信息
     */
    PositionProfit getDiagnosePositionSection(Map<String,Object> params);
}