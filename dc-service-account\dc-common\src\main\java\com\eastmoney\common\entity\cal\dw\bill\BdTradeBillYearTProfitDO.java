package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 做T
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearTProfitDO {
    // 2024年累计做T次数
    private Long trdTTimes;
    // 2024年做T成功率
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal trdTSuccessRatio;
    // 2024年做T次数超越股友百分比
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal trdTTimesPercent;
    // 2024年做T“正收益”最高的个股名称
    private String trdTMaxpName;
    // 2024年做T“正收益最高”的个股的T收益
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal trdTMaxpProfit;

    public Long getTrdTTimes() {
        return trdTTimes;
    }

    public void setTrdTTimes(Long trdTTimes) {
        this.trdTTimes = trdTTimes;
    }

    public BigDecimal getTrdTSuccessRatio() {
        return trdTSuccessRatio;
    }

    public void setTrdTSuccessRatio(BigDecimal trdTSuccessRatio) {
        this.trdTSuccessRatio = trdTSuccessRatio;
    }

    public BigDecimal getTrdTTimesPercent() {
        return trdTTimesPercent;
    }

    public void setTrdTTimesPercent(BigDecimal trdTTimesPercent) {
        this.trdTTimesPercent = trdTTimesPercent;
    }

    public String getTrdTMaxpName() {
        return trdTMaxpName;
    }

    public void setTrdTMaxpName(String trdTMaxpName) {
        this.trdTMaxpName = trdTMaxpName;
    }

    public BigDecimal getTrdTMaxpProfit() {
        return trdTMaxpProfit;
    }

    public void setTrdTMaxpProfit(BigDecimal trdTMaxpProfit) {
        this.trdTMaxpProfit = trdTMaxpProfit;
    }
}
