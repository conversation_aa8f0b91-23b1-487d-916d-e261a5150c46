###拉取服务配置###
#端口
pullServerPort: 5858
#线程数
pull_server_thread_pool: 100
#服务队列数
pull_server_queue_capacity: 200000

#http端口
pushServerPort: 5999
#http线程数
http_server_thread_pool: 100
#http服务队列数
http_server_queue_capacity: 200000

#拉取一次默认数量
page_size: 100

# 自定义区间放开为5年
customize_date_range_day: 1830

#交易日临时收益获取时间
temp_profit_cal_time: 170000

#使用tidb
tidb_flag: 1


#redis配置
redis:
  servers: 10.10.89.141:7001,10.10.89.141:7002,10.10.89.141:7003
  password: tkfJfnMjvniitHDG
  max-total: 100
  max-idle: 30
  min-idle: 10
  test-on-borrow: false
  test-while-idle: true
