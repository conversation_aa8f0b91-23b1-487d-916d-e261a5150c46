package com.eastmoney.service.http;

import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.service.util.SpringConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by sunyuncai on 2016/6/23.
 */
public class HttpServerStarter {

    private static final Logger LOG = LoggerFactory.getLogger(HttpServerStarter.class);

    public static void start(){
        try{
            SpringConfig springConfig = SpringContextUtil.getBean(SpringConfig.class);
            HttpDispatcher.init();
            int httpServerPort = springConfig.getPushServerPort();
            new Thread(new HttpServer(httpServerPort)).start();
        }catch (Exception e){
            LOG.error("启动服务容器异常",e);
        }
    }
}
