<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.oracle.ProfitRateSectionMapper">
    <resultMap id="BaseResultMap" type="as_profitRateSection">
        <result column="FUNDID" property="fundId"/>
        <result column="PROFIT_RATE" property="profitRate"/>
        <result column="UNIT" property="unit"/>
        <result column="EUTIME" property="euTime"/>
        <result column="BAK_BIZDATE" property="bakBizDate"/>
        <result column="RANK_INDEX" property="rankIndex"/>
        <result column="RANK_PERCENT" property="rankPercent"/>
        <result column="INDEX_DATE" property="indexDate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID, PROFIT_RATE, UNIT, RANK_INDEX,RANK_PERCENT,BAK_BIZDATE,INDEX_DATE,EUTIME
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="All_Column"/>
        FROM ATCENTER.PROFIT_RATE_SECTION
        <where>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
        </where>
    </select>

</mapper>
