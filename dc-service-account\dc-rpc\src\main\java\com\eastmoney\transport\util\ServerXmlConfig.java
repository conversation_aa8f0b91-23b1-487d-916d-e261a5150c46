package com.eastmoney.transport.util;

import com.eastmoney.transport.model.ServerInfo;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * Created by sunyuncai on 2016/10/9.
 */
public class ServerXmlConfig {
    public static final Map<String, List<ServerInfo>> SERVER_CONFIG_MAP = new HashMap<>();
    static {
        InputStream xmlIn = null;
        try {
            //解析配置的服务器
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db=dbf.newDocumentBuilder();
            xmlIn = Constants.class.getClassLoader().getResourceAsStream("server.xml");
            Document doc=db.parse(xmlIn);
            Element root=doc.getDocumentElement();
            NodeList groupList=root.getElementsByTagName("group");
            for (int i = 0; i < groupList.getLength(); i++) {
                List<ServerInfo> serverInfos = new ArrayList<ServerInfo>();
                Node node = groupList.item(i);
                String group = ((Element)node) .getAttribute("name");
                NodeList serverList = ((Element) node).getElementsByTagName("server");
                for (int j = 0; j < serverList.getLength(); j++) {
                    Node n = serverList.item(j);
                    String ip = n.getAttributes().getNamedItem("ip").getNodeValue();
                    int port = Integer.parseInt(((Element) n).getAttribute("port"));
                    int connectNum = Integer.parseInt(((Element) n).getAttribute("connectNum"));
                    ServerInfo serverInfo = new ServerInfo(ip,port,connectNum);
                    serverInfos.add(serverInfo);
                }
                SERVER_CONFIG_MAP.put(group, serverInfos);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(xmlIn != null)
                    xmlIn.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
