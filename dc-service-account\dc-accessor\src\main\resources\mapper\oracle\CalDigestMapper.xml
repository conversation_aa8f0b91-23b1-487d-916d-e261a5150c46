<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.CalDigestMapper">

    <select id="selectByCondition" resultType="as_calDigest">
        SELECT CALINDEX,STKTYPE,STKTYPENAME,DIGESTID,DIGESTNAME,REMARK,USEFLAG
        FROM ATCENTER.B_CAL_DIGEST
        <where>
            <if test="calIndexes != null">
                AND CALINDEX IN
                <foreach item="item" index="index" collection="calIndexes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getDigestIdAndName" resultType="as_calDigest">
        SELECT DISTINCT DIGESTID, DIGESTNAME
        FROM ATCENTER.B_CAL_DIGEST
        WHERE USEFLAG = 1
    </select>
    
</mapper>