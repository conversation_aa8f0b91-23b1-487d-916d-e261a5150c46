package com.eastmoney.accessor.datasource;

import com.eastmoney.common.util.ClassUtil;
import com.eastmoney.common.util.PropertiesUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;

import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/31
 *
 * <AUTHOR>
 */
public class DynamicDataSourceUtils {
    private static final Logger LOG = LoggerFactory.getLogger(DynamicDataSourceUtils.class);

    /**
     * 设置数据源各项属性
     *
     * @param map
     * @param dataSource
     */
    public static void setDsProperties(Map<String, String> map, DataSource dataSource) {

        for (Map.Entry<String, String> entry : map.entrySet()) {

            String name = entry.getKey();
            PropertyDescriptor propertyDescriptor = ClassUtil.getPropertyDescriptor(dataSource.getClass(), name);
            if (propertyDescriptor == null || propertyDescriptor.getWriteMethod() == null) {
                continue;
            }

            Method writeMethod = propertyDescriptor.getWriteMethod();
            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                writeMethod.setAccessible(true);
            }
            Object value = ClassUtil.toTargetTypeValue(entry.getValue(), propertyDescriptor.getPropertyType());
            ClassUtil.invokeMethod(writeMethod, dataSource, value);
        }
    }

    /**
     * 获取并移除元素
     *
     * @param map
     * @return
     */
    public static String getAndRemoveValue(Map<String, String> map, String key) {
        return getAndRemoveValue(map, key, null);
    }

    /**
     * 获取并移除元素
     *
     * @param map
     * @return
     */
    public static String getAndRemoveValue(Map<String, String> map, String key, String defaultValue) {
        String value = map.get(key);
        map.remove(key);
        if (StringUtils.isBlank(value) && StringUtils.isBlank(defaultValue)) {
            LOG.warn("属性不能为空:" + key);
        }
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

    /**
     * 解析数据源信息
     *
     * @param dsConfigFile
     * @return
     */
    public static List<Map<String, String>> parseDataSources(String dsConfigFile) {
        try {
            DocumentBuilder documentBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = documentBuilder.parse(PropertiesUtil.loadResource(dsConfigFile));
            Element dataSources = doc.getDocumentElement();
            NodeList datasourceNodes = dataSources.getChildNodes();
            if (datasourceNodes == null || datasourceNodes.getLength() == 0) {
                throw new Exception("动态数据源配置信息错误");
            }
            List<Map<String, String>> dataSourceList = new ArrayList<>(10);
            Map<String, String> commProMap = null;
            for (int i = 0; i < datasourceNodes.getLength(); i++) {
                Node node = datasourceNodes.item(i);
                if (node.getNodeType() != Node.ELEMENT_NODE) {
                    continue;
                }
                NodeList propertyNodeList = node.getChildNodes();
                Map<String, String> proMap = parseProperties(propertyNodeList);
                if ("commonConfig".equals(node.getNodeName())) {
                    commProMap = proMap;
                    continue;
                }
                String dataSourceId = getAttr(node, DataSourceConstant.ATTR_ID);
                String dataSourceClass = getAttr(node, DataSourceConstant.ATTR_CLASS);
                String isDefaultDataSource = getAttr(node, DataSourceConstant.ATTR_DEFAULT);
                String dbType = getAttr(node, DataSourceConstant.ATTR_DB_TYPE);
                String flag = getAttr(node, DataSourceConstant.ATTR_FLAG);
                String serverId = getAttr(node, DataSourceConstant.ATTR_SERVER_ID);

                proMap.put(DataSourceConstant.ATTR_ID, dataSourceId);
                proMap.put(DataSourceConstant.ATTR_CLASS, dataSourceClass);
                proMap.put(DataSourceConstant.ATTR_DEFAULT, isDefaultDataSource);
                proMap.put(DataSourceConstant.ATTR_DB_TYPE, dbType);
                proMap.put(DataSourceConstant.ATTR_FLAG, flag);
                proMap.put(DataSourceConstant.ATTR_SERVER_ID, serverId);
                dataSourceList.add(proMap);
            }
            for (Map<String, String> proMap : dataSourceList) {
                for (Map.Entry<String, String> commEntry : commProMap.entrySet()) {
                    if (!proMap.containsKey(commEntry.getKey())) {
                        proMap.put(commEntry.getKey(), commEntry.getValue());
                    }
                }
            }
            return dataSourceList;
        } catch (Exception e) {
            LOG.error("解析动态数据源配置文件【{}】出错:", dsConfigFile, e);
        }
        return null;
    }

    /**
     * 获取属性值
     *
     * @param node
     * @param attrName
     * @return
     */
    private static String getAttr(Node node, String attrName) {
        NamedNodeMap attributes = node.getAttributes();
        Node namedItem = attributes.getNamedItem(attrName);
        if (namedItem == null) {
            return null;
        }
        return namedItem.getNodeValue();
    }

    /**
     * 解析property标签
     *
     * @param propertyNodeList
     * @return
     */
    private static Map<String, String> parseProperties(NodeList propertyNodeList) {

        Map<String, String> propertyMap = new HashMap<>();
        if (propertyNodeList == null || propertyNodeList.getLength() == 0) {
            return propertyMap;
        }
        for (int i = 0; i < propertyNodeList.getLength(); i++) {
            Node node = propertyNodeList.item(i);
            if (node.getNodeType() != Node.ELEMENT_NODE) {
                continue;
            }
            NamedNodeMap attributes = node.getAttributes();
            String name = attributes.getNamedItem(DataSourceConstant.ATTR_NAME).getNodeValue();
            String value = attributes.getNamedItem(DataSourceConstant.ATTR_VALUE).getNodeValue();
            propertyMap.put(name, value);
        }
        return propertyMap;
    }
}
