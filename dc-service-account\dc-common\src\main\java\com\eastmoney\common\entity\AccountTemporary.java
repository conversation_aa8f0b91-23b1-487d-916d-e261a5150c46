package com.eastmoney.common.entity;

import java.util.Date;

/**
 * Created by sunyuncai on 2017/8/21.
 */
public class AccountTemporary {
    private Date euTime;//数据修改时间
    private long fundId;
    private double profit;
    private double profitRate;
    private double shiftOut;
    private double shiftIn;
    private double asset;
    private int bizDate;
    private double otcAsset;    //OTC 资产

    // 港股通假期收益
    private Double hkProfit;

    private Integer serverId;

    public double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public long getFundId() {
        return fundId;
    }

    public void setFundId(long fundId) {
        this.fundId = fundId;
    }

    public double getProfit() {
        return profit;
    }

    public void setProfit(double profit) {
        this.profit = profit;
    }

    public double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(double profitRate) {
        this.profitRate = profitRate;
    }

    public double getShiftOut() {
        return shiftOut;
    }

    public void setShiftOut(double shiftOut) {
        this.shiftOut = shiftOut;
    }

    public double getShiftIn() {
        return shiftIn;
    }

    public void setShiftIn(double shiftIn) {
        this.shiftIn = shiftIn;
    }

    public double getAsset() {
        return asset;
    }

    public void setAsset(double asset) {
        this.asset = asset;
    }

    public int getBizDate() {
        return bizDate;
    }

    public void setBizDate(int bizDate) {
        this.bizDate = bizDate;
    }

    public Double getHkProfit() {
        return hkProfit;
    }

    public void setHkProfit(Double hkProfit) {
        this.hkProfit = hkProfit;
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }
}
