package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiStockMapper;
import com.eastmoney.common.entity.Stock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("stockDao")
public class TiStockDaoImpl extends BaseDao<TiStockMapper, Stock, Integer> implements StockDao {

    @Override
    public Stock getStock(String stkCode, String market) {
        return mapper.getStock(StringUtils.trim(stkCode), market);
    }

    @Override
    public List<Stock> queryAllStock() {
        return mapper.queryAllStock();
    }

}
