package com.eastmoney.service.service.profit.section;

import com.eastmoney.accessor.dao.oracle.ProfitDayDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateDayDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.profit.base.ProfitService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description
 * @auther pengjiejie
 * @create 2020-09-07 19:25
 */
public abstract class AbstractProfitSectionService implements ProfitSectionService {

    @Autowired
    protected AssetNewService assetNewService;
    @Resource(name = "profitSectionServiceSettle")
    private ProfitSectionService profitSectionServiceSettle;
    @Resource(name = "profitSectionServiceRealTime")
    protected ProfitSectionService profitSectionServiceRealTime;
    @Autowired
    protected ProfitDayDao profitDayDao;
    @Autowired
    protected ProfitRateDayDao profitRateDayDao;
    @Autowired
    protected TradeDateDao tradeDateDao;

    @Override
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        SectionProfitBean profitSectionSettle = profitSectionServiceSettle.getProfitSection(params);
        if (profitSectionSettle == null) {
            return null;
        }
        setSectionParams(params, assetNew, profitSectionSettle);
        SectionProfitBean profitSectionRealTime = profitSectionServiceRealTime.getProfitSection(params);
        return buildProfitSection(
                CommonUtil.convert(params.get("startDate"), Integer.class),
                profitSectionSettle, profitSectionRealTime);
    }

    /**
     * 根据收益额，收益率信息，构造区间收益
     *
     * @param startDate
     * @param profitSectionSettle
     * @param profitSectionRealTime
     * @return
     */
    protected SectionProfitBean buildProfitSection(Integer startDate, SectionProfitBean profitSectionSettle, SectionProfitBean profitSectionRealTime) {
        SectionProfitBean result = new SectionProfitBean();
        double profit = profitSectionSettle.getProfit();
        double profitRate = profitSectionSettle.getProfitRate();
        result.setIndexDate(startDate);
        result.setEuTime(profitSectionSettle.getEuTime());
        result.setBizDate(profitSectionSettle.getBizDate());
        if (profitSectionRealTime != null) {
            profit = ArithUtil.add(profit, profitSectionRealTime.getProfit());
            profitRate = ArithUtil.round(CommonUtil.multiply(profitRate, profitSectionRealTime.getProfitRate()), 4);
            if (profitSectionRealTime.getEuTime() != null) {
                result.setEuTime(profitSectionRealTime.getEuTime());
            }
            if (profitSectionRealTime.getBizDate() != 0) {
                result.setBizDate(profitSectionRealTime.getBizDate());
            }
            result.setHkProfit(profitSectionRealTime.getHkProfit());
        }
        result.setProfit(profit);
        result.setProfitRate(profitRate);
        return result;
    }

    /**
     * 设定区间参数
     * startDate 区间针对用户有效起始日期
     * accountBizDate 最新清算日期
     *
     * @param params              区间收益参数集
     * @param assetNew            最新清算资产
     * @param sectionProfitSettle 最新清算区间收益
     */
    protected abstract void setSectionParams(Map<String, Object> params, AssetNew assetNew, SectionProfitBean sectionProfitSettle);
}
