package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.LogAssetMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.LogAsset;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("logAssetDao")
public class LogAssetDaoImpl extends BaseDao<LogAssetMapper, LogAsset, Integer> implements LogAssetDao {

    @Override
    public List<Object> getLogAssetSum(Map<String, Object> params) {
        return getMapper().getLogAssetSum(params);
    }

    @Override
    public List<LogAsset> getStkTradeList(Map<String, Object> params) {
        return getMapper().getStkTradeList(params);
    }

    @Override
    public List<LogAsset> getStkShiftList(Map<String, Object> params) {
        return getMapper().getStkShiftList(params);
    }

    @Override
    public List<LogAsset> getOtherDetailList(Map<String, Object> params) {
        return getMapper().getOtherDetailList(params);
    }

    @Override
    public List<LogAsset> getTradeShiftList(Map<String, Object> params) {
        return getMapper().getTradeShiftList(params);
    }

    @Override
    public List<LogAsset> getHoldTradeShiftList(Map<String, Object> params) {
        return null;
    }

    @Override
    public List<LogAsset> getBSTradeList(Map<String, Object> params) {
        return getMapper().getBSTradeList(params);
    }
}
