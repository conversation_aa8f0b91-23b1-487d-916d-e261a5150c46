package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.sqlserver.OggMixedConfigDao;
import com.eastmoney.accessor.model.MixedConfig;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class MixedConfigService {
    private static Logger LOG = LoggerFactory.getLogger(MixedConfigService.class);
    @Resource(name = "mixedConfigCache")
    private LoadingCache<String, Optional<String>> mixedConfigCache;
    @Autowired
    private OggMixedConfigDao oggMixedConfigDao;
    @Autowired
    private CoreConfigService coreConfigService;

    @Bean(name = "mixedConfigCache")
    public LoadingCache<String, Optional<String>> mixedConfigCache() {
        LoadingCache<String, Optional<String>> maxedConfigCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<String>>() {
                    @Override
                    public Optional<String> load(String key) throws Exception {
                        String paraValue = null;
                        try {
                            Map<String, Object> param = new HashMap<>(2);
                            String[] split = key.split("-");
                            param.put("serverId", split[1]);
                            param.put("paraId", split[0]);
                            MixedConfig mixedConfig = oggMixedConfigDao.getMixedConfig(param);
                            if (mixedConfig != null) {
                                paraValue = mixedConfig.getParaValue();
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(paraValue);
                    }
                });
        return maxedConfigCache;
    }

    public String getParaValue(String paraKey, Integer serverId) {
        if(StringUtils.isEmpty(paraKey) || serverId == null){
            return null;
        }
        try {
            return mixedConfigCache.get(paraKey + "-" + serverId).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取柜台公共出参失败", e);
        }
        return null;
    }

    public String getMixedConfigParaValue(Map<String, Object> params, String defaultValue) {
        String paraKey = CommonUtil.convert(params.get("paraId"), String.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);

        try {
            Integer serverId = coreConfigService.getServerId(fundId);
            String value = mixedConfigCache.get(paraKey + "-" + serverId).orElse(null);
            defaultValue = StringUtils.isEmpty(value) ? defaultValue : value;
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取柜台公共出参失败", e);
        }
        return defaultValue;
    }
}
