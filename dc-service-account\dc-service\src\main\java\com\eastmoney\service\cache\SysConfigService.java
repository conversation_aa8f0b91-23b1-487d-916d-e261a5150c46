package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.sqlserver.OggSysConfigDao;
import com.eastmoney.common.entity.SysConfig;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/23 10:31
 */
@Service
public class SysConfigService {
    private static Logger LOG = LoggerFactory.getLogger(SysConfigService.class);

    @Resource(name = "sysConfigCache")
    private LoadingCache<String, Optional<SysConfig>> sysConfigCache;
    @Autowired
    private OggSysConfigDao oggSysConfigDao;

    @Bean(name = "sysConfigCache")
    public LoadingCache<String, Optional<SysConfig>> sysConfigCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(30, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<SysConfig>>() {
                    @Override
                    public Optional<SysConfig> load(String key) throws Exception {
                        SysConfig sysConfig = null;
                        try {
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("serverId", key);
                            List<SysConfig> sysConfigList = oggSysConfigDao.getSysConfig(param);
                            if (!CollectionUtils.isEmpty(sysConfigList)) {
                                sysConfig = sysConfigList.get(0);
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(sysConfig);
                    }
                });
    }

    public SysConfig getSysConfig(String serverId) {
        try {
            if (StringUtils.isEmpty(serverId)) {
                serverId = "1";
            }
            return sysConfigCache.get(serverId).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取最新资产失败", e);
        }
        return null;
    }

}
