package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.oracle.ProfitRateContrastDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.common.entity.PositionInfo;
import com.eastmoney.common.entity.SysConfig;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.ProfitRateContrast;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.SysConfigService;
import com.eastmoney.service.util.ServiceConstants;
import com.eastmoney.service.util.SpringConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * Created on 2020/8/17-8:54.
 *
 * <AUTHOR>
 */
@Service
public class CommonService {

    private static final String LOFTYPE_MONEY_OLD = "1";
    private static final String LOFTYPE_MONEY_NEW = "4";
    private static Logger LOG = LoggerFactory.getLogger(CommonService.class);
    @Autowired
    private TradeDateDao tradeDateDao;
    @Autowired
    private ProfitRateContrastDao profitRateContrastDao;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private SpringConfig springConfig;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private NodeConfigService nodeConfigService;

    /**
     * 计算实时收益日期
     * <p>
     * 1.1 今日是交易日 accountBizDate = today ---> 当日已经清算完成 不计算实时收益 返回0
     * 1.2.1          accountBizDate = today 的前一个交易日  时间在 9.25 之前 ---> 前一个交易日已清算，当日未开盘 返回0
     * 1.2.2                                               时间在 9.25 之后 ---> 前一个交易日已清算，当日已开盘 返回today
     * 1.3.1          accountBizDate < today 的前一个交易日  且时间在 9.25 之前 ---> 跨天未清算场景 返回前一个交易日
     * 1.3.2                                               且时间在 9.25 之后 ---> 跨天未清算场景，但时间过了9.25 返回today
     * 2.1 今日是非交易日 accountBizDate = today的前一个交易日 ---> 前一个交易日已清算 返回0
     * 2.2              accountBizDate < today的前一个交易日 ---> 前一个交易日未清算 返回上一个交易日
     *
     * @param accountBizDate 最新清算日期
     * @return 0:无需计算 preTradeDayOfToday:上一交易日 today:这个交易日
     */
    public int calRealTimeProfitBizDate(int accountBizDate) {
        int today = Integer.parseInt(DateUtil.getCuryyyyMMdd());
        int preTradeDayOfToday = Integer.parseInt(tradeDateDao.getPreMarketDay(today));
        boolean todayIsMarket = tradeDateDao.isMarket(today);
        if (todayIsMarket) {
            //1.1
            if (accountBizDate == today) {
                return 0;
            }
            //1.2.1 和 1.2.2
            if (accountBizDate == preTradeDayOfToday) {
                return isAfterMarketOpen() ? today : 0;
            }
            //1.3.1 和 1.3.2
            if (accountBizDate < preTradeDayOfToday) {
                //上一交易日没有清算，盘前计算前一日，盘中计算当日
                return isAfterMarketOpen() ? today : preTradeDayOfToday;
            }
            //清算日期大于当日 ---> 不存在的情况数据 数据出错
            LOG.error("交易日数据异常!清算日期{}大于当前日期{}", accountBizDate, today);
            return 0;
        }
        //2.1 和2.2
        return accountBizDate < preTradeDayOfToday ? preTradeDayOfToday : 0;
    }

    /**
     * 1. 当天为交易日
     * 2. 时间在92500之后
     * 3. 如果用户的serverId为空，则时间在170000之前
     * 4. 如果serverId不为空，则查询sysConfig
     * 4.1. 如果sysDate等于当前日期，并且status等于0，则返回true
     * 4.2. 否则，返回false
     * <p>
     * [更新]：交易日 92500~240000 展示当日盈亏 (APPAGILE-134973)
     *
     * @param serverId
     * @return
     */
    public boolean needCalDayProfit(String serverId) {
        try {
            if (!tradeDateDao.todayIsMarket()) {
                return false;
            }
            if (!isAfterMarketOpen()) {
                return false;
            }
            if (nodeConfigService.getSecProfitShowFlag()) {
                return true;
            }
            int curHHmmss = Integer.parseInt(DateUtil.getCurHHmmss());
            if (serverId == null) {
                return 170000 >= curHHmmss;
            }
            SysConfig sysConfig = sysConfigService.getSysConfig(serverId);
            if (sysConfig == null) {
                return 170000 >= curHHmmss;
            }
            return Objects.equals(sysConfig.getStatus(), 0)
                    && Objects.equals(sysConfig.getSysDate(), DateUtil.getCuryyyyMMddInteger());
        } catch (NumberFormatException e) {
            LOG.error("持仓操作分析 获取当前时间异常! ", e);
            return false;
        }
    }

    /**
     * 所有投资者收益率排名分析
     *
     * @param params params
     * @return ProfitRateContrast
     */
    public ProfitRateContrast getProfitRateContrast(Map<String, Object> params) {
        List<ProfitRateContrast> profitRateContrastList = profitRateContrastDao.query(params);
        if (profitRateContrastList != null && profitRateContrastList.size() > 0) {
            return profitRateContrastList.get(0);
        }
        return null;
    }

    /**
     * 判断当前时间是否已经开盘
     * 当日已开盘，返回true
     * 当日未开盘，返回false
     *
     * @return
     */
    public boolean isAfterMarketOpen() {
        int nowTime = Integer.parseInt(DateUtil.getCurHHmmss());
        return nowTime >= 92500;
    }

    public boolean isStkUnsupported(PositionInfo positionInfo) {
        // B股、港股通
        if (StringUtils.isBlank(positionInfo.getMarket()) ||
                MarketEnum.SH_B.getValue().equals(positionInfo.getMarket()) || MarketEnum.SZ_B.getValue().equals(positionInfo.getMarket())) {
            return false;
        }
        // L LOF基金，1 老式货币基金（盘中价格不变），4 新式货币基金（价格固定0.01）
        if (STKTEnum.STKT_LOF.getValue().equals(positionInfo.stkType) &&
                (LOFTYPE_MONEY_OLD.equals(positionInfo.lofMoneyFlag) || LOFTYPE_MONEY_NEW.equals(positionInfo.lofMoneyFlag))) {
            return false;
        }
        // 排除深市的 F 非交易开放基金
        return !STKTEnum.STKT_LOF.getValue().equals(positionInfo.stkType) || !"F".equals(positionInfo.stkLevel)
                || !MarketEnum.SZ_A.getValue().equals(positionInfo.market);
    }


    /**
     * 自定义区间校验
     *
     * @param params
     * @return
     */
    public boolean formatCustomizeQueryTime(Map<String, Object> params) {
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        if (startDate == null || endDate == null) {
            return false;
        }
        if (startDate > endDate) {
            return false;
        }
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return false;
        }

        Integer assetStartDate = assetNew.getStartDate();
        //查询的开始时间不得小于首次拥有资产时间
        if (startDate < assetStartDate) {
            startDate = assetStartDate;
        }
        int curDate = DateUtil.getCuryyyyMMddInteger();
        //查询的截止时间小于等于当前时间
        if (endDate > curDate) {
            endDate = curDate;
        }
        if (startDate > curDate) {
            return false;
        }

        int rangeSize = DateUtil.getDayInterval(startDate, endDate);
        if (rangeSize > springConfig.getCUSTOMIZE_DATE_RANGE_DAY()) {
            return false;
        }

        params.put("assetStartDate", assetStartDate);
        params.put("settleDate", assetNew.getBizDate());
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return true;
    }

    /**
     * 获取临时收益生效时间
     *
     * @return
     */
    public Integer useTempProfitTime() {
        String tempProfitTime = nodeConfigService.getNodeConfig(ServiceConstants.TEMP_PROFIT_CAL_TIME);
        if (StringUtils.isEmpty(tempProfitTime)) {
            return 170000;
        } else {
            return Integer.parseInt(tempProfitTime);
        }
    }

    /**
     * redis 缓存时间区间 固定9:00-17:00 判断当前时间是否在该区间
     *
     * @param redisCacheFlag
     */
    public boolean isInRedisCacheTimeSection(String redisCacheFlag) {
        if ("0".equals(redisCacheFlag)) {
            return false;
        }
        LocalTime now = LocalTime.now();
        boolean isInRedisCacheSectionTime = now.isBefore(nodeConfigService.getRedisCacheEndTime())
                && now.isAfter(nodeConfigService.getRedisCacheStartTime());
        if ("1".equals(redisCacheFlag)) {
            return isInRedisCacheSectionTime && tradeDateDao.todayIsMarket();
        }
        return isInRedisCacheSectionTime;
    }
}
