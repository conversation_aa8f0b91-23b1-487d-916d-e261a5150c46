package com.eastmoney.common.entity.cal;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.eastmoney.common.serializer.DoubleToStringSerializer;

/**
 * 新股新债打新收益表
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
public class IPOPositionProfitDO {
    private Long fundId;//资金账号
    private String stkCode;//证券代码
    private String subCode;//申购代码
    private String market;//市场
    private String stkType;//证券类型
    private Integer startDate;//持仓开始日期,中签缴款日
    private Integer endDate;//持仓结束日期,中签部分清仓日
    @JSONField(serializeUsing = DoubleToStringSerializer.class)
    private Double totalBuyAmt;//中签缴款金额
    private Double totalSaleAmt;//卖出总金额
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Double profit;//持仓收益额
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Double profitRate;//持仓收益率
    private Long totalBuyNum;//中签数量
    private Long totalSaleNum;//累计卖出数量
    private String stkName;//证券名称
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer calFlag;//计算标识 默认值0 值说明：0-持仓 1-清仓
    private Integer holdDays;//持有天数
    private String secuId;//股东账号
    private Double newPrice;//收盘价

    public IPOPositionProfitDO() {
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }

    public Double getTotalBuyAmt() {
        return totalBuyAmt;
    }

    public void setTotalBuyAmt(Double totalBuyAmt) {
        this.totalBuyAmt = totalBuyAmt;
    }

    public Double getTotalSaleAmt() {
        return totalSaleAmt;
    }

    public void setTotalSaleAmt(Double totalSaleAmt) {
        this.totalSaleAmt = totalSaleAmt;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Long getTotalBuyNum() {
        return totalBuyNum;
    }

    public void setTotalBuyNum(Long totalBuyNum) {
        this.totalBuyNum = totalBuyNum;
    }

    public Long getTotalSaleNum() {
        return totalSaleNum;
    }

    public void setTotalSaleNum(Long totalSaleNum) {
        this.totalSaleNum = totalSaleNum;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Integer getCalFlag() {
        return calFlag;
    }

    public void setCalFlag(Integer calFlag) {
        this.calFlag = calFlag;
    }

    public Integer getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(Integer holdDays) {
        this.holdDays = holdDays;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public Double getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(Double newPrice) {
        this.newPrice = newPrice;
    }
}
