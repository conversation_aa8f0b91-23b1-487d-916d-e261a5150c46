<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggLogBankTranMapper">
    <select id="selectBySysDate" resultType="as_logBankTran">
        SELECT  opertime, sno, bankid, fundid, banktranid, fundeffect, status
        FROM run.dbo.logbanktran with (nolock)
        where fundid = #{fundId}
          and banktranid IN ('1','2','5','6','M','N','F') AND fundeffect != 0 AND status IN ('2','6','7','8')
          and sysdate = #{nextBizDate}
    </select>


    <select id="selectByCondition" resultType="as_logBankTran">
        SELECT opertime, sno, bankid, fundid, banktranid, fundeffect, status
        FROM run.dbo.logbanktran with (nolock)
        where fundid = #{fundId}
          and banktranid IN ('1','2','5','6','M','N','F') AND fundeffect != 0 AND status IN ('2','6','7','8')
    </select>


    <select id="selectFundIaAdjustFundAsset" resultType="java.lang.Double">
        select isnull(sum(fundbalance),0) AS fundbalance
        from run.dbo.dclogassettotal with (nolock) WHERE fundid = #{fundId}
        AND bizdate >= (SELECT "sysdate" FROM run.dbo.sysconfig with (nolock))
    </select>

</mapper>