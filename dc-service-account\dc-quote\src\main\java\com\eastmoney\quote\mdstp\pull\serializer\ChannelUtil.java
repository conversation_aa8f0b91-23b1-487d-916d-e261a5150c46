package com.eastmoney.quote.mdstp.pull.serializer;

import io.netty.channel.Channel;

import java.net.InetSocketAddress;

/**
 * Created by 1 on 15-8-17.
 */
public class ChannelUtil {
    public static String getId(Channel channel) {
        InetSocketAddress socketAddress = (InetSocketAddress) channel.remoteAddress();
        return socketAddress.getHostString() + ":" + socketAddress.getPort();
    }
}
