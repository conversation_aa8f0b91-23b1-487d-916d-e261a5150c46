package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.TradeDateHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by Administrator on 2017/7/3.
 */
@Action
@Component
public class TradeDateAction {
    @Autowired
    TradeDateHandler tradeDateHandler;

    //获取交易日期
    @FunCodeMapping("getTradeDateList")
    public Object getTradeDateList(Map<String, Object> params) throws Exception {
        CommonUtil.checkParamNotNull(params, new String[]{"tradeDate"});
        return tradeDateHandler.getTradeDateList(params);
    }
}
