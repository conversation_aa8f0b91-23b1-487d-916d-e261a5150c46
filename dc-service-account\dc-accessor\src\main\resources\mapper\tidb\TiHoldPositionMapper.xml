<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiHoldPositionMapper">

    <resultMap id="BaseResultMap" type="com.eastmoney.common.entity.HoldPosition">
        <result column="FUNDID" property="fundId"/>
        <result column="SERVERID" property="serverId"/>
        <result column="SECUID" property="secuId"/>
        <result column="STKCODE" property="stkCode"/>
        <result column="MARKET" property="market"/>
        <result column="STARTDATE" property="startDate"/>
        <result column="CLEARSTARTDATE" property="clearStartDate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID, SECUID, STKCODE, MARKET, CLEARSTARTDATE
    </sql>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="All_Column"/>
        FROM ATCENTER.HOLD_POSITION use index (PK_LG_HOLD_POSITION)
        WHERE FUNDID = #{fundId}
    </select>
</mapper>