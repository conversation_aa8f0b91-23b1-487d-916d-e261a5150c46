<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.CustCalenderMapper">

    <select id="getByBizDateRange" resultType="com.eastmoney.common.entity.CustCalenderDO">
        select cust.scal,
        cust.isom,
        cust.holiday
        from atcenter.cust_calender cust
        where cust.week in ('Monday','Tuesday','Wednesday','Thursday','Friday')
        <if test="dateFrom != null">
            and cust.tscal &gt;= #{dateFrom}
        </if>
        <if test="dateTo != null">
            and cust.tscal &lt;= #{dateTo}
        </if>
    </select>
</mapper>