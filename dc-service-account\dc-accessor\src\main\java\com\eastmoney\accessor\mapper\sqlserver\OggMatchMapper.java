package com.eastmoney.accessor.mapper.sqlserver;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.Match;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/8
 *
 * <AUTHOR>
 */
@Repository
public interface OggMatchMapper extends BaseMapper<Match, Integer> {
    /**
     * 获取盘中成交记录
     *
     * 220000证券买入 0B
     * 221001证券卖出 0S
     * 220188盘后定价买 3m
     * 220189盘后定价卖 3n
     *
     * @param params fundId, market, stkCode, serverId
     * @return
     */
    List<Match> getRealTimeMatchList(Map<String, Object> params);

    /**
     * 查询盘中所有成交记录 包括沪港通
     * @param params
     * @return
     */
    List<Match> getAllRealTimeMatchList(Map<String, Object> params);
}
