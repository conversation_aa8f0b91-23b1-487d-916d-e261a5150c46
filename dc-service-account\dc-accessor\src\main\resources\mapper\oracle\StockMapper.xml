<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.StockMapper">
    <select id="getStock" resultType="com.eastmoney.common.entity.Stock">
        SELECT  market,stkCode,stkName,linkStk,stktype,stkstatus,stkfc,spellId,stkFullName as expandNameAbbr
        FROM  ATCENTER.STOCK
        where
            STKCODE = #{stkCode}
            <if test='market != null and market != "5" and market != "S"'>
                AND MARKET = #{market}
            </if>
            AND rownum = 1
    </select>

    <select id="queryAllStock" resultType="com.eastmoney.common.entity.Stock">
        select stkcode,market,stkname,stktype,stkfc,spellId,stkFullName as expandNameAbbr
        from ATCENTER.stock
        where serverid = 1
        union
        select stkcode,market,stkname,stktype,stkfc,spellId,stkFullName as expandNameAbbr
        from ATCENTER.stock
        where market in ('5','S')
    </select>
</mapper>