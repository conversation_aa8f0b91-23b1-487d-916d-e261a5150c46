<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.OraStkAssetMapper">
    <select id="selectByCondition" resultType="as_stkAsset">
        SELECT /*+ index_ss(t pk_lg_stkasset_new) */ <include refid="All_Column"/> FROM atcenter.stkasset t
        WHERE fundid = #{fundId}
        and asset_date = #{assetDate}
        and MoneyType = #{moneyType}
        <if test="serverId != null">
            and serverId = #{serverId}
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND stkCode IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    and stkCode = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            and market = #{market}
        </if>
    </select>
    <sql id="All_Column">
        serverid, custid, orgid, fundid, moneytype, market, secuid, seat, stkcode, stklastbal, stkbal, stkavl, stkbuy,
        stksale, stkbuysale, stkuncomebuy, stkuncomesale, stkfrz, stkunfrz, stknightfrz, stktrdfrz, stktrdunfrz, stkdiff,
        stksalediff, stkremain, stkcorpremain, creditstkbal, creditstkbuysale, stkflag, lastbuycost, lastprofitcost, buycost,
        profitcost, mktval, stkavl_in, stkavl_out, stkbuysale2
    </sql>

    <select id="selectOne" resultType="as_stkAsset">
        SELECT <include refid="All_Column"/> FROM ATCENTER.stkasset
        <where>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="serverId != null">
                and serverId = #{serverId}
            </if>
            <if test="assetDate != null">
                and asset_date = #{assetDate}
            </if>
            <if test="market != null">
                and market = #{market}
            </if>
            <if test="markets != null">
                and market in
                <foreach item="item" index="index" collection="markets" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        and rownum = 1
    </select>
</mapper>