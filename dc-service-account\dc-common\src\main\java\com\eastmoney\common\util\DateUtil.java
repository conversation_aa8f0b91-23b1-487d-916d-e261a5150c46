package com.eastmoney.common.util;

import com.eastmoney.common.model.DatePair;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * User: xww
 * Date: 14-8-13
 * Time: 下午4:05
 */
public class DateUtil {
    // 显示日期的格式
    public static final String yyyy_MM_dd = "yyyy-MM-dd";

    // 显示日期的格式
    public static final String yyyy = "yyyy";

    // 显示日期的格式
    public static final String yyyyMMdd = "yyyyMMdd";

    // 显示日期的格式
    public static final String yyyy_MM = "yyyy-MM";

    // 显示日期的格式
    public static final String yyyyMM = "yyyyMM";

    // 显示日期时间的格式
    public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";

    // 显示日期时间的格式
    public static final String yyyy_MM_dd_HH_mm_ss_SSS = "yyyy-MM-dd HH:mm:ss:SSS";

    // 显示日期时间的格式
    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";

    // 显示简体中文日期的格式
    public static final String yyyy_MM_dd_zh = "yyyy年MM月dd日";

    // 显示简体中文日期时间的格式
    public static final String yyyy_MM_dd_HH_mm_ss_zh = "yyyy年MM月dd日HH时mm分ss秒";

    // 显示简体中文日期时间的格式
    public static final String yyyy_MM_dd_HH_mm_zh = "yyyy年MM月dd日HH时mm分";

    public static String currentTime;
    public static String currentDate;


    //W——周
    public static final String DATE_UNIT_W = "W";
    //M——月
    public static final String DATE_UNIT_M = "M";
    //Y——年
    public static final String DATE_UNIT_Y = "Y";


    private static ThreadLocal dateFormat = new ThreadLocal() {
        @Override
        protected Object initialValue() {
            return new SimpleDateFormat(yyyy_MM_dd);
        }
    };


    private static ThreadLocal yyyy_MM_dd_HH_mm_ss_DateTimeFormat = new ThreadLocal() {
        @Override
        protected Object initialValue() {
            return new SimpleDateFormat(yyyy_MM_dd_HH_mm_ss);
        }
    };


    private static ThreadLocal yyyy_MM_dd_zh_DateTimeFormat = new ThreadLocal() {
        @Override
        protected Object initialValue() {
            return new SimpleDateFormat(yyyy_MM_dd_zh);
        }
    };

    private static ThreadLocal yyyy_MM_dd_HH_mm_ss_zh_DateTimeFormat = new ThreadLocal() {
        @Override
        protected Object initialValue() {
            return new SimpleDateFormat(yyyy_MM_dd_HH_mm_ss_zh);
        }
    };

    private static ThreadLocal yyyyMMddHHmmss_DateTimeFormat = new ThreadLocal() {

        @Override
        protected Object initialValue() {
            return new SimpleDateFormat(yyyyMMddHHmmss);
        }
    };

    private static DateFormat getDateFormat(String formatStr) {
        if (formatStr.equalsIgnoreCase(yyyy_MM_dd)) {
            return (DateFormat) dateFormat.get();

        } else if (formatStr.equalsIgnoreCase(yyyy_MM_dd_HH_mm_ss)) {
            return (DateFormat) yyyy_MM_dd_HH_mm_ss_DateTimeFormat.get();
        } else if (formatStr.equalsIgnoreCase(yyyy_MM_dd_zh)) {
            return (DateFormat) yyyy_MM_dd_zh_DateTimeFormat.get();
        } else if (formatStr.equalsIgnoreCase(yyyy_MM_dd_HH_mm_ss_zh)) {
            return (DateFormat) yyyy_MM_dd_HH_mm_ss_zh_DateTimeFormat.get();

        } else if (formatStr.equalsIgnoreCase(yyyyMMddHHmmss)) {
            return (DateFormat) yyyyMMddHHmmss_DateTimeFormat.get();
        } else {
            return new SimpleDateFormat(formatStr);
        }
    }

    /**
     * 字符串转日期 字符串格式:"yyyy-MM-dd HH:mm:ss"的形式
     *
     * @param str
     * @return
     */
    public static Date strToDate(String str) {
        return strToDate(str, yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 字符串转日期
     *
     * @param str
     * @param style
     * @return
     */
    public static Date strToDate(String str, String style) {
        try {
            if (str == null || str.equals("")) {
                return null;
            }
            DateFormat sdf = getDateFormat(style);
            Date d = sdf.parse(str);
            return d;

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * long 转换Str
     *
     * @param date
     * @return
     */
    public static String dateToStr(long date, String style) {
        return dateToStr(new Date(date), style);
    }

    /**
     * long 转换Str
     *
     * @param date
     * @return
     */
    public static String dateToStr(long date) {
        return dateToStr(new Date(date), yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 将Date转换成字符串
     *
     * @param date
     * @param style
     * @return
     */
    public static String dateToStr(Date date, String style) {
        DateFormat df = getDateFormat(style);
        return df.format(date);
    }

    /**
     * 获取当前日期yyyy-MM-dd的形式
     *
     * @return
     */
    public static String getCuryyyy_MM_dd() {
        return dateToStr(Calendar.getInstance().getTime(), yyyy_MM_dd);
    }

    /**
     * 获取当前日期yyyyMMdd的形式
     *
     * @return
     */

    public static String getCuryyyy() {
        return dateToStr(Calendar.getInstance().getTime(), yyyy);
    }
    public static String getCuryyyyMM() {
        return dateToStr(Calendar.getInstance().getTime(), yyyyMM);
    }

    public static String getCuryyyyMMdd() {
        if(!StringUtils.isEmpty(currentDate)){
            return currentDate;
        }
        return dateToStr(Calendar.getInstance().getTime(), yyyyMMdd);
    }

    public static int getCuryyyyMMddInteger() {
        if(!StringUtils.isEmpty(currentDate)){
            return Integer.parseInt(currentDate);
        }
        return Integer.parseInt(dateToStr(Calendar.getInstance().getTime(), yyyyMMdd));
    }

    public static long getCurMMDDHHmmss() {
        return Long.parseLong(dateToStr(Calendar.getInstance().getTime(), yyyyMMddHHmmss));
    }

    /**
     * 获取当前日期yyyy年MM月dd日的形式
     *
     * @return
     */
    public static String getCuryyyyMMddzh() {
        return dateToStr(new Date(), yyyy_MM_dd_zh);
    }

    /**
     * 获取当前日期时间yyyy-MM-dd HH:mm:ss的形式
     *
     * @return
     */
    public static String getCurDateTime() {
        return dateToStr(new Date(), yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 获取当前日期时间yyyy年MM月dd日HH时mm分ss秒的形式
     *
     * @return
     */
    public static String getCurDateTimezh() {
        return dateToStr(new Date(), yyyy_MM_dd_HH_mm_ss_zh);
    }

    public static Date getInternalDateByYear(Date d, int years) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        now.add(Calendar.YEAR, years);
        return now.getTime();
    }

    public static Date getInternalDateBySec(Date d, int sec) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        now.add(Calendar.SECOND, sec);
        return now.getTime();
    }

    public static Date getInternalDateByMin(Date d, int min) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        now.add(Calendar.MINUTE, min);
        return now.getTime();
    }

    public static Date getInternalDateByHour(Date d, int hours) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        now.add(Calendar.HOUR_OF_DAY, hours);
        return now.getTime();
    }

    /**
     * 将 时间 fromStr 从fromStyle格式转换到toStyle的格式
     *
     * @param fromStr
     * @param fromStyle
     * @param toStyle
     * @return
     */
    public static String strToStr(String fromStr, String fromStyle,
                                  String toStyle) {
        Date d = strToDate(fromStr, fromStyle);
        return dateToStr(d, toStyle);
    }

    /**
     * 比较两个"yyyy-MM-dd HH:mm:ss"格式的日期，之间相差多少毫秒,time2-time1
     *
     * @param time1
     * @param time2
     * @return
     */
    public static long compareDate(String time1, String time2) {
        Date d1 = strToDate(time1);
        Date d2 = strToDate(time2);
        return d2.getTime() - d1.getTime();
    }

    /**
     * 比较两个"yyyy-MM-dd HH:mm:ss"格式的日期，之间相差多少毫秒,time2-time1
     *
     * @param time1
     * @param time2
     * @return
     */
    public static long compareDate(Date time1, Date time2) {
        return time1.getTime() - time2.getTime();
    }

    /**
     * 获取Date中的分钟
     *
     * @param d
     * @return
     */
    public static int getMin(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.MINUTE);
    }

    /**
     * 获取Date中的小时(24小时)
     *
     * @param d
     * @return
     */
    public static int getHour(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取昨日日期
     * @return 昨日日期
     */
    public static Integer getYesterdayDate(){
        Calendar cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE,   -1);
        String yesterday = new SimpleDateFormat( "yyyyMMdd").format(cal.getTime());
        return Integer.valueOf(yesterday);
    }

    /**
     * 获取Date中的秒
     *
     * @param d
     * @return
     */
    public static int getSecond(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.SECOND);
    }

    /**
     * 获取Date中的毫秒
     *
     * @param d
     * @return
     */
    public static int getMilliSecond(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.MILLISECOND);
    }

    /**
     * 获取xxxx-xx-xx的日
     *
     * @param d
     * @return
     */
    public static int getDay(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取月份，1-12月
     *
     * @param d
     * @return
     */
    public static int getMonth(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取19xx,20xx形式的年
     *
     * @param d
     * @return
     */
    public static int getYear(Date d) {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        now.setTime(d);
        return now.get(Calendar.YEAR);
    }

    /**
     * 传入数字型日期 获取年份
     * 获取19xx,20xx形式的年
     * <AUTHOR>
     * @param yyyyMMdd
     * @return
     */
    public static int getYear(Integer yyyyMMdd) {
        return yyyyMMdd/10000;
    }


    /**
     * 传入数字型日期 获取年月份
     * 获取19xx,20xx形式的年
     * <AUTHOR>
     * @param yyyyMMdd
     * @return
     */
    public static int getYearAndMonth(Integer yyyyMMdd){return yyyyMMdd/100;}

    /**
     * 传入数字型日期 获取当月第一天的数字日期
     * @param yyyyMMdd
     * @return
     */
    public static int getMonthFirstDay(Integer yyyyMMdd){return getYearAndMonth(yyyyMMdd)*100+1;}

    /**
     * 获取当前日期所在周 周几的日期
     * @param date 当前日期
     * @param offset 日期偏移量,传入值范围[1,7]
     * 1(周一),2(周二),3(周三),4(周四),5(周五),6(周六),7(周天)
     * 不在这个范围的偏移量默认返回周日
     * @return
     */
    public static Date getWeekIndexDay(Date date,Integer offset){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            // 如果入参的日期是周日，取上周一的日期
            cal.add(Calendar.WEEK_OF_YEAR, -1);
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        }else {
            // 如果入参的日期不是周日，取本周一的日期
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); //获取本周一的日期
        }
        if(offset >=1 && offset <= 7){
            cal.add(Calendar.DATE,offset-1);
        }else{
            cal.add(Calendar.DATE,6);
        }
        return cal.getTime();
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取周一日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getWeekFirstDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date weekFirstDate = getWeekIndexDay(date, 1);
        DateFormat df = getDateFormat(style);
        return df.format(weekFirstDate);
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取周日日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getWeekEndDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date weekEndDate = getWeekIndexDay(date, 7);
        DateFormat df = getDateFormat(style);
        return df.format(weekEndDate);
    }

    /**
     * 获取当前日期所在月 第几日的日期
     * @param date 当前日期
     * @param offset 日期偏移量,传入值范围[1,28|29|30|31] 最大值范围 根据当月实际有多少天决定
     * 不在这个范围的偏移量默认返回月末日期
     * @return
     */
    public static Date getMonthIndexDay(Date date,Integer offset){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        Integer maxOffest = cal.getActualMaximum(Calendar.DATE);
        cal.set(Calendar.DAY_OF_MONTH,1);
        if(offset >=1 && offset <= maxOffest){
            cal.add(Calendar.DATE,offset-1);
        }else {
            cal.add(Calendar.DATE,maxOffest-1);
        }
        return cal.getTime();
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取月首日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getMonthFirstDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date monthFirstDate = getMonthIndexDay(date, 1);
        DateFormat df = getDateFormat(style);
        return df.format(monthFirstDate);
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取月末日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getMonthEndDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date monthEndDate = getMonthIndexDay(date, 32);
        DateFormat df = getDateFormat(style);
        return df.format(monthEndDate);
    }

    /**
     * 获取当前日期所在季度 第几日的日期
     * @param date 当前日期
     * @param offset 日期偏移量,传入值范围[1,90|91|92] 最大值范围 根据当季度实际有多少天决定
     * 不在这个范围的偏移量默认返回月末日期
     * @return
     */
    public static Date getSeasonIndexDay(Date date,Integer offset){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        Integer season = getSeason(date);
        Integer maxOffest = getSeasonDays(season,isLeapYear(date));
        switch (season){
            case 1:
                cal.set(Calendar.MONTH,Calendar.JANUARY);
                break;
            case 2:
                cal.set(Calendar.MONTH,Calendar.APRIL);
                break;
            case 3:
                cal.set(Calendar.MONTH,Calendar.JULY);
                break;
            case 4:
                cal.set(Calendar.MONTH,Calendar.OCTOBER);
                break;
        }
        cal.set(Calendar.DAY_OF_MONTH,1);
        if(offset >=1 && offset <= maxOffest){
            cal.add(Calendar.DATE,offset-1);
        }else {
            cal.add(Calendar.DATE,maxOffest-1);
        }
        return cal.getTime();
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取季度首日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getSeasonFirstDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date seasonFirstDate = getSeasonIndexDay(date, 1);
        DateFormat df = getDateFormat(style);
        return df.format(seasonFirstDate);
    }
    /**
     * 传入日期 和 时间格式表达式
     * 获取季度末日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getSeasonEndDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date seasonEndDate = getSeasonIndexDay(date, 95);
        DateFormat df = getDateFormat(style);
        return df.format(seasonEndDate);
    }

    /**
     * 获取当前日期所在年 第几日的日期
     * @param date 当前日期
     * @param offset 日期偏移量,传入值范围[1,365|366] 最大值范围 根据当月实际有多少天决定
     * 不在这个范围的偏移量默认返回月末日期
     * @return
     */
    public static Date getYearIndexDay(Date date,Integer offset){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        Integer maxOffest = cal.getActualMaximum(Calendar.DAY_OF_YEAR);
        cal.set(Calendar.DAY_OF_YEAR,1);
        if(offset >=1 && offset <= maxOffest){
            cal.add(Calendar.DATE,offset-1);
        }else {
            cal.add(Calendar.DATE,maxOffest-1);
        }
        return cal.getTime();
    }

    /**
     * 传入日期 和 时间格式表达式
     * 获取年度首日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getYearFirstDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date yearFirstDate = getYearIndexDay(date, 1);
        DateFormat df = getDateFormat(style);
        return df.format(yearFirstDate);
    }
    /**
     * 传入日期 和 时间格式表达式
     * 获取年度末日期的字符串(根据传入的时间格式表达式进行转换)
     * @param dateStr
     * @param style
     * @return
     */
    public static String getYearEndDay(String dateStr, String style){
        Date date = strToDate(dateStr, style);
        Date yearEndDate = getYearIndexDay(date, 367);
        DateFormat df = getDateFormat(style);
        return df.format(yearEndDate);
    }

    public static Long getDiffDay(Integer startDay,Integer endDay){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        long checkday=0l;
        try {
            checkday = (formatter.parse(endDay+"").getTime()- formatter.parse(startDay+"").getTime())/(1000*24*60*60);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return checkday;
    }


    /**
     * 根据传入的日期 获取这个季度有多少天
     * @param date
     * @return
     */
    public static int getSeasonDays(Date date){
        Integer season = getSeason(date);
        return getSeasonDays(season,isLeapYear(date));
    }

    /**
     * 获取这个季度有多少天
     * @param season 季度序列值
     * @param isLeapYear 是否是闰年
     * @return
     */
    public static int getSeasonDays(int season,boolean isLeapYear){
        int result = 0;
        switch (season){
            case 1:
                if(isLeapYear){
                    result = 91;
                }else {
                    result = 90;
                }
                break;
            case 2:result=91;break;
            case 3:result=92;break;
            case 4:result=92;break;
            default:
        }
        return result;
    }

    /**
     * 根据日期 获取季度
     * @param date
     * @return
     */
    public static int getSeason(Date date){
        int season = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        switch (month){
            case Calendar.JANUARY:
            case Calendar.FEBRUARY:
            case Calendar.MARCH:
                season = 1;
                break;
            case Calendar.APRIL:
            case Calendar.MAY:
            case Calendar.JUNE:
                season = 2;
                break;
            case Calendar.JULY:
            case Calendar.AUGUST:
            case Calendar.SEPTEMBER:
                season = 3;
                break;
            case Calendar.OCTOBER:
            case Calendar.NOVEMBER:
            case Calendar.DECEMBER:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }

    /**
     * 判断今年是不是闰年 是返回true 不是返回false
     * @param date
     * @return
     */
    public static boolean isLeapYear(Date date){
        int yyyy = getYear(date);
        if(yyyy % 4 == 0){
            return true;
        }else {
            return false;
        }
    }

    public static int getYesterday(Integer yyyyMMdd){
        Date date = strToDate(yyyyMMdd+"",DateUtil.yyyyMMdd);
        long yesterday = date.getTime() - 86400000;
        Date temp = new Date(yesterday);
        return  Integer.parseInt(dateToStr(temp,DateUtil.yyyyMMdd));
    }

    public static int getNextDay(Integer yyyyMMdd){
        Date date = strToDate(yyyyMMdd+"",DateUtil.yyyyMMdd);
        long yesterday = date.getTime() + 86400000;
        Date temp = new Date(yesterday);
        return  Integer.parseInt(dateToStr(temp,DateUtil.yyyyMMdd));
    }

    public static int getAfterOffestDay(Integer yyyyMMdd,Integer offest){
        Date date = strToDate(yyyyMMdd+"",DateUtil.yyyyMMdd);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR,offest);
//        long yesterday = date.getTime() + 86400000 * offest;
//        Date temp = new Date(yesterday);
        Date temp = calendar.getTime();
        return  Integer.parseInt(dateToStr(temp,DateUtil.yyyyMMdd));
    }

    public static int getBeforeOffestDay(Integer yyyyMMdd,Integer offest){
        Date date = strToDate(yyyyMMdd + "", DateUtil.yyyyMMdd);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR,0-offest);
//        long yesterday = date.getTime() - 86400000 * offest;
//        Date temp = new Date(yesterday);
        Date temp = calendar.getTime();
        return  Integer.parseInt(dateToStr(temp,DateUtil.yyyyMMdd));
    }

    public static int getLastMonthFirstDay(Integer yyyyMMdd){
        Integer temp = getYearAndMonth(yyyyMMdd);
        if(temp%100 == 1){
            return (temp-100+11)*100+1;
        }else{
            return (temp-1)*100+1;
        }
    }


    /**
     * 得到d 的年份+月份,如200505
     *
     * @return
     */
    public static String getYearMonthOfDate(Date d) {
        return dateToStr(d, yyyyMM);
    }

    /**
     * 得到上个月的年份+月份,如200505
     *
     * @return
     */
    public static String getYearMonthOfLastMonth() {

        return dateToStr(new Date(addMonth(new Date().getTime(), -1)), yyyyMM);
    }

    /**
     * 得到当前日期的年和月如200509
     *
     * @return String
     */
    public static String getCurYearMonth() {
        Calendar now = Calendar.getInstance(TimeZone.getDefault());
        String DATE_FORMAT = "yyyyMM";
        SimpleDateFormat sdf = new SimpleDateFormat(
                DATE_FORMAT);
        sdf.setTimeZone(TimeZone.getDefault());
        return (sdf.format(now.getTime()));
    }

    /**
     * 获得系统当前月份的天数
     *
     * @return
     */
    public static int getCurentMonthDays() {
        Date date = Calendar.getInstance().getTime();
        return getMonthDay(date);
    }

    /**
     * 获得指定日期月份的天数
     *
     * @return
     */
    public static int getMonthDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getActualMaximum(Calendar.DAY_OF_MONTH);

    }


    /**
     * 在传入时间基础上加一定天数
     *
     * @param oldTime long
     * @param day     int
     * @return long
     */
    public static long addDay(final long oldTime, final int day) {

        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(oldTime);

        return addDay(c, day);
    }

    /**
     * 在传入时间基础上加一定天数
     *
     * @param oldTime Calendar
     * @param day     int
     * @return long
     */
    public static long addDay(final Calendar oldTime, final int day) {
        int year, month, date, hour, minute, second;
        year = oldTime.get(Calendar.YEAR);
        month = oldTime.get(Calendar.MONTH);
        date = oldTime.get(Calendar.DATE);
        hour = oldTime.get(Calendar.HOUR_OF_DAY);
        minute = oldTime.get(Calendar.MINUTE);
        second = oldTime.get(Calendar.SECOND);
        final Calendar cal = new GregorianCalendar(year, month, date + day,
                hour, minute, second);
        return cal.getTime().getTime();
    }

    public static String addDay(String str,String style,int day){
        return DateUtil.dateToStr(DateUtil.addDay(DateUtil.strToDate(str,style).getTime(),day),style);
    }
    public static String addMonth(String str,String style,int months){
        return DateUtil.dateToStr(DateUtil.addMonth(DateUtil.strToDate(str,style).getTime(),months),style);
    }
    /**
     * 在传入时间基础上加一定月份数
     *
     * @param oldTime long
     * @param months  int
     * @return long
     */
    public static long addMonth(final long oldTime, final int months) {
        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(oldTime);
        return addMonth(c, months);
    }


    /**
     * 获取当天零时时间戳
     *
     * @return
     */
    public static long getCurDayStarttime() {
        return strToDate(getCuryyyy_MM_dd() + " 00:00:00").getTime();
    }


    /**
     * 在传入时间基础上加一定年数
     *
     * @param oldTime long
     * @param day     int
     * @return long
     */
    public static long addYear(final long oldTime, final int day) {

        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(oldTime);

        return addYear(c, day);
    }

    /**
     * 在传入时间基础上加一定周数
     *
     * @param oldTime Calendar
     * @param weeks   int
     * @return long
     */
    public static long addWeek(final Calendar oldTime, final int weeks) {
        oldTime.add(Calendar.WEEK_OF_YEAR, weeks);
        return oldTime.getTime().getTime();
    }

    /**
     * 在传入时间基础上加一定月数
     *
     * @param oldTime Calendar
     * @param months  int
     * @return long
     */
    public static long addMonth(final Calendar oldTime, final int months) {
        oldTime.add(Calendar.MONTH, months);
        return oldTime.getTime().getTime();
    }

    /**
     * 在传入时间基础上加一定年数
     *
     * @param oldTime Calendar
     * @param years   int
     * @return long
     */
    public static long addYear(final Calendar oldTime, final int years) {
        oldTime.add(Calendar.YEAR, years);
        return oldTime.getTime().getTime();
    }


    //周期，日期，周期数
    public static DatePair getDatePairByUnit(String dateUnit, String date, int num) {
        // strTodate
        Date d = DateUtil.strToDate(date, DateUtil.yyyyMMdd);
        //dateToCalendar
        Calendar cal = Calendar.getInstance();
        if (d != null) {
            cal.setTime(d);
        }
        switch (dateUnit) {
            case DATE_UNIT_W:
                //TODO 周日做了特殊处理
                if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                    cal.add(Calendar.DATE, -1);
                }
                if (num == 0) {
                    return getDatePairByWeek(cal);
                } else if (num < 0) {
                    DatePair p1 = getDatePairByWeek(cal);
                    cal.setTime(new Date(addWeek(cal, num)));
                    DatePair p2 = getDatePairByWeek(cal);
                    return new DatePair(p2.getStartDate(), p1.getEndDate());
                } else {
                    return null;
                }

            case DATE_UNIT_M:
                if (num == 0) {
                    return getDatePairByMonth(cal);
                } else if (num < 0) {
                    DatePair p1 = getDatePairByMonth(cal);
                    cal.setTime(new Date(addMonth(cal, num)));
                    DatePair p2 = getDatePairByMonth(cal);
                    return new DatePair(p2.getStartDate(), p1.getEndDate());
                } else {
                    return null;
                }

            case DATE_UNIT_Y:
                if (num == 0) {
                    return getDatePairByYear(cal);
                } else if (num < 0) {
                    DatePair p1 = getDatePairByYear(cal);
                    cal.setTime(new Date(addYear(cal, num)));
                    DatePair p2 = getDatePairByYear(cal);
                    return new DatePair(p2.getStartDate(), p1.getEndDate());
                } else {
                    return null;
                }

        }
        return null;
    }


    private static DatePair getDatePairByWeek(Calendar cal) {
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        String startDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
        String endDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        return new DatePair(startDate, endDate);
    }

    private static DatePair getDatePairByYear(Calendar cal) {
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_YEAR, cal.getActualMinimum(Calendar.DAY_OF_YEAR));
        String startDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        cal.set(Calendar.DAY_OF_YEAR, cal.getActualMaximum(Calendar.DAY_OF_YEAR));
        String endDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        return new DatePair(startDate, endDate);
    }

    private static DatePair getDatePairByMonth(Calendar cal) {
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        String startDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        String endDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        return new DatePair(startDate, endDate);
    }

    private static DatePair getDatePairBySeason(Calendar cal) {
        int currentMonth = cal.get(Calendar.MONTH) + 1;
        if (currentMonth >= 1 && currentMonth <= 3)
            cal.set(Calendar.MONTH, 0);
        else if (currentMonth >= 4 && currentMonth <= 6)
            cal.set(Calendar.MONTH, 3);
        else if (currentMonth >= 7 && currentMonth <= 9)
            cal.set(Calendar.MONTH, 6);
        else if (currentMonth >= 10 && currentMonth <= 12)
            cal.set(Calendar.MONTH, 9);
        cal.set(Calendar.DATE, 1);
        String startDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        if (currentMonth >= 1 && currentMonth <= 3) {
            cal.set(Calendar.MONTH, 2);
            cal.set(Calendar.DATE, 31);
        } else if (currentMonth >= 4 && currentMonth <= 6) {
            cal.set(Calendar.MONTH, 5);
            cal.set(Calendar.DATE, 30);
        } else if (currentMonth >= 7 && currentMonth <= 9) {
            cal.set(Calendar.MONTH, 8);
            cal.set(Calendar.DATE, 30);
        } else if (currentMonth >= 10 && currentMonth <= 12) {
            cal.set(Calendar.MONTH, 11);
            cal.set(Calendar.DATE, 31);
        }
        String endDate = dateToStr(cal.getTime(), DateUtil.yyyyMMdd);
        return new DatePair(startDate, endDate);
    }

    /**
     * 比较两个日期的月份间隔 yyyymmdd
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getMonthInterval(int startDate,int endDate){
        int startYear = startDate / 10000;
        int startMonth = startDate / 100 % startYear;
        int endYear = endDate / 10000;
        int endMonth = endDate / 100 % endYear;
        return (endYear * 12 + endMonth) - (startYear * 12 + startMonth);
    }


    /**
     * 获取时分秒
     * @return
     * @throws ParseException
     */
    public static String getCurHHmmss(){
        if(!StringUtils.isEmpty(currentTime)){
            return currentTime;
        }
        String curDate = dateToStr(new Date(),"yyyyMMddHHmmss");
        return curDate.substring(8,curDate.length());
    }

    public static boolean isSameWeekWithToday(String day) {
        // 0.先把Date类型的对象转换Calendar类型的对象
        Date date = DateUtil.strToDate(day, DateUtil.yyyyMMdd);
        Calendar todayCal = Calendar.getInstance();
        Calendar dateCal = Calendar.getInstance();

        todayCal.setTime(new Date());
        dateCal.setTime(date);

        // 1.比较当前日期在年份中的周数是否相同
        if (todayCal.get(Calendar.WEEK_OF_YEAR) == dateCal.get(Calendar.WEEK_OF_YEAR)) {
            return true;
        } else {
            return false;
        }
    }
    public static boolean isSameMonth(String day1,String day2) {
        return day1.substring(0, 6).equals(day2.substring(0, 6));
    }

    public static void main(String[] args) {
        System.out.println("sunyuncai".substring(1,3));
    }

    /**
     * 返回两个日期的间隔天数 yyyymmdd
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getDayInterval(Integer startDate, Integer endDate){
        return getDayInterval(String.valueOf(startDate), String.valueOf(endDate));
    }

    /**
     * 返回两个日期的间隔天数 yyyymmdd
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static Integer getDayInterval(String startDateStr, String endDateStr){
        Date startDate = DateUtil.strToDate(startDateStr, yyyyMMdd);
        Date endDate = DateUtil.strToDate(endDateStr, yyyyMMdd);
        if (startDate == null || endDate == null) {
            return null;
        }
        long startTimestamp = startDate.getTime();
        long endTimestamp = endDate.getTime();
        return (int) Math.abs((endTimestamp - startTimestamp) / (1000L * 3600L * 24L));
    }
}
