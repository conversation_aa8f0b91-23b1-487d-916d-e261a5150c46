package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.service.TMatchService;
import com.eastmoney.common.entity.BusinessTaskStatusDO;
import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.VO.ExchangeRateInfoVO;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.sysEnum.TaskStatusEnum;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.BusinessTaskStatusCacheService;
import com.eastmoney.service.cache.ExchangeRateCache;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.TProfitService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 做T分析处理handle
 * @date 2024/4/29 17:20
 */
@Service
public class TOperationAnalyzeHandler {

    private static final String T_PROFIT_DAY_CAL_HANDLE = "tProfitCalHandle";

    @Autowired
    private TradeDateDao tradeDateDao;

    @Autowired
    private AssetNewService assetNewService;

    @Resource(name = "tProfitService")
    private TProfitService tProfitService;

    @Resource(name = "tProfitRealTimeService")
    private TProfitService tProfitRealTimeService;

    @Autowired
    private BusinessTaskStatusCacheService businessTaskStatusCacheService;

    @Autowired
    private TMatchService tMatchService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ExchangeRateCache exchangeRateCache;

    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    /**
     * 获取做T收益 按照做T股票汇总
     */
    public TProfitBO getSecTProfitRankList(Map<String, Object> dealParams) {
        // 查询个股T收益
        List<tProfitRank> tProfitRankList = tProfitService.getSecTProfitRankList(dealParams);
        TProfitBO tProfitBO = new TProfitBO();
        BusinessTaskStatusDO tProfitBusinessTaskStatus = businessTaskStatusCacheService.getBusinessTaskStatus(dealParams, T_PROFIT_DAY_CAL_HANDLE);
        if (tProfitBusinessTaskStatus == null) {
            tProfitBO.setEndDate(String.valueOf(assetNewService.getAssetInfo(dealParams).getBizDate()));
        } else {
            tProfitBO.setEndDate(String.valueOf(tProfitBusinessTaskStatus.getBizDate()));
        }
        if (CollectionUtils.isNotEmpty(tProfitRankList)) {
            tProfitBO.setTProfitStockList(tProfitRankList);
        }
        return tProfitBO;
    }


    public List<TProfitDetail> getSecTDetailList(Map<String, Object> params) {
        // 做T收益计算清算状态
        List<TSecProfitDayDO> tSecProfitDayList;
        List<Match> tMatchList;
        // 入参业务日期大于等于最近交易日且未清算完成 才需要去查柜台
        //适配北交所代码转换  股票维度的T操作需要找出 新老代码所有的操作
        bseCodeAlterService.getCodeAlterParams(params);
        if (isNeedCalRealTimeFlag(params)) {
            // 做T买 bsFlag
            tMatchList = tProfitRealTimeService.getTMatchList(params);
            tSecProfitDayList = tProfitRealTimeService.getTSecProfitDayList(params);
        } else {
            tMatchList = tMatchService.getTMatchList(params);
            tSecProfitDayList = tProfitService.getTSecProfitDayList(params);
        }
        if (CollectionUtils.isEmpty(tMatchList)) {
            return new ArrayList<>();
        }
        if (tSecProfitDayList.size() > CommConstants.MAX_PAGE_SIZE) {
            tSecProfitDayList = tSecProfitDayList.subList(0, CommConstants.MAX_PAGE_SIZE - 1);
        }
        List<TProfitDetail> tProfitDetailList = TProfitDetail.buildList(tMatchList, tSecProfitDayList);
        //代码转换 830 B -> 920 B  用于前端进行行情跳转
        //代码转换 830 6 -> 920 B  用于前端进行行情跳转
        tProfitDetailList.forEach(r -> r.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(r.getStkCode(), r.getMarket())));
        return tProfitDetailList;
    }

    public TProfitBO getTProfitSection(Map<String, Object> dealParams) {
        String stkCode = CommonUtil.convert(dealParams, "stkCode", String.class);
        String unit = CommonUtil.convert(dealParams, "unit", String.class);
        boolean needCalRealTimeFlag = isNeedCalRealTimeFlag(dealParams);
        TProfitBO offLineTProfitBO = getOffLineTProfitBO(dealParams, stkCode, unit, needCalRealTimeFlag);
        // 库里没有初始化一个离线结果
        if (offLineTProfitBO == null) {
            offLineTProfitBO = new TProfitBO();
            offLineTProfitBO.setTotalTProfit(0.0);
            offLineTProfitBO.setTotalTTimes(0L);
            offLineTProfitBO.setTotalTSuccess(0L);
        }
        offLineTProfitBO.setEndDate(getUpdateTime(dealParams));
        // 做T最新清算状态
        if (!needCalRealTimeFlag) {
            return offLineTProfitBO;
        }
        // 计算实时
        //适配北交所代码转换  股票维度的T操作需要找出 新老代码所有的操作
        bseCodeAlterService.getCodeAlterParams(dealParams);
        TProfitBO realTimeTProfitBO = tProfitRealTimeService.getTProfitSection(dealParams);
        // 无实时直接返回离线结果
        if (realTimeTProfitBO == null) {
            return offLineTProfitBO;
        }
        return TProfitBO.mergeTProfitBO(offLineTProfitBO, realTimeTProfitBO);
    }

    private TProfitBO getOffLineTProfitBO(Map<String, Object> dealParams, String stkCode, String unit, boolean needCalRealTimeFlag) {
        // 需要实时计算 当日区间离线做t汇总为空
        if (needCalRealTimeFlag && DateUnitEnum.DAY.getValue().equals(unit)) {
            return null;
        }
        // 传标的则查个股做t操作汇总
        if (StringUtils.isNotEmpty(stkCode)) {
            //适配北交所代码转换  股票维度的T操作需要找出 新老代码所有的操作
            bseCodeAlterService.getCodeAlterParams(dealParams);
            return tProfitService.getSecTProfitSection(dealParams);
        }
        // 账户级别unit区间作缓存
        if (StringUtils.isNotEmpty(unit)) {
            return tProfitService.getTProfitSection(dealParams);
        }
        // 自定义区间不缓存
        return tProfitService.getTProfitCustomizeSection(dealParams);
    }

    /**
     * PC 端查询每日做T收益明细
     */
    public List<TSecProfitDayDO> getTProfitDayListPC(Map<String, Object> dealParams) {
        if (!isNeedCalRealTimeFlag(dealParams)) {
            return tProfitService.getTSecProfitDayListPC(dealParams);
        }
        List<TSecProfitDayDO> realTimeTSecProfitDayList = tProfitRealTimeService.getTSecProfitDayList(dealParams);
        // 无实时数据
        if (CollectionUtils.isEmpty(realTimeTSecProfitDayList)) {
            return tProfitService.getTSecProfitDayListPC(dealParams);
        }
        // 分页处理
        Integer pageSize = CommonUtil.convert(dealParams.get(CommConstants.PAGE_SIZE), Integer.class);
        Integer pageNo = CommonUtil.convert(dealParams.get(CommConstants.PAGE_NO), Integer.class);
        List<List<TSecProfitDayDO>> subLists = Lists.partition(realTimeTSecProfitDayList, pageSize);
        if (pageNo < subLists.size()) {
            return subLists.get(pageNo - 1);
        }
        if (pageNo > subLists.size()) {
            Integer startNum = (pageNo - 1) * pageSize - realTimeTSecProfitDayList.size();
            dealParams.put(CommConstants.START_NUM, startNum);
            return tProfitService.getTSecProfitDayListPC(dealParams);
        }
        pageSize = pageSize - subLists.get(pageNo - 1).size();
        List<TSecProfitDayDO> resultList = subLists.get(pageNo - 1);
        if (pageSize != 0) {
            dealParams.put(CommConstants.PAGE_SIZE, pageSize);
            dealParams.put(CommConstants.START_NUM, 0);
            resultList.addAll(tProfitService.getTSecProfitDayListPC(dealParams));
        }
        return resultList;
    }

    /**
     * PC 端查询每日做T收益明细 个股级别
     */
    public List<TSecProfitDayDO> getSecTProfitDayListPC(Map<String, Object> dealParams) {
        return tProfitService.getTSecProfitDayListPC(dealParams);
    }

    /**
     * APP 端查询每日做T收益
     */
    public List<TProfitDayDO> getTProfitDayList(Map<String, Object> dealParams) {
        bseCodeAlterService.getCodeAlterParams(dealParams);
        // 通用入参检查并处理
        if (!isNeedCalRealTimeFlag(dealParams)) {
            // 不需要计算实时直接返回离线
            return tProfitService.getTProfitDayList(dealParams);
        }
        // 接口需要计算实时
        List<TProfitDayDO> realTimeTProfitDayList = tProfitRealTimeService.getTProfitDayList(dealParams);
        if (CollectionUtils.isEmpty(realTimeTProfitDayList)) {
            // 实时为空也直接返回离线
            return tProfitService.getTProfitDayList(dealParams);
        }
        // 处理分页
        Integer pageNo = CommonUtil.convert(dealParams.get(CommConstants.PAGE_NO), Integer.class);
        if (pageNo > 1) {
            // 实时固定一条 大于1页 只需要查离线
            Integer startNum = CommonUtil.convert(dealParams.get(CommConstants.START_NUM), Integer.class) - 1;
            dealParams.put(CommConstants.START_NUM, startNum);
            return tProfitService.getTProfitDayList(dealParams);
        }
        // pageNo=1只有一页时
        Integer pageSize = CommonUtil.convert(dealParams.get(CommConstants.PAGE_SIZE), Integer.class);
        if (pageSize == 1) {
            // 一页1条只返回实时
            return realTimeTProfitDayList;
        }
        // pageSize大于1条时离线参数处理
        dealParams.put(CommConstants.PAGE_SIZE, pageSize - 1);
        dealParams.put(CommConstants.START_NUM, 0);
        realTimeTProfitDayList.addAll(tProfitService.getTProfitDayList(dealParams));
        return realTimeTProfitDayList;
    }


    public List<Match> getSecTDetailListPC(Map<String, Object> params) {
        List<Match> resultList;
        bseCodeAlterService.getCodeAlterParams(params);
        if (isNeedCalRealTimeFlag(params)) {
            resultList = tProfitRealTimeService.getTMatchList(params);
        } else {
            resultList = tMatchService.getTMatchList(params);
        }
        if (CollectionUtils.isNotEmpty(resultList) && resultList.size() > CommConstants.MAX_PAGE_SIZE) {
            resultList = resultList.subList(0, CommConstants.MAX_PAGE_SIZE - 1);
        }
        return resultList;
    }

    public ExchangeRateInfoVO getExchangeRateInfo(Map<String, Object> params) {
        Integer curDate=DateUtil.getCuryyyyMMddInteger();
        Integer bizDate = CommonUtil.convert(params.get("bizDate"), Integer.class);
        if(bizDate<20150106){
            throw new RuntimeException("港股通汇率最大支持日期为2015-01-06日");
        }
        if(bizDate>curDate){
            throw new RuntimeException("最大查询日期不能超过当日");
        }
        ExchangeRateInfoVO exchangeRateInfoVO = exchangeRateCache.getExchangeRateInfoVO(params);
        return exchangeRateInfoVO;
    }


    public boolean isNeedCalRealTimeFlag(Map<String, Object> dealParams) {
        Integer bizDate = CommonUtil.convert(dealParams, "bizDate", Integer.class);
        if (bizDate == null) {
            Integer endDate = CommonUtil.convert(dealParams, "endDate", Integer.class);
            bizDate = tradeDateDao.getLatestTrdDateOfSpecifyDate(endDate);
        }
        BusinessTaskStatusDO tProfitCalTaskStatus = businessTaskStatusCacheService.getBusinessTaskStatus(dealParams, T_PROFIT_DAY_CAL_HANDLE);
        if (tProfitCalTaskStatus == null || bizDate > tProfitCalTaskStatus.getBizDate()) {
            return true;
        }
        if (bizDate < tProfitCalTaskStatus.getBizDate()) {
            return false;
        }
        // 当 bizDate 等于 businessTaskStatusDO.getBizDate() 时
        return !TaskStatusEnum.STATUS_SUCCESS.getValue().equals(tProfitCalTaskStatus.getExecuteStatus());
    }

    public String getUpdateTime(Map<String, Object> dealParams) {
        // 实时已经算完 或者 在开盘前
        if (!isNeedCalRealTimeFlag(dealParams) || !commonService.isAfterMarketOpen()) {
            return String.valueOf(businessTaskStatusCacheService.getBusinessTaskStatus(dealParams, T_PROFIT_DAY_CAL_HANDLE)
                    .getEuTime().getTime());
        }
        // 盘中截止到交易日16:00
        Date closeDateTime = DateUtil.strToDate(tradeDateDao
                .getLatestTrdDateOfSpecifyDate(DateUtil.getCuryyyyMMddInteger()) + "160000", DateUtil.yyyyMMddHHmmss);
        Date now = new Date();
        return DateUtil.compareDate(now, closeDateTime) < 0 ?
                String.valueOf(now.getTime()) : String.valueOf(closeDateTime.getTime());
    }

}