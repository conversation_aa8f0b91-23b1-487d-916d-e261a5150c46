package com.eastmoney.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;

/**
 * Created by robin on 2016/9/7.
 * 端口检查和占用工具
 * <AUTHOR>
 */
public class PortUtil {

    private static Logger LOG = LoggerFactory.getLogger(PortUtil.class);

    private static ServerSocket ss;
    /**
     * 端口是否被占用
     * @param port
     * @return
     */
    public static boolean isUsed(Integer port){
        boolean result = false;
        try {
            ss = new ServerSocket(port);
            result = false;
        } catch (IOException e) {
            LOG.error("port [{}] is used",port,e);
            result = true;
        }finally {
            if(ss != null){
                try {
                    ss.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public static void usePort(Integer port){
        new Thread(new Server(port)).start();
    }

    private static class Server implements  Runnable{
        private ServerSocket ss;
        private Integer port;
        public Server(int port){
            this.port = port;
        }

        @Override
        public void run() {
            try{
                ss = new ServerSocket(this.port);
                while (true){
                    Thread.sleep(1000 * 3600 * 24);
                }
            }catch (Exception e){
                LOG.error("port[{}] has been occupied...",port,e);
            }finally {
                try {
                    ss.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }
}
