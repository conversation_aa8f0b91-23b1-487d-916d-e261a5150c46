package com.eastmoney.accessor.annotation;

import java.lang.annotation.*;

/**
 * @program: SqlServerTarget
 * @description:
 * @author: huang<PERSON><PERSON><PERSON>@eastmoney.com
 * @create: 2022/11/1
 */
@Target({ElementType.TYPE,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface SqlServerTarget {

    /**
     * run / his
     * @return
     */
    String value() default "run";
}
