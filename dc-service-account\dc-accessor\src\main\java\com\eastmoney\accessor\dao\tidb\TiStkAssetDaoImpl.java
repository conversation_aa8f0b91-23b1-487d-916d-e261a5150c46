package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.StkAssetDao;
import com.eastmoney.accessor.mapper.tidb.TiStkAssetMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.StkAsset;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/8
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("stkAssetDao")
public class TiStkAssetDaoImpl extends BaseDao<TiStkAssetMapper, StkAsset, Integer> implements StkAssetDao {
    @Override
    public List<StkAsset> getRealTimePosition(Map<String, Object> params) {
        return mapper.getRealTimePosition(params);
    }

    @Override
    public List<StkAsset> selectOne(Map<String, Object> params) {
        return mapper.selectOne(params);
    }
}
