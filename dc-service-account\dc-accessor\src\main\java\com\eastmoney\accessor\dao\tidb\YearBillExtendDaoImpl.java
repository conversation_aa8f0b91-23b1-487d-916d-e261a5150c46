package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiYearBillExtendMapper;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.ProfitDay;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("yearBillExtendDao")
public class YearBillExtendDaoImpl extends BaseDao<TiYearBillExtendMapper, YearBillExtend, Long> implements YearBillExtendDao {

    @Override
    public ProfitDay getBestProfitDay(Map<String, Object> params) {
        return mapper.getBestDay(params);
    }

}
