<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiHgtTradeDateMapper">
<!-- 开启本mapper的mybatis二级缓存
<cache />
-->
    <!-- 判断今日是否为交易日,(根据沪市日期匹配)  -->
    <select id="todayIsMarket" useCache="false" resultType="as_hgtTradeDay">
        <if test="bizdate != null">
            SELECT PPDATE,TDATE, TEXCH, TYPECODE, TRADEMARKETCODE,
            AUCTIONOPAM, AUCTIONCOAM,OPENTRADEAM, CLOSETRADEAM, OPENTRADEPM,
            CLOSETRADEPM, AUCTIONOPPM, AUCTIONCOPM
            FROM ATCENTER.HGT_TRADE_DAY WHERE PPDATE = DATE_FORMAT(#{bizdate},'%Y%m%d') AND  TRADEMARKETCODE='069002004'
        </if>
        <if test="bizdate == null">
            SELECT PPDATE,TDATE, TEXCH, TYPECODE, TRADEMARKETCODE,
            AUCTIONOPAM, AUCTIONCOAM,OPENTRADEAM, CLOSETRADEAM, OPENTRADEPM,
            CLOSETRADEPM, AUCTIONOPPM, AUCTIONCOPM
            FROM ATCENTER.HGT_TRADE_DAY WHERE PPDATE = DATE_FORMAT(CURDATE(),'%Y%m%d') AND TRADEMARKETCODE='069002004'
        </if>
    </select>

    <select id="calHkProfitFlag" useCache="false"  resultType="integer">
        select count(1) from
        ( select tdate from atcenter.HGT_TRADE_DAY h
        <where>
        <if test="bizDate != null">
            h.TDATE <![CDATA[<]]> #{bizDate}
        </if>
        order by tdate desc limit 1
        </where>
        )a,
        ( select max(a.TDATE) tdate from atcenter.TRADE_DAY a
        join atcenter.HGT_TRADE_DAY b on
        a.TDATE = b.TDATE
        <where>
        a.MARKET = 'SH'
        <if test="bizDate != null">
            and a.TDATE <![CDATA[<]]> #{bizDate}
        </if>
        </where>
        ) b
        where a.tdate > b.tdate
    </select>
</mapper>