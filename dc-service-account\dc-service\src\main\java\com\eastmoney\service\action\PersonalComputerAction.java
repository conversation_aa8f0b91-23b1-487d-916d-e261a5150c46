package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.PersonalComputerHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * PC版账户分析数据
 * <AUTHOR>
 * @create 2018-03-26 13:31
 **/
@Action
@Component
@RequestMapping("")
public class PersonalComputerAction {

    @Autowired
    private PersonalComputerHandler personalComputerHandler;

    //仓位比例数据
    @FunCodeMapping("getPositionsRate")
    @RequestMapping("/getPositionsRate")
    public Map getPositionsRate(Map<String, Object> params){
        CommonUtil.checkParamNotNull(params,new String[]{"fundId"});
        return personalComputerHandler.getPositionsRate(params);
    }
}
