<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiLogAssetMapper">

    <select id="getStkTradeList" resultType="as_logAsset">
        SELECT  a.STKCODE,
        CASE MARKET
            WHEN  '5' THEN a.CLEARDATE
            WHEN  'S' THEN a.CLEARDATE
            ELSE a.BIZDATE
        END AS BIZDATE, a.matchprice,a.MATCHQTY,a.matchamt,a.market,a.matchTime, a.bsflag
        FROM ATCENTER.logasset a use index(idx_logasset_0502_f)
        where
            digestid in (220000,220003,220012,220023,220024,220027,220028,220029,220031,220032,
        220033,220034,220049,221049, 220036,220037,221003,220035,221023,220006,221033,221034,
        220100,220101,221001,221002,221035,221101,221102,221024,220188,220189,
        -- 20210315 港股通新增digestid
        220094,220095,220098,220099)
            AND a.FUNDID = #{fundId}
            and a.MARKET = #{market}
            and a.bsflag is not null and a.bsflag != '  '
            AND (
                (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                    OR
                (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
            <if test="oldStkCode == null">
                AND a.STKCODE = #{stkCode}
            </if>
            <if test="oldStkCode != null">
                AND a.STKCODE in (#{stkCode},#{oldStkCode})
            </if>
        order by a.BIZDATE ASC
    </select>

    <select id="getStkShiftList" resultType="as_logAsset">
        select a.FUNDID,a.digestid,a.STKCODE, CASE MARKET
            WHEN  '5' THEN a.CLEARDATE
            WHEN  'S' THEN a.CLEARDATE
            ELSE a.BIZDATE
            END AS BIZDATE,
        a.MARKET, a.stkEffect,a.market,a.bsflag,a.matchAmt,b.DIGESTNAME
        from ATCENTER.logasset a use index(idx_logasset_0502_f)
        inner join ATCENTER.B_CAL_DIGEST b on a.digestid = b.digestid and a.stktype = b.stktype
        where b.CALINDEX = 'positionShareChange' and useFlag = '1'
            and a.digestid not in ('220000','221001','220049','221049','220188','220189',
                    -- 20210315 港股通新增digestid
                    '220094','220095','220098','220099')
            and a.FUNDID = #{fundId} and a.MARKET = #{market}
            AND (
                (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                OR
                (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
            <if test="oldStkCode == null">
                AND a.STKCODE = #{stkCode}
            </if>
            <if test="oldStkCode != null">
                AND a.STKCODE in (#{stkCode},#{oldStkCode})
            </if>
        order by a.BIZDATE ASC
    </select>

    <select id="selectByCondition" resultType="as_logAsset">
        SELECT EID, BIZDATE, MONEYTYPE, SNO, STKCODE, SECUID, MATCHAMT, FUNDEFFECT, BSFLAG, MATCHPRICE, MATCHQTY,
        FEE_YHS, FEE_SXF, FEE_JYGF, FEE_GHF, FUNDID
        FROM ATCENTER.LogAsset use index (idx_logasset_0502_f)
        <where>
            <if test="bizDate != null">
                AND BIZDATE = #{bizDate}
            </if>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
        </where>
    </select>
    <sql id="Base_Column_List">
        EID,EITIME,EUTIME,RESEND_FLAG
        ,FUNDID,SERVERID,BIZDATE,MONEYTYPE,SNO
        ,STKCODE,SECUID,MATCHAMT,FUNDEFFECT,BSFLAG
        ,MATCHPRICE,MATCHQTY,FEE_YHS,FEE_SXF,FEE_JYGF,FEE_GHF
    </sql>

    <select id="getTradeShiftList" resultType="as_logAsset">
        SELECT  a.STKCODE,a.DIGESTID,
        CASE
            WHEN a.MARKET in('5','S') THEN a.CLEARDATE
            ELSE a.BIZDATE
        END AS BIZDATE,
        a.MARKET,
        CASE WHEN a.DIGESTID in (
        SELECT digestid FROM atcenter.b_cal_digest WHERE stktype = '-' and calindex = 'positionExpand' and useFlag = '1')
            THEN abs(a.FUNDEFFECT)
            ELSE a.MATCHAMT END AS MATCHAMT
        ,a.MATCHPRICE,a.MATCHQTY,a.MATCHTIME,a.ORDERTIME,b.DIGESTNAME,
        a.STKEFFECT,a.STKBAL, a.SNO,
        IFNULL(fee_sxf,0) + IFNULL(fee_yhs,0) + IFNULL(fee_ghf,0) + IFNULL(fee_qsf,0) +
        IFNULL(fee_jygf,0) + IFNULL(fee_jsf,0) + IFNULL(fee_zgf,0) +
        IFNULL(fee_qtf,0) + IFNULL(feefront,0) + IFNULL(fee_chjzf,0) AS FEETOTAL
        FROM ATCENTER.LOGASSET a use index(idx_logasset_0502_f)
        left join ATCENTER.B_CAL_DIGEST b on b.DIGESTID=a.DIGESTID and a.STKTYPE = b.STKTYPE
        left join atcenter.POSITION_PROFIT_BONUS_RELATION c
        on a.fundid = c.fundid
        and a.serverid = c.serverid and a.sno = c.sno and a.bizdate = c.bizdate
        where a.FUNDID = #{fundId}
        <if test="startDate != null and endDate != null">
            AND(
                (a.CLEARDATE BETWEEN #{startDate} and #{endDate})
                OR
                (
                c.startdate >= #{startDate} AND c.endDate <![CDATA[<=]]> #{endDate}
                AND c.DIGESTID IN (
            SELECT digestid FROM atcenter.b_cal_digest WHERE stktype = '-' and calindex = 'positionExpand' and useFlag = '1')
                )
            )
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND a.STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND a.STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            AND a.MARKET = #{market}
        </if>
        <if test="calIndexes != null">
            and b.CALINDEX IN
            <foreach item="item" index="index" collection="calIndexes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stkTypes != null">
            and  b.STKTYPE IN
            <foreach item="item" index="index" collection="stkTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND b.useFlag = '1'
        order by a.BIZDATE desc,a.SNO desc
        <if test="pageSize != null and startNum != null">
            limit #{startNum}, #{pageSize}
        </if>
    </select>


    <select id="getHoldTradeShiftList" resultType="com.eastmoney.common.entity.LogAsset">
        SELECT  a.STKCODE,a.DIGESTID,
        CASE
        WHEN a.MARKET in('5','S') THEN a.CLEARDATE
        ELSE a.BIZDATE
        END AS BIZDATE,
        a.MARKET,
        CASE WHEN a.DIGESTID in (
        SELECT digestid FROM atcenter.b_cal_digest WHERE stktype = '-' and calindex = 'positionExpand' and useFlag = '1'
        )
        THEN abs(a.FUNDEFFECT)
        ELSE a.MATCHAMT END AS MATCHAMT
        ,a.MATCHPRICE,a.MATCHQTY,a.MATCHTIME,a.ORDERTIME,b.DIGESTNAME,
        a.STKEFFECT,a.STKBAL, a.SNO,
        IFNULL(fee_sxf,0) + IFNULL(fee_yhs,0) + IFNULL(fee_ghf,0) + IFNULL(fee_qsf,0) +
        IFNULL(fee_jygf,0) + IFNULL(fee_jsf,0) + IFNULL(fee_zgf,0) +
        IFNULL(fee_qtf,0) + IFNULL(feefront,0) + IFNULL(fee_chjzf,0) AS FEETOTAL
        FROM ATCENTER.LOGASSET a use index(idx_logasset_0502_f)
        left join ATCENTER.B_CAL_DIGEST b on b.DIGESTID=a.DIGESTID and a.STKTYPE = b.STKTYPE
        where a.FUNDID = #{fundId}
        <if test="startDate != null and endDate != null">
             AND a.CLEARDATE BETWEEN #{startDate} and #{endDate}
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND a.STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND a.STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            AND a.MARKET = #{market}
        </if>
        <if test="calIndexes != null">
            and b.CALINDEX IN
            <foreach item="item" index="index" collection="calIndexes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stkTypes != null">
            and  b.STKTYPE IN
            <foreach item="item" index="index" collection="stkTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND b.useFlag = '1'
        order by a.BIZDATE desc,a.SNO desc
        <if test="pageSize != null and startNum != null">
            limit #{startNum}, #{pageSize}
        </if>
    </select>

    <select id="getOtherDetailList" resultType="com.eastmoney.common.entity.LogAsset">
        SELECT a.FUNDID,a.digestid,a.STKCODE,
        TRIM(a.STKNAME) STKNAME,a.bizDate,a.MARKET, a.STKEFFECT,a.FUNDEFFECT,a.bsflag,a.sno
        FROM ATCENTER.LOGASSET a use index (idx_logasset_0502_f)
        <where>
            a.MONEYTYPE = '0'
            AND a.BIZDATE BETWEEN #{startDate} and #{endDate}
            AND (
                a.digestid in (
                    SELECT DISTINCT DIGESTID
                    from ATCENTER.B_CAL_DIGEST
                    where  CALINDEX = 'assetOther' and USEFLAG=1
                )
                OR
                (
                    a.digestid in (
                        SELECT DISTINCT DIGESTID
                        from ATCENTER.B_CAL_DIGEST
                        where CALINDEX = 'assetShareIO'
                        and stktype in ('0','1','2','5','8','C','E','L','e','h','g','i','k','o','r','R','U','W','X','u')
                        and USEFLAG = 1 )
                    AND a.market in ('0', '1')
                )
                OR
                (
                    a.digestid in (
                        SELECT DISTINCT DIGESTID
                        from ATCENTER.B_CAL_DIGEST
                        where CALINDEX = 'assetShareIO'
                        and (stktype = '0' or stktype = 'J')
                        and USEFLAG = 1 )
                    AND a.market = '6'
                    )
                )
            <if test="fundId != null">
                AND a.FUNDID = #{fundId}
            </if>
        </where>
        <if test='sort != null'>
            ${sort}
        </if>
        <if test="pageSize != null and startNum != null">
            limit #{startNum}, #{pageSize}
        </if>
    </select>

    <select id="getBSTradeList" resultType="as_logAsset">
        select
            a.DIGESTID,a.MATCHAMT,a.MATCHPRICE,a.MATCHQTY,
            CASE MARKET
                WHEN  '5' THEN a.CLEARDATE
                WHEN  'S' THEN a.CLEARDATE
            ELSE a.BIZDATE
            END AS BIZDATE
        FROM ATCENTER.LOGASSET a use index (idx_logasset_0502_f)
        where FUNDID = #{fundId}
        <if test="startDate != null and endDate != null">
            AND (
            (a.MARKET IN ('5', 'S') AND a.CLEARDATE BETWEEN #{startDate} and #{endDate})
            OR
            (a.MARKET NOT IN ('5', 'S') AND a.BIZDATE BETWEEN #{startDate} and #{endDate})
            )
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND a.STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND a.STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            AND a.MARKET = #{market}
        </if>
        <if test="digestIdList != null">
            and a.DIGESTID IN
            <foreach item="item" index="index" collection="digestIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.BIZDATE desc,a.SNO desc
        <if test="maxSize != null">
            limit #{maxSize}
        </if>
    </select>

</mapper>