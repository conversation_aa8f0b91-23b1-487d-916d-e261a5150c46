package com.eastmoney.service.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.common.entity.StkPrice;
import com.eastmoney.quote.mdstp.container.QuoteContainer;
import com.eastmoney.quote.mdstp.model.Rec;
import com.eastmoney.service.cache.TradeTimeCacheService;
import com.eastmoney.service.service.quote.QuoteServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class QuoteServiceImplTest {

    @InjectMocks
    private QuoteServiceImpl quoteService;

    @Mock
    private TradeTimeCacheService tradeTimeCacheService;

    @BeforeEach
    void setUp() {
    }

    /**
     * TC01: 行情数据存在且行情价有效
     */
    @Test
    void testGetStkPrice_WithValidQtPrice() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            Rec mockRec = mock(Rec.class);
            when(mockRec.getRealAIndexSharePrice()).thenReturn(100.5);
            when(mockRec.getRealAIndexShareClose()).thenReturn(100.0);
            when(mockRec.getDwTime()).thenReturn(930);

            mockedStatic.when(() -> QuoteContainer.getRec("519001", "1")).thenReturn(mockRec);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "519001";
            stkInfo.market = "1";
            stkInfo.stkType = "L";

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(1.005, result.getPrice(), 0.001);
            assertTrue(result.isQtPrice);
        }
    }

    /**
     * TC02: 行情数据存在但行情价无效，使用收盘价
     */
    @Test
    void testGetStkPrice_WithQtClosePrice() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            Rec mockRec = mock(Rec.class);
            when(mockRec.getRealAIndexSharePrice()).thenReturn(0.0);
            when(mockRec.getRealAIndexShareClose()).thenReturn(10.0);

            mockedStatic.when(() -> QuoteContainer.getRec("000001", "0")).thenReturn(mockRec);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "000001";
            stkInfo.market = "0";

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(10.0, result.getPrice(), 0.001);
        }
    }

    /**
     * TC03: 行情数据不存在，使用lastPrice
     */
    @Test
    void testGetStkPrice_NoQuote_UseLastPrice() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            mockedStatic.when(() -> QuoteContainer.getRec("000001", "0")).thenReturn(null);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "000001";
            stkInfo.market = "0";
            stkInfo.lastPrice = 10.5;

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(10.5, result.getPrice(), 0.001);
        }
    }

    /**
     * TC07: 集合竞价前，使用closePrice
     */
    @Test
    void testGetStkPrice_BeforeMarketOpen_UseClosePrice() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            Rec mockRec = mock(Rec.class);
            when(mockRec.getRealAIndexSharePrice()).thenReturn(10.5);
            when(mockRec.getRealAIndexShareClose()).thenReturn(10.0);
            mockedStatic.when(() -> QuoteContainer.getRec("000001", "0")).thenReturn(mockRec);

            when(tradeTimeCacheService.isBeforeMarketOpen("0")).thenReturn(true);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "000001";
            stkInfo.market = "0";

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(10.0, result.getPrice(), 0.001); // 强制使用closePrice
        }
    }

    /**
     * TC08: LOF基金，旧货币基金类型
     */
    @Test
    void testGetStkPrice_LOFOldMoneyFund() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            mockedStatic.when(() -> QuoteContainer.getRec("160001", "0")).thenReturn(null);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "160001";
            stkInfo.market = "0";
            stkInfo.stkType = STKTEnum.STKT_LOF.getValue();
            stkInfo.lofMoneyFlag = "1"; // LOFTYPE_MONEY_OLD

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(1.0, result.getPrice(), 0.001);
        }
    }

    /**
     * TC11: 持仓收益计算，需要加利息
     */
    @Test
    void testGetStkPrice_PositionProfit_AddBondInterest() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            mockedStatic.when(() -> QuoteContainer.getRec("000001", "0")).thenReturn(null);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "000001";
            stkInfo.market = "0";
            stkInfo.lastPrice = 10.0;
            stkInfo.bondIntr = 0.5;

            StkPrice result = quoteService.getStkPrice(stkInfo, true);

            assertNotNull(result);
            assertEquals(10.5, result.getPrice(), 0.001);
            assertEquals(0.5, result.getBondIntr(), 0.001);
        }
    }

    /**
     * TC13: 债券类型，不加利息
     */
    @Test
    void testGetStkPrice_BondType_NoAddBondInterest() {
        try (MockedStatic<QuoteContainer> mockedStatic = Mockito.mockStatic(QuoteContainer.class)) {
            mockedStatic.when(() -> QuoteContainer.getRec("000001", "0")).thenReturn(null);

            StkInfo stkInfo = new StkInfo();
            stkInfo.stkCode = "000001";
            stkInfo.market = "0";
            stkInfo.lastPrice = 10.0;
            stkInfo.bondIntr = 0.5;
            stkInfo.stkType = STKTEnum.STKT_GZ.getValue(); // 属于债券类型

            StkPrice result = quoteService.getStkPrice(stkInfo, false);

            assertNotNull(result);
            assertEquals(10.0, result.getPrice(), 0.001);
            assertNotNull(result.getBondIntr());
        }
    }
}
