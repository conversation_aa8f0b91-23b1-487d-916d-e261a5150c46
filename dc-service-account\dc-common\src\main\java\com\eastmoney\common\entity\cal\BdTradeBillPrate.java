package com.eastmoney.common.entity.cal;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @program: BdTradeBillPrate
 * @description:大数据振幅
 * @author: <EMAIL>
 * @create: 2022/11/16
 */
public class BdTradeBillPrate {
    /**
     * 资金账号
     */
    private Long fundId;

    /**
     * indexKey
     */
    private Integer indexKey;

    /**
     * 每日收益率峰值
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
     private BigDecimal maxPrate;

    /**
     * 每日收益率谷值
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
     private BigDecimal minPrate;

    /**
     * 振幅
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal absVol;

    /**
     * 振幅超越东财交易普通交易用户的百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
     private BigDecimal absVolPercent;

    /**
     * 最大回撤
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal maxDrawDown;

    /**
     * 最大回测超越百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal maxDrawDownPerc;

    /**
     * 与最近一次初始化日的累计收益率比较 (2024年账单新增,数据中心)
     *  2-扭亏为盈、1-由盈转亏、3-全年都正、0-全年都负
     *  2024年账单-新增（数据中心提供）
     */
    private Integer profitRateStatus;

    public BigDecimal getMaxDrawDown() {
        return maxDrawDown;
    }

    public void setMaxDrawDown(BigDecimal maxDrawDown) {
        this.maxDrawDown = maxDrawDown;
    }

    public BigDecimal getMaxDrawDownPerc() {
        return maxDrawDownPerc;
    }

    public void setMaxDrawDownPerc(BigDecimal maxDrawDownPerc) {
        this.maxDrawDownPerc = maxDrawDownPerc;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public BigDecimal getMaxPrate() {
        return maxPrate;
    }

    public void setMaxPrate(BigDecimal maxPrate) {
        this.maxPrate = maxPrate;
    }

    public BigDecimal getMinPrate() {
        return minPrate;
    }

    public void setMinPrate(BigDecimal minPrate) {
        this.minPrate = minPrate;
    }

    public BigDecimal getAbsVol() {
        return absVol;
    }

    public void setAbsVol(BigDecimal absVol) {
        this.absVol = absVol;
    }

    public BigDecimal getAbsVolPercent() {
        return absVolPercent;
    }

    public void setAbsVolPercent(BigDecimal absVolPercent) {
        this.absVolPercent = absVolPercent;
    }

    public Integer getProfitRateStatus() {
        return profitRateStatus;
    }

    public void setProfitRateStatus(Integer profitRateStatus) {
        this.profitRateStatus = profitRateStatus;
    }
}
