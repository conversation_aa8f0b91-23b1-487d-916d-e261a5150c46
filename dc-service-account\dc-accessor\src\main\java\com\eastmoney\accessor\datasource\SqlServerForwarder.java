package com.eastmoney.accessor.datasource;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.accessor.service.FundInfoCacheService;
import com.eastmoney.common.entity.FundInfo;
import com.eastmoney.common.util.CommonUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: SqlServerForwarder
 * @description:
 * @author: <EMAIL>
 * @create: 2022/11/1
 */
@Service
public class SqlServerForwarder {

    public static final String SERVER_ID = "serverId";

    public static final String FUND_ID = "fundId";
    @Autowired
    private CoreConfigService coreConfigService;
    @Autowired
    private FundInfoCacheService fundInfoCacheService;

    /**
     * 转发
     * 如果入参有 serverid,直接返回对应的数据源
     * 如果入参有 fundId,
     *
     * @param joinPoint
     * @return
     */
    @SuppressWarnings("all")
    public String forward(JoinPoint joinPoint) throws Throwable {
        SqlServerTarget sqlServerTarget = joinPoint.getTarget().getClass().getAnnotation(SqlServerTarget.class);
        if (sqlServerTarget == null) {
            return null;
        }

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        Parameter[] parameters = targetMethod.getParameters();
        String dataSourceType = sqlServerTarget.value();
        Object[] args = joinPoint.getArgs();
        String method = methodSignature.getName();

        for (int index = 0; index < parameters.length; index++) {
            Parameter parameter = parameters[index];
            String paramName = parameter.getName();
            Object arg = args[index];

            if (arg instanceof Map) {
                Map<String, Object> paramMap = (Map) arg;
                Object keyObject = null;
                paramMap = paramMap.entrySet()
                        .stream()
                        .collect(Collectors.toMap(o -> o.getKey().toUpperCase(), o -> o.getValue()));

                // 后台缓存根据serverid查询数据源
                if (paramMap.keySet().contains(SERVER_ID.toUpperCase())
                        && (method.contains("getFundInfo")
                        || method.contains("getStkPriceMap")
                        || method.contains("getMixedConfig")
                        || method.contains("getSysConfig")
                        || method.contains("getHgtReferRate")
                        || method.contains("selectInterceptFundIds"))
                ) {
                    return dataSourceType + paramMap.get(SERVER_ID.toUpperCase());
                } else if (paramMap.keySet().contains(FUND_ID.toUpperCase())) {
                    Long fundId = CommonUtil.convert(paramMap.get(FUND_ID.toUpperCase()), Long.class);
                    Integer serverId = coreConfigService.getServerId(fundId);
                    FundInfo fundInfo = fundInfoCacheService.getFundInfo(fundId, serverId);
                    if (Objects.nonNull(fundInfo)) {
                        ((Map<Object, Object>) arg).put("serverId", fundInfo.getServerId());
                        ((Map<Object, Object>) arg).put("custId", fundInfo.getCustId());
                        ((Map<Object, Object>) arg).put("orgId", fundInfo.getOrgId());
                    }
                    return dataSourceType + serverId;
                }
            }
        }
        throw new Exception(String.format("method:[%s]没有找到serverId参数", targetMethod.getName()));
    }

}
