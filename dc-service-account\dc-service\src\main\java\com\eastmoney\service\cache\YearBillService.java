package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.*;
import com.eastmoney.accessor.dao.tidb.*;
import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.entity.cal.dw.bill.*;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.profit.section.ProfitSectionServiceFacade;
import com.eastmoney.service.util.KryoSerializerUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class YearBillService {
    private static final Logger LOG = LoggerFactory.getLogger(YearBillService.class);
    @Autowired
    private MajorBillDao majorBillDao;
    @Autowired
    private StockBillDao stockBillDao;
    @Autowired
    private ProfitDayDao profitDayDao;
    @Autowired
    private PublishBillDao publishBillDao;
    @Autowired
    private IPOTradeBillDao ipoTradeBillDao;
    @Autowired
    private YearBillExtendDao yearBillExtendDao;
    @Autowired
    private NodeConfigDao nodeConfigDao;

    @Autowired
    private BdUserTradeBillDao bdUserTradeBillDao;

    @Autowired
    private BdTradeBillYearExtDao bdTradeBillYearExtDao;

    @Autowired
    private BdTradeBillYearDao bdTradeBillYearDao;

    @Autowired
    private StockService stockService;

    @Resource(name = "profitSectionServiceFacade")
    private ProfitSectionServiceFacade profitSectionServiceFacade;

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private YearBillCacheService yearBillCacheService;

    @Autowired
    private AccountTemporaryDao accountTemporaryDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private TradeDateDao tradeDateDao;

    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;

    @Autowired
    private ProfitRateSectionRankCacheService profitRateSectionRankCacheService;

    @Autowired
    private BdTradeBillYearReviewDao  bdTradeBillYearReviewDao;

    @Bean(name = "yearBillExtendCache")
    public LoadingCache<String, Optional<byte[]>> yearBillExtendCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(100)
                .maximumSize(1000)
                .expireAfterWrite(300, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<byte[]>>() {
                    @Override
                    public Optional<byte[]> load(String key) {
                        String[] split = key.split("-");
                        YearBillExtend yearBill = getYearBill(Long.valueOf(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]), Integer.parseInt(split[3]));
                        byte[] resultByte = KryoSerializerUtil.serialize(yearBill);
                        return Optional.ofNullable(resultByte);
                    }
                });
    }

    public YearBillExtend getYearBill(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer indexKey = CommonUtil.convert(params.get("indexKey"), Integer.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        try {
            return getUserYearBill(fundId, indexKey, startDate, endDate);
        } catch (Throwable e) {
            LOG.error("{}:获取年账单数据失败", fundId, e);
        }
        return new YearBillExtend();
    }

    private YearBillExtend getYearBill(Long fundId, int indexKey, int startDate, int endDate) {

        if (indexKey <= 2021) {
            return get2021YearBill(fundId, indexKey, startDate, endDate);
        } else {
            return getYearBillInfo(fundId, indexKey, startDate, endDate);
        }
    }

    /**
     * 2022 年账单、2023 年账单
     * 204 年账单：新增redis缓存、日收益实时更新
     *
     * @param fundId
     * @param indexKey
     * @param startDate
     * @param endDate
     * @return
     */
    private YearBillExtend getUserYearBill(Long fundId, int indexKey, int startDate, int endDate) {
        YearBillExtend result = null;

        byte[] resultByte;
        if (nodeConfigService.isYearBillUseRedisCache()) {
            // 查询redis缓存,生效时间 9:00~17:00
            resultByte = yearBillCacheService.getUserYearBillFromRedisCache(fundId, indexKey, startDate, endDate);
        } else {
            resultByte = yearBillCacheService.getUserYearBillFromMemoryCache(fundId, indexKey, startDate, endDate);
        }

        if (Objects.nonNull(resultByte)) {
            result = KryoSerializerUtil.deserialize(resultByte, YearBillExtend.class);
        }
        // 开关启用: 20241231年账单清算前需实时查询收益  开关不启用：直接查询账单表
        if (Objects.nonNull(result) && nodeConfigService.getYearBillProfitCalFlag()) {
            getRealProfit(fundId, result);
        }
        //end add
        return result;
    }

    /**
     * 查询年账单数据
     *
     * @param fundId
     * @param indexKey
     * @param startDate
     * @param endDate
     * @return
     */
    private YearBillExtend getYearBillInfo(long fundId, int indexKey, int startDate, int endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("indexKey", indexKey);
        params.put("startDate", startDate);
        params.put("endDate", endDate);

        YearBillExtend result = new YearBillExtend();
        result.setFundId(fundId);
        result.setIndexKey(indexKey);

        //扩展数据表
        List<YearBillExtend> yearBillExtendList = yearBillExtendDao.query(params);
        if (!CollectionUtils.isEmpty(yearBillExtendList)) {
            YearBillExtend yearBillExtend = yearBillExtendList.get(0);
            result.setTradesCount(yearBillExtend.getTradesCount());
            result.setTradesCountRankPercent(yearBillExtend.getTradesCountRankPercent());
            result.setTradeWinDays(yearBillExtend.getTradeWinDays());
            result.setTradeWinRate(yearBillExtend.getTradeWinRate());
            result.setTradeWinRateRankPercent(yearBillExtend.getTradeWinRateRankPercent());
            result.setProfitMonthCount(yearBillExtend.getProfitMonthCount());
            result.setProfitMonthCountRankPercent(yearBillExtend.getProfitMonthCountRankPercent());
            result.setProfitBestMonth(yearBillExtend.getProfitBestMonth());
            result.setClearProfitSecurityNum(yearBillExtend.getClearProfitSecurityNum());
            result.setClearMostProfitSecurityName(yearBillExtend.getClearMostProfitSecurityName());
            if (StringUtils.isEmpty(result.getClearMostProfitSecurityName()) && !StringUtils.isEmpty(yearBillExtend.getClearMostProfitSecurityCode())) {
                String stkName = stockService.getStkName(yearBillExtend.getClearMostProfitSecurityCode(), yearBillExtend.getClearMostProfitSecurityMarket());
                result.setClearMostProfitSecurityName(stkName);
            }
            result.setClearMostProfit(yearBillExtend.getClearMostProfit());
            result.setClearLossSecurityNum(yearBillExtend.getClearLossSecurityNum());
            result.setClearMostLossSecurityName(yearBillExtend.getClearMostLossSecurityName());
            if (StringUtils.isEmpty(result.getClearMostLossSecurityName()) && !StringUtils.isEmpty(yearBillExtend.getClearMostLossSecurityCode())) {
                String stkName = stockService.getStkName(yearBillExtend.getClearMostLossSecurityCode(), yearBillExtend.getClearMostLossSecurityMarket());
                result.setClearMostLossSecurityName(stkName);
            }
            result.setClearMostLoss(yearBillExtend.getClearMostLoss());
            if (Objects.nonNull(result.getTradesCount()) && result.getTradesCount() > 0) {
                result.setHasPositionStk(true);
            } else {
                result.setHasPositionStk(false);
            }
        } else {
            result.setHasPositionStk(false);
        }

        // 开关打开：20241231 账单清算前,全年总收益使用 profit_section、profit_rate_section 数据（与账户表现页面一致）
        if (!nodeConfigService.getYearBillProfitCalFlag()) {
            List<MajorBillStrInfo> majorBillList = majorBillDao.selectByYearBill(params);
            if (!CollectionUtils.isEmpty(majorBillList)) {
                MajorBillStrInfo majorBillStrInfo = majorBillList.get(0);
                result.setMajorBill(majorBillStrInfo);
            }
        }

        List<BdTradeBillYearExt> bdTradeBillYearExtList = bdTradeBillYearExtDao.query(params);
        long subsNewCnt = 0;
        BigDecimal subsNewPerc = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(bdTradeBillYearExtList)) {
            BdTradeBillYearExt tradeBillYearExt = bdTradeBillYearExtList.get(0);
            //大数据神操作
            BdTradeBillFinal bdTradeBillFinal = tradeBillYearExt.getTradeBillFinal();
            result.setBdTradeBillFinal(bdTradeBillFinal);
            if (Objects.nonNull(bdTradeBillFinal)) {
                subsNewCnt = bdTradeBillFinal.getSubsNewCnt() == null ? 0L : bdTradeBillFinal.getSubsNewCnt();
                subsNewPerc = bdTradeBillFinal.getSubsNewPerc() == null ? BigDecimal.ZERO : bdTradeBillFinal.getSubsNewPerc();
            }
            //大数据振幅
            result.setBdTradeBillPrate(tradeBillYearExt.getTradeBillPrate());
            //牛市指标
            result.setBullProfitInfo(tradeBillYearExt.getBullProfitInfo());
            result.setBullTradeInfo(tradeBillYearExt.getBullTradeInfo());
            result.setHoldInfo(tradeBillYearExt.getHoldInfo());
            result.settProfitInfo(tradeBillYearExt.gettProfitInfo());

            // 查询账单指标中的持仓标识
            BdTradeBillYearHoldDO holdInfo = tradeBillYearExt.getHoldInfo();
            if (Objects.nonNull(holdInfo)) {
                Boolean holdFlag = Objects.equals(holdInfo.getCurrentHoldFlag(), 1);
                Boolean tradeFlag = result.getHasPositionStk();
                // 有交易或有持仓
                result.setHasPositionStk(holdFlag || tradeFlag);
            }

            //投顾指标
            result.setInvestAdvisInfo(tradeBillYearExt.getInvestAdvisInfo());
        }

        //查询打新账单数据
        List<IPOTradeBill> ipoTradeBillList = ipoTradeBillDao.selectYearBill(fundId, indexKey);
        IPOTradeBill ipoTradeBill;
        //modify by yangkai 2023年账单需求,账户分析ipo表打新次数、打新次数排名占比弃用,该两个字段由数据中心提供
        if (!CollectionUtils.isEmpty(ipoTradeBillList)) {
            ipoTradeBill = ipoTradeBillList.get(0) == null ? new IPOTradeBill() : ipoTradeBillList.get(0);
        } else {
            ipoTradeBill = new IPOTradeBill();
        }
        //end modify
        ipoTradeBill.setTradeCount(subsNewCnt);
        ipoTradeBill.setTradeCountPercent(subsNewPerc);
        result.setIpoTradeBill(ipoTradeBill);

        //add by yangkai 2023年账单需求新增指标(数据中心提供)
        List<BdTradeBillYearDO> bdTradeBillYears = bdTradeBillYearDao.query(params);
        if (!CollectionUtils.isEmpty(bdTradeBillYears)) {
            BdTradeBillYearDO bdTradeBillYear = bdTradeBillYears.get(0);
            //基础信息
            result.setBaseInfo(bdTradeBillYear.getBaseInfo());
            //首次交易相关
            result.setFirstDay(bdTradeBillYear.getFirstDay());
            //单日交易次数最多
            result.setMostTrdDay(bdTradeBillYear.getMostTrdDay());
            //热门板块
            result.setConceptSectors(bdTradeBillYear.getConceptSectors());
            // 最高日收益(2024改为查询数据中心指标)
            BdTradeBillMaxProfitDO bestProfitDay = bdTradeBillYear.getMaxProfit();
            if (bestProfitDay != null) {
                result.setBestDay(bestProfitDay);
            }
            if (bdTradeBillYear.getYearClear() != null) {
                //赚钱最多的产品的最后一次清仓日期
                result.setClearMostProfitLDate(bdTradeBillYear.getYearClear().getClearMostProfitLDate());
                //月收益金额最高的月份
                result.setProfitBestMonthDate(bdTradeBillYear.getYearClear().getProfitBestMonthDate());
                //累计清仓总次数
                result.setClearNum(bdTradeBillYear.getYearClear().getClearNum());
                //累计获利清仓总次数
                result.setClearProfitNum(bdTradeBillYear.getYearClear().getClearProfitNum());
                //清仓盈利率
                result.setClearProfitRate(bdTradeBillYear.getYearClear().getClearProfitRate());
                //清仓股平均持股天数
                result.setAvgHoldDays(bdTradeBillYear.getYearClear().getAvgHoldDays());
                //扭亏为盈的人数占普通交易用户的百分比
                result.setTurnEarnPerc(bdTradeBillYear.getYearClear().getTurnEarnPerc());
            }

            //2024年终账单新增指标
            result.setClearProfit(bdTradeBillYear.getClearProfit());

            //2025 年中账单新增行情回顾页
            List<BdTradeBillYearReviewDO> reviewDOList = bdTradeBillYearReviewDao.query(params);
            if(!CollectionUtils.isEmpty(reviewDOList)) {
                result.setReviewInfo(reviewDOList.get(0));
            }
        }
        return result;
    }

    /**
     * 实时查询当日收益
     * 更新账单页面的全年总收益和牛市以来总收益
     *
     * @param
     * @return
     */
    private YearBillExtend getRealProfit(Long fundId, YearBillExtend result) {
        // 全年总收益基于区间收益表计算
        MajorBillStrInfo majorBill = getMajorBillByYSection(fundId, result);
        // 全年收益清算日期
        Integer majorDate = majorBill.getBizDate();
        // 当日未清算完成需查询实时收益
        Boolean majorRealFlag = Objects.nonNull(majorDate) && !Objects.equals(commonService.calRealTimeProfitBizDate(majorDate), 0);

        // 清算后牛市以来总收益
        BdTradeBillYearBullProfitDO bullProfitDO = Objects.isNull(result.getBullProfitInfo()) ? new BdTradeBillYearBullProfitDO() : result.getBullProfitInfo();
        // 牛市账单清算日期
        Integer bullDate = bullProfitDO.getBizDate();
        // 当日未清算完成需查询实时收益
        Boolean bullRealFlag = Objects.nonNull(bullDate) && !Objects.equals(commonService.calRealTimeProfitBizDate(bullDate), 0);

        // 查询全年收益、实时查询当日收益数据
        ProfitDay realProfitInfo = new ProfitDay();
        if (majorRealFlag || bullRealFlag) {
            realProfitInfo = calDayRealProfit(fundId);
        }
        BigDecimal realProfit = ArithUtil.toBigDecimal(realProfitInfo.getProfit(), BigDecimal.ZERO);
        BigDecimal realProfitRate = ArithUtil.toBigDecimal(realProfitInfo.getProfitRate(), BigDecimal.ZERO);

        BigDecimal yearProfitRate = majorBill.getProfitRate();
        // 全年清算收益计入实时收益
        if (majorRealFlag) {
            majorBill.setProfit(ArithUtil.add(realProfit, majorBill.getProfit()));
            yearProfitRate = ArithUtil.round(CommonUtil.multiplyProfitRate(majorBill.getProfitRate(), realProfitRate), 4);
            majorBill.setProfitRate(yearProfitRate);
        }

        // 计算全年收益率排名占比
        Optional<Double> profitRateSectionRank = profitRateSectionRankCacheService.getProfitRateSectionRank(DateUnitEnum.YEAR.getValue(), ArithUtil.toBigDecimal(yearProfitRate, null));
        majorBill.setProfitRankPercent(ArithUtil.toBigDecimal(profitRateSectionRank.orElse(1d), BigDecimal.ONE));

        // 牛市以来收益计入实时收益
        if (bullRealFlag) {
            bullProfitDO.setBullProfit(ArithUtil.add(realProfit, bullProfitDO.getBullProfit()));
            bullProfitDO.setBullProfitRate(ArithUtil.round(CommonUtil.multiplyProfitRate(bullProfitDO.getBullProfitRate(), realProfitRate), 4));
        }

        // 更新全年收益和牛市收益
        result.setMajorBill(majorBill);
        result.setBullProfitInfo(bullProfitDO);

        return result;
    }

    /**
     * 1. 计算当日实时收益
     * 2. 查询本年度清算收益并计入实时收益
     *
     * @param fundId
     */
    private ProfitDay calDayRealProfit(long fundId) {
        ProfitDay profitDay = new ProfitDay();
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("calProfitRate", true);
        params.put("unit", DateUnitEnum.DAY.getValue());

        // 查询当日收益
        SectionProfitBean realProfit = profitSectionServiceFacade.getProfitSection(params);
        if (Objects.nonNull(realProfit)) {
            profitDay.setProfit(realProfit.getProfit());
            profitDay.setProfitRate(realProfit.getProfitRate());
        } else {
            // 非交易日&跨天未清算场景会返回null,需查询临时收益
            DayProfitBean tempProfit = accountTemporaryDao.getDayProfitBean(fundId);
            if (Objects.nonNull(tempProfit)) {
                profitDay.setProfit(tempProfit.getProfit());
                profitDay.setProfitRate(tempProfit.getProfitRate());
            }
        }

        return profitDay;
    }

    /**
     * 使用今年区间收益数据赋值年账单
     *
     * @param result
     * @return
     */
    private MajorBillStrInfo getMajorBillByYSection(Long fundId, YearBillExtend result) {
        MajorBillStrInfo majorBill = Objects.isNull(result.getMajorBill()) ? new MajorBillStrInfo() : result.getMajorBill();

        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("calProfitRate", true);
        params.put("unit", DateUnitEnum.YEAR.getValue());

        // 查询全年清算收益
        ProfitSection profitSection = profitSectionCacheService.getProfitSection(params);
        ProfitRateSection profitRateSection = profitSectionCacheService.getProfitRateSection(params);
        if (Objects.nonNull(profitSection) && Objects.nonNull(profitRateSection)) {
            majorBill.setProfit(ArithUtil.toBigDecimal(profitSection.getProfit(), BigDecimal.ZERO));
            majorBill.setProfitRate(ArithUtil.toBigDecimal(profitRateSection.getProfitRate(), BigDecimal.ZERO));
            majorBill.setBizDate(tradeDateDao.getNextMarketDay(profitSection.getBakBizDate()));
        }
        return majorBill;
    }

    /**
     * 2021 年账单
     *
     * @param fundId
     * @param indexKey
     * @param startDate
     * @param endDate
     * @return
     */
    private YearBillExtend get2021YearBill(long fundId, int indexKey, int startDate, int endDate) {
        YearBillExtend result = new YearBillExtend();
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("indexKey", indexKey);
        List<MajorBill> billList = majorBillDao.query(params);
        if (billList.size() > 0) {
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            Double profit = profitDayDao.getSumProfit(params);
            MajorBill majorBill = billList.get(0);
            majorBill.setProfit(profit);
            result.setMajorBill(MajorBillStrInfo.buildStrInfo(majorBill));
            Double tradeCount = majorBill.getTradeCount();
            if (tradeCount != null) {
                result.setTradesCount(tradeCount.longValue());
            }

        }

        //扩展数据表
        List<YearBillExtend> yearBillExtendList = yearBillExtendDao.query(params);
        if (!CollectionUtils.isEmpty(yearBillExtendList)) {
            YearBillExtend extend = yearBillExtendList.get(0);
            result.setMostHoldingStkName(extend.getMostHoldingStkName());
            result.setMostHoldingStkHoldingDays(extend.getMostHoldingStkHoldingDays());
            result.setMostTradeStkName(extend.getMostTradeStkName());
            result.setMostTradeStkTradeTimes(extend.getMostTradeStkTradeTimes());
            result.setTradesCountRankPercent(extend.getTradesCountRankPercent());
        }

//        //整年大盘，打败大盘
//        List<Double> marketIndexProfitY = getMarketIndexProfit("marketIndexProfitY-" + indexKey);
//        if (!CollectionUtils.isEmpty(marketIndexProfitY)) {
//            result.setProfitRateMarketIndex(marketIndexProfitY.get(0));
//            if (result.getMajorBill() != null && result.getMajorBill().getProfitRate() != null) {
//                result.setProfitRateDefeatMarketIndex(ArithUtil.sub(result.getMajorBill().getProfitRate(), marketIndexProfitY.get(0)));
//            }
//
//        }
//
//        //月收益率表现
//        Map<Integer, List<ProfitRateDay>> monthProfitRateList = majorBillDao.selectMonthProfitRate(fundId, startDate, endDate);
//        List<Double> marketIndexProfitM = getMarketIndexProfit("marketIndexProfitM-" + indexKey);
//        if (marketIndexProfitM != null && marketIndexProfitM.size() == 12) {
//            Integer currIndex = Integer.valueOf(indexKey + "01");
//            List<MonthBillBO> monthBillList = new ArrayList<>();
//            for (Double monthIndexProfitRate : marketIndexProfitM) {
//                List<ProfitRateDay> profitRateDays = monthProfitRateList.get(currIndex);
//                if (!CollectionUtils.isEmpty(profitRateDays) && profitRateDays.size() == 1) {
//                    MonthBillBO monthBill = new MonthBillBO();
//                    monthBill.setIndexKey(currIndex);
//                    monthBill.setProfitRate(profitRateDays.get(0).getProfitRate());
//                    monthBill.setMarketIndexProfitRate(monthIndexProfitRate);
//                    monthBillList.add(monthBill);
//                }
//                currIndex++;
//            }
//            result.setMonthBill(monthBillList);
//        }


//        List<StockBill> stockBillList = stockBillDao.queryEarnLossMostStock(params);
//        if (!CollectionUtils.isEmpty(stockBillList)) {
//            Map<Boolean, List<StockBill>> collect = stockBillList.stream()
//                    .collect(Collectors.partitioningBy(stockBill -> stockBill.getProfit() >= 0));
//            //盈利最多个股
//            if (!CollectionUtils.isEmpty(collect.get(true))) {
//                StockBill stockBillEarnMost = collect.get(true).get(0);
//                if (stockBillEarnMost.getProfit() > 0) {
//                    result.setEarnMostStock(stockBillEarnMost);
//                }
//            }
//            //亏损最多个股
//            if (!CollectionUtils.isEmpty(collect.get(false))) {
//                StockBill stockBillLossMost = collect.get(false).get(0);
//                if (stockBillLossMost.getProfit() < 0) {
//                    result.setLossMostStock(stockBillLossMost);
//                }
//            }
//
//            Map<Boolean, Long> stockBillCounting = stockBillList.stream()
//                    .collect(Collectors.partitioningBy(stockBill -> stockBill.getProfit() > 0, Collectors.counting()));
//            result.setEarnStockCount(stockBillCounting.get(true));
//            result.setLossStockCount(stockBillCounting.get(false));
//        }
//
//        //最爱投资的行业TOP3
//        params.put("count", 3);
//        List<PublishBill> publishBillList = publishBillDao.query(params);
//        result.setTopPublishBill(publishBillList);

        //查询打新账单数据
        IPOTradeBill ipoTradeBill = new IPOTradeBill();
        List<IPOTradeBill> ipoBill = ipoTradeBillDao.selectYearBill(fundId, indexKey);
        //是否有打新记录
        if (!CollectionUtils.isEmpty(ipoBill)) {
            ipoTradeBill = ipoBill.get(0);
            Long investCount = ipoBill.get(0).getInvestCount();
            //是否有中签记录
            ipoTradeBill.setInvestSuccess(investCount != null && investCount > 0);
        }
        result.setIpoTradeBill(ipoTradeBill);

//        //大数据账单
//        List<BdUserTradeBillDO> bdUserTradeBillList = bdUserTradeBillDao.query(params);
//        if (!CollectionUtils.isEmpty(bdUserTradeBillList)) {
//            BdUserTradeBillDO bdUserTradeBill = bdUserTradeBillList.get(0);
//            result.setBdUserTradeBill(bdUserTradeBill);
//        }
        return result;
    }

    /**
     * 传参 code=‘marketIndexProfitY2021’ 返回当年沪深300收益率
     * 传参 code='marketIndexProfitM2021' 按顺序返回当年沪深300每个月的收益率。
     *
     * @param code
     * @return
     */
    private List<Double> getMarketIndexProfit(String code) {
        Map<String, Object> map = new HashMap<>();
        map.put("key", code);
        List<NodeConfigDO> query = nodeConfigDao.query(map);
        if (CollectionUtils.isEmpty(query) || query.get(0) == null || StringUtils.isEmpty(query.get(0).getValue())) {
            return null;
        }

        String[] split = query.get(0).getValue().split(",");
        return Arrays.stream(split)
                .map(Double::valueOf)
                .map(val -> ArithUtil.div(val, 100))
                .collect(Collectors.toList());
    }
}
