package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * 持仓盈亏分析
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/3.
 */
public class PositionSection extends BaseEntityCal {

    private Long fundId;//资金帐号
    private String unit;//单位
    private Long tradesSecurityNum;//交易证券数
    private Long gainSecurityNum;//交易证券数
    private Long tradesNum;//持仓循环数
    private Long gainNum;//持仓循环盈利数
    private Double profit;//收益
    private Integer indexDate;
    private int bakBizDate;
    private Double gainProfit;//盈利股票收益之和(合并相同股票收益)
    private Double lossProfit;//亏损股票收益之和(合并相同股票收益)
    private Double maxProfit;//最大盈利(合并相同股票收益)
    private Double minProfit;//最大亏损(合并相同股票收益)
    private Long tradesHolddays;//合计持仓天数
    private Double gainRate;//交易成功率=盈利股票数量/交易股票数量
    private Double avgHolddays;//平均持股天数：合计持股天数/合计清仓股票数
    private Double avgGain;//平均盈利=盈利股票的合计盈利/盈利股票数
    private Double avgLoss;//平均亏损=亏损股票的合计亏损/亏损股票数

    public Integer getIndexDate() {
        return indexDate;
    }

    public void setIndexDate(Integer indexDate) {
        this.indexDate = indexDate;
    }

    public int getBakBizDate() {
        return bakBizDate;
    }

    public void setBakBizDate(int bakBizDate) {
        this.bakBizDate = bakBizDate;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit.trim();
    }

    public Long getTradesSecurityNum() {
        return tradesSecurityNum;
    }

    public void setTradesSecurityNum(Long tradesSecurityNum) {
        this.tradesSecurityNum = tradesSecurityNum;
    }

    public Long getGainSecurityNum() {
        return gainSecurityNum;
    }

    public void setGainSecurityNum(Long gainSecurityNum) {
        this.gainSecurityNum = gainSecurityNum;
    }

    public Long getTradesNum() {
        return tradesNum;
    }

    public void setTradesNum(Long tradesNum) {
        this.tradesNum = tradesNum;
    }

    public Long getGainNum() {
        return gainNum;
    }

    public void setGainNum(Long gainNum) {
        this.gainNum = gainNum;
    }

    public Double getGainProfit() {
        return gainProfit;
    }

    public void setGainProfit(Double gainProfit) {
        this.gainProfit = gainProfit;
    }

    public Double getLossProfit() {
        return lossProfit;
    }

    public void setLossProfit(Double lossProfit) {
        this.lossProfit = lossProfit;
    }

    public Double getMaxProfit() {
        return maxProfit;
    }

    public void setMaxProfit(Double maxProfit) {
        this.maxProfit = maxProfit;
    }

    public Double getMinProfit() {
        return minProfit;
    }

    public void setMinProfit(Double minProfit) {
        this.minProfit = minProfit;
    }

    public Long getTradesHolddays() {
        return tradesHolddays;
    }

    public void setTradesHolddays(Long tradesHolddays) {
        this.tradesHolddays = tradesHolddays;
    }

    public Double getGainRate() {
        return gainRate;
    }

    public void setGainRate(Double gainRate) {
        this.gainRate = gainRate;
    }

    public Double getAvgHolddays() {
        return avgHolddays;
    }

    public void setAvgHolddays(Double avgHolddays) {
        this.avgHolddays = avgHolddays;
    }

    public Double getAvgGain() {
        return avgGain;
    }

    public void setAvgGain(Double avgGain) {
        this.avgGain = avgGain;
    }

    public Double getAvgLoss() {
        return avgLoss;
    }

    public void setAvgLoss(Double avgLoss) {
        this.avgLoss = avgLoss;
    }
}
