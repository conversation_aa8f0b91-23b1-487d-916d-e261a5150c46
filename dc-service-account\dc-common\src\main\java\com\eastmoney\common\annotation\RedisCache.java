package com.eastmoney.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * redis缓存注解
 * 作用在方法上 支持list<T> ; T会转成自定义对象
 * 其他对象均为Object
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisCache {

    String keyGenerator();

    long expireSeconds() default 0;
}
