package com.eastmoney.common.entity;

import java.util.Objects;

/**
 * Created on 2016/10/28
 * 成交流水父类
 */
public class BaseMatch extends PushEntity {
    private String fillFlag; //回填委托标识
    private Long matchSno;/*流水号*/
    private Integer trdDate;/*交易日期*/
    private Long custId;/*客户代码*/
    private Long fundId;/*资金帐号*/
    private String moneyType;/*货币代码 0人民币 1港币 2美元*/
    private String fundKind;/*资金分类*/
    private String fundLevel;/*资金室号(级别)*/
    private String fundGroup;/*资金分组*/
    private String orgId;/*分支机构*/
    private String brhId;/*机构分支*/
    private String secuId;/*股东代码*/
    private String rptSecuId;/*报盘股东代码*/
    private String bsFlag;/*买卖方向*/
    private String rptBs;/*申报填类别*/
    private String matchType;/*成交类型*/
    private Long orderSno;/*申报编号*/
    private String orderId;/*订单合同号*/
    private String market;/*交易市场*/
    private String stkCode;/*证券代码*/
    private String stkName;/*证券名称*/
    private String stkType;/*证券类别*/
    private String trdId;/*交易类型*/
    private Double orderPrice;/*委托价格*/
    private Long orderQty;/*委托数量*/
    private String seat;/*席位代码*/
    private Long matchTime;/*成交时间*/
    private Double matchPrice;/*成交价格*/
    private Long matchQty;/*成交数量*/
    private Double matchAmt;/*成交金额*/
    private String matchCode;/*成交编号*/
    private Double clearAmt;/*实时清算金额*/
    private Long operId;/*操作柜员代码*/
    private String operLevel;/*操作柜员级别*/
    private String operOrg;/*操作机构编码*/
    private String operWay;/*操作方式*/
    private String remark;/*备注信息*/

    public String getFillFlag() {
        return fillFlag;
    }

    public void setFillFlag(String fillFlag) {
        this.fillFlag = fillFlag;
    }

    public Long getMatchSno() {
        return matchSno;
    }

    public void setMatchSno(Long matchSno) {
        this.matchSno = matchSno;
    }

    public Integer getTrdDate() {
        return trdDate;
    }

    public void setTrdDate(Integer trdDate) {
        this.trdDate = trdDate;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind;
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel;
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public String getRptSecuId() {
        return rptSecuId;
    }

    public void setRptSecuId(String rptSecuId) {
        this.rptSecuId = rptSecuId;
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag;
    }

    public String getRptBs() {
        return rptBs;
    }

    public void setRptBs(String rptBs) {
        this.rptBs = rptBs;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Long getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Long orderSno) {
        this.orderSno = orderSno;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = trdId;
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Long getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Long orderQty) {
        this.orderQty = orderQty;
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat;
    }

    public Long getMatchTime() {
        return matchTime;
    }

    public void setMatchTime(Long matchTime) {
        this.matchTime = matchTime;
    }

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public String getMatchCode() {
        return matchCode;
    }

    public void setMatchCode(String matchCode) {
        this.matchCode = matchCode;
    }

    public Double getClearAmt() {
        return clearAmt;
    }

    public void setClearAmt(Double clearAmt) {
        this.clearAmt = clearAmt;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getOperLevel() {
        return operLevel;
    }

    public void setOperLevel(String operLevel) {
        this.operLevel = operLevel;
    }

    public String getOperOrg() {
        return operOrg;
    }

    public void setOperOrg(String operOrg) {
        this.operOrg = operOrg;
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BaseMatch)) return false;
        BaseMatch baseMatch = (BaseMatch) o;
        return Objects.equals(fillFlag, baseMatch.fillFlag) && Objects.equals(matchSno, baseMatch.matchSno) && Objects.equals(trdDate, baseMatch.trdDate) && Objects.equals(custId, baseMatch.custId) && Objects.equals(fundId, baseMatch.fundId) && Objects.equals(moneyType, baseMatch.moneyType) && Objects.equals(fundKind, baseMatch.fundKind) && Objects.equals(fundLevel, baseMatch.fundLevel) && Objects.equals(fundGroup, baseMatch.fundGroup) && Objects.equals(orgId, baseMatch.orgId) && Objects.equals(brhId, baseMatch.brhId) && Objects.equals(secuId, baseMatch.secuId) && Objects.equals(rptSecuId, baseMatch.rptSecuId) && Objects.equals(bsFlag, baseMatch.bsFlag) && Objects.equals(rptBs, baseMatch.rptBs) && Objects.equals(matchType, baseMatch.matchType) && Objects.equals(orderSno, baseMatch.orderSno) && Objects.equals(orderId, baseMatch.orderId) && Objects.equals(market, baseMatch.market) && Objects.equals(stkCode, baseMatch.stkCode) && Objects.equals(stkName, baseMatch.stkName) && Objects.equals(stkType, baseMatch.stkType) && Objects.equals(trdId, baseMatch.trdId) && Objects.equals(orderPrice, baseMatch.orderPrice) && Objects.equals(orderQty, baseMatch.orderQty) && Objects.equals(seat, baseMatch.seat) && Objects.equals(matchTime, baseMatch.matchTime) && Objects.equals(matchPrice, baseMatch.matchPrice) && Objects.equals(matchQty, baseMatch.matchQty) && Objects.equals(matchAmt, baseMatch.matchAmt) && Objects.equals(matchCode, baseMatch.matchCode) && Objects.equals(clearAmt, baseMatch.clearAmt) && Objects.equals(operId, baseMatch.operId) && Objects.equals(operLevel, baseMatch.operLevel) && Objects.equals(operOrg, baseMatch.operOrg) && Objects.equals(operWay, baseMatch.operWay) && Objects.equals(remark, baseMatch.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fillFlag, matchSno, trdDate, custId, fundId, moneyType, fundKind, fundLevel, fundGroup, orgId, brhId, secuId, rptSecuId, bsFlag, rptBs, matchType, orderSno, orderId, market, stkCode, stkName, stkType, trdId, orderPrice, orderQty, seat, matchTime, matchPrice, matchQty, matchAmt, matchCode, clearAmt, operId, operLevel, operOrg, operWay, remark);
    }
}
