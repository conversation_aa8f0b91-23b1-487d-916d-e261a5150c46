package com.eastmoney.service.service.profit.ipo;

import com.eastmoney.accessor.dao.tidb.IpoPositionProfitDao;
import com.eastmoney.common.entity.cal.IPOPositionProfitDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class IpoPositionProfitServiceImpl implements IpoPositionProfitService {
    @Autowired
    private IpoPositionProfitDao IPOPositionProfitDao;

    @Override
    public List<IPOPositionProfitDO> getIPOPositionProfitList(Long fundId, Integer startDate, Integer endDate) {
        return IPOPositionProfitDao.getIPOPositionProfitList(fundId, startDate, endDate)
                .stream()
                .filter(o -> o.getMarket() != null && o.getStkCode() != null)
                .collect(Collectors.toList());
    }
}
