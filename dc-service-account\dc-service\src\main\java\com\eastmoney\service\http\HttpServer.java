package com.eastmoney.service.http;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequestDecoder;
import io.netty.handler.codec.http.HttpResponseEncoder;
import org.slf4j.LoggerFactory;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/11/16
 * Time: 16:13
 */
public class HttpServer implements Runnable{
    private final static org.slf4j.Logger LOG = LoggerFactory.getLogger(HttpServer.class);
    private final int port;
    private volatile boolean closed = false;
    private volatile EventLoopGroup bossGroup;
    private volatile EventLoopGroup workerGroup;
    private volatile ServerBootstrap bootstrap;

    public HttpServer(int port) {
        this.port = port;
    }

    public void init() {
        closed = false;
        //配置服务端的NIO线程组
        bossGroup = new NioEventLoopGroup();
        workerGroup = new NioEventLoopGroup();
        bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup);

        bootstrap.channel(NioServerSocketChannel.class)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    public void initChannel(SocketChannel ch) throws Exception {
                        // server端发送的是httpResponse，所以要使用HttpResponseEncoder进行编码
                        ch.pipeline().addLast(new HttpResponseEncoder());
                        // server端接收到的是httpRequest，所以要使用HttpRequestDecoder进行解码
                        ch.pipeline().addLast(new HttpRequestDecoder());
                        // 把多个消息转换为一个单一的FullHttpRequest或是FullHttpResponse,解决Post请求参数解析
                        ch.pipeline().addLast(new HttpObjectAggregator(2048));
                        ch.pipeline().addLast(new HttpServerHandler());
                    }
                }).option(ChannelOption.SO_BACKLOG, 128)
                .childOption(ChannelOption.SO_KEEPALIVE, true);
    }
    public void bind() throws Exception{
        if (isClosed()) {
            return;
        }
        //绑定端口，开始绑定server，通过调用sync同步方法阻塞直到绑定成功
        ChannelFuture channelFuture = bootstrap.bind(port).sync();
        System.out.println("********************** HTTP服务器启动成功，端口号：" + port+"**********************");
        //应用程序会一直等待，直到channel关闭
        channelFuture.channel().closeFuture().sync();
        System.out.println("服务器退出完毕，端口号：" + port);
    }
    public void close() {
        closed = true;
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();
        System.out.println("关闭http服务器: " + port);
    }
    @Override
    public void run() {
        try {
            HttpServer server = new HttpServer(port);
            server.init();
            while (true) {
                try {
                    server.bind();
                }catch (Exception e){
                    LOG.error("",e);
                    e.printStackTrace();
                }finally {
                    server.close();
                }
                Thread.sleep(2000);
            }
        } catch (Exception e) {
            LOG.error("",e);
            e.printStackTrace();
        }
    }
    public boolean isClosed() {
        return closed;
    }
}
