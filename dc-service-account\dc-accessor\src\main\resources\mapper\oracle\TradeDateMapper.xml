<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.TradeDateMapper">
<!-- 开启本mapper的mybatis二级缓存
<cache />
-->
    <!-- 判断今日是否为交易日,(根据沪市日期匹配)  -->
    <select id="todayIsMarket" useCache="false" resultType="int">
        <if test="bizdate != null">
            SELECT COUNT(1) FROM XTZX.EM_TX_TRADEDATE WHERE TRADEDATE = to_date(#{bizdate},'yyyymmdd') AND MARKET ='SH'
        </if>
        <if test="bizdate == null">
            SELECT COUNT(1) FROM XTZX.EM_TX_TRADEDATE WHERE TRADEDATE = to_date(to_char(SYSDATE,'yyyy/mm/dd'),'yyyy/mm/dd') AND MARKET ='SH'
        </if>
    </select>

    <!-- 取得上一个交易日,(根据沪市日期匹配)  -->
    <select id="getPreMarketDay" useCache="false" resultType="String">
        <if test="bizdate != null">
            select pre_tradedate from (select t.tradedate, lead(t.tradedate) over(order by tradedate desc) pre_tradedate
            from XTZX.EM_TX_TRADEDATE t where MARKET ='SH') i where to_date(#{bizdate},'yyyymmdd') > pre_tradedate and  rownum  = 1
        </if>
        <if test="bizdate == null">
            select pre_tradedate from (select t.tradedate, lead(t.tradedate) over(order by tradedate desc) pre_tradedate
            from XTZX.EM_TX_TRADEDATE t where MARKET ='SH') i where to_date(to_char(SYSDATE,'yyyy/mm/dd'),'yyyy/mm/dd') > pre_tradedate and  rownum  = 1
        </if>

     </select>

    <select id="getNextMarketDay" useCache="false" resultType="String">
      <if test="bizdate != null">
          SELECT next_tradedate FROM (SELECT T.tradedate,LAG (T.tradedate) OVER (ORDER BY tradedate ASC) next_tradedate	FROM XTZX.EM_TX_TRADEDATE T	WHERE	MARKET = 'SH')
          WHERE	TO_DATE (#{bizdate},'yyyy/mm/dd') <![CDATA[<]]> next_tradedate AND ROWNUM = 1
      </if>
      <if test="bizdate == null">
          SELECT next_tradedate FROM (SELECT T.tradedate,LAG (T.tradedate) OVER (ORDER BY tradedate ASC) next_tradedate	FROM XTZX.EM_TX_TRADEDATE T	WHERE	MARKET = 'SH')
          WHERE	TO_DATE (TO_CHAR (SYSDATE, 'yyyy/mm/dd'),'yyyy/mm/dd') <![CDATA[<]]> next_tradedate AND ROWNUM = 1
      </if>
    </select>
    
    <select id="getTradeDateList" resultType="com.eastmoney.common.entity.TradeDate">
        SELECT to_char(TRADEDATE,'yyyyMMdd') as tradeDate FROM XTZX.EM_TX_TRADEDATE WHERE to_char(TRADEDATE,'yyyyMM') = #{tradeDate} and MARKET = 'SH'  order by TRADEDATE ASC
    </select>

    <select id="getAllTradeDateList" resultType="com.eastmoney.common.entity.TradeDate">
        SELECT to_char(TRADEDATE,'yyyyMMdd') as tradeDate FROM XTZX.EM_TX_TRADEDATE WHERE
        to_char(TRADEDATE,'yyyyMMdd') <![CDATA[>=]]> #{startDate}
        and MARKET = 'SH'  order by TRADEDATE ASC
    </select>

 </mapper>