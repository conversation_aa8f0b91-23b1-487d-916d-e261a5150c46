package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.sqlserver.OggFundStockInnovateDao;
import com.eastmoney.common.entity.LogAsset;
import com.eastmoney.common.entity.PosDBItem;
import com.eastmoney.common.entity.PositionInfo;
import com.eastmoney.common.entity.QryFund;
import com.eastmoney.common.sysEnum.PosDBTypeEnum;
import com.eastmoney.common.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
@Service
public class FundStockInnovateServiceImpl implements FundStockInnovateService {

    @Autowired
    private OggFundStockInnovateDao oggFundStockInnovateDao;

    @Override
    public List query(Map<String, Object> params) {
        return oggFundStockInnovateDao.query(params);
    }

    private List queryOld(Map<String, Object> params){
        return oggFundStockInnovateDao.queryOld(params);
    }

    @Override
    public PosDBItem getDBResultInfo(Map params) {
        List list = query(params);
        CommonUtil.checkList(list);
        return handleDBResult(list);
    }

    @Override
    public PosDBItem getDBResultInfoOld(Map params) {
        List list = queryOld(params);
        CommonUtil.checkList(list);
        return handleDBResult(list);
    }

    private PosDBItem handleDBResult(List list){
        PosDBItem posDBItem = new PosDBItem();
        List<QryFund> qryFundList = new ArrayList<>();
        List<PositionInfo> stkInfoList = new ArrayList<>();
        List<LogAsset> logAssetList = new ArrayList<>();
        List<PositionInfo> profitStkInfoList = new ArrayList<>();
        List<LogAsset> profitLogAssetList = new ArrayList<>();

        boolean exist_temp_position_type = false;
        boolean exist_temp_match_type = false;

        for (int index = 1; index < list.size(); ++index) {
            List<PosDBItem> item = (List<PosDBItem>) list.get(index);
            if (CollectionUtils.isEmpty(item) || null == item.get(0).getFundStkType()) {
                continue;
            }
            String fundStkType = item.get(0).getFundStkType();
            if (Objects.equals(fundStkType, PosDBTypeEnum.ASSET_TYPE.getFundStkType())) {
                qryFundList = PosDBItem.typeConvert(item, QryFund.class);
            } else if (Objects.equals(fundStkType, PosDBTypeEnum.POSITION_TYPE.getFundStkType())) {
                stkInfoList = PosDBItem.typeConvert(item, PositionInfo.class);
            } else if (Objects.equals(fundStkType, PosDBTypeEnum.MATCH_TYPE.getFundStkType())) {
                logAssetList = PosDBItem.typeConvert(item, LogAsset.class);
            } else if (Objects.equals(fundStkType, PosDBTypeEnum.TEMP_POSITION_TYPE.getFundStkType())) {
                profitStkInfoList = PosDBItem.typeConvert(item, PositionInfo.class);
                exist_temp_position_type = true;
            } else if (Objects.equals(fundStkType, PosDBTypeEnum.TEMP_MATCH_TYPE.getFundStkType())) {
                profitLogAssetList = PosDBItem.typeConvert(item, LogAsset.class);
                exist_temp_match_type = true;
            }
        }

        if(!exist_temp_position_type){
            for (PositionInfo item: stkInfoList) {
                PositionInfo object = PositionInfo.of(item);
                profitStkInfoList.add(object);
            }
        }

        if(!exist_temp_match_type){
            profitLogAssetList = logAssetList;
        }

        posDBItem.setQryFundList(qryFundList);
        posDBItem.setStkInfoList(stkInfoList);
        posDBItem.setLogAssetList(logAssetList);
        posDBItem.setProfitStkInfoList(profitStkInfoList);
        posDBItem.setProfitLogAssetList(profitLogAssetList);
        return posDBItem;
    }

}
