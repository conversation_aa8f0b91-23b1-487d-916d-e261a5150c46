package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.ProfitStat;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/19.
 */
@Repository
public interface ProfitDayMapper extends BaseMapper<ProfitDay, Long> {

    List<ProfitStat> getProfitStatList(Map<String, Object> params);

    Double getSumProfit(Map<String, Object> params);
}
