package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.tidb.BusinessTaskStatusDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.BusinessTaskStatusDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("businessTaskStatusService")
public class BusinessTaskStatusService {

    @Resource(name = "businessTaskStatusDao")
    private BusinessTaskStatusDao businessTaskStatusDao;

    @RedisCache(keyGenerator = "'jzjy_businesstaskstatus_' + #businessTaskHandle + '_' + #serverId")
    public BusinessTaskStatusDO getBusinessTaskStatus(Integer serverId, String businessTaskHandle) {
        Map<String, Object> params = new HashMap<>();
        params.put("serverId", serverId);
        params.put("businessTaskHandle", businessTaskHandle);
        return  businessTaskStatusDao.selectOneTaskNewestStatus(params);
    }


}
