package com.eastmoney.test;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.dao.kgdb.OtcAssetDetailDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateDayDao;
import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.sqlserver.OggMatchDao;
import com.eastmoney.common.entity.QryFund;
import com.eastmoney.common.entity.TradeDate;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.common.entity.fundia.InvestFeeDetail;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.service.cache.ProfitRateSectionRankCacheService;
import com.eastmoney.service.handler.*;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

/**
 * Created by sunyuncai on 2016/10/14.
 * update by xiaoyongyong
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class AccountAnalyzeTest {

    @InjectMocks
    ProfitHandler handler;
    @InjectMocks
    ProfitRateHandler profitRateHandler;
    @InjectMocks
    ProfitHandler profitHandler;
    @InjectMocks
    PositionHandler positionHandler;

    @InjectMocks
    TradeDateDao tradeDateDao;
    @InjectMocks
    AssetHandler assetHandler;

    @InjectMocks
    TpseStSechyreDao tpseStSechyreDao;

    @InjectMocks
    OtcAssetDetailDao otcAssetDetailDao;

    @InjectMocks
    BillHandler billHandler;

    @InjectMocks
    LogAssetHandler logAssetHandler;

    @InjectMocks
    FundAssetHandler fundAssetHandler;

    @InjectMocks
    TradeDateHandler tradeDateHandler;

    private Map<String, Object> params = new HashMap<>();

    @BeforeEach
    public void init() {
//        TradeDateCache.initTradeDateList();
//        QtStarter.start();
        params.put("fundId", "************");
//        params.put("unit","M");
//        params.put("indexKey", "2022");
        params.put("stkCode", "601279");
        params.put("market", '1');
        params.put("startDate", "20220701");
        params.put("endDate", "20220725");
        params.put("pageSize", "20");
        params.put("pageNo", "1");
    }

    @Test
    public void getHoldingPositionProfit() {
        MergeTradeResult holdPositionMergeTradeList = positionHandler.getHoldPositionMergeTradeList(params);
        for (int i = 0; i < holdPositionMergeTradeList.getTotalLogAsset().size(); i++) {
            System.out.println(holdPositionMergeTradeList.getTotalLogAsset().get(i).getDigestId());
            System.out.println(holdPositionMergeTradeList.getTotalLogAsset().get(i).getBizDate());
        }
        Assertions.assertNotNull(holdPositionMergeTradeList);
    }

    @Test
    public void getClearPositionProfit() {
        MergeTradeResult clearPositionMergeTradeList = (MergeTradeResult) positionHandler.getClearPositionMergeTradeList(params);
        clearPositionMergeTradeList.getTotalLogAsset().forEach(asset -> System.out.println(asset.getSno()));
        Assertions.assertNotNull(clearPositionMergeTradeList);
    }


    @InjectMocks
    private OggMatchDao oggMatchDao;
    @Test
    public void getQt() {
        Map<String, Object> params = new HashMap<>();
        params.put("trdDate", 20170315);
        params.put("fundId", "540800231997");
        oggMatchDao.getRealTimeMatchList(params);
        System.out.println();
    }

    @Test
    public void getRealTimeOtcAsset() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", 110100000188L);
        System.out.println();
    }


    //总收益
    @Test
    public void testGetProfitInfo() throws IOException {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "310100020522");
        params.put("fundId", "************");
        String[] units = new String[]{"M", "PHY", "ALL"};
//        String[] units = new String[]{"ALL"};
        for (String unit : units) {

            params.put("unit", unit);
            Object obj = handler.getProfitInfo(params);
            System.out.println(JSON.toJSON(obj));
        }
    }

    //分片收益列表
    @Test
    public void getStkTradeList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540700265412");
        params.put("stkCode", "600009");

        params.put("startDate", "20160612");
        params.put("endDate", "20160613");
        params.put("market", "1");
        Object list = logAssetHandler.getStkTradeList(params);
        System.out.println(JSON.toJSON(list));
    }

    //转入转出
    @Test
    public void getStkShiftList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540100075660");
        params.put("stkCode", "519888");

        params.put("startDate", "20150701");
        params.put("endDate", "20150710");
        params.put("market", "1");
        Object list = logAssetHandler.getStkShiftList(params);
        System.out.println(JSON.toJSON(list));
    }

    @InjectMocks
    private ProfitRateDayDao profitRateDayDao;

    @Test
    public void testFF() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540600006726");
        params.put("startDate", "20161018");
        params.put("ensDate", "20170516");
        List<ProfitRateDay> profitRateDayList = profitRateDayDao.query(params);
        System.out.println();
    }

    //日收益率
    @Test
    public void testGetProfitRateDayList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("unit", "P1Y");
        params.put("beginIndex", -1);
        params.put("startDate", "20161109");
        params.put("endDate", "20171113");
        List<ProfitRateDay> profitRateDayList = profitRateHandler.getProfitRateDayList(params);
        double profitTemp = 0.0;
        if (CollectionUtils.isEmpty(profitRateDayList)) {
            return;
        }
        for (int i = 0; i < profitRateDayList.size(); i++) {
            ProfitRateDay profitRateDay = profitRateDayList.get(i);
            if (i == 0) {
                profitTemp = ((1 + 0) * (1 + profitRateDay.getProfitRate())) - 1;
                continue;
            }
            profitTemp = ((1 + 0) * (1 + profitRateDay.getProfitRate())) - 1;
        }
        System.out.println(JSON.toJSON(profitRateDayList));
    }

    //日收益率
    @Test
    public void getAssetList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540800232077");
        params.put("startDate", 20161207);
        params.put("endDate", 20161208);
        AssetNew assetHisList = assetHandler.getAssetInfo(params);
        System.out.println(JSON.toJSON(assetHisList));
    }

    //日收益
    @Test
    public void testGetProfitDayList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540800232077");
        params.put("startDate", 20161202);
        params.put("endDate", 20161209);
        List<ProfitDay> profitDayList = profitHandler.getProfitDayList(params);
        System.out.println(JSON.toJSON(profitDayList));
    }

    //收益率排名信息
    @Test
    public void testGetProfitRateRankInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("unit", "W");
        Map<String, Object> resultMap = profitRateHandler.getProfitRateRankInfo(params);
        System.out.println(JSON.toJSON(resultMap));
    }

    //getAsset
    @Test
    public void getAssetSection() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540700011515");
        String[] units = new String[]{"W", "PHY", "ALL"};
//        String[] units = new String[]{"ALL"};
        for (String unit : units) {

            params.put("unit", unit);
            Object obj = assetHandler.getAssetSection(params);
            System.out.println(JSON.toJSON(obj));
        }
    }

    @Test
    public void getJyr() {
        System.out.println(tradeDateDao.getPreMarketDay(20161010));
    }

    //行业类别
    @Test
    public void testTpseStSechyre() {
        Map<String, Object> params = new HashMap<>();
        params.put("stkCode", "600048");
//        List resultMap = tpseStSechyreDao.getTpseStSechyreList(params);
//        System.out.println(JSON.toJSON(resultMap));
    }

    @Test
    public void getStockBill() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540800232077");
        params.put("indexKey", "2017");
        billHandler.getStockBill(params);
    }

    @Test
    public void getMajorBill() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540700191611");
        params.put("indexKey", "201608");
        billHandler.getMajorBill(params);
    }

    //总资产
    @Test
    public void getRealTimeAsset() {
//        try {
//            QtStarter.start();
//        } catch (Exception e) {
//
//        }
//        TradeDateCache.initTradeDateList();
        Map<String, Object> params = new HashMap<>();
//        params.put("orgId", "1101");
//        params.put("custId", "540700266587");
        params.put("fundId", "540740082275");
        params.put("moneyType", "0");
//        params.put("flag", "0");
        params.put("flag1", "0");
        params.put("flag2", "0");
        params.put("stockFlag", "0");
        QryFund qryFund = fundAssetHandler.getRealTimeAsset(params);

        System.out.println(qryFund.getFundAll());
    }

    //交易日期
    @Test
    public void getTradeDateList() {
        Map<String, Object> abc = new HashMap<>();
        abc.put("a", "aa");
        abc.put("b", "bb");
        abc.put("c", "cc");

        System.out.println(ArithUtil.add(null, 123));

        Set<String> ss = abc.keySet();
        System.out.println(abc.size() + ";;;;" + ss);

        Map<String, Object> params = new HashMap<>();
        params.put("tradeDate", "201612");
        List<TradeDate> list = tradeDateHandler.getTradeDateList(params);
        for (TradeDate tradeDate : list) {

            System.out.println(tradeDate.getTradeDate());
        }
    }

    //五维分析
    @Test
    public void getAbilityBillList() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "540700191611");
        params.put("indexKey", "201608");
        List<AbilityBill> list = billHandler.getAbilityBillList(params);
        System.out.println();
    }

    @Test
    public void getYearBill() {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", "************");
        params.put("indexKey", "2021");
        YearBillExtend userYearBill = billHandler.getUserYearBill(params);
        System.out.println(("建定交易次数排名:" + userYearBill.getTradesCountRankPercent()));
        Assertions.assertNotNull(userYearBill);
    }

    @Test
    public void testPositionHandler() {
        positionHandler.setQueryTime(params);
    }

    @InjectMocks
    private ProfitRateSectionRankCacheService profitRateSectionRankCacheService;

    @Test
    public void testProfitRateRank() {
//        profitRateHandler.getProfitRateRankInfo(params);
        double testProfitRate = 0.01;
        Optional<Double> wProfit = profitRateSectionRankCacheService.getProfitRateSectionRank("W", 0.1);
        System.out.println("testProfitRate" + testProfitRate + "为" + wProfit);
    }

    //收益率排名信息
    @Test
    public void testGetProfitRateDistributeList() {
        Map<String, Object> params = new HashMap<>();
        params.put("unit", "W");
        List<ProfitRateContrastChart> profitRateDistributeList = profitHandler.getProfitRateDistributeList(params);
        Assertions.assertEquals(profitRateDistributeList.size(), 52);
        System.out.println(123);
    }

    @InjectMocks
    private StockService stockService;

    @Test
    public void testStkName() {
        String stkName = stockService.getStkName("300059", "0");
        System.out.println(stkName);
    }

    @Test
    public void test() {
        InvestFeeDetail detail = new InvestFeeDetail();
        detail.setBizDate(20220510);
        String latestProfitBizdate = "20220510";

        String detalBizdate = "20220510";
        Assertions.assertTrue(detalBizdate.equals(latestProfitBizdate));
    }

    @InjectMocks
    private StkAssetService stkAssetService;

    @Test
    public void testGetOpenMktVal() {
        Double reduce = stkAssetService.getOpenMktVal(************l, 20220512, "0", 3, "0", "000001", null);
        System.out.println(reduce);
    }

    @Test
    public void testGetPositionProfitSearchData() {

        List<PositionProfitBO> positionProfitSearchData = positionHandler.getPositionProfitSearchData(params);
        Assertions.assertFalse(positionProfitSearchData.isEmpty());
    }

    @Test
    public void testMergeMap() {

        Map<String, Double> kgdbOtcAssetDetail = new HashMap<>();
        kgdbOtcAssetDetail.put("0", 0.01);
        Map<String, Double> realTimeOtcAsset = new HashMap<>();
        realTimeOtcAsset.put("0", 0.02);
        kgdbOtcAssetDetail.forEach((moneyType, asset) -> realTimeOtcAsset.merge(moneyType, asset, ArithUtil::add));
        Assertions.assertEquals(realTimeOtcAsset.get("0"), 0.03);
    }
}
