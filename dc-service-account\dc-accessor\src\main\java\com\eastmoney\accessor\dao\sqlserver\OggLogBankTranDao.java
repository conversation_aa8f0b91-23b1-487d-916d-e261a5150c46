package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.LogBankTran;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/2/20.
 */
public interface OggLogBankTranDao extends IBaseDao<LogBankTran, Integer> {
    List<LogBankTran> selectBySysDate(Map<String, Object> params);
    Double selectFundIaAdjustFundAsset(Map<String, Object> params);
}
