package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.AssetDetailMapper;
import com.eastmoney.common.entity.cal.AssetDetailDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Repository
public class AssetDetailDaoImpl extends BaseDao<AssetDetailMapper, AssetDetailDO, Long> implements AssetDetailDao {
    @Override
    public List<AssetDetailDO> getAllFundAsset(Map<String, Object> params) {
        return getMapper().getAllFundAsset(params);
    }
}
