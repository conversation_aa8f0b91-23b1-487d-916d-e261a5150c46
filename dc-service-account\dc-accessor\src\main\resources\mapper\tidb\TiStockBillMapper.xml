<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiStockBillMapper">

    <sql id="All_Column">
        indexKey,profitType,rankIndex,stkCode,market,stkName, profit,positionDays,positionTimes,
        totalCost,totalBuy,totalSale,totalMatchQtyBuy,totalMatchQtySale,chgAmtRate
    </sql>


    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.StockBill">
        select <include refid="All_Column"/>
        from atcenter.B_Stock_Bill
        where fundid = #{fundId} and indexKey = #{indexKey}
        order by profit desc
    </select>

    <select id="queryEarnLossMostStock" resultType="com.eastmoney.common.entity.cal.StockBill">
        select * from
            (select  indexKey,profitType,rankIndex,stkCode,market,stkName, profit,positionDays,positionTimes,
                     totalCost,totalBuy,totalSale,totalMatchQtyBuy,totalMatchQtySale,chgAmtRate
             from atcenter.B_Stock_Bill
             where fundid = #{fundId} and indexKey = #{indexKey} and profit <![CDATA[>=]]>0
             order by profit desc
            )t1
        union all select * from(
                                   select indexKey,profitType,rankIndex,stkCode,market,stkName, profit,positionDays,positionTimes,
                                          totalCost,totalBuy,totalSale,totalMatchQtyBuy,totalMatchQtySale,chgAmtRate
                                   from atcenter.B_Stock_Bill
                                   where fundid = #{fundId} and indexKey = #{indexKey}  and profit <![CDATA[<]]>0
                                   order by profit
                                  )t2
    </select>

</mapper>