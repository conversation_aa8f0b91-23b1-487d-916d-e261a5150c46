package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/1.
 */
public class FundAssetSummary {
    public Double fundBal;
    public Double fundAvl;
    public Double fundBuySale;
    public Double fundBrkBuy;
    public Double fundSale;
    public Double fundBuy;
    public Double fundUncomeSale;
    public Double fundUncomeBuy;
    public Double fundUnfrz;
    public Double fundTrdUnfrz;
    public Double fundFrz;
    public Double fundTrdFrz;
    public Double fundLoan;
    public Double fundStandby;
    public Double fundNightFrz;
    public Double exchCashLmt;
    public Double fdFund;
    public Double fdFundAsset;
    public Double punishIntr;
    public Double punishAmt;
    public Double fundCash;
    public Double fundCheck;
    public String intRateKind;
    public Double fundRemote;
    public Double fundClearSale;
    public Double fundClearBuy;
    public Double fundBbAmt;
    public Double fundBbUncomeAmt;
    public Double fundBbFrzAmt;
    public Long custId;
    public Double fundPreIn;
    public Double fundPreOut;
    public Double lastFundClear;
    public Double fundRealBuy;
    public double fundCashPro;

    public double getFundCashPro() {
        return fundCashPro;
    }

    public void setFundCashPro(double fundCashPro) {
        this.fundCashPro = fundCashPro;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundBrkBuy() {
        return fundBrkBuy;
    }

    public void setFundBrkBuy(Double fundBrkBuy) {
        this.fundBrkBuy = fundBrkBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundUncomeSale() {
        return fundUncomeSale;
    }

    public void setFundUncomeSale(Double fundUncomeSale) {
        this.fundUncomeSale = fundUncomeSale;
    }

    public Double getFundUncomeBuy() {
        return fundUncomeBuy;
    }

    public void setFundUncomeBuy(Double fundUncomeBuy) {
        this.fundUncomeBuy = fundUncomeBuy;
    }

    public Double getFundUnfrz() {
        return fundUnfrz;
    }

    public void setFundUnfrz(Double fundUnfrz) {
        this.fundUnfrz = fundUnfrz;
    }

    public Double getFundTrdUnfrz() {
        return fundTrdUnfrz;
    }

    public void setFundTrdUnfrz(Double fundTrdUnfrz) {
        this.fundTrdUnfrz = fundTrdUnfrz;
    }

    public Double getFundFrz() {
        return fundFrz;
    }

    public void setFundFrz(Double fundFrz) {
        this.fundFrz = fundFrz;
    }

    public Double getFundTrdFrz() {
        return fundTrdFrz;
    }

    public void setFundTrdFrz(Double fundTrdFrz) {
        this.fundTrdFrz = fundTrdFrz;
    }

    public Double getFundLoan() {
        return fundLoan;
    }

    public void setFundLoan(Double fundLoan) {
        this.fundLoan = fundLoan;
    }

    public Double getFundStandby() {
        return fundStandby;
    }

    public void setFundStandby(Double fundStandby) {
        this.fundStandby = fundStandby;
    }

    public Double getFundNightFrz() {
        return fundNightFrz;
    }

    public void setFundNightFrz(Double fundNightFrz) {
        this.fundNightFrz = fundNightFrz;
    }

    public Double getExchCashLmt() {
        return exchCashLmt;
    }

    public void setExchCashLmt(Double exchCashLmt) {
        this.exchCashLmt = exchCashLmt;
    }

    public Double getFdFund() {
        return fdFund;
    }

    public void setFdFund(Double fdFund) {
        this.fdFund = fdFund;
    }

    public Double getFdFundAsset() {
        return fdFundAsset;
    }

    public void setFdFundAsset(Double fdFundAsset) {
        this.fdFundAsset = fdFundAsset;
    }

    public Double getPunishIntr() {
        return punishIntr;
    }

    public void setPunishIntr(Double punishIntr) {
        this.punishIntr = punishIntr;
    }

    public Double getPunishAmt() {
        return punishAmt;
    }

    public void setPunishAmt(Double punishAmt) {
        this.punishAmt = punishAmt;
    }

    public Double getFundCash() {
        return fundCash;
    }

    public void setFundCash(Double fundCash) {
        this.fundCash = fundCash;
    }

    public Double getFundCheck() {
        return fundCheck;
    }

    public void setFundCheck(Double fundCheck) {
        this.fundCheck = fundCheck;
    }

    public String getIntRateKind() {
        return intRateKind;
    }

    public void setIntRateKind(String intRateKind) {
        this.intRateKind = intRateKind;
    }

    public Double getFundRemote() {
        return fundRemote;
    }

    public void setFundRemote(Double fundRemote) {
        this.fundRemote = fundRemote;
    }

    public Double getFundClearSale() {
        return fundClearSale;
    }

    public void setFundClearSale(Double fundClearSale) {
        this.fundClearSale = fundClearSale;
    }

    public Double getFundClearBuy() {
        return fundClearBuy;
    }

    public void setFundClearBuy(Double fundClearBuy) {
        this.fundClearBuy = fundClearBuy;
    }

    public Double getFundBbAmt() {
        return fundBbAmt;
    }

    public void setFundBbAmt(Double fundBbAmt) {
        this.fundBbAmt = fundBbAmt;
    }

    public Double getFundBbUncomeAmt() {
        return fundBbUncomeAmt;
    }

    public void setFundBbUncomeAmt(Double fundBbUncomeAmt) {
        this.fundBbUncomeAmt = fundBbUncomeAmt;
    }

    public Double getFundBbFrzAmt() {
        return fundBbFrzAmt;
    }

    public void setFundBbFrzAmt(Double fundBbFrzAmt) {
        this.fundBbFrzAmt = fundBbFrzAmt;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Double getFundPreIn() {
        return fundPreIn;
    }

    public void setFundPreIn(Double fundPreIn) {
        this.fundPreIn = fundPreIn;
    }

    public Double getFundPreOut() {
        return fundPreOut;
    }

    public void setFundPreOut(Double fundPreOut) {
        this.fundPreOut = fundPreOut;
    }

    public Double getLastFundClear() {
        return lastFundClear;
    }

    public void setLastFundClear(Double lastFundClear) {
        this.lastFundClear = lastFundClear;
    }

    public Double getFundRealBuy() {
        return fundRealBuy;
    }

    public void setFundRealBuy(Double fundRealBuy) {
        this.fundRealBuy = fundRealBuy;
    }

}
