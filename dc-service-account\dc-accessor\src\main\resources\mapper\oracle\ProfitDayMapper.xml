<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.oracle.ProfitDayMapper">
    <resultMap id="BaseResultMap" type="as_profitDay" >
        <result column="FUNDID" property="fundId"/>
        <result column="PROFIT" property="profit"/>
    </resultMap>

    <resultMap id="StatResultMap" type="com.eastmoney.common.entity.cal.ProfitStat" >
        <result column="FUNDID" property="fundId"/>
        <result column="PROFIT" property="profit"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID, PROFIT, BIZDATE
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT /*+ index_ss(t PK_LG_PROFIT_DAY) */ <include refid="All_Column"/> FROM ATCENTER.PROFIT_DAY t
        where FUNDID = #{fundId}
        <if test="startDate != null">
            AND BIZDATE BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="bizDate != null">
            AND BIZDATE = #{bizDate}
        </if>
        ORDER BY FUNDID,BIZDATE
    </select>

    <select id="getProfitStatList" resultMap="StatResultMap">
        select sum(Profit) profit,floor(BizDate/100) bizDate
        FROM ATCENTER.PROFIT_DAY
        where FUNDID = #{fundId}
          and BizDate BETWEEN #{startDate} AND #{endDate}
        group by floor(bizdate/100)
        order by bizdate asc
    </select>

    <select id="getSumProfit" resultType="double">
        select /*+index_ss(t PK_LG_PROFIT_DAY)*/ nvl(sum(Profit),0) sumProfit
        FROM ATCENTER.PROFIT_DAY t
        where FUNDID = #{fundId}
        and BizDate BETWEEN #{startDate} AND #{endDate}
    </select>
</mapper>
