package com.eastmoney.quote.app.model;/**
 * Created by 1 on 16-10-20.
 */

import com.eastmoney.quote.app.serializer.Packet;

/**
 * Created on 16-10-20
 *
 * <AUTHOR>
 */
public class Input5059 extends Packet {

    private short clientID;
    private byte pushType;
    private byte sortID; //排序字段
    private byte sortType; //排序方式
    private short startPlace; //起始位置
    private short reqNum; //请求个数
    private  byte reqIdNums; //请求字段ID数组个数
    private byte[] reqId; //请求字段ID数组
    private byte reqType; //请求类型
    private short reqDataNum; //请求数据个数
    private short reqDataLength;//请求数据内容长度
    private ReqData5059[] reqDataContext; //请求数据内容

    public short getClientID() {
        return clientID;
    }

    public void setClientID(short clientID) {
        this.clientID = clientID;
    }

    public byte getPushType() {
        return pushType;
    }

    public void setPushType(byte pushType) {
        this.pushType = pushType;
    }

    public byte getSortID() {
        return sortID;
    }

    public void setSortID(byte sortID) {
        this.sortID = sortID;
    }

    public byte getSortType() {
        return sortType;
    }

    public void setSortType(byte sortType) {
        this.sortType = sortType;
    }

    public short getStartPlace() {
        return startPlace;
    }

    public void setStartPlace(short startPlace) {
        this.startPlace = startPlace;
    }

    public short getReqNum() {
        return reqNum;
    }

    public void setReqNum(short reqNum) {
        this.reqNum = reqNum;
    }

    public byte getReqIdNums() {
        return reqIdNums;
    }

    public void setReqIdNums(byte reqIdNums) {
        this.reqIdNums = reqIdNums;
    }

    public byte[] getReqId() {
        return reqId;
    }

    public void setReqId(byte[] reqId) {
        this.reqId = reqId;
    }

    public byte getReqType() {
        return reqType;
    }

    public void setReqType(byte reqType) {
        this.reqType = reqType;
    }

    public short getReqDataNum() {
        return reqDataNum;
    }

    public void setReqDataNum(short reqDataNum) {
        this.reqDataNum = reqDataNum;
    }

    public short getReqDataLength() {
        return reqDataLength;
    }

    public void setReqDataLength(short reqDataLength) {
        this.reqDataLength = reqDataLength;
    }

    public ReqData5059[] getReqDataContext() {
        return reqDataContext;
    }

    public void setReqDataContext(ReqData5059[] reqDataContext) {
        this.reqDataContext = reqDataContext;
    }
}
