package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

/**
 * <AUTHOR>
 * @description t_profit_day 做T收益表DO
 * @date 2024/4/29 17:25
 */
public class TProfitDayDO extends BaseEntity {
    /**
     * 资金账号
     */
    private Integer bizDate;
    /**
     * 做T盈亏 （人民币）
     */
    private Double tProfit;

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Double getTProfit() {
        return tProfit;
    }

    public void setTProfit(Double tProfit) {
        this.tProfit = tProfit;
    }
}
