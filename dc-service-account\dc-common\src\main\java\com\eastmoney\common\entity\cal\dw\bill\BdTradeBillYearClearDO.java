package com.eastmoney.common.entity.cal.dw.bill;

import java.math.BigDecimal;

/**
 * @Project dc-service-account
 * @Description 全年清仓相关------ 2023年账单新增表,由数据中心提供
 * <AUTHOR>
 * @Date 2023/11/23 16:44
 * @Version 1.0
 */
public class BdTradeBillYearClearDO {

    /**
     * 全年累计清仓
     */
    private Long clearNum;
    /**
     * 全年获利清仓次数
     */
    private Long clearProfitNum;
    /**
     * 全年清仓获利盈利率
     */
    private BigDecimal clearProfitRate;
    /**
     * 全年赚钱最多的产品的最后一次清仓日期
     */
    private Integer clearMostProfitLDate;

    /**
     * 月收益金额最高的月份
     */
    private Integer profitBestMonthDate;

    /**
     * 扭亏为盈的人数占普通交易用户的百分比
     */
    private BigDecimal turnEarnPerc;


    /**
     * 清仓股平均持仓天数
     */
    private Long avgHoldDays;


    public Long getAvgHoldDays() {
        return avgHoldDays;
    }

    public void setAvgHoldDays(Long avgHoldDays) {
        this.avgHoldDays = avgHoldDays;
    }

    public Long getClearNum() {
        return clearNum;
    }

    public void setClearNum(Long clearNum) {
        this.clearNum = clearNum;
    }

    public Long getClearProfitNum() {
        return clearProfitNum;
    }

    public void setClearProfitNum(Long clearProfitNum) {
        this.clearProfitNum = clearProfitNum;
    }

    public BigDecimal getClearProfitRate() {
        return clearProfitRate;
    }

    public void setClearProfitRate(BigDecimal clearProfitRate) {
        this.clearProfitRate = clearProfitRate;
    }

    public Integer getClearMostProfitLDate() {
        return clearMostProfitLDate;
    }

    public void setClearMostProfitLDate(Integer clearMostProfitLDate) {
        this.clearMostProfitLDate = clearMostProfitLDate;
    }

    public Integer getProfitBestMonthDate() {
        return profitBestMonthDate;
    }

    public void setProfitBestMonthDate(Integer profitBestMonthDate) {
        this.profitBestMonthDate = profitBestMonthDate;
    }

    public BigDecimal getTurnEarnPerc() {
        return turnEarnPerc;
    }

    public void setTurnEarnPerc(BigDecimal turnEarnPerc) {
        this.turnEarnPerc = turnEarnPerc;
    }
}
