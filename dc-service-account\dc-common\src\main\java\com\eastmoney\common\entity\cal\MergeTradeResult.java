package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.LogAsset;

import java.util.ArrayList;
import java.util.List;

public class MergeTradeResult {
    private List<PositionProfit> stkProfits = new ArrayList<>();    //个股区间盈亏数据
    private List<LogAsset> stkTrades = new ArrayList<>();   //买入卖出统计
    private List<LogAsset> stkShifts = new ArrayList<>();   //转入转出统计
    private List<LogAsset> totalLogAsset = new ArrayList<>();//交割单总信息

    public List<LogAsset> getTotalLogAsset() {
        return totalLogAsset;
    }

    public void setTotalLogAsset(List<LogAsset> totalLogAsset) {
        this.totalLogAsset = totalLogAsset;
    }

    public List<PositionProfit> getStkProfits() {
        return stkProfits;
    }

    public void setStkProfits(List<PositionProfit> stkProfits) {
        this.stkProfits = stkProfits;
    }

    public List<LogAsset> getStkTrades() {
        return stkTrades;
    }

    public void setStkTrades(List<LogAsset> stkTrades) {
        this.stkTrades = stkTrades;
    }

    public List<LogAsset> getStkShifts() {
        return stkShifts;
    }

    public void setStkShifts(List<LogAsset> stkShifts) {
        this.stkShifts = stkShifts;
    }
}
