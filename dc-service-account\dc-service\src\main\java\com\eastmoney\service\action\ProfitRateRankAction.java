package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.ProfitHandler;
import com.eastmoney.service.handler.ProfitRateHandler;
import com.eastmoney.service.util.FunCodeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created on 2020/8/3-17:50.
 *
 * <AUTHOR>
 */
@Action
@Component
public class ProfitRateRankAction {
    @Autowired
    private ProfitHandler profitHandler;
    @Autowired
    private ProfitRateHandler profitRateHandler;

    //收益率排名信息
    @RequestMapping("/getProfitRateRankInfo")
    @FunCodeMapping(FunCodeConstants.GET_PROFIT_RATE_RANK_INFO)
    public Map getProfitRateRankInfo(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "unit"});
        return profitRateHandler.getProfitRateRankInfo(params);
    }

    //收益率分布列表
    @RequestMapping("/getProfitRateDistributeList")
    @FunCodeMapping("getProfitRateDistributeList")
    public Object getProfitRateDistributeList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"unit"});
        return profitHandler.getProfitRateDistributeList(params);
    }
}
