package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.BusinessDictMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.BusinessDict;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2021/2/4-17:26.
 *
 * <AUTHOR>
 */
@Service("businessDictDao")
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
public class BusinessDictDaoImpl extends BaseDao<BusinessDictMapper, BusinessDict, String> implements BusinessDictDao {
}
