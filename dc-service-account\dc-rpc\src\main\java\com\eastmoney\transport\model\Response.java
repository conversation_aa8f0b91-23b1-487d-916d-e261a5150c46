/*
 * Copyright 1999-2011 Alibaba Group.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.eastmoney.transport.model;

import org.apache.commons.lang3.StringUtils;

/**
 * Response
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class Response {
    
    /**
     * ok.
     */
    public static final byte OK   = 0;
    public static final byte TIMEOUT   = -1;
    public static final byte BAD_REQUEST   = -2;
    public static final byte BAD_RESPONSE   = -3;

    private String             id               ;
    private String           version;
    private byte             status           = OK;
    private String           errorMsg;
    private Object           result;

    public Response(){
    }

    public Response(String id){
        this.id = id;
    }

    public Response(String id, String version){
        this.id = id;
        this.version = version;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public byte getStatus() {
        return status;
    }

    public void setStatus(byte status) {
        this.status = status;
    }
    
    public Object getResult() {
        return result;
    }

    public void setResult(Object msg) {
        result = msg;
    }

    public String getErrorMessage() {
        return errorMsg;
    }

    public void setErrorMessage(String msg) {
        errorMsg = msg;
    }

    @Override
    public String toString() {
        String string = "Response [id=" + id + ", version=" + version + ", status=" + status
               + ", errorMsg=" + errorMsg;
        if (StringUtils.isBlank(errorMsg)) {
            string +=  ", message=" + message + "]";
        }else {
            string += "]";
        }
        return string;
    }

    private Message message;

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }
}