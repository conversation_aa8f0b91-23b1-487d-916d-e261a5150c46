package com.eastmoney.service.service.asset.concretesection;

import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.service.service.asset.AbstractAssetSectionService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created on 2020/8/13-14:33.
 *
 * <AUTHOR>
 */
@Service("assetSectionServiceAll")
public class AssetSectionServiceAllImpl extends AbstractAssetSectionService {

    @Override
    protected AssetHis getAssetStart(Map<String, Object> params, AssetHis assetNew) {
        AssetHis assetStart = new AssetHis();
        if (assetNew.getStartDate() != null && assetNew.getStartDate() <= CommConstants.START_DATE) {
            assetStart.setAsset(assetNew.getAssetInit());
        }
        return assetStart;
    }
}
