package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.sqlserver.OggStkPriceDao;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.common.entity.StkPrice;
import com.eastmoney.service.util.SpringConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/6
 */
@Service
public class StkPriceCacheService {
    private static Logger LOG = LoggerFactory.getLogger(StkPriceCacheService.class);
    @Autowired
    private OggStkPriceDao oggStkPriceDao;

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private CoreConfigService coreConfigService;

    private Map<String, Map<String, StkPrice>> stkPriceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("stkPrice-cache-flush-thread");
            return thread;
        });
        Runnable runnable = this::flushData;
        scheduledExecutorService.scheduleWithFixedDelay(runnable, 0, 1, TimeUnit.HOURS);
    }

    private void flushData() {
        LOG.info("开始刷新stkPrice缓存数据...");
        try {
            Map<String, Map<String, StkPrice>> allStkPriceMap = new HashMap<>();
            for (Integer i : nodeConfigService.getServerIds()) {
                Map<String, Object> params = new HashMap<>();
                params.put("serverId", i.toString());
                Map<String, StkPrice> stkPriceMap = oggStkPriceDao.getStkPriceMap(params);
                if (CollectionUtils.isEmpty(stkPriceMap)) {
                    continue;
                }
                allStkPriceMap.put(i.toString(), stkPriceMap);
            }
            this.stkPriceMap = allStkPriceMap;
            LOG.info("stkPrice数据加载完成，总数为{}...", allStkPriceMap.size());
        } catch (Exception e) {
            LOG.info("stkPrice数据加载异常...", e);
        }
    }

    public <T extends StkInfo> Map<String, StkPrice> getStkPrice(Long fundId, List<T> stkInfoList) {
        Map<String, StkPrice> stkPriceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(stkInfoList)) {
            return new HashMap<>();
        }
        try {
            String serverId = String.valueOf(coreConfigService.getServerId(fundId));
            if (!this.stkPriceMap.containsKey(serverId)) {
                return null;
            }
            Map<String, StkPrice> cacheMap = this.stkPriceMap.get(serverId);
            stkInfoList.stream()
                    .filter(o -> o.getMarket() != null && o.getStkCode() != null)
                    .map(o -> {
                        String key = String.join("-", o.getMarket().trim(), o.getStkCode().trim());
                        if (cacheMap.containsKey(key)) {
                            stkPriceMap.put(key, cacheMap.get(key));
                        }
                        return stkPriceMap;
                    }).collect(Collectors.toList());
            return stkPriceMap;
        } catch (Exception e) {
            LOG.error("获取柜台StkPrice缓存失败", e);
        }
        return new HashMap<>();
    }

    /**
     * 使用柜台数据更新持仓表
     *
     * @param fundId
     * @param stkInfoList
     * @return
     */
    public <T extends StkInfo> List<T> updateStkInfoList(Long fundId, List<T> stkInfoList) {
        Map<String, StkPrice> stkPriceMap = getStkPrice(fundId, stkInfoList);
        if (CollectionUtils.isEmpty(stkPriceMap)) {
            return stkInfoList;
        }
        stkInfoList.forEach(o -> {
            String key = String.join("-", o.getMarket().trim(), o.getStkCode().trim());
            if (stkPriceMap.containsKey(key)) {
                StkPrice stkPrice = stkPriceMap.get(key);
                o.setBondIntr(Objects.isNull(stkPrice.getBondIntr()) ? 0d : stkPrice.getBondIntr());
                o.setClosePrice(stkPrice.getClosePrice());
                o.setOpenPrice(stkPrice.getOpenPrice());
                o.setLastPrice(stkPrice.getLastPrice());
                o.setMtkCalFlag(stkPrice.getMtkCalFlag());
                o.setStkType(stkPrice.getStkType());
                o.setLofMoneyFlag(Objects.isNull(stkPrice.getLofMoneyFlag()) ? "0" : stkPrice.getLofMoneyFlag());
                o.setStkLevel(stkPrice.getStkLevel());
                o.setTicketPrice(stkPrice.getTicketPrice());
                o.setStkName(stkPrice.getStkName());
                o.setQuitDate(stkPrice.getQuitDate());
                o.setTrdId(stkPrice.getTrdId());
            }
        });
        return stkInfoList;
    }

    public Map<String, StkPrice> getStkPrice(String serverId) {
        return stkPriceMap.get(serverId);
    }
}
