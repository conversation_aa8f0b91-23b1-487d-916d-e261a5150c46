package com.eastmoney.accessor.datasource;

/**
 * Created on 2016/3/2
 *
 * <AUTHOR>
 */
public class DataSourceContextHolder {

    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>(); // 线程本地环境

    // 获取数据源类型 　　
    public static String getDataSourceType() {
        return contextHolder.get();
    }

    // 设置数据源类型
    public static void setDataSourceType(String dataSourceType) {
        contextHolder.set(dataSourceType);
    }

    // 清除数据源类型
    public static void clearDataSourceType() {
        contextHolder.remove();
    }

}
