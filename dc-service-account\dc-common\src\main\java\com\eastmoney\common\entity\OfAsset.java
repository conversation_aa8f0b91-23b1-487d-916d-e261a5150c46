package com.eastmoney.common.entity;

import com.eastmoney.common.util.DateUtil;

import java.util.Date;

/**
 * Created on 2016/3/1
 * 基金持仓
 *
 * <AUTHOR>
 */
public class OfAsset extends BaseEntity {
    //    private String eid;//系统物理主键
//    private String eiTime;//数据入库时间
//    private String euTime;//数据修改时间
//    private String resend_flag;//重发标记
//    private Integer serverId;//机器编码
    private Long custId;/*客户代码*/
    private Long fundId;/*资金帐号*/
    private String orgId;/*机构编码*/
    private String taAcc;/*基金帐号*/
    private String ofCode; //基金代码
    private String transAcc;/*交易帐号*/
    private Long taCode;/*注册登记公司内部代码*/
    private Double ofLastBal;/*基金昨日余额*/
    private Double ofBal;/*基金余额*/
    private Double ofAvl;//基金可用
    private Double ofTrdFrz;/*基金交易冻结数*/
    private Double ofLongFrz;/*基金长期冻结数*/
    private Double lastCost;/*基金昨日买入成本*/
    private Double currentCost;/*基金买入成本*/
    private Double marketValue;/*基金证券市值*/
    private String shareClass;/* 收费方式,'0' 前收费 '1' 后收费 '2'前端＋后端*/
    private Integer assetDate;//持仓日期

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public String getTaAcc() {
        return taAcc;
    }

    public void setTaAcc(String taAcc) {
        this.taAcc = taAcc.trim();
    }

    public String getOfCode() {
        return ofCode;
    }

    public void setOfCode(String ofCode) {
        this.ofCode = ofCode.trim();
    }

    public String getTransAcc() {
        return transAcc;
    }

    public void setTransAcc(String transAcc) {
        this.transAcc = transAcc.trim();
    }

    public Long getTaCode() {
        return taCode;
    }

    public void setTaCode(Long taCode) {
        this.taCode = taCode;
    }

    public Double getOfLastBal() {
        return ofLastBal;
    }

    public void setOfLastBal(Double ofLastBal) {
        this.ofLastBal = ofLastBal;
    }

    public Double getOfBal() {
        return ofBal;
    }

    public void setOfBal(Double ofBal) {
        this.ofBal = ofBal;
    }

    public Double getOfAvl() {
        return ofAvl;
    }

    public void setOfAvl(Double ofAvl) {
        this.ofAvl = ofAvl;
    }

    public Double getOfTrdFrz() {
        return ofTrdFrz;
    }

    public void setOfTrdFrz(Double ofTrdFrz) {
        this.ofTrdFrz = ofTrdFrz;
    }

    public Double getOfLongFrz() {
        return ofLongFrz;
    }

    public void setOfLongFrz(Double ofLongFrz) {
        this.ofLongFrz = ofLongFrz;
    }

    public Double getLastCost() {
        return lastCost;
    }

    public void setLastCost(Double lastCost) {
        this.lastCost = lastCost;
    }

    public Double getCurrentCost() {
        return currentCost;
    }

    public void setCurrentCost(Double currentCost) {
        this.currentCost = currentCost;
    }

    public Double getMarketValue() {
        return marketValue;
    }

    public void setMarketValue(Double marketValue) {
        this.marketValue = marketValue;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass.trim();
    }

    public Integer getAssetDate() {
        if (assetDate == null) {
            assetDate = Integer.parseInt(DateUtil.dateToStr(new Date(), DateUtil.yyyyMMdd));
        }
        return assetDate;
    }

    public void setAssetDate(Integer assetDate) {
        this.assetDate = assetDate;
    }
}
