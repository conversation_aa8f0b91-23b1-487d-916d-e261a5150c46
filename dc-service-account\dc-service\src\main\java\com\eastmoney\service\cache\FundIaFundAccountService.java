package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.fundia.FundIaFundInvestAccountDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class FundIaFundAccountService {
    private static Logger LOG = LoggerFactory.getLogger(FundIaFundAccountService.class);
    @Resource(name = "fundIaFundAccountCache")
    private LoadingCache<Long, Optional<Boolean>> fundIaFundAccountCache;
    @Autowired
    private FundIaFundInvestAccountDao fundIaFundInvestAccountDao;

    @Bean(name = "fundIaFundAccountCache")
    public LoadingCache<Long, Optional<Boolean>> fundIaFundAccountCache() {
        LoadingCache<Long, Optional<Boolean>> fundIaFundAccountCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(100000)
                .refreshAfterWrite(1800, TimeUnit.SECONDS)
                .build(new CacheLoader<Long, Optional<Boolean>>() {
                    @Override
                    public Optional<Boolean> load(Long fundId) throws Exception {
                        Boolean fundAccountExist = fundIaFundInvestAccountDao.fundInvestAccountExist(fundId);
                        return Optional.ofNullable(fundAccountExist);
                    }
                });
        return fundIaFundAccountCache;
    }

    @RedisCache(keyGenerator = "'jzjy_fundiafundaccount_' + #params.get('fundId')",expireSeconds = 1800)
    public Boolean getFundIaFundAccount(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        try {
            return fundIaFundAccountCache.get(fundId).orElse(false);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取最新资产失败", e);
        }
        return null;
    }
}
