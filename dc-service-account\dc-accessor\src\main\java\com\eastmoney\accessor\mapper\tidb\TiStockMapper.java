package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.Stock;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 *         Date：2016/11/3  9:53
 */
public interface TiStockMapper extends BaseMapper<Stock, Integer> {
    List<Stock> queryAllStock();

    Stock getStock(@Param(value = "stkCode") String stkCode, @Param(value = "market") String market);
}
