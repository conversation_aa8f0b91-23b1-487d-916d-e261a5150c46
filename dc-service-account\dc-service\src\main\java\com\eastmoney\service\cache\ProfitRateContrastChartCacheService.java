package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.ProfitRateContrastChartDao;
import com.eastmoney.common.entity.cal.ProfitRateContrastChart;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class ProfitRateContrastChartCacheService {
    private static Logger LOG = LoggerFactory.getLogger(ProfitRateContrastCacheService.class);
    @Resource(name = "profitRateContrastChartCache")
    private LoadingCache<String, Optional<List<ProfitRateContrastChart>>> profitRateContrastChartCache;
    @Autowired
    private ProfitRateContrastChartDao profitRateContrastChartDao;

    @Bean(name = "profitRateContrastChartCache")
    public LoadingCache<String, Optional<List<ProfitRateContrastChart>>> profitRateContrastChartCache() {
        LoadingCache<String, Optional<List<ProfitRateContrastChart>>> profitRateContrastChartCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(6)
                .maximumSize(10)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<List<ProfitRateContrastChart>>>() {
                    @Override
                    public Optional<List<ProfitRateContrastChart>> load(String key) throws Exception {
                        List<ProfitRateContrastChart> profitRateContrastCharts = null;
                        try {
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("unit", key);
                            profitRateContrastCharts = profitRateContrastChartDao.query(param);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(profitRateContrastCharts);
                    }
                });
        return profitRateContrastChartCache;
    }

    public List<ProfitRateContrastChart> getProfitRateContrastChart(String unit) {
        if(StringUtils.isEmpty(unit)){
            return new ArrayList<>();
        }
        try {
            return profitRateContrastChartCache.get(unit).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误！通过guava获取收益率参照报表失败{}", e.getMessage());
        }
        return null;
    }
}
