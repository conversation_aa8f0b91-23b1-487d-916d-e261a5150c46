package com.eastmoney.accessor.dao.tidb;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateStatDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitRateStatMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateStat;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by huachengqi on 2016/7/27.
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitRateStatDao")
public class TiProfitRateStatDaoImpl extends BaseDao<TiProfitRateStatMapper, ProfitRateStat, Long> implements ProfitRateStatDao {

}
