package com.eastmoney.service.service.profit.hgt;

import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.HgtReferRate;
import com.eastmoney.common.entity.StkAsset;
import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.common.entity.StkPrice;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.HgtReferRateService;
import com.eastmoney.service.cache.HgtTradeDateService;
import com.eastmoney.service.cache.HkProfitFlagCacheService;
import com.eastmoney.service.cache.StkPriceCacheService;
import com.eastmoney.service.service.quote.QuoteService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import com.eastmoney.service.util.BusinessUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 港股通假期收益计算服务
 * @date 2025/3/27 15:10
 */
@Service
public class HgtHolidayProfitServiceImpl implements HgtHolidayProfitService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HgtHolidayProfitServiceImpl.class);

    /**
     * 缓存
     */
    private final HkProfitFlagCacheService hkProfitFlagCacheService;
    private final HgtReferRateService hgtReferRateService;
    private final StkPriceCacheService stkPriceCacheService;
    private final HgtTradeDateService hgtTradeDateService;
    private final AssetNewService assetNewService;
    private final StkAssetService stkAssetService;
    /**
     * 行情
     */
    private final QuoteService quoteService;

    public HgtHolidayProfitServiceImpl(HkProfitFlagCacheService hkProfitFlagCacheService,
                                       HgtReferRateService hgtReferRateService, StkPriceCacheService stkPriceCacheService,
                                       HgtTradeDateService hgtTradeDateService, AssetNewService assetNewService,
                                       StkAssetService stkAssetService, QuoteService quoteService) {
        this.hkProfitFlagCacheService = hkProfitFlagCacheService;
        this.hgtReferRateService = hgtReferRateService;
        this.stkPriceCacheService = stkPriceCacheService;
        this.hgtTradeDateService = hgtTradeDateService;
        this.assetNewService = assetNewService;
        this.stkAssetService = stkAssetService;
        this.quoteService = quoteService;
    }

    @Override
    public double getHgtHolidayProfitWithTryCatch(final long fundId, final int accountBizDate) {
        try {
            return getHgtHolidayProfit(fundId, accountBizDate);
        } catch (Exception e) {
            LOGGER.error("获取港股通假期收益失败", e);
            return 0;
        }
    }

    @Override
    public double getHgtHolidayProfit(final long fundId, final int accountBizDate) {
        // 获取港股通持仓
        List<StkAsset> hgtStkAssetList = checkAndGetHgtStkAsset(fundId, accountBizDate);
        if (CollectionUtils.isEmpty(hgtStkAssetList)) {
            return 0;
        }

        // 获取港股通汇率（T-1日）
        HgtReferRate sHHgtReferRate = hgtReferRateService.getOggHgtReferRateService(fundId, MarketEnum.SH_HK.getValue());
        HgtReferRate sZHgtReferRate = hgtReferRateService.getOggHgtReferRateService(fundId, MarketEnum.SZ_HK.getValue());

        // 计算每个持仓的假期收益
        double hkProfit = 0;
        for (StkAsset stkAsset : hgtStkAssetList) {
            long stkQty = ArithUtil.addIgnoreNull(stkAsset.stkBal, stkAsset.stkBuySale, stkAsset.stkUnComeBuy,
                    stkAsset.stkUnComeSale == null ? null : -stkAsset.stkUnComeSale);
            if (stkQty <= 0) {
                continue;
            }

            double cnyPrice = getCnyPrice(stkAsset,
                    MarketEnum.SH_HK.getValue().equals(stkAsset.getMarket())
                            ? sHHgtReferRate.getSettRate()
                            : sZHgtReferRate.getSettRate());

            Double closeMktVal = ArithUtil.mul(cnyPrice, stkQty);
            Double openMktVal = stkAsset.mktVal;
            // 期末市值 - 期初市值
            Double singleHkProfit = ArithUtil.sub(closeMktVal, openMktVal);
            hkProfit = ArithUtil.add(hkProfit, singleHkProfit);
        }

        return hkProfit;
    }

    /**
     * 检查是否需要计算港股通假期收益，并返回上一交易日的港股通持仓
     *
     * @param fundId         资金账号 无修改
     * @param accountBizDate 账户分析清算日期 无修改
     * @return 港股通持仓
     */
    private List<StkAsset> checkAndGetHgtStkAsset(long fundId, int accountBizDate) {
        // 检查是否需计算港股通假期收益
        boolean isCalHkProfit = hkProfitFlagCacheService.getHkProfitFlag();
        if (!isCalHkProfit) {
            return Collections.emptyList();
        }

        // 获取最新资产
        AssetNew assetNew = assetNewService.getAssetInfo(Collections.singletonMap("fundId", fundId));
        if (Objects.isNull(assetNew)) {
            return Collections.emptyList();
        }

        // 获取港股通持仓信息
        List<StkAsset> allStkAssetList = stkAssetService.getStkAsset(fundId, accountBizDate, assetNew.getServerId());
        List<StkAsset> hgtStkAssetList = allStkAssetList.stream()
                .filter(stkAsset -> BusinessUtil.isGgtMarket(stkAsset.getMarket()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hgtStkAssetList)) {
            return Collections.emptyList();
        }
        return hgtStkAssetList;
    }

    /**
     * 获取人民币期末行情价
     *
     * @param stkAsset 持仓 无修改
     * @param settRate 买入汇率 无修改
     * @return 人民币期末行情价
     */
    private double getCnyPrice(StkAsset stkAsset, double settRate) {
        StkInfo stkInfo = new StkInfo(stkAsset.getStkCode(), stkAsset.getMarket());
        // 查询stkPrice缓存
        List<StkInfo> updatedStkInfo = stkPriceCacheService.updateStkInfoList(
                stkAsset.getFundId(), Collections.singletonList(stkInfo)
        );
        // 获取行情
        StkPrice stkPrice = quoteService.getStkPrice(updatedStkInfo.get(0));
        // 获取港币行情
        Double hkdPrice = getHkClosePrice(stkPrice);
        // 计算人民币行情
        return ArithUtil.mul(hkdPrice, settRate);
    }

    /**
     * 获取港股在A股开市前最后一个交易日的收盘价
     * <p>
     * 如果今天是交易日，返回收盘价，否则返回昨收价
     *
     * @param stkPrice 行情 无修改
     * @return 收盘价
     */
    @Nullable
    private Double getHkClosePrice(StkPrice stkPrice) {
        if (Boolean.TRUE.equals(hgtTradeDateService.todayIsMarket())) {
            return stkPrice.getClosePrice();
        } else {
            return stkPrice.getLastPrice();
        }
    }
}
