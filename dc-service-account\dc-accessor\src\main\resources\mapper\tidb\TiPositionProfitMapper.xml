<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiPositionProfitMapper">
    <resultMap id="BaseResultMap" type="as_positionProfit" >
        <result column="STKCODE" property="stkCode"/>
        <result column="TYPE" property="type"/>
        <result column="STARTDATE" property="startDate"/>
        <result column="ENDDATE" property="endDate"/>
        <result column="TOTAL_BUY" property="totalBuy"/>
        <result column="TOTAL_SALE" property="totalSale"/>
        <result column="PROFIT" property="profit"/>
        <result column="MARKET" property="market"/>
        <result column="TOTALIN" property="totalIn"/>
        <result column="TOTALOUT" property="totalOut"/>
        <result column="TOTALBUYNUM" property="totalBuyNum"/>
        <result column="TOTALSALENUM" property="totalSaleNum"/>
        <result column="TOTALINNUM" property="totalInNum"/>
        <result column="TOTALOUTNUM" property="totalOutNum"/>
        <result column="profitTotal" property="profitTotal"/>
        <result column="holdDays" property="holdDays"/>
        <result column="tradeTotal" property="tradeTotal"/>
        <result column="buyTimes" property="buyTimes"/>
        <result column="saleTimes" property="saleTimes"/>
        <result column="CLEAR_TIMES" property="clearTimes"/>
        <result column="PROFIT_RATE" property="profitRate"/>
        <result column="SUCCESS_RATE" property="successRate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID,STKCODE,ENDDATE,TOTAL_BUY,TOTAL_SALE,PROFIT,SERVERID,MARKET,
        TYPE,STARTDATE,TOTALBUYNUM,TOTALSALENUM,TOTALIN,TOTALOUT,TOTALINNUM,TOTALOUTNUM
    </sql>

    <!-- 获取所有盈亏持仓 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT<include refid="All_Column"/>
        FROM ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        <where>
            FUNDID = #{fundId}
            <if test="endDate != null">
                AND ENDDATE = #{endDate}
            </if>
            <if test="serverId != null">
                AND SERVERID = #{serverId}
            </if>
            <if test="queryStartDate != null">
                AND ENDDATE >= #{queryStartDate}
            </if>
            <if test="queryEndDate != null">
                AND ENDDATE <![CDATA[<=]]> #{queryEndDate}
            </if>
            <if test='profitFlag == "1"'>
                AND PROFIT > 0
            </if>
            <if test='profitFlag == "2"'>
                AND PROFIT <![CDATA[<]]> 0
            </if>
            order by ENDDATE DESC,stkcode asc
            <if test="pageSize != null and pageNo != null">
                <!--limit后的计算要放到dao层去做-->
                limit #{startNum}, #{pageSize}
            </if>
        </where>

    </select>

    <!-- 合并相同股票盈亏 -->
    <select id="getPositionMergeList" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT STKCODE,market,sum(ifnull(PROFIT,0)) profitTotal,sum(ifnull(HOLDDAYS,0)) holdDays,
            sum(ifnull(BUYTIMES,0)+ifnull(SALETIMES,0)+ifnull(INTIMES,0)+ifnull(OUTTIMES,0)) tradeTotal,count(1) as CLEAR_TIMES,min(ENDDATE) as ENDDATE
            FROM ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
            where FUNDID = #{fundId}
                AND (
                    (MARKET IN ('5', 'S') AND CLEARENDDATE BETWEEN #{startDate} and #{endDate})
                        OR
                    (MARKET NOT IN ('5', 'S') AND ENDDATE BETWEEN #{startDate} and #{endDate})
                )
            group by STKCODE,market
            ) a
        where 1=1
        <if test='profitFlag != null and profitFlag == "1"'>
            AND a.profitTotal > 0
        </if>
        <if test='profitFlag != null and profitFlag == "2"'>
            AND a.profitTotal <![CDATA[<]]> 0
        </if>
        <if test='sort != null'>
            ${sort}
        </if>
        <if test="pageSize != null and startNum != null">
            limit #{startNum}, #{pageSize}
        </if>

    </select>

    <!-- 查询单个股票盈亏 -->
    <select id="getSinglePositionProfit" resultMap="BaseResultMap">
        select STKCODE,market,PROFIT,PROFIT_RATE, TRADETAXFEE ,
        CLEARSTARTDATE AS STARTDATE,
        CLEARENDDATE AS ENDDATE,
        HOLDDAYS,IFNULL(BUYTIMES,0) + IFNULL(SALETIMES,0) + IFNULL(INTIMES,0) + IFNULL(OUTTIMES,0) tradeTotal
        from ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    and stkcode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and stkcode = #{stkCode}
                </otherwise>
            </choose>
            and market = #{market}
            <if test="startDate != null and endDate!=null">
                AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
            </if>
            and CALFLAG=1
        order by ENDDATE desc
    </select>


    <!-- 查询单个股票盈亏 -->
    <select id="getHoldSinglePositionProfit" resultMap="BaseResultMap">
        select a.STKCODE, a.market, a.tradetaxfee,
               CASE
                   WHEN  MARKET IN ('5', 'S') THEN CLEARSTARTDATE
                   ELSE STARTDATE
                   END as STARTDATE
        from ATCENTER.POSITION_PROFIT a use index(pk_lg_position_profit)
        where a.fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    and a.stkcode in
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and a.stkcode = #{stkCode}
                    </if>
                </otherwise>
            </choose>
          and a.market = #{market}
          and a.ENDDATE is null
        order by a.startDate desc
        limit 1
    </select>



    <!-- 新版已清仓股票列表 根据时间查询/单只股票查询  -->
    <select id="getClearPositionProfitListByPage" resultMap="BaseResultMap">
        select FUNDID,STKCODE,
        CLEARSTARTDATE STARTDATE,
        CLEARENDDATE ENDDATE,
        PROFIT,PROFIT_RATE,MARKET,HOLDDAYS
        FROM ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where FUNDID = #{fundId}
            AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
            and CALFLAG=1
        <if test="market != null">
            and MARKET=#{market}
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test='sort != null'>
            ${sort}
        </if>
        <if test="pageSize != null and startNum != null">
            limit #{startNum}, #{pageSize}
        </if>
    </select>


    <!-- 查询单支股票汇总信息 -->
    <select id="getSinglePositionMergeProfit" resultMap="BaseResultMap">
        SELECT
            STKCODE,
            MARKET,
            SUM(TOTAL_BUY) as TOTAL_BUY,
            SUM(TOTALIN) as TOTALIN,
            SUM(PROFIT) AS PROFIT,
            SUM(HOLDDAYS) AS HOLDDAYS,
            MIN(STARTDATE) AS STARTDATE,
            MAX(ENDDATE) AS ENDDATE,
            COUNT(FLAG) AS CLEAR_TIMES,
            SUM(FLAG)/ COUNT(FLAG) AS  SUCCESS_RATE,
            AVG(IFNULL(PROFIT_RATE, 0)) AS PROFIT_RATE,
            SUM(TRADETOTAL) AS TRADETOTAL,
            SUM(TRADETAXFEE) AS TRADETAXFEE
        FROM
            (
            SELECT
                #{stkCode} STKCODE,
                TOTAL_BUY,
                TOTALIN,
                MARKET,
                PROFIT,
                CLEARSTARTDATE STARTDATE,
                CLEARENDDATE ENDDATE,
                HOLDDAYS,
                PROFIT_RATE,
                IFNULL(BUYTIMES,0) + IFNULL(SALETIMES,0) + IFNULL(INTIMES,0) + IFNULL(OUTTIMES,0) TRADETOTAL,
                IFNULL(TRADETAXFEE,0) AS TRADETAXFEE,
                CASE
                    WHEN PROFIT>0 THEN 1
                    ELSE 0
                END FLAG
            FROM
                ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
                where FUNDID = #{fundId}
                <choose>
                    <when test="stkCodeList != null and stkCodeList.size() > 0">
                        and STKCODE IN
                        <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        and STKCODE = #{stkCode}
                    </otherwise>
                </choose>
                and market = #{market} and CALFLAG=1
                <if test="startDate != null and endDate!=null">
                    AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
                </if>
            ) t
        GROUP BY
            STKCODE,MARKET
    </select>

    <select id="getOpenPositionList" resultMap="BaseResultMap">
        select MARKET, STKCODE, MAX(STARTDATE) STARTDATE
        from ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where fundId = #{fundId}
        and calflag = 0
        and ENDDATE is null
        <if test='stkCode != null'>
            and stkcode = #{stkCode}
        </if>
        <if test='market != null'>
            and market = #{market}
        </if>
        <if test='type != null'>
            and "TYPE" = #{type}
        </if>
        group by market, stkcode
    </select>

    <select id="getClearPositionList" resultType="as_positionProfitBO">
        select distinct MARKET, STKCODE
        from ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where fundId = #{fundId}
        and calflag = 1
        and ENDDATE is not null
        order by stkcode
    </select>

    <select id="getClearPositionSummary" resultMap="BaseResultMap">
        select count(1) as CLEAR_TIMES, sum(profit) as PROFITTOTAL
        FROM ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where FUNDID = #{fundId}
        AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
        AND CALFLAG=1
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND stkCode IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    and stkCode = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test="market != null">
            and market = #{market}
        </if>
    </select>


    <select id="getDiagnosePositionSection" resultMap="BaseResultMap">
        SELECT count(1) as CLEAR_TIMES, sum(holddays) as HOLDDAYS
        FROM ATCENTER.POSITION_PROFIT use index(pk_lg_position_profit)
        where FUNDID = #{fundId}
        AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
        AND CALFLAG=1
    </select>

</mapper>