package com.eastmoney.service.service.profit.list;

import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.profit.base.SecProfitService;
import com.eastmoney.service.service.profit.base.SecProfitServiceSettleImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.eastmoney.accessor.enums.ProfitSectionStatEnum.*;

/**
 * 个股日收益明细
 * <AUTHOR>
 * @create 2024/3/17
 */
@Service("secProfitDayListServiceFacade")
public class SecProfitDayListServiceFacade {

    @Resource(name = "secProfitServiceSettle")
    private SecProfitServiceSettleImpl secProfitServiceSettle;
    @Resource(name = "secProfitServiceRealTime")
    private SecProfitService secProfitServiceRealTime;
    @Resource(name = "secProfitServiceTemp")
    private SecProfitService secProfitServiceTemp;

    /**
     * 查询实时个股日收益明细
     * @param params
     * @return
     */
    public List<SecProfitDayDO> getSecRealTimeProfitDayList(Map<String, Object> params) {
        List<SecProfitDayDO> result = new ArrayList<>();

        // 计算实时收益日期
        Integer realBizDate = CommonUtil.convert(params.getOrDefault("realBizDate", 0), Integer.class);
        if (realBizDate == 0) {
            return result;
        }

        List<SecProfitDayDO> secProfitDayTemp = secProfitServiceTemp.getSecProfitDayByRange(params);
        List<SecProfitDayDO> secProfitDayRealTime = null;
        if (CollectionUtils.isEmpty(secProfitDayTemp) || secProfitDayTemp.get(0).getBizDate() < realBizDate) {
            secProfitDayRealTime = secProfitServiceRealTime.getSecProfitDayByRange(params);
            secProfitDayRealTime.forEach(s -> s.setBizDate(realBizDate));
        }

        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        filterProfitDayByBizDate(secProfitDayTemp, startDate, endDate, result);
        filterProfitDayByBizDate(secProfitDayRealTime, startDate, endDate, result);

        return result;
    }

    /**
     * 查询清算后的个股日收益明细
     *
     * @param params
     * @return
     */
    public List<SecProfitDayDO> getSecSettleProfitDayList(Map<String, Object> params) {
        String unit = CommonUtil.convert(params.get("unit"), String.class);

        if (M.getType().equals(unit)) {
            return secProfitServiceSettle.getSecProfitDay(params);
        } else {
            return secProfitServiceSettle.getSecProfitDayByRange(params);
        }
    }

    /**
     * 根据日期范围过滤结果集
     * @param secProfitDays
     * @param startDate
     * @param endDate
     * @param result
     */
    private void filterProfitDayByBizDate(List<SecProfitDayDO> secProfitDays, Integer startDate, Integer endDate,
                                          List<SecProfitDayDO> result) {
        if (CollectionUtils.isEmpty(secProfitDays)) {
            return;
        }

        List<SecProfitDayDO> secProfitDayFileter = secProfitDays.stream()
                .filter(s -> s.getProfit() != null && s.getBizDate() >= startDate && s.getBizDate() <= endDate)
                .peek(secProfitDayDO -> secProfitDayDO.setHoldFlag(1))
                .collect(Collectors.toList());
        result.addAll(secProfitDayFileter);
    }

}
