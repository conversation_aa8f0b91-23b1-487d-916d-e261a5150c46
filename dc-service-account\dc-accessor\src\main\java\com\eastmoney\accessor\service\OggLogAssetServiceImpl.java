package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.sqlserver.OggLogAssetDao;
import com.eastmoney.common.entity.LogAsset;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
@Service
public class OggLogAssetServiceImpl implements OggLogAssetService {
    @Autowired
    private OggLogAssetDao oggLogAssetDao;


    @Override
    public List<LogAsset> selectTransferAmt(Map<String, Object> params) {
        return oggLogAssetDao.selectTransferAmt(params);
    }

    @Override
    public List<LogAsset> getRealTimeLogassetList(Map<String, Object> params) {
        return oggLogAssetDao.getRealTimeLogassetList(params);
    }
}
