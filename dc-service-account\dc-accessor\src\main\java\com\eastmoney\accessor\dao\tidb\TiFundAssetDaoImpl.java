package com.eastmoney.accessor.dao.tidb;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.FundAssetDao;
import com.eastmoney.accessor.mapper.tidb.TiFundAssetMapper;

import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.FundAsset;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by sunyuncai on 2017/2/8
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("fundAssetDao")
public class TiFundAssetDaoImpl extends BaseDao<TiFundAssetMapper, FundAsset, Long> implements FundAssetDao {

}
