dependencies {
    implementation rootProject.ext.dependencies['commons-io']
    implementation rootProject.ext.dependencies['commons-math3']
    implementation rootProject.ext.dependencies['httpclient']
    implementation rootProject.ext.dependencies['mail']
}

jar {
    manifest {
        attributes 'Manifest-Version': '1.0'
        attributes 'Created-By': 'Gradle'
        attributes 'Class-Path': ". config/ libs/" + configurations.runtimeClasspath.collect { it.name }.join(' libs/')
    }
}
