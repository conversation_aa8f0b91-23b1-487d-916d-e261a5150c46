<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggMixedConfigMapper">
    <resultMap id="MixedConfig" type="com.eastmoney.accessor.model.MixedConfig"></resultMap>

    <select id="getMixedConfig" resultMap="MixedConfig">
        select * from run.dbo.mixedconfig with (nolock) where paraid = #{paraId}
    </select>
</mapper>