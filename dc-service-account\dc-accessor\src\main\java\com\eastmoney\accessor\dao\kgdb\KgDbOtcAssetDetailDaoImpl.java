package com.eastmoney.accessor.dao.kgdb;

import com.eastmoney.accessor.mapper.otckg.KgdbOtcAssetDetailMapper;
import com.eastmoney.common.entity.OtcAssetDetail;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-02 17:52
 */
@Service("kgdbOtcAssetDetailDao")
public class KgDbOtcAssetDetailDaoImpl implements OtcAssetDetailDao {

    @Resource(name = "kgdbOtcAssetDetailMapper")
    private KgdbOtcAssetDetailMapper kgdbOtcAssetDetailMapper;

    @Override
    public Map<String, Double> getRealTimeOtcAsset(Map<String, Object> params) {
        //OTC 资产接口通过币种保存值 key=moneyType,value=OTC资产
        Map<String, Double> otcAssetMap = new HashMap<>();
        List<OtcAssetDetail> list = kgdbOtcAssetDetailMapper.getRealTimeOtcAsset(params);
        for (OtcAssetDetail otcAssetDetail : list) {
            otcAssetMap.put(otcAssetDetail.getCurrency(), otcAssetDetail.getOtcAsset());
        }
        return otcAssetMap;
    }
}
