package com.eastmoney.common.entity;

import java.util.List;
import java.util.Objects;

/**
 * Created by sunyuncai on 2016/10/26.
 * 持仓信息
 */
public class PositionInfo extends StkInfo {
    public String postStr;//定位串
    public Long custId;//客户代码
    public String secuId;//股东代码
    public int orgId;//机构编码
    public Double costPrice;//成本价格
    public Double costPriceEx;//成本价格扩展
    public Double mktVal;//最新市值
    public Double profitCost;//参考成本
    public Double profitPrice;//参考成本价
    public Double stkBuy;//股份买入解冻
    public Double stkSale;//股份卖出冻结
    public Double stkFrz;//冻结数量
    public Double stkTrdFrz;//买入申赎差
    public Double stkTrdUnFrz;//申赎数量
    public Double stkDiff;//可申赎数量
    public String priceFlag;
    public Double income;//累计盈亏
    public Double proIncome;//参考盈亏
    public double profitRate;//盈亏比例
    public String publishName; //行业分类名称
    public Double price; //价格
    public boolean isClear = false;
    public Double dayProfit;//日收益
    public Double dayProfitRate;//日收益率

    /**
     * 持仓开始日期
     */
    private Integer startDate;
    /**
     * 持仓天数
     */
    private Integer holdDays;
    /**
     * 标签
     */
    private List<String> labels;
    /**
     * 仓位
     */
    private Double positionRate;

    /**
     * 转换920代码
     */
    private String corResCode;

    /******* PCMP-10616646 【柜台配合】港股通当日收益计算优化 start *******/
    public Long totalBuyQty;    // 当日累计买入数量，港股通收益使用
    public Double totalBuyAmt;  // 当日累计买入实时清算金额，港股通收益使用
    public Long totalSaleQty;   // 当日累计卖出数量，港股通收益使用
    public Double totalSaleAmt; // 当日累计卖出实时清算金额，港股通收益使用
    /******* PCMP-10616646 【柜台配合】港股通当日收益计算优化 end *******/

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getPublishName() {
        return publishName;
    }

    public void setPublishName(String publishName) {
        this.publishName = publishName;
    }

    public String getPostStr() {
        return postStr;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public int getOrgId() {
        return orgId;
    }

    public void setOrgId(int orgId) {
        this.orgId = orgId;
    }

    public Double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    }

    public Double getCostPriceEx() {
        return costPriceEx;
    }

    public void setCostPriceEx(Double costPriceEx) {
        this.costPriceEx = costPriceEx;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    public Double getProfitCost() {
        return profitCost;
    }

    public void setProfitCost(Double profitCost) {
        this.profitCost = profitCost;
    }

    public Double getProfitPrice() {
        return profitPrice;
    }

    public void setProfitPrice(Double profitPrice) {
        this.profitPrice = profitPrice;
    }

    public Double getStkBuy() {
        return stkBuy;
    }

    public void setStkBuy(Double stkBuy) {
        this.stkBuy = stkBuy;
    }

    public Double getStkSale() {
        return stkSale;
    }

    public void setStkSale(Double stkSale) {
        this.stkSale = stkSale;
    }

    public Double getStkFrz() {
        return stkFrz;
    }

    public void setStkFrz(Double stkFrz) {
        this.stkFrz = stkFrz;
    }

    public Double getStkTrdFrz() {
        return stkTrdFrz;
    }

    public void setStkTrdFrz(Double stkTrdFrz) {
        this.stkTrdFrz = stkTrdFrz;
    }

    public Double getStkTrdUnFrz() {
        return stkTrdUnFrz;
    }

    public void setStkTrdUnFrz(Double stkTrdUnFrz) {
        this.stkTrdUnFrz = stkTrdUnFrz;
    }

    public Double getStkDiff() {
        return stkDiff;
    }

    public void setStkDiff(Double stkDiff) {
        this.stkDiff = stkDiff;
    }

    public String getPriceFlag() {
        return priceFlag;
    }

    public void setPriceFlag(String priceFlag) {
        this.priceFlag = priceFlag;
    }

    public Double getIncome() {
        return income;
    }

    public void setIncome(Double income) {
        this.income = income;
    }

    public Double getProIncome() {
        return proIncome;
    }

    public void setProIncome(Double proIncome) {
        this.proIncome = proIncome;
    }

    public double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(double profitRate) {
        this.profitRate = profitRate;
    }

    public boolean isClear() {
        return isClear;
    }

    public void setClear(boolean clear) {
        isClear = clear;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(Integer holdDays) {
        this.holdDays = holdDays;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    public Double getPositionRate() {
        return positionRate;
    }

    public void setPositionRate(Double positionRate) {
        this.positionRate = positionRate;
    }

    public Double getDayProfit() {
        return dayProfit;
    }

    public void setDayProfit(Double dayProfit) {
        this.dayProfit = dayProfit;
    }

    public Double getDayProfitRate() {
        return dayProfitRate;
    }

    public void setDayProfitRate(Double dayProfitRate) {
        this.dayProfitRate = dayProfitRate;
    }

    public String getCorResCode() {
        return corResCode;
    }

    public void setCorResCode(String corResCode) {
        this.corResCode = corResCode;
    }

    public Long getTotalBuyQty() {
        return totalBuyQty;
    }

    public void setTotalBuyQty(Long totalBuyQty) {
        this.totalBuyQty = totalBuyQty;
    }

    public Double getTotalBuyAmt() {
        return totalBuyAmt;
    }

    public void setTotalBuyAmt(Double totalBuyAmt) {
        this.totalBuyAmt = totalBuyAmt;
    }

    public Long getTotalSaleQty() {
        return totalSaleQty;
    }

    public void setTotalSaleQty(Long totalSaleQty) {
        this.totalSaleQty = totalSaleQty;
    }

    public Double getTotalSaleAmt() {
        return totalSaleAmt;
    }

    public void setTotalSaleAmt(Double totalSaleAmt) {
        this.totalSaleAmt = totalSaleAmt;
    }

    public static PositionInfo of(PositionInfo object) {
        PositionInfo item = new PositionInfo();
        if (Objects.isNull(object)) {
            return item;
        }

        item.setFundId(object.getFundId());
        item.setOrgId(object.getOrgId());
        item.setSecuId(object.getSecuId());
        item.setMoneyType(object.getMoneyType());
        item.setMarket(object.getMarket());
        item.setPostStr(object.getPostStr());
        item.setStkCode(object.getStkCode());
        item.setStkType(object.getStkType());
        item.setMtkCalFlag(object.getMtkCalFlag());
        item.setBondIntr(object.getBondIntr());
        item.setTicketPrice(object.getTicketPrice());
        item.setClosePrice(object.getClosePrice());
        item.setOpenPrice(object.getOpenPrice());
        item.setLastPrice(object.getLastPrice());
        item.setLofMoneyFlag(object.getLofMoneyFlag());
        item.setStkLevel(object.getStkLevel());
        item.setSettRate(object.getSettRate());
        item.setSaleSettRate(object.getSaleSettRate());
        item.setStkBal(object.getStkBal());
        item.setStkAvl(object.getStkAvl());
        item.setBuyCost(object.getBuyCost());
        item.setStkUnComeBuy(object.getStkUnComeBuy());
        item.setStkUnComeSale(object.getStkUnComeSale());
        item.setMktVal(object.getMktVal());
        item.setProfitCost(object.getProfitCost());
        item.setStkBuy(object.getStkBuy());
        item.setStkSale(object.getStkSale());
        item.setStkFrz(object.getStkFrz());
        item.setStkTrdFrz(object.getStkTrdFrz());
        item.setStkTrdUnFrz(object.getStkTrdUnFrz());
        item.setStkDiff(object.getStkDiff());
        item.setStkBuySale(object.getStkBuySale());
        item.setStkQty(object.getStkQty());
        item.setQuitDate(object.getQuitDate());
        item.setTotalBuyAmt(object.getTotalBuyAmt());
        item.setTotalSaleAmt(object.getTotalSaleAmt());
        item.setTotalBuyQty(object.getTotalBuyQty());
        item.setTotalSaleQty(object.getTotalSaleQty());
        return item;
    }

    public static PositionInfo of(PosDBItem object) {
        PositionInfo item = new PositionInfo();
        if (Objects.isNull(object)) {
            return item;
        }

        item.setFundId(object.getFundId());
        item.setOrgId(Integer.parseInt(object.getOrgId()));
        item.setSecuId(object.getSecuId());
        item.setMoneyType(object.getMoneyType());
        item.setMarket(object.getMarket());
        item.setPostStr(object.getPostStr());
        item.setStkCode(object.getStkCode());
        item.setStkType(object.getStkType());
        item.setMtkCalFlag(object.getMtkCalFlag());
        item.setBondIntr(object.getBondIntr());
        item.setTicketPrice(object.getTicketPrice());
        item.setClosePrice(object.getClosePrice());
        item.setOpenPrice(object.getOpenPrice());
        item.setLastPrice(object.getLastPrice());
        item.setLofMoneyFlag(object.getLofMoneyFlag());
        item.setStkLevel(object.getStkLevel());
        item.setSettRate(object.getSettRate());
        item.setSaleSettRate(object.getSaleSettRate());
        item.setStkBal(object.getStkBal());
        item.setStkAvl(object.getStkAvl());
        item.setBuyCost(object.getBuyCost());
        item.setStkUnComeBuy(object.getStkUnComeBuy());
        item.setStkUnComeSale(object.getStkUnComeSale());
        item.setMktVal(object.getMktVal());
        item.setProfitCost(object.getProfitCost());
        item.setStkBuy(object.getStkBuy());
        item.setStkSale(object.getStkSale());
        item.setStkFrz(object.getStkFrz());
        item.setStkTrdFrz(object.getStkTrdFrz());
        item.setStkTrdUnFrz(object.getStkTrdUnFrz());
        item.setStkDiff(object.getStkDiff());
        item.setStkBuySale(object.getStkBuySale());
        item.setStkQty(object.getStkQty());
        item.setQuitDate(object.getQuitDate());
        item.setTotalBuyAmt(object.getTotalBuyAmt());
        item.setTotalSaleAmt(object.getTotalSaleAmt());
        item.setTotalBuyQty(object.getTotalBuyQty());
        item.setTotalSaleQty(object.getTotalSaleQty());
        return item;
    }

}
