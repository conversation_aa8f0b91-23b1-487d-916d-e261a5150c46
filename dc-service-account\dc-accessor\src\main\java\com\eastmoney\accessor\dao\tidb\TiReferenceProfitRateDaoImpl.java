package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ReferenceProfitRateDao;
import com.eastmoney.accessor.mapper.tidb.TiReferenceProfitRateMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ReferenceProfitRate;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by Administrator on 2017/4/14.
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("referenceProfitRateDao")
public class TiReferenceProfitRateDaoImpl extends BaseDao<TiReferenceProfitRateMapper,ReferenceProfitRate,Long> implements ReferenceProfitRateDao {
}
