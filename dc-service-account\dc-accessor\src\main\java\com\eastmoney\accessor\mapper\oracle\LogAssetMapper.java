package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.common.entity.LogAsset;
import com.eastmoney.accessor.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 */
@Repository("oraLogAssetMapper")
public interface LogAssetMapper extends BaseMapper<LogAsset, Integer> {
    /**
     * 单客户获取交割单的佣金、交易额
     * @param params
     * @return
     */
    List<Object> getLogAssetSum(Map<String, Object> params);

    /**
     * 根据用户一只股票一个区间的所有买卖信息
     * @param params
     * @return
     */
    List<LogAsset> getStkTradeList(Map<String, Object> params);

    /**
     * 查询用户单只股票转入，转出信息
     * @param params
     * @return
     */
    List<LogAsset> getStkShiftList(Map<String, Object> params);


    List<LogAsset> getOtherDetailList(Map<String, Object> params);

    /**
     * 分页查询 positionShareChange 交割单的佣金、交易额
     * @param params
     * @return
     */
    List<LogAsset> getTradeShiftList(Map<String, Object> params);

    List<LogAsset> getBSTradeList(Map<String,Object> params);
}
