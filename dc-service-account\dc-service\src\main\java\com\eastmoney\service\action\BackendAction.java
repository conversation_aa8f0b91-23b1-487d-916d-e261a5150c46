package com.eastmoney.service.action;

import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.service.cache.DigestNameCache;
import com.eastmoney.service.cache.LocalAlterCacheInitializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 后台类接口，仅支持手动调用，不对外
 * <AUTHOR>
 * @create 2024/11/4
 */
@Action
@Component
public class BackendAction {
    @Autowired
    private DigestNameCache digestNameCache;
    @Autowired
    private TpseStSechyreDao tpseStSechyreDao;
    @Autowired
    LocalAlterCacheInitializer initializer;

    /**
     * digestName缓存刷新
     * @param params
     */
    @RequestMapping("/flushDigestName")
    public void flushDigestName(Map<String, Object> params) {
        digestNameCache.flushData();
    }

    /**
     * 行业类别信息刷新
     * @param params
     */
    @RequestMapping("/flushTpseStSechyre")
    public void flushTpseStSechyre(Map<String, Object> params) {
        tpseStSechyreDao.loadTpseStSechyreMap();
    }

    /**
     * 代码变更信息刷新
     */
    @RequestMapping("/flushCodeAlterCache")
    public void flushCodeAlterCache(Map<String, Object> params) {
        initializer.flushDate();
    }

}
