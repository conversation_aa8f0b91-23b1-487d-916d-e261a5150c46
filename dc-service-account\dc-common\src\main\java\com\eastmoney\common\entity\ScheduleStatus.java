package com.eastmoney.common.entity;

/**
 * Created on 2016/3/3
 * 定时任务状态
 *
 * <AUTHOR>
 */
public class ScheduleStatus extends BaseEntity {
    //    private Long eid;//系统物理主键
//    private Date eiTime;//数据入库时间
//    private Date euTime;//数据修改时间
    private String schedule_name;//定时任务名称
    private String schedule_name_like;//用于数据库查询，任务名称的模糊查询
    private Integer status;//状态(详情请参考StatusEnum 0-初始化 1-运行中 2-完成 3-超时中断)
    private Long position;//位置
    private Integer bizDate;//位置
    private String sys_flag;//所属系统标识

    public ScheduleStatus(String sys_flag) {
        this.sys_flag = sys_flag.trim();
    }

    public ScheduleStatus(){

    }

    private Integer codisBizeDate;//codis同步任务日期 因sql-tsync->oracle 用不到bizDate作为限定条件，但又会传入bizDate，因此需要另外设计codisBizDate

    public String getSchedule_name() {
        return schedule_name;
    }

    public void setSchedule_name(String schedule_name) {
        this.schedule_name = schedule_name.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getPosition() {
        return position;
    }

    public void setPosition(Long position) {
        this.position = position;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public void setSchedule_name_like(String schedule_name_like) {
        this.schedule_name_like = schedule_name_like.trim();
    }

    public String getSchedule_name_like() {

        return schedule_name_like;
    }

    public Integer getCodisBizeDate() {
        return codisBizeDate;
    }

    public void setCodisBizeDate(Integer codisBizeDate) {
        this.codisBizeDate = codisBizeDate;
    }

    public String getSys_flag() {
        return sys_flag;
    }

    public void setSys_flag(String sys_flag) {
        this.sys_flag = sys_flag.trim();
    }
}
