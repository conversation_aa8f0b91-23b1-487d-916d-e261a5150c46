package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.StockCodeAlter;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
public interface StockCodeAlterDao extends IBaseDao<StockCodeAlter, Integer> {


    /**
     * 查询更换过的股票代码
     * @param params
     * @return
     */
    public List<StockCodeAlter> getStockCodeAlterList(Map<String, Object> params);
}
