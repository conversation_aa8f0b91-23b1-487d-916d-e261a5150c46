package com.eastmoney.common.model;

import java.util.Date;

/**
 * Created by sunyuncai on 2017/8/22.
 */
public class ProfitInfo {
    public double profitTotal;
    public double profitRateTotal;
    private Integer bizDate;
    private Integer startDate;
    private Date updateTime;

    // 港股通假期收益
    private Double hkProfit;

    /**
     * 没有清算数据
     */
    private Boolean noInit;

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public double getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public double getProfitRateTotal() {
        return profitRateTotal;
    }

    public void setProfitRateTotal(double profitRateTotal) {
        this.profitRateTotal = profitRateTotal;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Double getHkProfit() {
        return hkProfit;
    }

    public void setHkProfit(Double hkProfit) {
        this.hkProfit = hkProfit;
    }

    public Boolean getNoInit() {
        return noInit;
    }

    public void setNoInit(Boolean noInit) {
        this.noInit = noInit;
    }
}
