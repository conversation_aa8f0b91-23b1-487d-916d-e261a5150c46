package com.eastmoney.accessor.enums;

/**
 * 汇率类型枚举
 * <AUTHOR>
 * @create 2022/11/20
 */
public enum ExchangeRateTypeEnum {

    BUY_SETT("001", "买入结算汇率"),
    SELL_SETT("002", "卖出结算汇率");
    /**
     * 汇率类型
     */
    private final String type;

    private final String remark;

    ExchangeRateTypeEnum(String type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public String getRemark() {
        return remark;
    }
}
