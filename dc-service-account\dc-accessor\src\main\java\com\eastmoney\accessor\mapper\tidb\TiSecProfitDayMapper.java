package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 个股日收益明细mapper
 * <AUTHOR>
 * @create 2024/3/7
 */
@Repository
public interface TiSecProfitDayMapper extends BaseMapper<SecProfitDayDO,Integer> {

    /**
     * 查询某天个股日收益明细
     * @param params
     * @return
     */
    List<SecProfitDayDO> getSecProfitDay(Map<String, Object> params);

    /**
     * 查询某区间个股日收益明细
     * @param params
     * @return
     */
    List<SecProfitDayDO> getSecProfitDayByRange(Map<String, Object> params);

    /**
     * 查询账户级别现金收益
     * @param params
     * @return
     */
    Double getCashProfit(Map<String, Object> params);

    /**
     * 查询总条数
     * @param params
     * @return
     */
    Integer selectCountByCondition(Map<String, Object> params);
}
