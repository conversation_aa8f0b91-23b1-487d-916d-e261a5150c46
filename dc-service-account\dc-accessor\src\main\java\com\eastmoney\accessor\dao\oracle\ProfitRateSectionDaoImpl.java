package com.eastmoney.accessor.dao.oracle;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateSectionMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateSection;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyongyong on 2016/7/20.
 *
 * 增加updateRank,mergeIntoProfitRate
 * update by huachengqi on 2016/7/27.
 *
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitRateSectionDao")
public class ProfitRateSectionDaoImpl extends BaseDao<ProfitRateSectionMapper, ProfitRateSection, Long> implements ProfitRateSectionDao {

}
