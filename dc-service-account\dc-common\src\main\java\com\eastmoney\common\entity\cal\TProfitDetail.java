package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.Match;
import com.eastmoney.common.util.CommConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 10:41
 */
public class TProfitDetail extends TSecProfitDayDO {

    private List<Match> tMatchList;

    public static List<TProfitDetail> buildList(List<Match> tMatchList, List<TSecProfitDayDO> tSecProfitDayList) {
        Map<String, List<Match>> tMatchMap = tMatchList
                .stream().collect(Collectors.groupingBy(t ->
                        StringUtils.joinWith("-", t.getMarket(), t.getStkCode())));
        return tSecProfitDayList.stream()
                .map(tSecProfitDayDO -> buildTProfitDetail(tMatchMap, tSecProfitDayDO))
                .collect(Collectors.toList());
    }

    private static TProfitDetail buildTProfitDetail(Map<String, List<Match>> logAssetMap, TSecProfitDayDO tSecProfitDayDO) {
        TProfitDetail tProfitDetail = new TProfitDetail();
        String key = StringUtils.joinWith("-", tSecProfitDayDO.getMarket(), tSecProfitDayDO.getStkCode());
        List<Match> tMatchList = logAssetMap.getOrDefault(key, new ArrayList<>());
        if (tMatchList.size() > CommConstants.MAX_PAGE_SIZE) {
            tMatchList = tMatchList.subList(0, CommConstants.MAX_PAGE_SIZE - 1);
        }
        tProfitDetail.setTMatchList(tMatchList);
        tProfitDetail.setStkCode(tSecProfitDayDO.getStkCode());
        tProfitDetail.setMarket(tSecProfitDayDO.getMarket());
        tProfitDetail.setTDifferences(tSecProfitDayDO.getTDifferences());
        tProfitDetail.setTMatchQty(tSecProfitDayDO.getTMatchQty());
        tProfitDetail.setTProfit(tSecProfitDayDO.getTProfit());
        tProfitDetail.setStkName(tSecProfitDayDO.getStkName());
        tProfitDetail.setHoldFlag(tSecProfitDayDO.getHoldFlag());
        if (tSecProfitDayDO.getTProfitHk() != null) {
            tProfitDetail.setTProfitHk(tSecProfitDayDO.getTProfitHk());
        }
        return tProfitDetail;
    }


    public List<Match> getTMatchList() {
        return tMatchList;
    }

    public void setTMatchList(List<Match> tMatchList) {
        this.tMatchList = tMatchList;
    }
}
