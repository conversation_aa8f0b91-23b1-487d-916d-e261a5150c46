package com.eastmoney.common.entity.fundia;

import java.util.Date;

/**
 * Created by Administrator on 2017/5/25.
 */
public class FundIaFundAsset {
    private Long eid; //系统物理主键
    private Date eitime; //数据入库时间
    private Date eutime; //最近一次修改时间
    private Long fundId;  // 资金帐号
    private String investId; // 投顾账户
    private String custId;  // 客户代码
    private Double fundLastBal;  // 昨日余额
    private Double fundBal;  // 到帐余额
    private Double fundAvl;  // 可用资金
    private Double fundWay;  // 在途金额
    private Double fundBuy; //买入冻结(委托买入计加、撤单计减)
    private Double fundSale;  // 卖出解冻(成交卖出计加)
    private Double fundBuySale;  // 买卖差额（卖出成交增加、买入成交减少）
    private Double fundAssetAdjamt; //补偿资金资产
    private Double stkAssetAdjamt; //补偿证券资产
    private String remark; //备注
    private String createUserId; //创建人ID
    private String createUserName; //创建人名称
    private String editUserId; //修改人ID
    private String editUserName; //修改人名称

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Date getEitime() {
        return eitime;
    }

    public void setEitime(Date eitime) {
        this.eitime = eitime;
    }

    public Date getEutime() {
        return eutime;
    }

    public void setEutime(Date eutime) {
        this.eutime = eutime;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getInvestId() {
        return investId;
    }

    public void setInvestId(String investId) {
        this.investId = investId;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public Double getFundLastBal() {
        return fundLastBal;
    }

    public void setFundLastBal(Double fundLastBal) {
        this.fundLastBal = fundLastBal;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getFundWay() {
        return fundWay;
    }

    public void setFundWay(Double fundWay) {
        this.fundWay = fundWay;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundAssetAdjamt() {
        return fundAssetAdjamt;
    }

    public void setFundAssetAdjamt(Double fundAssetAdjamt) {
        this.fundAssetAdjamt = fundAssetAdjamt;
    }

    public Double getStkAssetAdjamt() {
        return stkAssetAdjamt;
    }

    public void setStkAssetAdjamt(Double stkAssetAdjamt) {
        this.stkAssetAdjamt = stkAssetAdjamt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getEditUserId() {
        return editUserId;
    }

    public void setEditUserId(String editUserId) {
        this.editUserId = editUserId;
    }

    public String getEditUserName() {
        return editUserName;
    }

    public void setEditUserName(String editUserName) {
        this.editUserName = editUserName;
    }
}
