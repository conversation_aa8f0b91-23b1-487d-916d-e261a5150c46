<?xml version="1.0" encoding="UTF-8"?>


<configuration>
    <substitutionProperty name="DC-SERVICE" value="./logs/dc-service"/>
    <substitutionProperty name="DC-ACCESSOR" value="./logs/dc-accessor"/>
    <substitutionProperty name="DC-RPC" value="./logs/dc-rpc"/>
    <substitutionProperty name="USER_ACTION" value="./logs/user_action"/>
    <substitutionProperty name="DC-QUOTE" value="./logs/dc-quote"/>
    <!-- 控制台输出日志 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}-%M -%msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出日志，按天分割 -->
    <appender name="dc-service-account" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DC-SERVICE}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${DC-SERVICE}.%d{yyyyMMdd}.log.zip</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="dc-rpc" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DC-RPC}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${DC-RPC}.%d{yyyyMMdd}.log.zip</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="dc-accessor" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DC-ACCESSOR}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${DC-ACCESSOR}.%d{yyyyMMdd}.log.zip</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- quote-->
    <appender name="dc-quote" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${DC-QUOTE}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${DC-QUOTE}.%d{yyyyMMdd}.log.zip</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level %logger{80} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--这里指定logger name 是为jmx设置日志级别做铺垫 -->
    <logger name="com.eastmoney.service" level="info">
        <appender-ref ref="dc-service-account"/>
    </logger>
    <logger name="com.eastmoney.transport" level="debug">
        <appender-ref ref="dc-rpc"/>
    </logger>
    <logger name="com.eastmoney.accessor" level="debug">
        <appender-ref ref="dc-accessor"/>
    </logger>
    <logger name="com.eastmoney.quote" level="debug">
        <appender-ref ref="dc-quote"/>
    </logger>
    <!-- user_action日志系统-->
    <appender name="user_action" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${USER_ACTION}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${USER_ACTION}.%d{yyyyMMdd}.log.zip</FileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="com.eastmoney.common.util.LogUtil" level="INFO" additivity="false">
        <appender-ref ref="user_action" />
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>