package com.eastmoney.service.service.profit.list;

import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.profit.base.ProfitService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/12-18:28.
 *
 * <AUTHOR>
 */
@Service("profitDayListServiceRealTime")
public class ProfitDayListServiceRealTimeImpl implements ProfitDayListService {
    @Resource(name = "profitServiceRealTime")
    private ProfitService profitServiceRealTime;
    @Resource(name = "profitServiceTemp")
    private ProfitService profitServiceTemp;
    @Resource
    private CommonService commonService;

    @Override
    public List<ProfitDay> getDayProfitList(Map<String, Object> params) {
        int calRealTimeProfitBizDate = commonService.calRealTimeProfitBizDate(CommonUtil.convert(params.get("accountBizDate"), Integer.class));
        if (calRealTimeProfitBizDate == 0) {
            return null;
        }

        DayProfitBean profitDayTemp = profitServiceTemp.getProfitDay(params);
        DayProfitBean profitDayRealTime = null;
        if (profitDayTemp == null || profitDayTemp.getBizDate() < calRealTimeProfitBizDate) {
            params.put("realBizDate",calRealTimeProfitBizDate);
            profitDayRealTime = profitServiceRealTime.getProfitDay(params);
            profitDayRealTime.setBizDate(calRealTimeProfitBizDate);
        }

        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        List<ProfitDay> result = new ArrayList<>();
        filterProfitDayByBizDate(profitDayTemp, startDate, endDate, result);
        filterProfitDayByBizDate(profitDayRealTime, startDate, endDate, result);

        return result;
    }

    @Override
    public List<ProfitRateDay> getDayProfitRateList(Map<String, Object> params) {
        int calRealTimeProfitBizDate = commonService.calRealTimeProfitBizDate(CommonUtil.convert(params.get("accountBizDate"), Integer.class));
        if (calRealTimeProfitBizDate == 0) {
            return null;
        }

        DayProfitBean profitDayTemp = profitServiceTemp.getProfitDay(params);
        DayProfitBean profitDayRealTime = null;
        if (profitDayTemp == null || profitDayTemp.getBizDate() < calRealTimeProfitBizDate) {
            params.put("calProfitRate", true);
            //计算该日的实时收益
            params.put("realBizDate",calRealTimeProfitBizDate);
            profitDayRealTime = profitServiceRealTime.getProfitDay(params);
            profitDayRealTime.setBizDate(calRealTimeProfitBizDate);
        }

        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        List<ProfitRateDay> result = new ArrayList<>();
        filterProfitRateDayByBizDate(profitDayTemp, startDate, endDate, result);
        filterProfitRateDayByBizDate(profitDayRealTime, startDate, endDate, result);

        return result;
    }

    private void filterProfitDayByBizDate(DayProfitBean profitDayRealTime, Integer startDate, Integer endDate, List<ProfitDay> result) {
        if (profitDayRealTime != null && profitDayRealTime.getBizDate() >= startDate && profitDayRealTime.getBizDate() <= endDate) {
            ProfitDay profitDay = new ProfitDay();
            profitDay.setProfit(profitDayRealTime.getProfit());
            profitDay.setProfitRate(profitDayRealTime.getProfitRate());
            profitDay.setBizDate(profitDayRealTime.getBizDate());
            result.add(profitDay);
        }
    }

    private void filterProfitRateDayByBizDate(DayProfitBean profitDayRealTime, Integer startDate, Integer endDate, List<ProfitRateDay> result) {
        if (profitDayRealTime != null && profitDayRealTime.getBizDate() >= startDate && profitDayRealTime.getBizDate() <= endDate) {
            ProfitRateDay profitRateDay = new ProfitRateDay();
            profitRateDay.setProfitRate(ArithUtil.round(profitDayRealTime.getProfitRate(), 4));
            profitRateDay.setBizDate(profitDayRealTime.getBizDate());
            result.add(profitRateDay);
        }
    }

}
