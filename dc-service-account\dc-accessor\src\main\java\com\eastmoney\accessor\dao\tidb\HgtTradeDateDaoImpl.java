package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiHgtTradeDateMapper;
import com.eastmoney.common.entity.HgtTradeDayDO;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by robin on 2016/6/24.
 *
 * <AUTHOR>
 */
@Service("hgtTradeDateDao")
public class HgtTradeDateDaoImpl extends BaseDao<TiHgtTradeDateMapper, Object, Integer> implements HgtTradeDateDao {
    @Override
    public HgtTradeDayDO isMarket(Integer bizdate) {
        Map<String, Object> params = new HashMap<>();
        params.put("bizdate", bizdate);
        HgtTradeDayDO hgtTradeDay = getMapper().todayIsMarket(params);
        return hgtTradeDay;
    }

    @Override
    public Integer calHkProfitFlag(Integer bizDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("bizDate", bizDate);
        return getMapper().calHkProfitFlag(params);
    }
}
