package com.eastmoney.common.mail;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/7/27.
 */
public class MailUtil {
    private static final BlockingQueue<MailSenderInfo> mailQueue=new ArrayBlockingQueue<MailSenderInfo>(10000);

    static {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true){
                    try {
                        MailSenderInfo mailSenderInfo = mailQueue.take();
                        sendTextMail(mailSenderInfo);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }).start();
    }
    public static boolean sendMail(String toAddress,String subject,String content){
        //这个类主要是设置邮件
        MailSenderInfo mailInfo = new MailSenderInfo();
        mailInfo.setMailServerHost(MailConfig.mailServerHost);
        mailInfo.setMailServerPort(MailConfig.mailServerPort);
        mailInfo.setValidate(true);
        mailInfo.setUserName(MailConfig.userName);
        mailInfo.setPassword(MailConfig.password);//您的邮箱密码
        mailInfo.setFromAddress(MailConfig.userName);
        mailInfo.setToAddress(toAddress);
        mailInfo.setSubject(subject);
        mailInfo.setContent(content);
        //这个类主要来发送邮件
        return mailQueue.offer(mailInfo);
    }

    private static boolean sendTextMail(MailSenderInfo mailInfo) {
        // 判断是否需要身份认证
        MailAuthenticator authenticator = null;
        Properties pro = mailInfo.getProperties();
        if (mailInfo.isValidate()) {
            // 如果需要身份认证，则创建一个密码验证器
            authenticator = new MailAuthenticator(mailInfo.getUserName(), mailInfo.getPassword());
        }
        // 根据邮件会话属性和密码验证器构造一个发送邮件的session
        Session sendMailSession = Session.getInstance(pro,authenticator);
        try {
            // 根据session创建一个邮件消息
            Message mailMessage = new MimeMessage(sendMailSession);
            // 创建邮件发送者地址
            Address from = new InternetAddress(mailInfo.getFromAddress());
            // 设置邮件消息的发送者
            mailMessage.setFrom(from);
            // 创建邮件的接收者地址，并设置到邮件消息中
            Address to = new InternetAddress(mailInfo.getToAddress());
            mailMessage.setRecipient(Message.RecipientType.TO,to);
            // 设置邮件消息的主题
            mailMessage.setSubject(mailInfo.getSubject());
            // 设置邮件消息发送的时间
            mailMessage.setSentDate(new Date());
            // 设置邮件消息的主要内容
            String mailContent = mailInfo.getContent();
            mailMessage.setText(mailContent);
            // 发送邮件
            Transport.send(mailMessage);
            return true;
        } catch (MessagingException ex) {
            ex.printStackTrace();
        }
        return false;
    }

}
