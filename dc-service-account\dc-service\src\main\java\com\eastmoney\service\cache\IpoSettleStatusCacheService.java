package com.eastmoney.service.cache;

import com.eastmoney.common.entity.cal.IPOProfitInfoBO;
import com.eastmoney.service.service.profit.ipo.IpoSettleStatusService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 打新收益最新清算日期缓存
 */
@Service
public class IpoSettleStatusCacheService {
    private static Logger LOG = LoggerFactory.getLogger(IpoSettleStatusCacheService.class);
    @Autowired
    private IpoSettleStatusService ipoSettleStatusService;
    private static final String BUSINESS_CODE = "IPOPositionProfitCal";
    @Autowired
    private LoadingCache<Integer, Optional<IPOProfitInfoBO>> ipoSettleStatusCache;

    @Bean(name = "ipoSettleStatusCache")
    public LoadingCache<Integer, Optional<IPOProfitInfoBO>> IpoSettleStatusCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1)
                .maximumSize(6)
                .refreshAfterWrite(300, TimeUnit.SECONDS)
                .build(new CacheLoader<Integer, Optional<IPOProfitInfoBO>>() {
                    IPOProfitInfoBO ipoSettle = new IPOProfitInfoBO();

                    @Override
                    public Optional<IPOProfitInfoBO> load(Integer key) {
                        try {
                            ipoSettle = ipoSettleStatusService.getIpoSettleStatus(key, BUSINESS_CODE);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }

                        return Optional.ofNullable(ipoSettle);
                    }
                });
    }

    public IPOProfitInfoBO getIpoSettleStatus(Integer serverId) {
        try {
            return ipoSettleStatusCache.get(serverId).orElse(new IPOProfitInfoBO());
        } catch (ExecutionException e) {
            LOG.error("通过guava获取打新收益清算日期失败", e);
        }
        return new IPOProfitInfoBO();
    }
}
