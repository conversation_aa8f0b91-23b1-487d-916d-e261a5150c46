package com.eastmoney.common.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.*;

/**
 * Created by sunyuncai on 2017/2/22.
 */
public class ExecutorBuilder {
    private int corePoolSize;
    private BlockingQueue<Runnable> workQueue;
    private RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();
    public ExecutorBuilder setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
        return this;
    }
    public ExecutorBuilder setWorkQueue(BlockingQueue<Runnable> workQueue) {
        this.workQueue = workQueue;
        return this;
    }
    public ExecutorBuilder setRejectedExecutionHandler(RejectedExecutionHandler handler) {
        this.handler = handler;
        return this;
    }
    public ThreadPoolExecutor build() {
        return build(this);
    }

    private static ThreadPoolExecutor build(ExecutorBuilder builder) {
        return new ThreadPoolExecutor(builder.corePoolSize, builder.corePoolSize, 0L,
                TimeUnit.MILLISECONDS,builder.workQueue, null, builder.handler);
    }
    public static ScheduledExecutorService newScheduledThreadPool(int nThreads, String poolName) {
        ThreadFactory threadFactory =  new ThreadFactoryBuilder().setNameFormat(poolName + "-task-%d").build();
        return new ScheduledThreadPoolExecutor(nThreads, threadFactory);
    }

    public static ExecutorService newFixedThreadPool(int nThreads,String poolName) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(poolName + "-task-%d").build();
        return new ThreadPoolExecutor(nThreads, nThreads,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),threadFactory);
    }
    public static ExecutorService newFixedThreadPool(int nThreads,BlockingQueue<Runnable> workQueue,String poolName) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(poolName + "-task-%d").build();
        return new ThreadPoolExecutor(nThreads, nThreads,
                0L, TimeUnit.MILLISECONDS,
                workQueue,threadFactory);
    }
    public static ExecutorService newFixedThreadPool(int nThreads,int queueSize,String poolName) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(poolName + "-task-%d").build();
        return new ThreadPoolExecutor(nThreads, nThreads,
                0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(queueSize),threadFactory);
    }
    public static ScheduledExecutorService newSingleThreadScheduledExecutor(String poolName) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(poolName + "-task-%d").build();
        return Executors.newSingleThreadScheduledExecutor(threadFactory);
    }
}
