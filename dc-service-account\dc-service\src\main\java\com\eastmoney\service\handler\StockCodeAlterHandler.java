package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.StockCodeAlterDao;
import com.eastmoney.common.entity.StockCodeAlter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018-02-06 10:53
 */
@Service
public class StockCodeAlterHandler {
    @Autowired
    private StockCodeAlterDao stockCodeAlterDao;

    public List<StockCodeAlter> getStockCodeAlterList(Map<String, Object> params) {
        return stockCodeAlterDao.getStockCodeAlterList(params);
    }
}
