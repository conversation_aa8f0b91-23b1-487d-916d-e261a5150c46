package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.ProfitRateContrastDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.ProfitRateContrast;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class ProfitRateContrastCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(ProfitRateContrastCacheService.class);
    @Resource(name = "profitRateContrastCache")
    private LoadingCache<String, Optional<ProfitRateContrast>> profitRateContrastCache;
    @Autowired
    private ProfitRateContrastDao profitRateContrastDao;

    @Bean(name = "profitRateContrastCache")
    public LoadingCache<String, Optional<ProfitRateContrast>> profitRateContrastCache() {
        LoadingCache<String, Optional<ProfitRateContrast>> profitRateContrastCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(6)
                .maximumSize(10)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<ProfitRateContrast>>() {
                    @Override
                    public Optional<ProfitRateContrast> load(String key) throws Exception {
                        List<ProfitRateContrast> profitRateContrast = null;
                        try {
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("unit", key);
                            profitRateContrast = profitRateContrastDao.query(param);

                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        if (!CollectionUtils.isEmpty(profitRateContrast)) {
                            return Optional.ofNullable(profitRateContrast.get(0));
                        }
                        return Optional.empty();
                    }
                });
        return profitRateContrastCache;
    }

    @RedisCache(keyGenerator = "'jzjy_profitratecontrast_' + #unit")
    public ProfitRateContrast getProfitRateContrast(String unit) {
        if (StringUtils.isEmpty(unit)) {
            return null;
        }
        try {
            return profitRateContrastCache.get(unit).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误！通过guava获取收益率参照表失败{}", e.getMessage());
        }
        return null;
    }
}
