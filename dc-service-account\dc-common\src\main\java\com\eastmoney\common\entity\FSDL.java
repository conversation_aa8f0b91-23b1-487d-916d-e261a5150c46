package com.eastmoney.common.entity;

import java.util.Date;

/**
 * 两融短信表FSDL
 * Created by Administrator on 2016/9/14.
 */
public class FSDL extends BaseEntity{
    private Long guId;
    private int dxjb;
    private Long customerId;
    private Long entityId;
    private Long ownerId;
    private String sjhm;
    private double dxfy;
    private int xxlx;
    private String lmjc;
    private String gpdm;
    private String xxnr;
    private Date zhxgrq;
    private Integer bizDate;//业务日

    public Long getGuId() {
        return guId;
    }

    public void setGuId(Long guId) {
        this.guId = guId;
    }

    public int getDxjb() {
        return dxjb;
    }

    public void setDxjb(int dxjb) {
        this.dxjb = dxjb;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getSjhm() {
        return sjhm;
    }

    public void setSjhm(String sjhm) {
        this.sjhm = sjhm;
    }

    public double getDxfy() {
        return dxfy;
    }

    public void setDxfy(double dxfy) {
        this.dxfy = dxfy;
    }

    public int getXxlx() {
        return xxlx;
    }

    public void setXxlx(int xxlx) {
        this.xxlx = xxlx;
    }

    public String getLmjc() {
        return lmjc;
    }

    public void setLmjc(String lmjc) {
        this.lmjc = lmjc;
    }

    public String getGpdm() {
        return gpdm;
    }

    public void setGpdm(String gpdm) {
        this.gpdm = gpdm;
    }

    public String getXxnr() {
        return xxnr;
    }

    public void setXxnr(String xxnr) {
        this.xxnr = xxnr;
    }

    public Date getZhxgrq() {
        return zhxgrq;
    }

    public void setZhxgrq(Date zhxgrq) {
        this.zhxgrq = zhxgrq;
    }

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }
}
