package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.ProfitRateSectionDao;
import com.eastmoney.accessor.dao.oracle.ProfitSectionDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.ProfitRateSection;
import com.eastmoney.common.entity.cal.ProfitSection;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class ProfitSectionCacheService {
    private static Logger LOG = LoggerFactory.getLogger(AssetNewService.class);
    @Resource(name = "profitSectionCache")
    private LoadingCache<String, Optional<ProfitSection>> profitSectionCache;
    @Resource(name = "profitRateSectionCache")
    private LoadingCache<String, Optional<ProfitRateSection>> profitRateSectionCache;

    @Autowired
    private ProfitSectionDao profitSectionDao;
    @Autowired
    private ProfitRateSectionDao profitRateSectionDao;

    @Bean(name = "profitSectionCache")
    public LoadingCache<String, Optional<ProfitSection>> profitSectionCache() {
        LoadingCache<String, Optional<ProfitSection>> profitSectionLoadingCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(50000)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<ProfitSection>>() {
                    @Override
                    public Optional<ProfitSection> load(String key) {
                        ProfitSection profitSection = null;
                        try {
                            String[] split = key.split("-");
                            Long fundId = Long.valueOf(split[0]);
                            String unit = split[1];
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("fundId", fundId);
                            param.put("unit", unit);
                            List<ProfitSection> profitSectionList = profitSectionDao.query(param);
                            if (!CollectionUtils.isEmpty(profitSectionList)) {
                                profitSection = profitSectionList.get(0);
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(profitSection);
                    }
                });
        return profitSectionLoadingCache;
    }

    @Bean(name = "profitRateSectionCache")
    public LoadingCache<String, Optional<ProfitRateSection>> profitRateSectionCache() {
        LoadingCache<String, Optional<ProfitRateSection>> profitRateSectionLoadingCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(50000)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Optional<ProfitRateSection>>() {
                    @Override
                    public Optional<ProfitRateSection> load(String key) {
                        ProfitRateSection profitRateSection = null;
                        try {
                            String[] split = key.split("-");
                            Long fundId = Long.valueOf(split[0]);
                            String unit = split[1];
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("fundId", fundId);
                            param.put("unit", unit);
                            List<ProfitRateSection> profitRateSectionList = profitRateSectionDao.query(param);
                            if (!CollectionUtils.isEmpty(profitRateSectionList)) {
                                profitRateSection = profitRateSectionList.get(0);
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(profitRateSection);
                    }
                });
        return profitRateSectionLoadingCache;
    }

    @RedisCache(keyGenerator = "'jzjy_profitsection'+'_'+#params.get('fundId') + '_' + #params.get('unit')")
    public ProfitSection getProfitSection(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        if (fundId == null || unit == null) {
            return null;
        }
        String key = fundId + "-" + unit;
        try {
            return profitSectionCache.get(key).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取片收益失败", e);
        }
        return null;
    }

    @RedisCache(keyGenerator = "'jzjy_profitratesection_' + #params.get('fundId') + '_' + #params.get('unit')")
    public ProfitRateSection getProfitRateSection(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        if (fundId == null || unit == null) {
            return null;
        }
        String key = fundId + "-" + unit;
        try {
            return profitRateSectionCache.get(key).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取片收益失败", e);
        }
        return null;
    }
}
