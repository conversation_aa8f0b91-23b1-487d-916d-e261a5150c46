package com.eastmoney.common.entity.cal;

public class BdUserTradeBillDO {
    private Long fundId;
    private Integer openDate;//开户成功日期
    private Integer appUseDay;//2021年访问APP的天数
    private Long saleCeil;//全年逃顶成功次数
    private Long buyFloor;//全年抄底成功次数
    private String snameSale; //全年逃顶最成功的股票名称
    private String downPerc;//逃顶最成功的股票逃顶后20日跌幅
    private String snameBuy; //全年抄底最成功的股票名称
    private String upPerc; //抄底最成功的股票抄底后20日涨幅
    private String beforeStartDate;//在20150106之前开户 0代表否 1代表是

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Integer openDate) {
        this.openDate = openDate;
    }

    public Integer getAppUseDay() {
        return appUseDay;
    }

    public void setAppUseDay(Integer appUseDay) {
        this.appUseDay = appUseDay;
    }

    public Long getSaleCeil() {
        return saleCeil;
    }

    public void setSaleCeil(Long saleCeil) {
        this.saleCeil = saleCeil;
    }

    public Long getBuyFloor() {
        return buyFloor;
    }

    public void setBuyFloor(Long buyFloor) {
        this.buyFloor = buyFloor;
    }

    public String getSnameSale() {
        return snameSale;
    }

    public void setSnameSale(String snameSale) {
        this.snameSale = snameSale;
    }

    public String getDownPerc() {
        return downPerc;
    }

    public void setDownPerc(String downPerc) {
        this.downPerc = downPerc;
    }

    public String getSnameBuy() {
        return snameBuy;
    }

    public void setSnameBuy(String snameBuy) {
        this.snameBuy = snameBuy;
    }

    public String getUpPerc() {
        return upPerc;
    }

    public void setUpPerc(String upPerc) {
        this.upPerc = upPerc;
    }

    public String getBeforeStartDate() {
        return beforeStartDate;
    }

    public void setBeforeStartDate(String beforeStartDate) {
        this.beforeStartDate = beforeStartDate;
    }
}
