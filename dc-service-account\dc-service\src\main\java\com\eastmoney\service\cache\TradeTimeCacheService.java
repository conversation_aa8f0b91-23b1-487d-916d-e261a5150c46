package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.service.util.BusinessUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;


import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 集合竞价的时间缓存
 *
 * <AUTHOR>
 */
@Service
@EnableScheduling
public class TradeTimeCacheService {
    @Autowired
    private HgtTradeDateService hgtTradeDateService;

    @Autowired
    private TradeDateDao tradeDateDao;

    private static final Logger LOGGER = LoggerFactory.getLogger(TradeTimeCacheService.class);

    /**
     * 缓存开盘时间map
     */
    private static final Map<String, Boolean> TRADE_TIME_CACHE = new ConcurrentHashMap<>();
    private static final String HGT_MARKET_TIME_CACHE = "hgtMarketTimeCache";
    private static final String MARKET_TIME_CACHE = "marketTimeCache";


    private final LocalTime before = LocalTime.of(9, 14, 59);
    private final LocalTime after = LocalTime.of(9, 25, 0);
    private final LocalTime hgtBefore = LocalTime.of(8, 59, 59);
    private final LocalTime hgtAfter = LocalTime.of(9, 22, 0);

    @Scheduled(cron = "0/5 0-30 9 * * ?")
    private void loadTimeCache() throws Throwable {
        try {
            LOGGER.info("------------------------开始刷新集合竞价时间缓存-------------------------" + LocalDate.now() + "  " + LocalTime.now());
            doLoadTimeCache();
            doLoadHgtTimeCache();
            LOGGER.info("------------------------集合竞价时间缓存刷新完成-------------------------" + LocalDate.now() + "  " + LocalTime.now());
        } catch (Exception e) {
            LOGGER.warn("------------------------集合竞价时间缓存刷新异常！-------------------------" + LocalDate.now() + "  " + LocalTime.now());
            repairTradeTimeCache();
            throw new Exception("集合竞价缓存刷新调度异常！");
        }
    }

    private void doLoadTimeCache() {
        //如果A股不开盘
        if (!tradeDateDao.todayIsMarket()) {
            TRADE_TIME_CACHE.put(MARKET_TIME_CACHE, false);
            return;
        }
        LocalTime now = LocalTime.now();
        //如果A股不在集合竞价期间
        if (now.isBefore(before) || now.isAfter(after)) {
            TRADE_TIME_CACHE.put(MARKET_TIME_CACHE, false);
            return;
        }
        //A股是否在集合竞价期间
        boolean isBeforeMarketOpen = tradeDateDao.todayIsMarket() && now.isAfter(before) && now.isBefore(after);
        TRADE_TIME_CACHE.put(MARKET_TIME_CACHE, isBeforeMarketOpen);
    }

    private void doLoadHgtTimeCache() {
        //如果港股不开盘
        if (!hgtTradeDateService.todayIsMarket()) {
            TRADE_TIME_CACHE.put(HGT_MARKET_TIME_CACHE, false);
            return;
        }
        LocalTime now = LocalTime.now();
        //如果港股不在集合竞价期间
        if (now.isBefore(hgtBefore) || now.isAfter(hgtAfter)) {
            TRADE_TIME_CACHE.put(HGT_MARKET_TIME_CACHE, false);
            return;
        }
        //港股是否在集合竞价期间
        boolean isBeforeMarketOpen = hgtTradeDateService.todayIsMarket() && now.isAfter(hgtBefore) && now.isBefore(hgtAfter);
        TRADE_TIME_CACHE.put(HGT_MARKET_TIME_CACHE, isBeforeMarketOpen);
    }


    public boolean isBeforeMarketOpen(String market) {
        if (BusinessUtil.isGgtMarket(market)) {
            return TRADE_TIME_CACHE.getOrDefault(HGT_MARKET_TIME_CACHE, false);
        } else {
            return TRADE_TIME_CACHE.getOrDefault(MARKET_TIME_CACHE, false);
        }
    }

    /**
     * 集合竞价期间的调度线程非正常死亡
     * 提供可修复的措施
     * 将集合竞价期间的时间缓存清空
     */
    public void repairTradeTimeCache() {
        TRADE_TIME_CACHE.clear();
    }

}
