package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.WholeStatDao;
import com.eastmoney.accessor.mapper.tidb.TiWholeStatMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.WholeStat;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2018/3/25
 *
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("wholeStatDao")
public class TiWholeStatDaoImpl extends BaseDao<TiWholeStatMapper, WholeStat, Long> implements WholeStatDao {

}
