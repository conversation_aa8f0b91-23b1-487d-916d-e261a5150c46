<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiZhfxSettleStatusMapper">

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.ZhfxSettleStatus">
        SELECT eitime, eutime, bizDate, serverId, status
        FROM atcenter.ZHFX_SETTLE_STATUS
        WHERE serverId = #{serverId} order by bizdate desc limit 1
    </select>

</mapper>