package com.eastmoney.service.service.profit.realtime;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.entity.SysConfig;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.MixedConfigService;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.cache.SysConfigService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.profit.base.ProfitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service("profitRealTimeServiceFacade")
public class ProfitRealTimeServiceFacade {
    @Resource(name = "profitServiceRealTime")
    private ProfitService profitServiceRealTime;
    @Resource(name = "commonService")
    private CommonService commonService;
    @Resource
    private AssetNewService assetNewService;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Resource(name = "mixedConfigService")
    private MixedConfigService mixedConfigService;
    @Autowired
    private CoreConfigService coreConfigService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private NodeConfigService nodeConfigService;

    /**
     * 当日盈亏时间段的显示规则:物理日期=交易日且物理时间>=9:25且集中交易系统状态=正常0，系统才显示当日盈亏。(数据库已经求出)
     * <p>
     * 更新：交易日 92500~240000 展示当日盈亏 (APPAGILE-134973)
     *
     * @param params
     * @return
     */
    public DayProfitBean getRealTimeProfit(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer serverId = coreConfigService.getServerId(fundId);
        if (serverId == null) {
            throw new RuntimeException("无法确定serverId--------------" + JSON.toJSONString(params));
        }
        String marketsRange = mixedConfigService.getParaValue("xzzcyhckykscfw", serverId);
        //如果柜台公共参数中台参考盈亏市场范围包含港股通，或配置为空保持原状
        if (StringUtils.isEmpty(marketsRange) || ((!marketsRange.contains("S")) && (!marketsRange.contains("5")))) {
            params.put("filterHgtProfit", true);
        }
        params.put("calProfitRate", true);
        int today = Integer.parseInt(DateUtil.getCuryyyyMMdd());
        boolean todayIsMarket = tradeDateDao.isMarket(today);

        //物理日期!=交易日 返回 -
        if (!todayIsMarket) {
            return null;
        }

        //物理时间<=9:25
        if (!commonService.isAfterMarketOpen()) {
            return null;
        }
        Integer preMarketDay = Integer.valueOf(tradeDateDao.getPreMarketDay(today));
        AssetNew assetInfo = assetNewService.getAssetInfo(params);
        //新开户用户，上一交易日还没有资产 返回 -
        if (assetInfo == null) {
            return null;
        }

        //集中交易系统状态=正常0
        SysConfig sysConfig = sysConfigService.getSysConfig(assetInfo.getServerId().toString());
        if (!nodeConfigService.getSecProfitShowFlag() && (!sysConfig.getStatus().equals(0) || today != sysConfig.getSysDate())) {
            return null;
        }

        Integer accountBizDate = assetInfo.getBizDate();
        //上个交易日已经清算完成
        if (preMarketDay.equals(accountBizDate)) {
            params.put("accountBizDate", accountBizDate);
        }
        //跨天还未清算
        if (preMarketDay > accountBizDate) {
            params.put("accountBizDate", accountBizDate);
        }
        //当日已经清算完成,使用上一交易日资产数据计算收益
        if (accountBizDate > preMarketDay) {
            params.put("accountBizDate", preMarketDay);
        }

        return profitServiceRealTime.getProfitDay(params);
    }
}
