package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created on 2016/8/3.
 *
 * 持仓收益表
 * <AUTHOR>
 */
public class PositionProfit  extends BaseEntityCal {

    private Long fundId;    //资金帐号
    private String stkCode; //证券代码
    private String market; //证券市场
    private String type;     //证券类型
    private Integer startDate; //持仓开始日期
    private Integer endDate; //持仓结束日期
    private Integer endDateCondition; //更新持仓收益时，结束日期作为条件传进去
    private Double totalBuy; //总买入金额
    private Double totalSale; //总卖出金额
    private Double totalIn; //总转入金额
    private Double totalOut; //总转出金额
    private Long totalBuyNum;// 累计买入数量
    private Long totalSaleNum;// 累计卖出数量
    private Long totalInNum;// 累计转入数量
    private Long totalOutNum;// 累计转出数量
    private Double  profit;   //盈亏金额
    private Integer serverId; //机器编码
    private Double totalCost;//总费用
    private String sno;
    private Integer calFlag;//计算标识 默认值0 值说明：0-待计算 1-已计算
    private String stkName;//证券名称
    private String expandNameAbbr; // 扩位简称
    public Double costPrice;//成本价格
    private Double profitTotal; //累计盈亏
    private Integer holdDays;    //累计持仓天数
    private Integer tradeTotal;     //买卖次数
    private Integer buyTimes;     //买入次数
    private Integer saleTimes;      //卖出次数
    private Integer clearTimes;    //清仓次数
    private Double  profitRate;    //收益率
    private Double tradeTaxFee; //区间累计费用总计

    /**
     * 成功率
     */
    private Double successRate;
    /**
     * 实时行情
     */
    public Double price;
    /**
     * 仓位
     */
    private Double positionRatio;
    /**
     * 当日参考收益
     */
    private Double dayProfit;
    /**
     * 当日参考收益率
     */
    private Double dayProfitRate;
    /**
     * 证券类型
     */
    private String stktype;
    /**
     * 真正开始日期
     */
    private Integer clearStartDate;
    /**
     * 真正结束日期
     */
    private Integer clearEndDate;
    /**
     * 持有证券数量
     */
    private Long stkQty;

    /**
     * 转换代码 830 -> 920  920 -> 920
     * @return
     */
    private String corResCode;

    public String getStktype() {
        return stktype;
    }

    public void setStktype(String stktype) {
        this.stktype = stktype;
    }

    public Double getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(Double successRate) {
        this.successRate = successRate;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Integer getClearTimes() {
        return clearTimes;
    }

    public void setClearTimes(Integer clearTimes) {
        this.clearTimes = clearTimes;
    }

    public Integer getBuyTimes() {
        return buyTimes;
    }

    public void setBuyTimes(Integer buyTimes) {
        this.buyTimes = buyTimes;
    }

    public Integer getSaleTimes() {
        return saleTimes;
    }

    public void setSaleTimes(Integer saleTimes) {
        this.saleTimes = saleTimes;
    }

    public Double getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(Double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public Integer getHoldDays() {
        return holdDays;
    }

    public void setHoldDays(Integer holdDays) {
        this.holdDays = holdDays;
    }

    public Integer getTradeTotal() {
        return tradeTotal;
    }

    public void setTradeTotal(Integer tradeTotal) {
        this.tradeTotal = tradeTotal;
    }

    public Double getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    }

    public Double getTotalIn() {
        return totalIn;
    }

    public void setTotalIn(Double totalIn) {
        this.totalIn = totalIn;
    }

    public Double getTotalOut() {
        return totalOut;
    }

    public void setTotalOut(Double totalOut) {
        this.totalOut = totalOut;
    }

    public Long getTotalBuyNum() {
        return totalBuyNum;
    }

    public void setTotalBuyNum(Long totalBuyNum) {
        this.totalBuyNum = totalBuyNum;
    }

    public Long getTotalSaleNum() {
        return totalSaleNum;
    }

    public void setTotalSaleNum(Long totalSaleNum) {
        this.totalSaleNum = totalSaleNum;
    }

    public Long getTotalInNum() {
        return totalInNum;
    }

    public void setTotalInNum(Long totalInNum) {
        this.totalInNum = totalInNum;
    }

    public Long getTotalOutNum() {
        return totalOutNum;
    }

    public void setTotalOutNum(Long totalOutNum) {
        this.totalOutNum = totalOutNum;
    }

    public String getSno() {
        return sno;
    }

    public void setSno(String sno) {
        this.sno = sno;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type.trim();
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }

    public Double getTotalBuy() {
        return totalBuy;
    }

    public void setTotalBuy(Double totalBuy) {
        this.totalBuy = totalBuy;
    }

    public Double getTotalSale() {
        return totalSale;
    }

    public void setTotalSale(Double totalSale) {
        this.totalSale = totalSale;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    @Override
    public Integer getServerId() {
        return serverId;
    }

    @Override
    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market.trim();
    }

    public Integer getCalFlag() {
        return calFlag;
    }

    public void setCalFlag(Integer calFlag) {
        this.calFlag = calFlag;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public Integer getEndDateCondition() {
        return endDateCondition;
    }

    public void setEndDateCondition(Integer endDateCondition) {
        this.endDateCondition = endDateCondition;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public String getExpandNameAbbr() {
        return expandNameAbbr;
    }

    public void setExpandNameAbbr(String expandNameAbbr) {
        this.expandNameAbbr = expandNameAbbr;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getPositionRatio() {
        return positionRatio;
    }

    public void setPositionRatio(Double positionRatio) {
        this.positionRatio = positionRatio;
    }

    public Double getDayProfit() {
        return dayProfit;
    }

    public void setDayProfit(Double dayProfit) {
        this.dayProfit = dayProfit;
    }

    public Double getDayProfitRate() {
        return dayProfitRate;
    }

    public void setDayProfitRate(Double dayProfitRate) {
        this.dayProfitRate = dayProfitRate;
    }

    public Integer getClearStartDate() {
        return clearStartDate;
    }

    public void setClearStartDate(Integer clearStartDate) {
        this.clearStartDate = clearStartDate;
    }

    public Integer getClearEndDate() {
        return clearEndDate;
    }

    public void setClearEndDate(Integer clearEndDate) {
        this.clearEndDate = clearEndDate;
    }

    public Long getStkQty() {
        return stkQty;
    }

    public void setStkQty(Long stkQty) {
        this.stkQty = stkQty;
    }

    public Double getTradeTaxFee(){return this.tradeTaxFee;}

    public void setTradeTaxFee(Double tradeTaxFee){this.tradeTaxFee = tradeTaxFee;}

    public String getCorResCode() {
        return corResCode;
    }

    public void setCorResCode(String corResCode) {
        this.corResCode = corResCode;
    }

    public boolean equals(Object obj) {
        PositionProfit val = null;
        boolean flag = obj instanceof PositionProfit;
        if(flag){
            val = (PositionProfit) obj;
        }else{
            return false;
        }
        if(this.serverId.equals(val.getServerId())
                && this.fundId.equals(val.getFundId())
                && this.stkCode.equals(val.getStkCode())
                && this.market.equals(val.getMarket())
                && this.startDate.equals(val.getStartDate())
                && this.clearStartDate.equals(val.getClearStartDate())
                && this.endDateCondition.equals(val.getEndDateCondition())
                ){
            return true;
        }else{
            return false;
        }
    }
}
