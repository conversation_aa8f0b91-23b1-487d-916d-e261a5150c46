package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.AssetHisMapper;
import com.eastmoney.common.entity.cal.AssetHis;

import java.util.List;
import java.util.Map;

/**
 * Created by huachengqi on 2016/7/18.
 */
//@ZhfxDataSource
//@Service("assetHisDao")
public class AssetHisDaoImpl extends BaseDao<AssetHisMapper, AssetHis, Integer> implements AssetHisDao {

    @Override
    public List<AssetHis> getAssetDayTrend(Map<String, Object> params) {
        return null;
    }

    @Override
    public List<AssetHis> getMonthAssetDayTrend(Map<String, Object> params) {
        return null;
    }
}
