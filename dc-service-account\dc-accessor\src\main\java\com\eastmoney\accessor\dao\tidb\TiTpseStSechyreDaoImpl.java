package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.accessor.mapper.tidb.TiTpseStSechyreMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.TpseStSechyre;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Administrator on 2017/3/28.
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("tpseStSechyreDao")
public class TiTpseStSechyreDaoImpl extends BaseDao<TiTpseStSechyreMapper, TpseStSechyre, Integer> implements TpseStSechyreDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(TiTpseStSechyreDaoImpl.class);

    private final ConcurrentHashMap<String, String> tpseStSechyreMap = new ConcurrentHashMap();

    public String getPublishName(String stkCode) {
        return tpseStSechyreMap.get(stkCode.trim());
    }

    @PostConstruct
    @Scheduled(cron = "0 0 22 * * ?")
    public void loadTpseStSechyreMap() {
        LOGGER.info("------------------------正在加载行业类别信息信息，请等待-------------------------" + DateUtil.getCurDateTime());
        doLoadStockMap();
        LOGGER.info("------------------------行业类别信息加载完成-------------------------" + DateUtil.getCurDateTime());
    }

    private void doLoadStockMap() {
        List<TpseStSechyre> tpseStSechyreList = getMapper().getTpseStSechyreList();
        for (TpseStSechyre tpseStSechyres : tpseStSechyreList) {
            tpseStSechyreMap.put(StringUtils.trim(tpseStSechyres.getSecurityCode()), StringUtils.trim(tpseStSechyres.getPublishName()));
        }
    }

}
