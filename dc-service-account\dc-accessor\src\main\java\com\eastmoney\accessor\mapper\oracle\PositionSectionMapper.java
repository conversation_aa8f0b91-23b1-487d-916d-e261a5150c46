package com.eastmoney.accessor.mapper.oracle;

import com.eastmoney.common.entity.cal.PositionSection;
import com.eastmoney.accessor.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by huachengqi on 2016/8/3.
 */
@Repository
public interface PositionSectionMapper extends BaseMapper<PositionSection, Integer> {

    List<PositionSection> getPositionProfitStatistics(Map<String,Object> param);
}
