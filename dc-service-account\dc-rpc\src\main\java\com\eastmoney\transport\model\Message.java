package com.eastmoney.transport.model;

/**
 * User: sunyuncai
 * Date: 15-9-1
 * Time: 下午2:41
 */
public class Message{
    public Message(){
    }

    public Message(byte split, int packLen, byte type, byte[] content) {
        this.split = split;
        this.packLen = packLen;
        this.type = type;
        this.content = content;
    }

    private byte[] requestId;

    private byte split;

    private int packLen;

    private byte type;

    private byte[] content;

    //版本号
    private byte version;
    //包体是否加密(0未加密 1加密)
    private byte cipher;
    //响应包体是否需要加密(0 不加密 1加密)
    private byte replyCipher;
    //包体是否压缩	=0(没用压缩)	=1(zlib压缩) 先压缩后加密/先解密后解压缩
    private byte compress;
    public byte getVersion() {
        return version;
    }

    public void setVersion(byte version) {
        this.version = version;
    }

    public byte getCipher() {
        return cipher;
    }

    public void setCipher(byte cipher) {
        this.cipher = cipher;
    }

    public byte getCompress() {
        return compress;
    }

    public void setCompress(byte compress) {
        this.compress = compress;
    }

    public byte getSplit() {
        return split;
    }

    public void setSplit(byte split) {
        this.split = split;
    }

    public int getPackLen() {
        return packLen;
    }

    public void setPackLen(int packLen) {
        this.packLen = packLen;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public byte[] getRequestId() {
        return requestId;
    }

    public void setRequestId(byte[] requestId) {
        this.requestId = requestId;
    }

    public byte getReplyCipher() {
        return replyCipher;
    }

    public void setReplyCipher(byte replyCipher) {
        this.replyCipher = replyCipher;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("message[split=" + split);
        sb.append(",type=" + type);
        sb.append(",packLen=" + packLen);
        sb.append(",version=" + version);
        sb.append(",cipher=" + cipher);
        sb.append(",replyCipher=" + replyCipher);
        sb.append(",compress=" + compress);
        if (packLen < 1000 && compress == 0) {
            if (content == null) {
                sb.append(",content=" + null);
            }else{
                sb.append(",content=" + new String(content));
            }
        }
        if (requestId != null) {
            sb.append(",requestId=" + new String(requestId) + "]");
        }
        return sb.toString();
    }

    public static void main(String[] args) {
//        Message message = new Message((byte) 0, 0, Constants.SOCKET_TYPE_PULL_RES, null);
//        message.setContent("喝咖啡".getBytes());
//        System.out.println(message);
//
//        Message msg = new Message();
//        System.out.println(msg);
        String name = "sunyc";
        System.out.println(name.substring(0,3));
    }
}
