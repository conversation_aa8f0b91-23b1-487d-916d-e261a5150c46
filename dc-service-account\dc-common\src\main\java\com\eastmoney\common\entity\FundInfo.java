package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/2.
 */
public class FundInfo {
    public Integer serverId;
    public Long custId;
    public String orgId;
    public String brhId;
    public String fundId;
    public String spellId;
    public String fundName;
    public String idType;
    public String idNo;
    public String operway;
    public String fundKind;
    public String fundLevel;
    public String fundGroup;
    public Integer fundUser0;
    public Integer fundUser1;
    public Long brokerId;
    public Long custMgrId;
    public String fundAgentRight;
    public String fundRight;
    public String fundLimit;
    public String fundTrdRule;
    public String trdRight;
    public String fundRule;
    public String bankCode;
    public String bankBranch;
    public String bankNetPlace;
    public Integer pwdCtrlDays;
    public String pwdType;
    public String fundPwd;
    public Integer linkFlag;
    public String fundTrdFlag;
    public Integer openDate;
    public Integer closeDate;
    public Integer printDate;
    public Integer fundPrintDate;
    public String status;
    public String policyId;
    public String policyKind;
    public String cobusiFlag;
    public String reportKind;
    public String sepecialFee;
    public String specialKind;
    public String sfBankCode;
    public String sfOpenMoney;
    public String fundType;
    public String activeFlag;
    public String noticeWay;
    public String billProWay;
    public String creditFlag;
    public String creditFund;
    public String creditStatus;
    public String lastCreditStatus;

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId;
    }

    public String getFundId() {
        return fundId;
    }

    public void setFundId(String fundId) {
        this.fundId = fundId;
    }

    public String getSpellId() {
        return spellId;
    }

    public void setSpellId(String spellId) {
        this.spellId = spellId;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getOperway() {
        return operway;
    }

    public void setOperway(String operway) {
        this.operway = operway;
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind;
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel;
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup;
    }

    public Integer getFundUser0() {
        return fundUser0;
    }

    public void setFundUser0(Integer fundUser0) {
        this.fundUser0 = fundUser0;
    }

    public Integer getFundUser1() {
        return fundUser1;
    }

    public void setFundUser1(Integer fundUser1) {
        this.fundUser1 = fundUser1;
    }

    public Long getBrokerId() {
        return brokerId;
    }

    public void setBrokerId(Long brokerId) {
        this.brokerId = brokerId;
    }

    public Long getCustMgrId() {
        return custMgrId;
    }

    public void setCustMgrId(Long custMgrId) {
        this.custMgrId = custMgrId;
    }

    public String getFundAgentRight() {
        return fundAgentRight;
    }

    public void setFundAgentRight(String fundAgentRight) {
        this.fundAgentRight = fundAgentRight;
    }

    public String getFundRight() {
        return fundRight;
    }

    public void setFundRight(String fundRight) {
        this.fundRight = fundRight;
    }

    public String getFundLimit() {
        return fundLimit;
    }

    public void setFundLimit(String fundLimit) {
        this.fundLimit = fundLimit;
    }

    public String getFundTrdRule() {
        return fundTrdRule;
    }

    public void setFundTrdRule(String fundTrdRule) {
        this.fundTrdRule = fundTrdRule;
    }

    public String getTrdRight() {
        return trdRight;
    }

    public void setTrdRight(String trdRight) {
        this.trdRight = trdRight;
    }

    public String getFundRule() {
        return fundRule;
    }

    public void setFundRule(String fundRule) {
        this.fundRule = fundRule;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getBankNetPlace() {
        return bankNetPlace;
    }

    public void setBankNetPlace(String bankNetPlace) {
        this.bankNetPlace = bankNetPlace;
    }

    public Integer getPwdCtrlDays() {
        return pwdCtrlDays;
    }

    public void setPwdCtrlDays(Integer pwdCtrlDays) {
        this.pwdCtrlDays = pwdCtrlDays;
    }

    public String getPwdType() {
        return pwdType;
    }

    public void setPwdType(String pwdType) {
        this.pwdType = pwdType;
    }

    public String getFundPwd() {
        return fundPwd;
    }

    public void setFundPwd(String fundPwd) {
        this.fundPwd = fundPwd;
    }

    public Integer getLinkFlag() {
        return linkFlag;
    }

    public void setLinkFlag(Integer linkFlag) {
        this.linkFlag = linkFlag;
    }

    public String getFundTrdFlag() {
        return fundTrdFlag;
    }

    public void setFundTrdFlag(String fundTrdFlag) {
        this.fundTrdFlag = fundTrdFlag;
    }

    public Integer getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Integer openDate) {
        this.openDate = openDate;
    }

    public Integer getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(Integer closeDate) {
        this.closeDate = closeDate;
    }

    public Integer getPrintDate() {
        return printDate;
    }

    public void setPrintDate(Integer printDate) {
        this.printDate = printDate;
    }

    public Integer getFundPrintDate() {
        return fundPrintDate;
    }

    public void setFundPrintDate(Integer fundPrintDate) {
        this.fundPrintDate = fundPrintDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public String getPolicyKind() {
        return policyKind;
    }

    public void setPolicyKind(String policyKind) {
        this.policyKind = policyKind;
    }

    public String getCobusiFlag() {
        return cobusiFlag;
    }

    public void setCobusiFlag(String cobusiFlag) {
        this.cobusiFlag = cobusiFlag;
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = reportKind;
    }

    public String getSepecialFee() {
        return sepecialFee;
    }

    public void setSepecialFee(String sepecialFee) {
        this.sepecialFee = sepecialFee;
    }

    public String getSpecialKind() {
        return specialKind;
    }

    public void setSpecialKind(String specialKind) {
        this.specialKind = specialKind;
    }

    public String getSfBankCode() {
        return sfBankCode;
    }

    public void setSfBankCode(String sfBankCode) {
        this.sfBankCode = sfBankCode;
    }

    public String getSfOpenMoney() {
        return sfOpenMoney;
    }

    public void setSfOpenMoney(String sfOpenMoney) {
        this.sfOpenMoney = sfOpenMoney;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(String activeFlag) {
        this.activeFlag = activeFlag;
    }

    public String getNoticeWay() {
        return noticeWay;
    }

    public void setNoticeWay(String noticeWay) {
        this.noticeWay = noticeWay;
    }

    public String getBillProWay() {
        return billProWay;
    }

    public void setBillProWay(String billProWay) {
        this.billProWay = billProWay;
    }

    public String getCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(String creditFlag) {
        this.creditFlag = creditFlag;
    }

    public String getCreditFund() {
        return creditFund;
    }

    public void setCreditFund(String creditFund) {
        this.creditFund = creditFund;
    }

    public String getCreditStatus() {
        return creditStatus;
    }

    public void setCreditStatus(String creditStatus) {
        this.creditStatus = creditStatus;
    }

    public String getLastCreditStatus() {
        return lastCreditStatus;
    }

    public void setLastCreditStatus(String lastCreditStatus) {
        this.lastCreditStatus = lastCreditStatus;
    }
}
