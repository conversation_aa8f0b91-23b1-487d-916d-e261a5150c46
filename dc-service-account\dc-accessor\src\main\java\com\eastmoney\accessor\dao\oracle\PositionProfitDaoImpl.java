package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.PositionProfitMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.PositionProfit;
import com.eastmoney.common.entity.cal.PositionProfitBO;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/8/3.
 *
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("positionProfitDao")
public class PositionProfitDaoImpl extends BaseDao<PositionProfitMapper, PositionProfit, Long> implements PositionProfitDao {

    @Override
    public List<PositionProfit> getPositionMergeList(Map<String, Object> params) {
        return getMapper().getPositionMergeList(params);
    }

    @Override
    public List<PositionProfit> getSinglePositionMergeProfit(Map<String, Object> params) {
        return getMapper().getSinglePositionMergeProfit(params);
    }

    @Override
    public PositionProfit getHoldSinglePositionProfit(Map<String, Object> params) {
        return getMapper().getHoldSinglePositionProfit(params);
    }

    @Override
    public List<PositionProfit> getClearPositionProfitListByPage(Map<String, Object> params) {
        return getMapper().getClearPositionProfitListByPage(params);
    }

    @Override
    public List<PositionProfit> getSinglePositionProfit(Map<String, Object> params) {
        return getMapper().getSinglePositionProfit(params);
    }

    @Override
    public List<PositionProfit> getOpenPositionList(Map<String, Object> params) {
        return getMapper().getOpenPositionList(params);
    }

    @Override
    public List<PositionProfitBO> getClearPositionList(Map<String, Object> params) {
        return getMapper().getClearPositionList(params);
    }

    @Override
    public PositionProfit getClearPositionSummary(Map<String, Object> params) {
        return null;
    }

    @Override
    public PositionProfit getDiagnosePositionSection(Map<String, Object> params) {
        return null;
    }
}
