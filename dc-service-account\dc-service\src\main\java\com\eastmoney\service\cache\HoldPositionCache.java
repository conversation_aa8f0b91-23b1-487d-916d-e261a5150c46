package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.HoldPositionDao;
import com.eastmoney.common.entity.HoldPosition;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 13:56
 */
@Component
public class HoldPositionCache {
    private static final Logger LOGGER = LoggerFactory.getLogger(HoldPositionCache.class);

    @Bean(name = "holdStartDateCache")
    public LoadingCache<Long, Map<String, Integer>> holdStartDateCache(HoldPositionDao holdPositionDao) {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(100000)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, Map<String, Integer>>() {
                    @Override
                    public Map<String, Integer> load(Long key) throws Exception {
                        try {
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("fundId", key);
                            return holdPositionDao.query(param)
                                    .stream()
                                    .filter(holdPosition -> holdPosition != null
                                            && holdPosition.getMarket() != null
                                            && holdPosition.getStkCode() != null
                                            && holdPosition.getClearStartDate() != null)
                                    .collect(Collectors.toMap(holdPosition -> holdPosition.getMarket() + "-" + StringUtils.trim(holdPosition.getStkCode()),
                                            HoldPosition::getClearStartDate, Math::max));
                        } catch (Exception e) {
                            LOGGER.error("查询持仓表数据异常 fundId={}", key, e);
                        }
                        return new HashMap<>();
                    }
                });
    }

}
