<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.ScheduleLogMapper">

    <sql id="Base_Column_List">
        EID,EITIME,EUTIME,SCHEDULE_NAME,RESULT,ERROR,START_TIME,END_TIME,BIZDATE,SYS_FLAG
    </sql>

    <sql id="Base_Column_Value">
        XTZX.SEQ_EID.NEXTVAL,SYSDATE,SYSDATE,#{schedule_name},#{result,jdbcType=INTEGER},#{error,jdbcType=VARCHAR},#{start_time},#{end_time},#{bizDate},#{sys_flag}
    </sql>

</mapper>