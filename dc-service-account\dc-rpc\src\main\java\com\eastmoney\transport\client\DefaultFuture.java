/*
 * Copyright 1999-2011 Alibaba Group.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.eastmoney.transport.client;

import com.eastmoney.transport.exception.RemotingException;
import com.eastmoney.transport.exception.TimeoutException;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.model.Response;
import com.eastmoney.transport.util.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * DefaultFuture.
 */
public class DefaultFuture implements ResponseFuture{
    private static final Logger logger = LoggerFactory.getLogger(DefaultFuture.class);
//    public static final Map<Long, MessageChannel>  CHANNELS   = new ConcurrentHashMap<Long, MessageChannel>();
//    public static final Map<Long, DefaultFuture> FUTURES   = new ConcurrentHashMap<Long, DefaultFuture>();
    public static final Map<String, MessageChannel>  CHANNELS   = new ConcurrentHashMap<String, MessageChannel>();
    public static final Map<String, DefaultFuture> FUTURES   = new ConcurrentHashMap<String, DefaultFuture>();

    // invoke id.
//    private final long                            id;
    private final String                            id;
    private final MessageChannel                  messageChannel;
    private final Message message;
    private final int                             timeout;
    private final Lock                            lock = new ReentrantLock();
    private final Condition                       done = lock.newCondition();
    private final long                            requestTime = System.currentTimeMillis();
    private volatile Response response;

    public DefaultFuture(MessageChannel messageChannel, Message message, int timeout){
        this.messageChannel = messageChannel;
        this.message = message;
//        this.id = message.getRequestId();
        this.id = new String(message.getRequestId());
        this.timeout = timeout;
        // put into waiting map.
        FUTURES.put(id, this);
        CHANNELS.put(id, messageChannel);
    }
    
    public Object get() throws RemotingException {
        return get(timeout);
    }

    public Object get(int timeout) throws RemotingException {
        if (timeout <= 0) {
            timeout = Constants.TIMEOUT;
        }
        if (response == null) {
            lock.lock();
            try {
                while (response == null) {
                    done.await(timeout, TimeUnit.SECONDS);
                    if (response != null || System.currentTimeMillis() - requestTime > timeout + 1000) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                throw new RemotingException(messageChannel,"get方法被中断",e);
            } finally {
                lock.unlock();
            }
            if (response == null) {
                //response为空说明是由于达到超时时间放弃等待,处理MAP中的对应的数据，之后若数据有返回，调用DefaultFuture.received方法也无法找到对应的记录
                throw new TimeoutException(messageChannel, getTimeoutMessage());
            }
        }
        return returnFromResponse();
    }
    
    public void cancel(){
        Response errorResult = new Response(id);
        errorResult.setErrorMessage("任务已取消");
        response = errorResult ;
        FUTURES.remove(id);
        CHANNELS.remove(id);
    }

    private Object returnFromResponse() throws RemotingException {
        Response res = response;
        if (res == null) {
            throw new IllegalStateException("response为空");
        }
        if (res.getStatus() == Response.OK) {
            return res.getMessage();
        }
        //下面的情况是由于扫描线程处理过的，封装了超时了res
        throw new RemotingException(messageChannel, res.getErrorMessage());
    }

    public Message getMessage() {
        return message;
    }

    public static DefaultFuture getFuture(long id) {
        return FUTURES.get(id);
    }

    public static void received(Response response) {
        try {
            DefaultFuture future = FUTURES.remove(response.getId());
            if (future != null) {
                future.doReceived(response);
            } else {
                //如果future为空，说明信息返回时间大于超时时间，future已经被移除了(超时扫描线程处理)
                logger.warn("超时返回，返回时间："
                            + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date())) 
                            + ", response " + response 
                            );
            }
        } finally {
            CHANNELS.remove(response.getId());
        }
    }

    private void doReceived(Response res) {
        lock.lock();
        try {
            response = res;
            if (done != null) {
                done.signal();
            }
        } finally {
            lock.unlock();
        }
    }

    private String getTimeoutMessage() {
        long nowTimestamp = System.currentTimeMillis();
        return "请求超时, 请求时间: "
                    + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(requestTime))) + ", 结束时间: "
                    + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date())) + ","
                    + " elapsed: " + (nowTimestamp - requestTime) + " ms, timeout: "
                    + timeout + " ms," + message ;
    }

    public boolean isDone() {
        return response != null;
    }
    public long getRequestTime() {
        return requestTime;
    }
    public long getTimeout() {
        return timeout;
    }

    public String getId() {
        return id;
    }
    private static class RemotingInvocationTimeoutScan implements Runnable {

        public void run() {
            while (true) {
                try {
                    for (DefaultFuture future : FUTURES.values()) {
                        if (future == null || future.isDone()) {
                            continue;
                        }
                        if (System.currentTimeMillis() - future.getRequestTime() > future.getTimeout()) {
                            Response timeoutResponse = new Response(future.getId());
                            timeoutResponse.setStatus(Response.TIMEOUT);
                            timeoutResponse.setErrorMessage(future.getTimeoutMessage());
                            // handle response.
                            logger.info("扫描线程正在对超时请求进行移除" + timeoutResponse);
                            DefaultFuture.received(timeoutResponse);
                        }
                    }
                    Thread.sleep(1);
                } catch (Throwable e) {
                    logger.error("Exception when scan the timeout invocation of remoting.", e);
                }
            }
        }
    }

    static {
//        Thread th = new Thread(new RemotingInvocationTimeoutScan(), "ResponseTimeoutScanTimer");
//        th.setDaemon(true);
//        th.start();
    }
}