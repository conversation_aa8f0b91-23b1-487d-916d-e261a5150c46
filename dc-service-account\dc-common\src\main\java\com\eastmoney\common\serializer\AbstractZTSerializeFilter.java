package com.eastmoney.common.serializer;

import com.alibaba.fastjson.serializer.NameFilter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by sunyuncai on 2017/2/6.
 */
public abstract class AbstractZTSerializeFilter extends AbstractSerializeFilter implements NameFilter{
    private final Map<String,String> fieldNamePairs = new HashMap();
    private final Map<String,String> reverseFieldNamePairs = new HashMap();

    public AbstractZTSerializeFilter() {
        reverseFieldNamePairs.putAll(genFieldNamePairs());
        if (reverseFieldNamePairs.size() == 0) {
            throw new RuntimeException("返回的字段对为空");
        }
        //获取中台字段名称集合
        Collection<String> ztIncludes = new ArrayList<>();
        for (String include : includes) {
            ztIncludes.add(include);
        }
        includes.clear();
        for (String ztFieldName : ztIncludes) {
            String oriFieldName = reverseFieldNamePairs.get(ztFieldName);
            if (oriFieldName == null) {
                throw new RuntimeException("属性:" + ztFieldName + "不存在");
            }
            includes.add(oriFieldName);
            fieldNamePairs.put(oriFieldName, ztFieldName);
        }
    }

    //中台字段
    protected abstract Collection<String> genIncludes();

    protected abstract Map<String,String> genFieldNamePairs();

    @Override
    public String process(Object object, String name, Object value) {
        String ztFieldName = fieldNamePairs.get(name);
        if (ztFieldName != null) {
            return ztFieldName;
        } else {
            return name;
        }
    }


}
