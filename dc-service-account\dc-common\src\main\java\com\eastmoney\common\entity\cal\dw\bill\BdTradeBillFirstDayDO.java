package com.eastmoney.common.entity.cal.dw.bill;


import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @Project dc-service-account
 * @Description 首次交易相关------ 2023年账单新增表,由数据中心提供
 * <AUTHOR>
 * @Date 2023/11/17 13:47
 * @Version 1.0
 */
public class BdTradeBillFirstDayDO {
    /**
     * 首次交易日期
     */
    private Integer fTradeDate;
    /**
     * 首次交易买/卖的产品名称
     */
    private String fTradeStkName;
    /**
     * 首次交易买/卖的产品编码
     */
    private String fTradeStkCode;
    /**
     * 首次交易买/卖的市场
     */
    private String fTradeMarket;
    /**
     * 首次交易的产品在全年累计交易次数
     */
    private Long fTradeAllCnt;
    /**
     * 首次交易的产品在全年的盈亏金额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal fTradeAllProfit;
    /**
     * 首次打新的日期
     */
    private Integer fSubsNewDate;
    /**
     * 首次中签的日期
     */
    private Integer fStockBallotDate;
    /**
     * 首次中签的产品名称
     */
    private String fStockBallotStkName;
    /**
     * 首次中签的产品的中签收益金额
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal fStockBallotProfit;


    public Integer getFTradeDate() {
        return fTradeDate;
    }

    public void setFTradeDate(Integer fTradeDate) {
        this.fTradeDate = fTradeDate;
    }

    public String getFTradeStkName() {
        return fTradeStkName;
    }

    public void setFTradeStkName(String fTradeStkName) {
        this.fTradeStkName = fTradeStkName;
    }

    public Long getFTradeAllCnt() {
        return fTradeAllCnt;
    }

    public void setFTradeAllCnt(Long fTradeAllCnt) {
        this.fTradeAllCnt = fTradeAllCnt;
    }

    public BigDecimal getFTradeAllProfit() {
        return fTradeAllProfit;
    }

    public void setFTradeAllProfit(BigDecimal fTradeAllProfit) {
        this.fTradeAllProfit = fTradeAllProfit;
    }

    public Integer getFSubsNewDate() {
        return fSubsNewDate;
    }

    public void setFSubsNewDate(Integer fSubsNewDate) {
        this.fSubsNewDate = fSubsNewDate;
    }

    public Integer getFStockBallotDate() {
        return fStockBallotDate;
    }

    public void setFStockBallotDate(Integer fStockBallotDate) {
        this.fStockBallotDate = fStockBallotDate;
    }

    public String getFStockBallotStkName() {
        return fStockBallotStkName;
    }

    public void setFStockBallotStkName(String fStockBallotStkName) {
        this.fStockBallotStkName = fStockBallotStkName;
    }

    public BigDecimal getFStockBallotProfit() {
        return fStockBallotProfit;
    }

    public void setFStockBallotProfit(BigDecimal fStockBallotProfit) {
        this.fStockBallotProfit = fStockBallotProfit;
    }

    public String getFTradeStkCode() {
        return fTradeStkCode;
    }

    public void setFTradeStkCode(String fTradeStkCode) {
        this.fTradeStkCode = fTradeStkCode;
    }

    public String getFTradeMarket() {
        return fTradeMarket;
    }

    public void setFTradeMarket(String fTradeMarket) {
        this.fTradeMarket = fTradeMarket;
    }
}
