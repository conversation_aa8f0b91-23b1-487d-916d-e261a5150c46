package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.HgtMatch;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * @auther $djs
 * @dateTime 2020-10-15 18:32
 * @content 港股通历史成交
 */

public interface TiHgtMatchMapper extends BaseMapper<HgtMatch, Long> {
    /**
     * 获取未交割的成交记录
     *
     * @param params
     * @return
     */
    List<HgtMatch> getUndeliveredMatchList(Map<String, Object> params);

}
