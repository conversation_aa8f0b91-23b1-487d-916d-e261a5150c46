package com.eastmoney.common.entity;

/**
 * Created on 2016/8/9
 * 深圳-新股市场配额
 *
 * <AUTHOR>
 */
public class SZSgMktQuota extends BaseEntity {

    private Integer dbfRec;
    private Long custId;
    private Integer market;
    private String secuId;
    private Integer custQuota;
    private Integer receiveDate;
    private String remark;

    public Integer getDbfRec() {
        return dbfRec;
    }

    public void setDbfRec(Integer dbfRec) {
        this.dbfRec = dbfRec;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Integer getMarket() {
        return market;
    }

    public void setMarket(Integer market) {
        this.market = market;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId.trim();
    }

    public Integer getCustQuota() {
        return custQuota;
    }

    public void setCustQuota(Integer custQuota) {
        this.custQuota = custQuota;
    }

    public Integer getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Integer receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark.trim();
    }
}
