package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/31.
 */
public interface TiMajorBillMapper extends BaseMapper<MajorBill,Long> {
    List<MajorBill> getBestMonth(Map<String, Object> params);

    List<ProfitRateDay> selectMonthProfitRate(Map<String,Object> params);

    /**
     * 查询区间内月收益
     * @param params
     * @return
     */
    List<ProfitDay> selectMonthProfit(Map<String, Object> params);

    /**
     * 查询区间内年收益额
     * @param params
     * @return
     */
    List<ProfitDay> selectYearProfit(Map<String, Object> params);

    /**
     * 查询区间内年收益率
     * @param params
     * @return
     */
    List<ProfitRateDay> selectYearProfitRate(Map<String, Object> params);

    List<MajorBillStrInfo> selectByYearBill(Map<String, Object> params);

}
