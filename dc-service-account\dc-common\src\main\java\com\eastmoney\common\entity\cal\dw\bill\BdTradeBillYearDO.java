package com.eastmoney.common.entity.cal.dw.bill;


/**
 * @Project dc-service-account
 * @Description 2023年账单新增表, 由数据中心提供 ATCENTER.BD_TRADE_BILL_YEAR
 * <AUTHOR>
 * @Date 2023/11/17 13:23
 * @Version 1.0
 */
public class BdTradeBillYearDO {
    /**
     * 资金账号
     */
    private Long fundId;
    /**
     * indexKey
     */
    private Integer indexKey;
    /**
     * 基础信息
     */
    private BdTradeBillBaseInfoDO baseInfo;
    /**
     * 首次交易
     */
    private BdTradeBillFirstDayDO firstDay;

    /**
     * 全年清仓相关
     */
    private BdTradeBillYearClearDO yearClear;

    /**
     * 单日交易次数最多
     */
    private BdTradeBillMostTradeDO mostTrdDay;
    /**
     * 概念热门板块
     */
    private BdTradeBillConceptSectorsDO conceptSectors;

    /**
     * 最高日收益
     * 2024年账单-新增（数据中心提供）
     */
    private BdTradeBillMaxProfitDO maxProfit;

    private BdTradeBillYearClearProfitDO clearProfit;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public BdTradeBillBaseInfoDO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BdTradeBillBaseInfoDO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public BdTradeBillFirstDayDO getFirstDay() {
        return firstDay;
    }

    public void setFirstDay(BdTradeBillFirstDayDO firstDay) {
        this.firstDay = firstDay;
    }

    public BdTradeBillYearClearDO getYearClear() {
        return yearClear;
    }

    public void setYearClear(BdTradeBillYearClearDO yearClear) {
        this.yearClear = yearClear;
    }

    public BdTradeBillMostTradeDO getMostTrdDay() {
        return mostTrdDay;
    }

    public void setMostTrdDay(BdTradeBillMostTradeDO mostTrdDay) {
        this.mostTrdDay = mostTrdDay;
    }

    public BdTradeBillConceptSectorsDO getConceptSectors() {
        return conceptSectors;
    }

    public void setConceptSectors(BdTradeBillConceptSectorsDO conceptSectors) {
        this.conceptSectors = conceptSectors;
    }

    public BdTradeBillMaxProfitDO getMaxProfit() {
        return maxProfit;
    }

    public void setMaxProfit(BdTradeBillMaxProfitDO maxProfit) {
        this.maxProfit = maxProfit;
    }

    public BdTradeBillYearClearProfitDO getClearProfit() {
        return clearProfit;
    }

    public void setClearProfit(BdTradeBillYearClearProfitDO clearProfit) {
        this.clearProfit = clearProfit;
    }
}
