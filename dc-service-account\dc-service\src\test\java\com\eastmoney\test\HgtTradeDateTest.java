package com.eastmoney.test;

import com.eastmoney.accessor.dao.tidb.HgtTradeDateDao;
import com.eastmoney.common.entity.HgtTradeDayDO;
import com.eastmoney.service.cache.HgtTradeDateService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * <AUTHOR>
 * @create 2023/2/13
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class HgtTradeDateTest {
    private static final Logger LOG = LoggerFactory.getLogger(HgtTradeDateTest.class);

    @InjectMocks
    private HgtTradeDateService hgtTradeDateService;

    @InjectMocks
    private HgtTradeDateDao hgtTradeDateDao;

    @Test
    public void test1() {
        Boolean market = hgtTradeDateService.isMarket(20230210);
        LOG.info("market: {}", market);
    }

    @Test
    public void test2() {
        HgtTradeDayDO hgtTradeDay = hgtTradeDateDao.isMarket(20230130);
        LOG.info("hgtTradeDay:{}", hgtTradeDay.toString());
    }
}
