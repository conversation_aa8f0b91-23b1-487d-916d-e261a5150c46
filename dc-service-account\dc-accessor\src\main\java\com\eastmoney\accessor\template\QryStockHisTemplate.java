package com.eastmoney.accessor.template;

import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/8/12.
 */
@Component
public class QryStockHisTemplate {
    public <T> List<T> doMsExcute(QryStockHisCallBack<T> callBack,Map<String,Object> params) {
        Integer bizDate = CommonUtil.convert(params.get("bizDate"), Integer.class);
        int startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        int endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        String qryFlag = CommonUtil.convert(params.get("qryFlag"), String.class);
        if (bizDate == null) {
            if ("1".equals(qryFlag)) {
                bizDate = startDate;
            } else {
                bizDate = endDate;
            }
        }
        int monthDiff;
        if ("1".equals(qryFlag)) {
            monthDiff = DateUtil.getMonthInterval(bizDate, endDate);
        } else {
            monthDiff = DateUtil.getMonthInterval(startDate, bizDate);
        }
        Integer count = CommonUtil.convert(params.get("count"), Integer.class);
        List<T> resultList = new ArrayList<>();

        int bizMonth = bizDate/100;
        for (int i = 0; i <= monthDiff; i++) {
            addTabNameSuffix(params, bizMonth);
            if (resultList.size() >= count) {
                break;
            }
            params.put("count", count - resultList.size());
            List<T> list = callBack.doQuery();
            resultList.addAll(list);

            if (i == 0) {
                params.remove("postStr");
            }
            //增加月份
            bizMonth = getNextBizMonth(bizMonth, qryFlag);
        }
        return resultList;
    }

    public <T> List<T> doOggExcute(QryStockHisCallBack<T> callBack,Map<String, Object> params) {
        return callBack.doQuery();
    }


    private int getNextBizMonth(int bizMonth, String qryFlag) {
        int nextBizMonth;
        if ("1".equals(qryFlag)) {
            if ((bizMonth + "").endsWith("12")) {
                nextBizMonth = (bizMonth / 100 + 1) * 100 + 1;
            } else {
                nextBizMonth = bizMonth + 1;
            }
        } else {
            if ((bizMonth + "").endsWith("01")) {
                nextBizMonth = (bizMonth / 100 - 1) * 100 + 12;
            } else {
                nextBizMonth = bizMonth - 1;
            }
        }
        return nextBizMonth;
    }

    private void addTabNameSuffix(Map<String,Object> params,int bizMonth) {
        String year = String.valueOf(bizMonth).substring(0,4);
        String month = String.valueOf(bizMonth).substring(4,6);
        String tabNameSuffix = "_" + year + "_" + month;
        params.put("tabNameSuffix", tabNameSuffix);
    }
}
