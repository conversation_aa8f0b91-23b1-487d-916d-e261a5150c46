<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.oracle.PositionProfitMapper">
    <resultMap id="BaseResultMap" type="as_positionProfit" >
        <result column="STKCODE" property="stkCode"/>
        <result column="TYPE" property="type"/>
        <result column="STARTDATE" property="startDate"/>
        <result column="ENDDATE" property="endDate"/>
        <result column="TOTAL_BUY" property="totalBuy"/>
        <result column="TOTAL_SALE" property="totalSale"/>
        <result column="PROFIT" property="profit"/>
        <result column="MARKET" property="market"/>
        <result column="TOTALIN" property="totalIn"/>
        <result column="TOTALOUT" property="totalOut"/>
        <result column="TOTALBUYNUM" property="totalBuyNum"/>
        <result column="TOTALSALENUM" property="totalSaleNum"/>
        <result column="TOTALINNUM" property="totalInNum"/>
        <result column="TOTALOUTNUM" property="totalOutNum"/>
        <result column="profitTotal" property="profitTotal"/>
        <result column="holdDays" property="holdDays"/>
        <result column="tradeTotal" property="tradeTotal"/>
        <result column="buyTimes" property="buyTimes"/>
        <result column="saleTimes" property="saleTimes"/>
        <result column="CLEAR_TIMES" property="clearTimes"/>
        <result column="PROFIT_RATE" property="profitRate"/>
        <result column="SUCCESS_RATE" property="successRate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID,STKCODE,ENDDATE,TOTAL_BUY,TOTAL_SALE,PROFIT,SERVERID,MARKET,
        TYPE,STARTDATE,TOTALBUYNUM,TOTALSALENUM,TOTALIN,TOTALOUT,TOTALINNUM,TOTALOUTNUM
    </sql>

    <!-- 获取所有盈亏持仓 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        select * from (
        select t.*,rownum as rno from (
        SELECT<include refid="All_Column"/>
        FROM ATCENTER.POSITION_PROFIT
        <where>
            FUNDID = #{fundId}
            <if test="endDate != null">
                AND ENDDATE = #{endDate}
            </if>
            <if test="serverId != null">
                AND SERVERID = #{serverId}
            </if>
            <if test="queryStartDate != null">
                AND ENDDATE >= #{queryStartDate}
            </if>
            <if test="queryEndDate != null">
                AND ENDDATE &lt;= #{queryEndDate}
            </if>
            <if test='profitFlag == "1"'>
                AND PROFIT > 0
            </if>
            <if test='profitFlag == "2"'>
                AND PROFIT &lt; 0
            </if>
            order by ENDDATE DESC,stkcode asc
        </where>
        ) t
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and ROWNUM <![CDATA[<=]]> #{pageSize} * #{pageNo}
        </if>
        ) a
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and rno <![CDATA[>]]> #{pageSize} * (#{pageNo} - 1)
        </if>
    </select>

    <!-- 合并相同股票盈亏 -->
    <select id="getPositionMergeList" resultMap="BaseResultMap">
        select * from (
            select t.*,rownum as rno from (
                SELECT * FROM (
                    SELECT STKCODE,MARKET,sum(nvl(PROFIT,0)) profitTotal,sum(nvl(HOLDDAYS,0)) holdDays,
                    sum(nvl(BUYTIMES,0)+nvl(SALETIMES,0)+nvl(INTIMES,0)+nvl(OUTTIMES,0)) tradeTotal,count(1) as CLEAR_TIMES,min(ENDDATE) as ENDDATE
                    FROM ATCENTER.POSITION_PROFIT
                    where FUNDID = #{fundId}
                        AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
                    group by STKCODE,market
                ) a
                where 1=1
                <if test='profitFlag != null and profitFlag == "1"'>
                    AND a.profitTotal > 0
                </if>
                <if test='profitFlag != null and profitFlag == "2"'>
                    AND a.profitTotal &lt; 0
                </if>
                <if test='sort != null'>
                   ${sort}
                </if>
            ) t
            where 1 = 1
            <if test="pageSize != null and pageNo != null">
                and ROWNUM <![CDATA[<=]]> #{pageSize} * #{pageNo}
            </if>
        ) b
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and rno <![CDATA[>]]> #{pageSize} * (#{pageNo} - 1)
        </if>
    </select>

    <!-- 查询单支股票汇总信息 -->
    <select id="getSinglePositionMergeProfit" resultMap="BaseResultMap">
        SELECT
            STKCODE,
            MARKET,
            SUM(TOTAL_BUY) as TOTAL_BUY,
            SUM(TOTALIN) as TOTALIN,
            SUM(PROFIT) AS PROFIT,
            SUM(HOLDDAYS) AS HOLDDAYS,
            MIN(STARTDATE) AS STARTDATE,
            MAX(ENDDATE) AS ENDDATE,
            COUNT(FLAG) AS CLEAR_TIMES,
            SUM(FLAG)/ COUNT(FLAG) AS  SUCCESS_RATE,
            AVG(NVL(PROFIT_RATE, 0)) AS PROFIT_RATE,
            SUM(TRADETOTAL) AS TRADETOTAL
        FROM
            (
            SELECT
                #{stkCode} STKCODE,
                TOTAL_BUY,
                TOTALIN,
                MARKET,
                PROFIT,
                CLEARSTARTDATE STARTDATE,
                CLEARENDDATE ENDDATE,
                HOLDDAYS,
                PROFIT_RATE,
                NVL(BUYTIMES,0) + NVL(SALETIMES,0) + NVL(INTIMES,0) + NVL(OUTTIMES,0) TRADETOTAL,
                CASE
                    WHEN PROFIT>0 THEN 1
                    ELSE 0
                END FLAG
            FROM
                ATCENTER.POSITION_PROFIT
            where FUNDID = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    and STKCODE IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and STKCODE = #{stkCode}
                </otherwise>
            </choose>
            and market = #{market} and CALFLAG=1
            <if test="startDate != null and endDate!=null">
                AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
            </if>
            )
        GROUP BY
            STKCODE,MARKET
    </select>


    <!-- 新版已清仓股票列表 根据时间查询/单只股票查询  -->
    <select id="getClearPositionProfitListByPage" resultMap="BaseResultMap">
        select * from (
        select t.*,rownum as rno
        from (select FUNDID,STKCODE,
            CLEARSTARTDATE STARTDATE,
            CLEARENDDATE ENDDATE,
            PROFIT,PROFIT_RATE,MARKET,HOLDDAYS
        FROM ATCENTER.POSITION_PROFIT
        where FUNDID = #{fundId}
            AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
            and CALFLAG=1
        <if test="market != null">
            and MARKET=#{market}
        </if>
        <choose>
            <when test="stkCodeList != null and stkCodeList.size() > 0">
                AND STKCODE IN
                <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <if test="stkCode != null">
                    AND STKCODE = #{stkCode}
                </if>
            </otherwise>
        </choose>
        <if test='sort != null'>
            ${sort}
        </if> ) t
        where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and ROWNUM <![CDATA[<=]]> #{pageSize} * #{pageNo}
        </if>
        ) b where 1 = 1
        <if test="pageSize != null and pageNo != null">
            and b.rno <![CDATA[>]]> #{pageSize} * (#{pageNo} - 1)
        </if>
    </select>


    <select id="getSinglePositionProfit" resultMap="BaseResultMap">
        select STKCODE,market,PROFIT,PROFIT_RATE,
        CLEARSTARTDATE AS STARTDATE,
        CLEARENDDATE AS ENDDATE,
        HOLDDAYS,NVL(BUYTIMES,0) + NVL(SALETIMES,0) + NVL(INTIMES,0) + NVL(OUTTIMES,0) tradeTotal
        from ATCENTER.POSITION_PROFIT
        where fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    and stkcode IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and stkcode = #{stkCode}
                </otherwise>
            </choose>
            and market = #{market}
            <if test="startDate != null and endDate!=null">
                AND CLEARENDDATE BETWEEN #{startDate} and #{endDate}
            </if>
            and CALFLAG=1
        order by ENDDATE desc
    </select>

    <!-- 查询单个股票盈亏 -->
    <select id="getHoldSinglePositionProfit" resultMap="BaseResultMap">
        select b.STKCODE, b.market, b.startDate, rownum
        from (
                 select a.STKCODE, a.market,
                        CASE WHEN MARKET IN ('5', 'S') THEN CLEARSTARTDATE
                            ELSE STARTDATE
                        END AS STARTDATE,
                        rownum
                 from ATCENTER.POSITION_PROFIT a
                 where a.fundid = #{fundId}
                    <choose>
                        <when test="stkCodeList != null and stkCodeList.size() > 0">
                            and a.stkcode in
                            <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="stkCode != null">
                                and a.stkcode = #{stkCode}
                            </if>
                        </otherwise>
                    </choose>
                   and a.market = #{market}
                   and a.ENDDATE is null
                 order by a.startDate desc
             ) b
        where rownum = 1
    </select>

    <select id="getOpenPositionList" resultMap="BaseResultMap">
        select /*+ index(t, PK_LG_POSITION_PROFIT) */  MARKET, STKCODE, MAX(STARTDATE) STARTDATE
        from ATCENTER.POSITION_PROFIT t
        where fundId = #{fundId}
        and calflag = 0
        and ENDDATE is null
        <if test='stkCode != null'>
            and stkcode = #{stkCode}
        </if>
        <if test='market != null'>
            and market = #{market}
        </if>
        group by market, stkcode
    </select>

    <select id="getClearPositionList" resultType="as_positionProfitBO">
        select /*+ index(t, PK_LG_POSITION_PROFIT) */  distinct MARKET, STKCODE
        from ATCENTER.POSITION_PROFIT
        where fundId = #{fundId}
          and calflag = 1
          and ENDDATE is not null
        order by stkcode
    </select>
</mapper>