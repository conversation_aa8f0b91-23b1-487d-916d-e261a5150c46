package com.eastmoney.accessor.dao.tidb;


import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateSectionDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitRateSectionMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateSection;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyongyong on 2016/7/20.
 *
 * 增加updateRank,mergeIntoProfitRate
 * update by huachengqi on 2016/7/27.
 *
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitRateSectionDao")
public class TiProfitRateSectionDaoImpl extends BaseDao<TiProfitRateSectionMapper, ProfitRateSection, Long> implements ProfitRateSectionDao {
}
