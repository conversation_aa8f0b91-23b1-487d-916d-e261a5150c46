package com.eastmoney.quote.app.client;

import java.net.InetSocketAddress;

/**
 * Created by 1 on 15-7-7.
 */
public class RpcConnectionFactory implements RpcConnFactInterface {
    private InetSocketAddress serverAddr;

    public RpcConnectionFactory(String host, int port) {
        this.serverAddr = new InetSocketAddress(host, port);
    }

    public RpcConnection getConnection() throws Throwable {
        return new RpcConnection(this.serverAddr.getHostName(),
                this.serverAddr.getPort());
    }

    public void recycle(RpcConnection connection) throws Throwable {
        if (null != connection) {
            connection.close();
        }
    }

}
