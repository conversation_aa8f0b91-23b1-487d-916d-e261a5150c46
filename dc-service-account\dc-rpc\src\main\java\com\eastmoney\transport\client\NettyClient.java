package com.eastmoney.transport.client;

import com.alibaba.fastjson.JSON;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.transport.codec.MessageDecoder;
import com.eastmoney.transport.codec.MessageEncoder;
import com.eastmoney.transport.exception.RemotingException;
import com.eastmoney.transport.hearbeat.HeartBeatHandler;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.ChannelUtil;
import com.eastmoney.transport.util.Constants;
import com.eastmoney.transport.util.NetUtils;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/27
 * Time: 15:26
 */
public class NettyClient {
    //保证变量可见性，写入操作不依赖变量的当前值或只有单个线程更新当前值
    public volatile Channel channel;
    private String ip;
    private int port;
    //上一次连接的时间点，不一定成功
    private volatile long lastConnectTime ;
    private static org.slf4j.Logger LOG = LoggerFactory.getLogger(NettyClient.class);
    private final boolean isNeedHeartbeat ;
    //配置客户端NIO线程组
    private EventLoopGroup group = new NioEventLoopGroup();

    public NettyClient(String ip, int port) {
        this(ip, port, true);
    }
    public NettyClient(String ip, int port,boolean isNeedHeartbeat) {
        this.ip = ip;
        this.port = port;
        this.isNeedHeartbeat = isNeedHeartbeat;
    }
    public void release() {
        if (group != null) {
            group.shutdownGracefully();
        }
    }

    public ResponseFuture request(String content,String requestId) throws RemotingException {
        if (!isConnected()) {
            LOG.info("连接已断开,手动重连。。。");
            //连接过程中，若连接失败，会直接抛出异常
            connect();
        }
        MessageChannel messageChannel = new MessageChannel(channel);
        LOG.debug("====客户端发送请求," + ChannelUtil.getChannelRoute(channel));
        Message message = new Message((byte) 0, 0, Constants.SOCKET_TYPE_PULL_REQ, null);
//        message.setReplyCipher((byte)1);
        if (StringUtils.isBlank(requestId)) {
            message.setRequestId(CommonUtil.generateUUID().getBytes());
        } else {
            message.setRequestId(requestId.getBytes());
        }
        message.setContent(content.getBytes());
        DefaultFuture future = new DefaultFuture(messageChannel, message,Constants.TIMEOUT);
        try {
            messageChannel.send(message);
        } catch (Throwable e) {
            future.cancel();
            throw new RemotingException("请求发送失败:" + e.getMessage(), e);
        }
        return future;
    }
    public ResponseFuture request(Map<String,Object> params,String requestId) throws RemotingException {
        return request(JSON.toJSONString(params), requestId);
    }
    public ResponseFuture request(String content) throws RemotingException {
        return request(content, null);
    }

    //订阅
    public ResponseFuture subscribe() throws RemotingException {
        if (!isConnected()) {
            LOG.info("连接已断开,手动重连。。。");
            //连接过程中，若连接失败，会直接抛出异常
            connect();
        }
        MessageChannel messageChannel = new MessageChannel(channel);
        LOG.debug("====客户端发送请求," + ChannelUtil.getChannelRoute(channel));
        Message message = new Message((byte) 0, 0, Constants.SOCKET_TYPE_KEY_REQ, null);

        message.setRequestId(CommonUtil.generateUUID().getBytes());
//        message.setContent(content.getBytes());
        DefaultFuture future = new DefaultFuture(messageChannel, message,Constants.TIMEOUT*2);
        try {
            messageChannel.send(message);
        } catch (Throwable e) {
            future.cancel();
            throw new RemotingException("请求发送失败:" + e.getMessage(), e);
        }
        return future;
    }
    //请求秘钥
    public ResponseFuture getDesKey() throws RemotingException {
        if (!isConnected()) {
            LOG.info("连接已断开,手动重连。。。");
            //连接过程中，若连接失败，会直接抛出异常
            connect();
        }
        MessageChannel messageChannel = new MessageChannel(channel);
        LOG.debug("====客户端请求密钥," + ChannelUtil.getChannelRoute(channel));
        Message message = new Message((byte) 0, 0, Constants.SOCKET_TYPE_KEY_REQ, null);

        message.setRequestId(CommonUtil.generateUUID().getBytes());
//        message.setContent(content.getBytes());
        DefaultFuture future = new DefaultFuture(messageChannel, message,Constants.TIMEOUT*2);
        try {
            messageChannel.send(message);
        } catch (Throwable e) {
            future.cancel();
            throw new RemotingException("请求发送失败:" + e.getMessage(), e);
        }
        return future;
    }

    public synchronized void connect() throws RemotingException {
        if (isConnected()) {
            return;
        }
        lastConnectTime = System.currentTimeMillis();
        try {
            Bootstrap b = new Bootstrap();
            b.group(group).channel(NioSocketChannel.class)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        public void initChannel(SocketChannel ch) throws Exception {
                            ch.pipeline().addLast("decode", new MessageDecoder());
                            ch.pipeline().addLast("encode", new MessageEncoder());
                            if (isNeedHeartbeat) {
                                ch.pipeline().addLast("heartbeat", new HeartBeatHandler(false, Constants.HEART_BEAT_INTERVAL));
                            }
//                            ch.pipeline().addLast("clientHandler", clientHandler);
                            ch.pipeline().addLast("clientHandler", new ClientHandler());
                        }
                    });
            // 发起异步连接操作
            ChannelFuture f = b.connect(ip, port).sync();
//            lastConnectTime = System.currentTimeMillis();
            if (this.channel != null) {
                channel.close();
            }
            this.channel = f.channel();
        } catch (Throwable e) {
            throw new RemotingException("无法连接到服务器"+NetUtils.getLocalHost() + " -> " + ip + ":" + port
                    + ", cause: " + e.getMessage(), e);
        }
    }

    public boolean isConnected() {
        return channel != null && channel.isActive();
    }

    public long getLastConnectTime() {
        return lastConnectTime;
    }

    public static void main(String[] args) throws RemotingException {
        NettyClient client;
        client = new NettyClient("************",58581);
        client.connect();
//        new NettyClient("************",5858).connect();
        while (true) {
            if (!client.isConnected()) {
                client.connect();
            }
        }
    }
}
