package com.eastmoney.service.rpc.pull;

import com.eastmoney.common.util.CommConstants;
import com.eastmoney.transport.client.NettyClient;
import com.eastmoney.transport.client.NettyClientHolder;
import com.eastmoney.transport.client.ResponseFuture;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/9/16
 * Time: 14:43
 */
public class ClientToServerTest {
    protected static org.slf4j.Logger LOG = LoggerFactory.getLogger(ClientToServerTest.class);
//    private static ScheduledExecutorService executor = Executors.newScheduledThreadPool(Constants.CLIENT_THREAD_POOL);
    private static ExecutorService executor;
    public static void main(String[] args) throws Exception{
        if (args == null || args.length == 0) {
//            args = new String[]{"400","100"};
//            args = new String[]{"400","20"};
            args = new String[]{"1","1"};
        }
        int reqNum = Integer.parseInt(args[0]);
        int poolSize = Integer.parseInt(args[1]);
        executor = Executors.newFixedThreadPool(poolSize);
        List<Task> taskList = new ArrayList<Task>();
        Constants.REQ_NUM = reqNum;
        for (int i = 0; i < Constants.REQ_NUM; i++) {
            taskList.add(new Task());
        }
        long beginTime = System.currentTimeMillis();
        List<Future<Object>> futures = executor.invokeAll(taskList);
        long endTime = System.currentTimeMillis();
        System.out.println("====总耗时：" + (endTime - beginTime) + "ms");
        executor.shutdownNow();
        executor.shutdown();

//        Thread.sleep(5000);
//        System.exit(0);
    }

   private static class Task implements Callable<Object> {
        private static AtomicInteger counter = new AtomicInteger(0);
        private static String fixContent = "";
        private static volatile long beginTime = System.currentTimeMillis();
        private static volatile long lastTime = System.currentTimeMillis();
        static {
            if (Constants.SEND_BYTE_SIZE > 0) {
                for (int i = 0; i < Constants.SEND_BYTE_SIZE - 10; i++) {
                    fixContent += "0";
                }
            }
        }
        public Object call() {
            NettyClient nettyClient = null;
            try {
//                nettyClient = NettyClientHolder.getNettyClient("third-pull");
//                List<Input> inputList = InputBuilder.getFunInputList();
//                for (Input input : inputList) {
//                    ResponseFuture future = nettyClient.request(JSON.toJSONString(input));
//                    Message message = (Message) future.get();
//                    String replyContent = new String(message.getContent(), "UTF-8");
//                    LOG.info("返回内容:" + replyContent);
//                }
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_BANK_TRAN_REC);
//                Input input = InputBuilder.buildInput("ssssssssssssss");
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_ORDER_REC);
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_ORDER_REC_HGT);
//                Input input = InputBuilder.buildInput("getRegionProfitByUnit");
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_DELIVERY_ORDER_SUM);
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_ASSET_DETAIL);
//                Input input = InputBuilder.buildInput(FunCodeConstants.GET_ASSET_WORTH_LIST);
//                String inputJson = "{\"Jjrzh\":\"*********\",\"fundIds\":\"************,************,************,************,************,************,************,************,************,************\",\"funCode\":\"getDeliveryOrderSum\",\"bizDate\":201605}";
//                String inputJson = "{\"Jjrzh\":\"*********\",\"Ym\":\"1\",\"fundIds\":\"540700264576,540700264578,540800000001,540800000002,540800000009,540800000111,540800000133,540800000199,540800000208,540800000220,************,************,************,************,************,************,************,************,************,************\",\"funCode\":\"getDeliveryOrderSum\",\"Qqhs\":\"20\",\"bizDate\":201605}";
//                String inputJson = "{\"fundId\":\"540700008505\",\"funCode\":\"getFundAsset\"}";
//                String inputJson = "{\"fundId\":\"5408002320771\",\"funCode\":\"getProfitInfo\"}";
//                String inputJson = "{\"startDate\":20161221,\"unit\":\"M\",\"fundId\":\"541000000025\",\"funCode\":\"getProfitRateDayList\",\"endDate\":20161221}";
//                String inputJson = "{\"fundId\":\"540700265771\",\"funCode\":\"getRealTimePositionList\"}";
//                String inputJson = "{\"fundId\":540700265561,\"count\":10,\"funCode\":\"getRealTimePositionList\",\"postStr\":0}";
//                String inputJson = "{'funCode':'getBindChangeList','beginId':0}";
//                String inputJson = "{'flag':'0','flag1':'0','fundId':540700265616,'funCode':'getRealTimeAsset'}";
//                String inputJson = "{'sessionId':'123','fundId':110100031181,'funCode':'getRealTimeAsset','flag1':'0','flag2':0,'moneyType':0}";
//                String inputJson = "{'funCode':'getRealTimeAsset','fundId':'540700265561','moneyType':'0','stockFlag':'0'}";
//                String inputJson = "{\"unit\":\"ALL\",\"profitFlag\":\"1\",\"count\":10,\"fundId\":\"540700265561\",\"requestId\":\"35585ccc16cc4e0ca419ec26898db883\",\"pageNo\":\"1\",\"funCode\":\"getPositionProfitList\",\"lastSno\":\"\",\"pageSize\":\"5\"}";
//                String inputJson = "{\"Jjrzh\":\"*********\",\"Ym\":\"1\",\"fundIds\":\"540drop 7002645761\",\"funCode\":\"getDeliveryOrderSum\",\"Qqhs\":\"20\",\"bizDate\":201605}";
//                String inputJson = "{\"unit\":\"P3M\",\"Syspm_ex\":\"\",\"fundId\":\"540300181332\",\"funCode\":\"getAssetWorthTrend\"}";
//                System.out.println(JSON.toJSONString(input));
//                ResponseFuture future = nettyClient.request(JSON.toJSONString(input));
                String inputJson = "{\"fundId\":540700011515,\"unit\":'W',\"userId\":\"123456\",\"funCode\":\"getProfitRateRankInfo\"}";
//                String inputJson = "{\"zjzh\":540700265588,\"khdm\":540700265992,\"jgbm\":\"5407\",\"funCode\":\"getRealTimePositionList\",'zqdm':'000001'}";
//                String inputJson = "{\"dwc\":\"\",\"funCode\":\"getRealTimePositionList\",\"jgbm\":\"3101\",\"khdm\":\"212145\",\"market\":\"\",\"qqhs\":0,\"zjzh\":\"310100011133\",\"zqdm\":\"\"}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getRealTimePositionList\"}";
//                String inputJson = "{\"qryFlag\":\"1\",\"fundId\":\"540700000032\",\"custProp\":\"0\",\"count\":\"10\",\"startDate\":20160101,\"endDate\":\"20161225\",\"custId\":\"540700000032\",\"funCode\":\"getMatchHis\",'orgId':5407}";
//                String inputJson = "{\"qryFlag\":\"1\",\"fundId\":\"540700265537\",\"custProp\":\"0\",\"count\":\"10\",\"startDate\":20160101,\"endDate\":\"20161225\",\"custId\":\"540700265941\",\"funCode\":\"getOrderHis\",'orgId':5407}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getRealTimeAsset\"}";
//                String inputJson = "{\"custId\":\"310100010810\",\"funCode\":\"getDayProfit\",\"fundId\":\"310100010810\",\"orgId\":\"6666666\"}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getDayProfit\"}";
//                String inputJson = "{\"fundId\":************,'startDate':********,'endDate':********,\"funCode\":\"getProfitDayList\"}";
//                String inputJson = "{\"fundId\"ty:************,\"funCode\":\"getRealTimePosition\"}";
//                String inputJson = "{\"unit\":\"P3M\",\"fundId\":*************,\"funCode\":\"getProfitRateRankInfo\"}";
//                ResponseFuture future = nettyClient.request(inputJson);
                ResponseFuture future = NettyClientHolder.request(CommConstants.SERVER_ACCOUNT, inputJson);
                Message message = (Message) future.get();
                String replyContent = new String(message.getContent(), "UTF-8");
                LOG.info("返回内容:" + replyContent);

                if ((counter.incrementAndGet()%10000) == 0) {
                    long nowTime = System.currentTimeMillis();
                    System.out.println("====已执行数据量:" + counter.get() + " 耗时:" + (nowTime - beginTime) + "ms,万笔耗时：" + (nowTime - lastTime) + "ms");
                    lastTime = nowTime;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }
}

