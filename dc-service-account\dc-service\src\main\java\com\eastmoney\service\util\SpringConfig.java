package com.eastmoney.service.util;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * application.yml 配置项
 *
 * <AUTHOR>
 * @date 2023/11/29
 */
@Configuration
@ConfigurationProperties
public class SpringConfig {
    private Integer pullServerPort = 5858;
    private Integer pushServerPort = 5999;
    private Integer pull_server_thread_pool;
    private Integer pull_server_queue_capacity;
    private Integer http_server_thread_pool;
    private Integer http_server_queue_capacity;
    private Integer page_size;
    private Integer codis_open_flag;
    private Integer temp_profit_cal_time;
    private Integer tidb_flag;
    private Integer CUSTOMIZE_DATE_RANGE_DAY = 1830;

    public Integer getPushServerPort() {
        return pushServerPort;
    }

    public void setPushServerPort(Integer pushServerPort) {
        this.pushServerPort = pushServerPort;
    }

    public Integer getHttp_server_thread_pool() {
        return http_server_thread_pool;
    }

    public void setHttp_server_thread_pool(Integer http_server_thread_pool) {
        this.http_server_thread_pool = http_server_thread_pool;
    }

    public Integer getHttp_server_queue_capacity() {
        return http_server_queue_capacity;
    }

    public void setHttp_server_queue_capacity(Integer http_server_queue_capacity) {
        this.http_server_queue_capacity = http_server_queue_capacity;
    }

    public Integer getPullServerPort() {
        return pullServerPort;
    }

    public void setPullServerPort(Integer pullServerPort) {
        this.pullServerPort = pullServerPort;
    }

    public Integer getPull_server_thread_pool() {
        return pull_server_thread_pool;
    }

    public void setPull_server_thread_pool(Integer pull_server_thread_pool) {
        this.pull_server_thread_pool = pull_server_thread_pool;
    }

    public Integer getPull_server_queue_capacity() {
        return pull_server_queue_capacity;
    }

    public void setPull_server_queue_capacity(Integer pull_server_queue_capacity) {
        this.pull_server_queue_capacity = pull_server_queue_capacity;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public Integer getCodis_open_flag() {
        return codis_open_flag;
    }

    public void setCodis_open_flag(Integer codis_open_flag) {
        this.codis_open_flag = codis_open_flag;
    }

    public Integer getTemp_profit_cal_time() {
        return temp_profit_cal_time;
    }

    public void setTemp_profit_cal_time(Integer temp_profit_cal_time) {
        this.temp_profit_cal_time = temp_profit_cal_time;
    }

    public Integer getTidb_flag() {
        return tidb_flag;
    }

    public void setTidb_flag(Integer tidb_flag) {
        this.tidb_flag = tidb_flag;
    }

    public Integer getCUSTOMIZE_DATE_RANGE_DAY() {
        return CUSTOMIZE_DATE_RANGE_DAY;
    }

    public void setCUSTOMIZE_DATE_RANGE_DAY(Integer CUSTOMIZE_DATE_RANGE_DAY) {
        this.CUSTOMIZE_DATE_RANGE_DAY = CUSTOMIZE_DATE_RANGE_DAY;
    }
}
