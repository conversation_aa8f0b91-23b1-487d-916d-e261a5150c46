package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

import java.util.Date;

/**
 * Created by robin on 2016/7/18.
 * 历史资产信息
 * <AUTHOR>
 */
public class AssetHis extends BaseEntityCal {

    private Long fundId;//资金帐号
    private Double asset;//当日总资产
    private Double shiftOut;//当日转出
    private Double shiftIn;//当日转入
    private Double mktval;//当日股票市值
    private Integer startDate;//统计起始日期
    private Double assetInit;//初始总资产
    private double shiftOutTotal;//累计转出
    private double shiftInTotal;//累计转入
    private double profitTotal;//总收益
    private Double otcShiftOutTotal;    //OTC累计转出金额
    private Double otcShiftInTotal;     //OTC累计转入金额
    private Double shareShiftOutTotal;  //股份累计转出金额
    private Double shareShiftInTotal;   //股份累计转入金额
    private Double otherShiftOutTotal;  //其他累计转出金额
    private Double otherShiftInTotal;   //其他累计转入金额

    private Double otcNetTransfer;  //OTC净转入
    private Double otherNetTransfer;    //其他净转入
    private Double bankNetTransfer;    //银证净转入
    private Double netTransfer;    //其他净转入
    private Double otcAsset;    //OTC资产

    /**
     * 更新时间
     */
    private Date euTime;

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public Double getNetTransfer() {
        return netTransfer;
    }

    public void setNetTransfer(Double netTransfer) {
        this.netTransfer = netTransfer;
    }

    public Double getOtcNetTransfer() {
        return otcNetTransfer;
    }

    public void setOtcNetTransfer(Double otcNetTransfer) {
        this.otcNetTransfer = otcNetTransfer;
    }

    public Double getOtherNetTransfer() {
        return otherNetTransfer;
    }

    public void setOtherNetTransfer(Double otherNetTransfer) {
        this.otherNetTransfer = otherNetTransfer;
    }

    public Double getBankNetTransfer() {
        return bankNetTransfer;
    }

    public void setBankNetTransfer(Double bankNetTransfer) {
        this.bankNetTransfer = bankNetTransfer;
    }

    public Double getOtcShiftOutTotal() {
        return otcShiftOutTotal;
    }

    public void setOtcShiftOutTotal(Double otcShiftOutTotal) {
        this.otcShiftOutTotal = otcShiftOutTotal;
    }

    public Double getOtcShiftInTotal() {
        return otcShiftInTotal;
    }

    public void setOtcShiftInTotal(Double otcShiftInTotal) {
        this.otcShiftInTotal = otcShiftInTotal;
    }

    public Double getShareShiftOutTotal() {
        return shareShiftOutTotal;
    }

    public void setShareShiftOutTotal(Double shareShiftOutTotal) {
        this.shareShiftOutTotal = shareShiftOutTotal;
    }

    public Double getShareShiftInTotal() {
        return shareShiftInTotal;
    }

    public void setShareShiftInTotal(Double shareShiftInTotal) {
        this.shareShiftInTotal = shareShiftInTotal;
    }

    public Double getOtherShiftOutTotal() {
        return otherShiftOutTotal;
    }

    public void setOtherShiftOutTotal(Double otherShiftOutTotal) {
        this.otherShiftOutTotal = otherShiftOutTotal;
    }

    public Double getOtherShiftInTotal() {
        return otherShiftInTotal;
    }

    public void setOtherShiftInTotal(Double otherShiftInTotal) {
        this.otherShiftInTotal = otherShiftInTotal;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Double getAsset() {
        return asset;
    }

    public void setAsset(Double asset) {
        this.asset = asset;
    }

    public Double getShiftOut() {
        return shiftOut;
    }

    public void setShiftOut(Double shiftOut) {
        this.shiftOut = shiftOut;
    }

    public Double getShiftIn() {
        return shiftIn;
    }

    public void setShiftIn(Double shiftIn) {
        this.shiftIn = shiftIn;
    }

    public Double getMktval() {
        return mktval;
    }

    public void setMktval(Double mktval) {
        this.mktval = mktval;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Double getAssetInit() {
        return assetInit;
    }

    public void setAssetInit(Double assetInit) {
        this.assetInit = assetInit;
    }

    public double getShiftOutTotal() {
        return shiftOutTotal;
    }

    public void setShiftOutTotal(double shiftOutTotal) {
        this.shiftOutTotal = shiftOutTotal;
    }

    public double getShiftInTotal() {
        return shiftInTotal;
    }

    public void setShiftInTotal(double shiftInTotal) {
        this.shiftInTotal = shiftInTotal;
    }

    public double getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public static AssetHis of(AssetHis assetHis){
        AssetHis asset = new AssetHis();
        asset.setAsset(assetHis.getAsset());
        asset.setOtcAsset(assetHis.getOtcAsset());
        asset.setShiftInTotal(assetHis.getShiftInTotal());
        asset.setShiftOutTotal(assetHis.getShiftOutTotal());
        return asset;
    }
}
