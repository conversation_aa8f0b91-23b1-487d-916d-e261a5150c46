package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.StockCodeAlterDao;
import com.eastmoney.accessor.mapper.tidb.TiStockCodeAlterMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.StockCodeAlter;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("stockCodeAlterDao")
public class TiStockCodeAlterDaoImpl extends BaseDao<TiStockCodeAlterMapper, StockCodeAlter, Integer> implements StockCodeAlterDao {


    @Override
    public List<StockCodeAlter> getStockCodeAlterList(Map<String, Object> params) {
        return getMapper().getStockCodeAlterList(params);
    }
}
