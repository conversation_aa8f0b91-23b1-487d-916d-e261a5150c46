package com.eastmoney.common.entity.fundia;

public class FundCombFeeClearSum {
    private Long fundId;
    private String investId;
    private Double investFee;
    private Double serviceFee;
    private Double custMaintenFee;
    private Double redeemFee;
    private Double convertFee;
    private Double applyFee;
    private String lastInvestId;
    private Double lastInvestFee;
    private Double lastServiceFee;
    private Double lastCustMaintenFee;
    private Double lastRedeemFee;
    private Double lastConvertFee;
    private Double lastApplyFee;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getInvestId() {
        return investId;
    }

    public void setInvestId(String investId) {
        this.investId = investId;
    }

    public Double getInvestFee() {
        return investFee;
    }

    public void setInvestFee(Double investFee) {
        this.investFee = investFee;
    }

    public Double getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(Double serviceFee) {
        this.serviceFee = serviceFee;
    }

    public Double getCustMaintenFee() {
        return custMaintenFee;
    }

    public void setCustMaintenFee(Double custMaintenFee) {
        this.custMaintenFee = custMaintenFee;
    }

    public Double getRedeemFee() {
        return redeemFee;
    }

    public void setRedeemFee(Double redeemFee) {
        this.redeemFee = redeemFee;
    }

    public Double getConvertFee() {
        return convertFee;
    }

    public void setConvertFee(Double convertFee) {
        this.convertFee = convertFee;
    }

    public Double getApplyFee() {
        return applyFee;
    }

    public void setApplyFee(Double applyFee) {
        this.applyFee = applyFee;
    }

    public String getLastInvestId() {
        return lastInvestId;
    }

    public void setLastInvestId(String lastInvestId) {
        this.lastInvestId = lastInvestId;
    }

    public Double getLastInvestFee() {
        return lastInvestFee;
    }

    public void setLastInvestFee(Double lastInvestFee) {
        this.lastInvestFee = lastInvestFee;
    }

    public Double getLastServiceFee() {
        return lastServiceFee;
    }

    public void setLastServiceFee(Double lastServiceFee) {
        this.lastServiceFee = lastServiceFee;
    }

    public Double getLastCustMaintenFee() {
        return lastCustMaintenFee;
    }

    public void setLastCustMaintenFee(Double lastCustMaintenFee) {
        this.lastCustMaintenFee = lastCustMaintenFee;
    }

    public Double getLastRedeemFee() {
        return lastRedeemFee;
    }

    public void setLastRedeemFee(Double lastRedeemFee) {
        this.lastRedeemFee = lastRedeemFee;
    }

    public Double getLastConvertFee() {
        return lastConvertFee;
    }

    public void setLastConvertFee(Double lastConvertFee) {
        this.lastConvertFee = lastConvertFee;
    }

    public Double getLastApplyFee() {
        return lastApplyFee;
    }

    public void setLastApplyFee(Double lastApplyFee) {
        this.lastApplyFee = lastApplyFee;
    }
}
