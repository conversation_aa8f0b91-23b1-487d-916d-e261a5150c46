/*
 * Copyright 1999-2011 Alibaba Group.
 *  
 */
package com.eastmoney.transport.exception;

import com.eastmoney.transport.client.MessageChannel;
import com.eastmoney.transport.util.ChannelUtil;

/**
 * <AUTHOR>
 * @export
 */
public class RemotingException extends Exception {

    private static final long serialVersionUID = -3160452149606778709L;

    public RemotingException(MessageChannel msgChannel, String msg){
        this(msgChannel, msg, null);
    }

    public RemotingException(String message, Throwable cause) {
        this(null, message, cause);
    }

    public RemotingException(MessageChannel msgChannel, Throwable cause){
        this(msgChannel,null, cause);
    }

    public RemotingException(MessageChannel msgChannel, String message, Throwable cause) {
        super(msgChannel == null ? message : message + ChannelUtil.getChannelRoute(msgChannel.getChannel()), cause);
    }
}