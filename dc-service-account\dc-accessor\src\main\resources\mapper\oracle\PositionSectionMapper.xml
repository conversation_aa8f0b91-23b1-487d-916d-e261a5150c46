<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.PositionSectionMapper">
    <resultMap id="BaseResultMap" type="as_positionSection" >
        <result column="FUNDID" property="fundId"/>
        <result column="UNIT" property="unit"/>
        <result column="TRADES_SECURITY_NUM" property="tradesSecurityNum"/>
        <result column="GAIN_SECURITY_NUM" property="gainSecurityNum"/>
        <result column="TRADES_NUM" property="tradesNum"/>
        <result column="GAIN_NUM" property="gainNum"/>
        <result column="PROFIT" property="profit"/>
        <result column="INDEX_DATE" property="indexDate"/>
        <result column="GAIN_PROFIT" property="gainProfit"/>
        <result column="LOSS_PROFIT" property="lossProfit"/>
        <result column="MAX_PROFIT" property="maxProfit"/>
        <result column="MIN_PROFIT" property="minProfit"/>
        <result column="TRADES_HOLDDAYS" property="tradesHolddays"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID,UNIT,TRADES_SECURITY_NUM,GAIN_SECURITY_NUM,TRADES_NUM,GAIN_NUM,PROFIT,INDEX_DATE,GAIN_PROFIT,LOSS_PROFIT,MAX_PROFIT,MIN_PROFIT,TRADES_HOLDDAYS
    </sql>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="All_Column"/>
        FROM ATCENTER.POSITION_SECTION
        <where>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
        </where>
    </select>

    <select id="getPositionProfitStatistics" resultMap="BaseResultMap">
        SELECT
        DISTINCT fundid,TRADES_NUM,GAIN_NUM,TRADES_SECURITY_NUM,
        SUM (CASE WHEN GAINS > 0 THEN 1 ELSE 0 END ) OVER (PARTITION BY fundid) GAIN_SECURITY_NUM,
        profit,
        buyTimes,
        saleTimes,
        gain_profit,
        loss_profit,
        max_profit,
        min_profit,
        trades_holddays
        FROM (
        SELECT
        DISTINCT T .fundid,stkcode,
        COUNT (1) OVER (PARTITION BY fundid) TRADES_NUM,
        SUM (CASE WHEN profit > 0 THEN 1 ELSE 0 END ) OVER (PARTITION BY fundid) GAIN_NUM,
        COUNT (DISTINCT stkcode) OVER (PARTITION BY fundid) TRADES_SECURITY_NUM,
        SUM (profit) OVER (PARTITION BY fundid, stkcode) GAINS,
        SUM (CASE WHEN profit <![CDATA[>]]> 0 THEN profit ELSE 0 END) OVER (PARTITION BY fundid) gain_profit,
        SUM (CASE WHEN profit <![CDATA[<=]]> 0 THEN profit ELSE 0 END) OVER (PARTITION BY fundid) loss_profit,
        MAX (CASE WHEN profit <![CDATA[>]]> 0 THEN profit ELSE 0 END) OVER(PARTITION BY fundid) max_profit,
        MIN (CASE WHEN profit <![CDATA[<]]> 0 THEN profit ELSE 0 END) OVER(PARTITION BY fundid) min_profit,
        SUM (profit) OVER (PARTITION BY fundid) profit,
        SUM (buyTimes) OVER (PARTITION BY fundid) buyTimes,
        SUM (saleTimes) OVER (PARTITION BY fundid) saleTimes,
        SUM(holddays) OVER(PARTITION BY fundid) trades_holddays
        FROM
        ATCENTER.POSITION_PROFIT T
        <where>
            CALFLAG = 1
            <if test="startDate != null">
                AND (
                MARKET IN ('0','1','6','B')
                OR
                (MARKET IN ('5','S')
                AND CLEARENDDATE <![CDATA[>=]]> #{startDate})
                )
            </if>
            <if test="startDate != null">
                AND CLEARENDDATE <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND CLEARENDDATE <![CDATA[<=]]> #{endDate}
            </if>
            <if test="serverId != null">
                AND serverid = #{serverId}
            </if>
            <if test="fundId != null">
                AND fundId = #{fundId}
            </if>
        </where>
        )
    </select>

</mapper>