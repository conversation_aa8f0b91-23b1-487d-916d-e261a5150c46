package com.eastmoney.accessor.util;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SpringContextUtil implements ApplicationContextAware {
	
	private static ApplicationContext applicationContext;
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		 SpringContextUtil.applicationContext = applicationContext;
	}

	public static ApplicationContext getApplicationContext() {
	    return applicationContext;
	}
	
	public static Object getBean(String name) throws BeansException {
	    return applicationContext.getBean(name);
	}
	
	public static Object getBean(String name, Class requiredType) throws BeansException {
	    return applicationContext.getBean(name, requiredType);
	}
	public static <T> T getBeanByNameAndType(String name, Class<T> clazz) throws BeansException {
		return (T)applicationContext.getBean(name);
	}
	public static <T> T getBean(Class<T> requiredType) throws BeansException{
		return applicationContext.getBean(requiredType);
	}
	public static boolean containsBean(String name) {
	   return applicationContext.containsBean(name);
	}
	
	public static boolean isSingleton(String name) throws NoSuchBeanDefinitionException {
		return applicationContext.isSingleton(name);
	}
		 
	public static Class getType(String name) throws NoSuchBeanDefinitionException {
		return applicationContext.getType(name);
	}
 
    /**
     * 如果给定的bean名字在bean定义中有别名，则返回这些别名   
    */
	public static String[] getAliases(String name) throws NoSuchBeanDefinitionException {
		return applicationContext.getAliases(name);
	}

	public static Map<String, Object> getBeansWithAnnotation(Class<? extends Annotation> annotationType) {
		return applicationContext.getBeansWithAnnotation(annotationType);
	}

}
