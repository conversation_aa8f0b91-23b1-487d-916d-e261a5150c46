<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiBdTradeBillYearMapper">

    <resultMap id="BdTradeBillYear" type="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearDO">
        <result column="FUND_CODE" property="fundId"/>
        <result column="INDEXKEY" property="indexKey"/>
        <association property="yearClear" javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearClearDO">
            <result column="CLEAR_NUM" property="clearNum"/>
            <result column="CLEAR_PROFIT_NUM" property="clearProfitNum"/>
            <result column="CLEAR_PROFIT_RATE" property="clearProfitRate"/>
            <result column="PROFIT_BESTMONTH_DATE" property="profitBestMonthDate"/>
            <result column="AVGHOLDDAYS" property="avgHoldDays"/>
        </association>
        <association property="baseInfo" javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillBaseInfoDO">
            <result column="OPEN_DATE" property="openDate"/>
            <result column="bull_open_date_flag" property="bullOpenDateFlag"/>
            <result column="bull_ftrade_flag" property="bullFtradeFlag"/>
            <result column="BULL_PROFIT_BESTMONTH_DATE_FLAG" property="bullProfitBestmonthDateFlag"/>
            <result column="TRADE_TERM" property="tradeTerm"/>
        </association>
        <association property="firstDay" javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillFirstDayDO">
            <result column="FTRADE_DATE" property="fTradeDate"/>
            <result column="FTRADE_STKNAME" property="fTradeStkName"/>
            <result column="FTRADE_ALL_PROFIT" property="fTradeAllProfit"/>
            <result column="FSTOCK_BALLOT_DATE" property="fStockBallotDate"/>
            <result column="FSTOCK_BALLOT_SKTNAME" property="fStockBallotStkName"/>
        </association>
        <association property="mostTrdDay" javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillMostTradeDO">
            <result column="MOST_TRADES_DATE" property="mostTradesDate"/>
            <result column="MOST_TRADES_CNT" property="mostTradesCnt"/>
            <result column="BULL_MOST_TRADES_DATE_FLAG" property="bullMostTradesDateFlag"/>
        </association>
        <association property="conceptSectors"
                     javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillConceptSectorsDO">
            <result column="CONCEPT_SECTORS_CNT" property="conceptSectorsCnt"/>
            <result column="CONCEPT_SECTORS_NAMES" property="conceptSectorsNames"/>
            <result column="CONCEPT_SECTORS_MOST" property="conceptSectorsMost"/>
            <result column="CONCEPT_SECTORS_UPPERC" property="conceptSectorsUpperc"/>
        </association>
        <association property="maxProfit"
                     javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillMaxProfitDO">
            <result column="HIGHEST_PROFIT_DATE" property="bizDate"/>
            <result column="HIGHEST_PROFIT_DAILY" property="profit"/>
            <result column="BULL_HIGHEST_PROFIT_DATE_FLAG" property="bullHighestProfitDateFlag"/>
        </association>
        <association property="clearProfit"
                     javaType="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearClearProfitDO">
            <result column="MAX_CLEAR_PROFIT_DATE" property="maxClearProfitDate"/>
            <result column="MAX_CLEAR_PROFIT_STK" property="maxClearProfitStk"/>
            <result column="MAX_CLEAR_PROFIT" property="maxClearProfit"/>
        </association>
    </resultMap>

    <!--    <sql id="allColumn">-->
    <!--    </sql>-->
    <select id="selectByCondition" resultMap="BdTradeBillYear" parameterType="map">
        SELECT
        *
        FROM ATCENTER.BD_TRADE_BILL_YEAR
        <where>
            <if test="fundId != null and fundId != 0">
                AND FUND_CODE = #{fundId}
            </if>
            <if test="indexKey != null and indexKey != 0">
                AND INDEXKEY = #{indexKey}
            </if>
        </where>
    </select>

</mapper>
