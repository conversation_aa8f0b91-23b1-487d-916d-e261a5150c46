package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.sqlserver.OggSysConfigMapper;
import com.eastmoney.common.entity.SysConfig;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * @date 2022/11/8
 */
@Service
@SqlServerTarget()
public class OggSysConfigDaoImpl extends BaseDao<OggSysConfigMapper, SysConfig, Integer> implements OggSysConfigDao {
    @Override
    public List<SysConfig> getSysConfig(Map<String, Object> map) {
        return getMapper().selectByCondition(map);
    }
}
