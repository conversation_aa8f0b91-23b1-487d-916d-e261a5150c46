package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ZhfxSettleStatusDao;
import com.eastmoney.accessor.mapper.tidb.TiZhfxSettleStatusMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ZhfxSettleStatus;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2022/11/3
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("zhfxSettleStatusDao")
public class TiZhfxSettleStatusDaoImpl extends BaseDao<TiZhfxSettleStatusMapper, ZhfxSettleStatus, <PERSON>> implements ZhfxSettleStatusDao {
}
