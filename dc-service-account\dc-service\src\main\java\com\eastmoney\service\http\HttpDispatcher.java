package com.eastmoney.service.http;

import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.model.MethodMapping;
import org.apache.commons.beanutils.ConvertUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created on 2016/3/8.
 *
 * <AUTHOR>
 */
public class HttpDispatcher {
    private static final Map<String, MethodMapping> ordinaryMethodMappingMap = new HashMap<>();
    private static final Map<String, MethodMapping> regexpMethodMappingMap = new HashMap<>();

    /**
     * 调用服务
     */
    public static Object service(String requestPath, Map<String, String> params) throws Exception {
        MethodMapping methodMapping = ordinaryMethodMappingMap.get(requestPath);
        if (methodMapping != null) {
            return methodMapping.getMethod().invoke(methodMapping.getActionBean(), params);
        } else {
            for (Map.Entry<String, MethodMapping> entry : regexpMethodMappingMap.entrySet()) {
                Matcher requestPathMatcher = Pattern.compile(entry.getKey()).matcher(requestPath);
                if (requestPathMatcher.matches()) {
                    methodMapping = entry.getValue();
                    List<Object> paramList = createPathParamList(requestPathMatcher, methodMapping);
                    return methodMapping.getMethod().invoke(methodMapping.getActionBean(), paramList.toArray());
                }
            }
        }
        //找不到方法
        throw new RuntimeException("找不到方法,requestPath=" + requestPath);
    }

    //创建方法参数
    private static List<Object> createPathParamList(Matcher requestPathMatcher, MethodMapping methodMapping) {
        List<Object> paramList = new ArrayList<>();
        Class<?>[] methodParamTypes = methodMapping.getMethod().getParameterTypes();
        // 遍历正则表达式中所匹配的组
        for (int i = 1; i <= requestPathMatcher.groupCount(); i++) {
            // 获取请求参数
            String param = requestPathMatcher.group(i);
            // 获取参数类型（支持四种类型：int/Integer、long/Long、double/Double、String）
            Class<?> paramType = methodParamTypes[i - 1];
            if (paramType.equals(int.class)) {
                paramList.add(ConvertUtils.convert(param, int.class));
            } else if (paramType.equals(Integer.class)) {
                paramList.add(ConvertUtils.convert(param, Integer.class));
            } else if (paramType.equals(long.class)) {
                paramList.add(ConvertUtils.convert(param, long.class));
            } else if (paramType.equals(Long.class)) {
                paramList.add(ConvertUtils.convert(param, Long.class));
            } else if (paramType.equals(double.class)) {
                paramList.add(ConvertUtils.convert(param, double.class));
            } else if (paramType.equals(Double.class)) {
                paramList.add(ConvertUtils.convert(param, Double.class));
            } else if (paramType.equals(String.class)) {
                paramList.add(param);
            }
        }
        // 返回参数列表
        return paramList;
    }

    public static void init() {
        Map<String, Object> actionMap = SpringContextUtil.getBeansWithAnnotation(Action.class);
        for (Map.Entry entry : actionMap.entrySet()) {
            // 获取并遍历该 Action 类中所有的方法
            Object actionBean = entry.getValue();
            Method[] actionMethods = actionBean.getClass().getDeclaredMethods();
            String basePath = "";
            if (actionBean.getClass().isAnnotationPresent(RequestMapping.class)) {
                basePath = actionBean.getClass().getAnnotation(RequestMapping.class).value();
            }
            for (Method actionMethod : actionMethods) {
                if (!actionMethod.isAnnotationPresent(RequestMapping.class)) {
                    continue;
                }
                MethodMapping methodMapping = new MethodMapping();
                methodMapping.setActionBean(actionBean);
                methodMapping.setMethod(actionMethod);
                String requestPath = basePath + actionMethod.getAnnotation(RequestMapping.class).value();
                //正则匹配
                if (requestPath.matches(".+\\{\\w+\\}.*")) {
                    // 将请求路径中的占位符 {\w+} 转换为正则表达式 (\w+)
                    requestPath = requestPath.replaceAll("\\{\\w+\\}", "(\\\\w+)");
                    regexpMethodMappingMap.put(requestPath, methodMapping);
                } else {
                    ordinaryMethodMappingMap.put(requestPath, methodMapping);
                }

            }
        }
    }
}
