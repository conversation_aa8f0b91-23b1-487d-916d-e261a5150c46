package com.eastmoney.common.util;

import com.eastmoney.common.entity.ProResult;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/9/15
 * Time: 14:29
 */
public class CommonUtil {
    private static String localIp = "";
    public static String generateUUID() {
        String str = UUID.randomUUID().toString();
        // 去掉"-"符号
        String temp = str.substring(0, 8) + str.substring(9, 13) + str.substring(14, 18) + str.substring(19, 23) + str.substring(24);
        return temp;
    }

    //类型转换
    public static <T> T convert(Object value, Class<T> clazz) {
        if (value == null) {
            return null;
        } else if(value instanceof String && StringUtils.isBlank((String)value)){
            return null;
        } else{
            return (T) org.apache.commons.beanutils.ConvertUtils.convert(value, clazz);
        }
    }

    //类型转换
    public static <T> T convert(Map<String,Object> params,String key, Class<T> clazz) {
        return CommonUtil.convert(params.get(key), clazz);
    }

    //检查必要的参数
    private static void checkParamNotNull(String key, Object value) {
        if (value instanceof String) {
            value = StringUtils.stripToNull(value+"");
        }
        if (value == null) {
            throw new RuntimeException("缺少必要参数:" + key);
        }
    }
    //检查必要的参数
    public static void checkParamNotNull(Map<String, Object> params, String[] keys) {
        for (String key : keys) {
            Object value = params.get(key);
            checkParamNotNull(key, value);
        }
    }


    //验证存储过程执行结果
    public static void checkList(List list){
        ProResult r1;
        try {
            r1 = (ProResult) ((List) list.get(0)).get(0);
        }catch (Throwable e){
            throw new RuntimeException("查询失败");
        }
        if (!"0".equals(r1.getErrorCode())) {
            throw new RuntimeException("查询失败 errorCode=" + r1.getErrorCode());
        }
    }

    //根据unit返回起止日期
    public static DateRange getDateRange(String unit) {
        return getDateRange(Integer.valueOf(DateUtil.getCuryyyyMMdd()),unit);
    }

    //根据日期和unit返回起止日期
    public static DateRange getDateRange(Integer intEndDate, String unit) {
        String endDate = intEndDate.toString();
        String startDate = null;
        if (DateUnitEnum.DAY.getValue().equals(unit) ||DateUnitEnum.ALL.getValue().equals(unit)) {
           startDate = endDate;
        }else if(DateUnitEnum.WEEK.getValue().equals(unit)){
            startDate = DateUtil.getWeekFirstDay(endDate, DateUtil.yyyyMMdd);
        } else if (DateUnitEnum.MONTH.getValue().equals(unit)) {
            startDate = endDate.substring(0, 6) + "01";
        } else if (DateUnitEnum.YEAR.getValue().equals(unit)) {
            startDate = endDate.substring(0, 4) + "0101";
        } else if (DateUnitEnum.QUARTER.getValue().equals(unit)){
            int month = Integer.valueOf(endDate.substring(4, 6));
            if(month >= 1 && month <= 3) {
                startDate = endDate.substring(0, 4) + "0101";
            } else if (month >= 4 && month <= 6) {
                startDate = endDate.substring(0, 4) + "0401";
            } else if (month >= 7 && month <= 9) {
                startDate = endDate.substring(0, 4) + "0701";
            } else if (month >= 10 && month <= 12) {
                startDate = endDate.substring(0, 4) + "1001";
            }
        } else if (DateUnitEnum.PAST_ONE_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -1);
        } else if (DateUnitEnum.PAST_TWO_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -2);
        } else if (DateUnitEnum.PAST_THREE_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -3);
        } else if (DateUnitEnum.PAST_FOUR_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -4);
        } else if (DateUnitEnum.PAST_FIVE_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -5);
        } else if (DateUnitEnum.PAST_HALF_YEAR.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -6);
        } else if (DateUnitEnum.PAST_SEVEN_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -7);
        } else if (DateUnitEnum.PAST_EIGHT_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -8);
        } else if (DateUnitEnum.PAST_NINE_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -9);
        } else if (DateUnitEnum.PAST_TEN_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -10);
        } else if (DateUnitEnum.PAST_ELEVEN_MONTH.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -11);
        } else if (DateUnitEnum.PAST_ONE_YEAR.getValue().equals(unit)) {
            startDate = DateUtil.addMonth(endDate, DateUtil.yyyyMMdd, -12);
        } else {
            throw new RuntimeException("not found unit[" + unit+"]");
        }

        return new DateRange(Integer.parseInt(startDate),Integer.parseInt(endDate));
    }


    public static String getOraDbDataSourceType() {
        return "oracleRead";
    }
    public static String getOggDbDataSourceType() {
        return "oracleOGG";
    }

    public static String getOtcDbDataSourceType() {
        return "oracleOtc";
    }
    public static String getOtcKGDbDataSourceType() {
        return "oracleOtcKGDB";
    }

    public static String getTidbDataSourceType(){
        return "tidbRead";
    }
    public static String getFundIaDataSourceType(){
        return "oracleFundIa";
    }
    public static String getMsRunDbDataSourceType(String serverId) {
        String dataSourceType = null;
        if (serverId != null) {
            dataSourceType = "run" + serverId;
        }
        return dataSourceType;
    }
    public static String getMsHisDbDataSourceType(String serverId) {
        String dataSourceType = null;
        if (serverId != null) {
            dataSourceType = "his" + serverId;
        }
        return dataSourceType;
    }
    static {
        try {
            localIp = ComputerAddressUtil.getIp();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
    }
    public static String getLocalIp() {
        return localIp;
    }

    public static void main(String[] args) {
    }

    /**
     * 第一个参数v1：清算收益率
     * 第二个参数v2：实时收益率
     *
     * @param v1
     * @param v2
     * @return
     */
    public static double multiply(Number v1, Number v2){
        v1 = ArithUtil.convertIfNullTo0(v1);
        v2 = ArithUtil.convertIfNullTo0(v2);
        return ArithUtil.sub(ArithUtil.mul(ArithUtil.add(v1, 1), ArithUtil.round(ArithUtil.add(v2, 1), 4)), 1);
    }

    /**
     * 第一个参数v1：清算收益率
     * 第二个参数v2：实时收益率
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal multiplyProfitRate(BigDecimal v1, BigDecimal v2){
        v1 = ArithUtil.convertBigDecimalIfNullTo0(v1);
        v2 = ArithUtil.convertBigDecimalIfNullTo0(v2);
        return ArithUtil.sub(ArithUtil.mul(ArithUtil.add(v1, BigDecimal.ONE), ArithUtil.round(ArithUtil.add(v2, BigDecimal.ONE), 4)), BigDecimal.ONE);
    }

    public static double multiplyNoHalf(Number v1, Number v2) {
        v1 = ArithUtil.convertIfNullTo0(v1);
        v2 = ArithUtil.convertIfNullTo0(v2);
        return ArithUtil.sub(ArithUtil.mul(ArithUtil.add(v1, 1), ArithUtil.add(v2, 1)), 1);
    }
}
