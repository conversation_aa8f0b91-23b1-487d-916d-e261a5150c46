package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/3.
 */
public class AssetDebt {
    public Double asset;
    public Double debt;
    public Double keepRate;
    public Double keepRateSub;
    public String riskStatus;
    public Double tranRate;
    public Double mktVal;
    public Double warnRate;
    public Double closeRate;
    public Double stdRate;
    public Double dueIntr;
    public Double taxAmt;
    public Double puniIntr;
    public Double conAmt;
    public Double custBondAmt;
    public Double custLimitreMain;
    public Double canrepaydEbt;
    public Double repayAmt;
    public Double fundMaxDrow;

    public Double getAsset() {
        return asset;
    }

    public void setAsset(Double asset) {
        this.asset = asset;
    }

    public Double getDebt() {
        return debt;
    }

    public void setDebt(Double debt) {
        this.debt = debt;
    }

    public Double getKeepRate() {
        return keepRate;
    }

    public void setKeepRate(Double keepRate) {
        this.keepRate = keepRate;
    }

    public Double getKeepRateSub() {
        return keepRateSub;
    }

    public void setKeepRateSub(Double keepRateSub) {
        this.keepRateSub = keepRateSub;
    }

    public String getRiskStatus() {
        return riskStatus;
    }

    public void setRiskStatus(String riskStatus) {
        this.riskStatus = riskStatus;
    }

    public Double getTranRate() {
        return tranRate;
    }

    public void setTranRate(Double tranRate) {
        this.tranRate = tranRate;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    public Double getWarnRate() {
        return warnRate;
    }

    public void setWarnRate(Double warnRate) {
        this.warnRate = warnRate;
    }

    public Double getCloseRate() {
        return closeRate;
    }

    public void setCloseRate(Double closeRate) {
        this.closeRate = closeRate;
    }

    public Double getStdRate() {
        return stdRate;
    }

    public void setStdRate(Double stdRate) {
        this.stdRate = stdRate;
    }

    public Double getDueIntr() {
        return dueIntr;
    }

    public void setDueIntr(Double dueIntr) {
        this.dueIntr = dueIntr;
    }

    public Double getTaxAmt() {
        return taxAmt;
    }

    public void setTaxAmt(Double taxAmt) {
        this.taxAmt = taxAmt;
    }

    public Double getPuniIntr() {
        return puniIntr;
    }

    public void setPuniIntr(Double puniIntr) {
        this.puniIntr = puniIntr;
    }

    public Double getConAmt() {
        return conAmt;
    }

    public void setConAmt(Double conAmt) {
        this.conAmt = conAmt;
    }

    public Double getCustBondAmt() {
        return custBondAmt;
    }

    public void setCustBondAmt(Double custBondAmt) {
        this.custBondAmt = custBondAmt;
    }

    public Double getCustLimitreMain() {
        return custLimitreMain;
    }

    public void setCustLimitreMain(Double custLimitreMain) {
        this.custLimitreMain = custLimitreMain;
    }

    public Double getCanrepaydEbt() {
        return canrepaydEbt;
    }

    public void setCanrepaydEbt(Double canrepaydEbt) {
        this.canrepaydEbt = canrepaydEbt;
    }

    public Double getRepayAmt() {
        return repayAmt;
    }

    public void setRepayAmt(Double repayAmt) {
        this.repayAmt = repayAmt;
    }

    public Double getFundMaxDrow() {
        return fundMaxDrow;
    }

    public void setFundMaxDrow(Double fundMaxDrow) {
        this.fundMaxDrow = fundMaxDrow;
    }
}
