package com.eastmoney.service.exception;

/**
 * Created by sunyuncai on 2016/10/24.
 */
public class ChannelException extends RuntimeException{
    public final static int ERRCODE_CHANNEL_NULL = -10;
    public final static int ERRCODE_CHANNEL_NOT_ACTIVE = -11;
    public final static int ERRCODE_CHANNEL_NOT_WRITABLE = -12;
    private int errCode;
    private String errMsg;

    public ChannelException() {
        super();
    }

    public ChannelException(int errCode,String errMsg){
        super();
        this.errCode = errCode;
        this.errMsg = errMsg;
    }
    public ChannelException(Exception e,int errCode,String errMsg){
        super(errMsg,e);
        this.errCode = errCode;
    }

    public int getErrCode() {
        return errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }
}
