package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.cal.dw.bill.*;

/**
 * 账单指标汇总表：
 * 1. 牛市指标集合
 * 2. 大数据振幅
 * 3. 大数据神操作
 * <p>
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
public class BdTradeBillYearExt {
    /**
     * 资金账号
     */
    private Long fundId;
    /**
     * indexKey
     */
    private Integer indexKey;
    private BdTradeBillFinal tradeBillFinal;
    private BdTradeBillPrate tradeBillPrate;
    private BdTradeBillYearBullProfitDO bullProfitInfo;
    private BdTradeBillYearBullTradeDO bullTradeInfo;
    private BdTradeBillYearHoldDO holdInfo;
    private BdTradeBillYearTProfitDO tProfitInfo;
    private BdTradeBillYearInvestAdvisDO investAdvisInfo;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public BdTradeBillFinal getTradeBillFinal() {
        return tradeBillFinal;
    }

    public void setTradeBillFinal(BdTradeBillFinal tradeBillFinal) {
        this.tradeBillFinal = tradeBillFinal;
    }

    public BdTradeBillPrate getTradeBillPrate() {
        return tradeBillPrate;
    }

    public void setTradeBillPrate(BdTradeBillPrate tradeBillPrate) {
        this.tradeBillPrate = tradeBillPrate;
    }

    public BdTradeBillYearBullProfitDO getBullProfitInfo() {
        return bullProfitInfo;
    }

    public void setBullProfitInfo(BdTradeBillYearBullProfitDO bullProfitInfo) {
        this.bullProfitInfo = bullProfitInfo;
    }

    public BdTradeBillYearBullTradeDO getBullTradeInfo() {
        return bullTradeInfo;
    }

    public void setBullTradeInfo(BdTradeBillYearBullTradeDO bullTradeInfo) {
        this.bullTradeInfo = bullTradeInfo;
    }

    public BdTradeBillYearHoldDO getHoldInfo() {
        return holdInfo;
    }

    public void setHoldInfo(BdTradeBillYearHoldDO holdInfo) {
        this.holdInfo = holdInfo;
    }

    public BdTradeBillYearTProfitDO gettProfitInfo() {
        return tProfitInfo;
    }

    public void settProfitInfo(BdTradeBillYearTProfitDO tProfitInfo) {
        this.tProfitInfo = tProfitInfo;
    }

    public BdTradeBillYearInvestAdvisDO getInvestAdvisInfo() {
        return investAdvisInfo;
    }

    public void setInvestAdvisInfo(BdTradeBillYearInvestAdvisDO investAdvisInfo) {
        this.investAdvisInfo = investAdvisInfo;
    }
}
