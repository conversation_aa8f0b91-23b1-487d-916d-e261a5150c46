package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateContrastDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitRateContrastMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateContrast;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by xiaoyongyong on 2016/7/20.
 * ProfitRateContrastServiceImpl
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitRateContrastDao")
public class TiProfitRateContrastDaoImpl extends BaseDao<TiProfitRateContrastMapper, ProfitRateContrast, <PERSON>> implements ProfitRateContrastDao {

}
