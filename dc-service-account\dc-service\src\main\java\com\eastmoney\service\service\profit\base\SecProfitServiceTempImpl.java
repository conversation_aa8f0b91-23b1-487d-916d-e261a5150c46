package com.eastmoney.service.service.profit.base;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.tidb.SecTemporaryProfitDayDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.StkAsset;
import com.eastmoney.common.entity.StkPrice;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.HgtTradeDateService;
import com.eastmoney.service.cache.StkPriceCacheService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 盘后个股日收益明细service
 *
 * <AUTHOR>
 * @create 2024/3/14
 */
@Service("secProfitServiceTemp")
public class SecProfitServiceTempImpl implements SecProfitService {
    @Autowired
    private SecTemporaryProfitDayDao secTemporaryProfitDayDao;
    @Autowired
    private TradeDateDao tradeDateService;
    @Autowired
    private StkAssetService stkAssetService;
    @Autowired
    private CommonService commonService;

    @Override
    public List<SecProfitDayDO> getSecProfitDayByRange(Map<String, Object> params) {
        return getTempDayProfit(params);
    }

    private List<SecProfitDayDO> getTempDayProfit(Map<String, Object> params) {
        int today = Integer.parseInt(DateUtil.getCuryyyyMMdd());
        Integer time = CommonUtil.convert(DateUtil.getCurHHmmss(), Integer.class);
        int tempProfitCalTime = commonService.useTempProfitTime();
        Integer preMarketDay = Integer.valueOf(tradeDateService.getPreMarketDay(today));
        Integer accountBizDate = CommonUtil.convert(params.get("accountBizDate"), Integer.class);
        boolean todayIsMarket = tradeDateService.isMarket(today);
        //只有在交易日且前一交易日已经清算完成并且时间在设定的临时收益计算时间之前，不需要获取临时收益。
        if (time < tempProfitCalTime && todayIsMarket && preMarketDay.equals(accountBizDate)) {
            return null;
        }
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        List<SecProfitDayDO> secProfitDays = secTemporaryProfitDayDao.getSecTemporaryProfitDay(fundId);
        if (!CollectionUtils.isEmpty(secProfitDays) && accountBizDate != null) {
            //如果临时表的计算日期大于离线数据的计算日期，需要把临时表的数据加上去
            if (secProfitDays.get(0).getBizDate() > accountBizDate) {
                return secProfitDays;
            }
        }
        return null;
    }
}
