package com.eastmoney.accessor.enums;

/**
 * Created on 2016/11/03
 * 币种
 *
 * <AUTHOR>
 */
public enum MoneyTypeEnum {

    RMB("0"),
    HK("1"),
    US("2");
    private String unit;

    MoneyTypeEnum(String unit) {
        this.unit = unit;
    }

    public String getValue() {
        return this.unit;
    }

    public static MoneyTypeEnum getEnum(String value) {
        switch (value) {
            case "0":
                return RMB;
            case "1":
                return HK;
            case "2":
                return US;
            default:
                throw new RuntimeException("not found Issue[" + value + "]");

        }
    }

}
