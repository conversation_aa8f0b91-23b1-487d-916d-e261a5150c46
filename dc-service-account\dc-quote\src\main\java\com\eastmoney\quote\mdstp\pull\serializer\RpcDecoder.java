package com.eastmoney.quote.mdstp.pull.serializer;


import com.eastmoney.quote.mdstp.model.QuoteTypeEnum;
import com.eastmoney.quote.mdstp.serializer.QtDecoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.LoggerFactory;

import java.nio.ByteOrder;
import java.util.List;

/**
 * Created by 1 on 15-7-7.
 */
public class RpcDecoder extends ByteToMessageDecoder {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(RpcDecoder.class);

    public RpcDecoder(){}

    @Override
    protected void decode(ChannelHandlerContext channelHandlerContext, ByteBuf in, List<Object> out) throws Exception {
        try {
            if (in.readableBytes() < 4) {
                return;
            }
            in=in.order(ByteOrder.LITTLE_ENDIAN);
            in.markReaderIndex();
            Packet packet = new Packet();
            packet.setDataLen(in.readInt());
            if (in.readableBytes() < packet.getDataLen()) {
                in.resetReaderIndex();
                return;
            }
            byte[] con = new byte[packet.getDataLen()];
            if (con == null || con.length == 0) {
                return;
            }
            in.readBytes(con);
            con = ZLibUtils.decompress(con);
            ByteBuf deConBuf = Unpooled.wrappedBuffer(con);
            deConBuf=deConBuf.order(ByteOrder.LITTLE_ENDIAN);
            byte cmdId = deConBuf.readByte();
            if(cmdId == QuoteTypeEnum.QuoteHSSnapshot.getValue()){
                QtDecoder.decode(deConBuf,false);
            }
            out.add(new Packet());
        } catch (Exception e){
            LOG.error(e.getMessage(), e);
        }
    }

}
