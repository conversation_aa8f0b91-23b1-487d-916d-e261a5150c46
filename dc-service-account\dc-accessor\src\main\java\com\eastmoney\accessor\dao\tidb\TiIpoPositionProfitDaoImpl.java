package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiIpoPositionProfitMapper;
import com.eastmoney.common.entity.cal.IPOPositionProfitDO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class TiIpoPositionProfitDaoImpl extends BaseDao<TiIpoPositionProfitMapper, IPOPositionProfitDO, Long> implements IpoPositionProfitDao {
    @Override
    public List<IPOPositionProfitDO> getIPOPositionProfitList(Long fundId, Integer startDate, Integer endDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("fundId", fundId);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        return getMapper().selectByCondition(param);
    }
}

