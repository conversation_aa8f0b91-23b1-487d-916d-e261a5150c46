package com.eastmoney.accessor.service;

import com.eastmoney.common.entity.Match;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
public interface MatchService {
    /**
     * 获取盘中非融券相关交易记录
     */
    List<Match> getRealTimeMatchList(Map<String, Object> params);

    /**
     * 获取所有盘中所有成交数据
     */
    List<Match> getAllRealTimeMatchList(Map<String, Object> params);
}
