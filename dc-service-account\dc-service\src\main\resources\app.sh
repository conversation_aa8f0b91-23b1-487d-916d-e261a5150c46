#!/bin/sh
source /etc/profile
pname=${jarFileName}.jar
name=${projectName}
method=\$1

echo --------------------------------------------------
vmoptions=\$(cat vm.info)
echo vmoptions:\${vmoptions}
echo The curr time:\$(date)

function check_pids(){
	javaps=\$(ps -ef | grep java | grep \${name} | grep -v grep | awk '{print \$2}')
	if [ -n "\${javaps}" ];
	then
		pids=\${javaps}
	else
		pids="0"
	fi
}

function status(){
    if [ "\${pids}" != "0" ];
    then
    	echo \${pname} is running at \${pids} !
    else
        echo \${pname} stoped !
    fi
}

function start()
{
    count=`ps -ef | grep java | grep \$name | grep -v grep |wc -l`
        if [ \$count != 0 ];
        then
            echo "\$name is running..."
        else
            echo "start \$name seccess..."
                nohup java \${vmoptions} -jar \${pname} > /dev/null 2>&1 &
        fi
}

function stop()
{
    echo "Stop \$name"
        boot_id=`ps -ef | grep java | grep \$name | grep -v grep |awk '{print \$2}'`
        count=`ps -ef | grep java | grep \$name | grep -v grep |wc -l`
        if [ \$count != 0 ];
        then
            kill -9 \$boot_id
        fi
}

check_pids

if [ "\${method}" = "status" ];then
    status
elif [ "\${method}" = "start" ];then
    start
elif [ "\${method}" = "stop" ];then
    stop
elif [ "\${method}"  = "restart" ];then
    stop
    sleep 2
    start
else
	echo \${pname} is running at \${pids} !
fi
