package com.eastmoney.common.entity;

import java.util.Date;

/**
 * Created on 2016/3/16
 *
 * <AUTHOR>
 */
public class BaseEntity {
    private Long eid;//系统物理主键
    private Date eiTime;//数据入库时间
    private Date euTime;//数据修改时间
    private String resend_flag;//重发标记
    private Integer serverId;//服务器编号
    private Integer bizDate;

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Date getEiTime() {
        return eiTime;
    }

    public void setEiTime(Date eiTime) {
        this.eiTime = eiTime;
    }

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public String getResend_flag() {
        return resend_flag;
    }

    public void setResend_flag(String resend_flag) {
        this.resend_flag = resend_flag.trim();
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }
}
