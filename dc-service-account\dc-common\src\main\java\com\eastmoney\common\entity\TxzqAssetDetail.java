package com.eastmoney.common.entity;

/**
 * Created on 2016/3/3
 * 每日资产
 *
 * <AUTHOR>
 */
public class TxzqAssetDetail extends BaseEntity {
    //    private Long eid;//系统物理主键
//    private Date eiTime;//数据入库时间
//    private Date euTime;//数据修改时间
//    private Integer serverId;/*机器编码*/
    private String orgId; /*分支机构编码*/
    private Long custId;/*客户代码*/
    private Long fundId;/*资金帐号*/
    private String moneyType;/*成交类型*/
    private Double asset;/*总资产*/
    private String assetType;/*资产类型*/
    private Integer bizDate;/*业务操作日期*/

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType.trim();
    }

    public Double getAsset() {
        return asset;
    }

    public void setAsset(Double asset) {
        this.asset = asset;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType.trim();
    }
}
