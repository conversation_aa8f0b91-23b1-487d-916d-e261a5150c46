package com.eastmoney.accessor.dao.fundia;

import com.eastmoney.accessor.mapper.fundia.FundIaFundInvestAccountMapper;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("fundIaFundInvestAccountDao")
public class FundIaFundInvestAccountDaoImpl implements FundIaFundInvestAccountDao {
    @Autowired
    private FundIaFundInvestAccountMapper fundIaFundInvestAccountMapper;

    @Override
    public boolean fundInvestAccountExist(Long fundId) {
        return fundIaFundInvestAccountMapper.selectCountByCondition(ImmutableMap.of("fundId", fundId)) >= 1;
    }
}
