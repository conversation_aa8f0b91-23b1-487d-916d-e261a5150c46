package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateContrastChartMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateContrastChart;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2016/7/19
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitRateContrastChartDao")
public class ProfitRateContrastChartDaoImpl extends BaseDao<ProfitRateContrastChartMapper, ProfitRateContrastChart, Long> implements ProfitRateContrastChartDao {

}
