package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/1.
 * 资金帐号信息
 */
public class FundAssetCurrent {
    public Integer serverId;
    public String orgId;
    public Integer fundSeq;
    public Long fundId;
    public String moneyType;
    public Long custId;
    public Double fundLastBal;
    public Double fundBal;
    public Double fundAvl;
    public Double overDraw;
    public Double fundBuy;
    public Double fundSale;
    public Double fundUncomeBuy;
    public Double fundUncomeSale;
    public Double fundFrz;
    public Double fundUnfrz;
    public Double fundTrdFrz;
    public Double fundTrdUnfrz;
    public Double fundNightFrz;
    public Double fundLoan;
    public Double creditBal;
    public Double creditBuySale;
    public String fundFlag;
    public Double marketValue;
    public Double fundStandBy;
    public Double fundBuySale;
    public Double fundBrkBuy;
    public Double fundRealBuy;

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getFundSeq() {
        return fundSeq;
    }

    public void setFundSeq(Integer fundSeq) {
        this.fundSeq = fundSeq;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Double getFundLastBal() {
        return fundLastBal;
    }

    public void setFundLastBal(Double fundLastBal) {
        this.fundLastBal = fundLastBal;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getOverDraw() {
        return overDraw;
    }

    public void setOverDraw(Double overDraw) {
        this.overDraw = overDraw;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getFundUncomeBuy() {
        return fundUncomeBuy;
    }

    public void setFundUncomeBuy(Double fundUncomeBuy) {
        this.fundUncomeBuy = fundUncomeBuy;
    }

    public Double getFundUncomeSale() {
        return fundUncomeSale;
    }

    public void setFundUncomeSale(Double fundUncomeSale) {
        this.fundUncomeSale = fundUncomeSale;
    }

    public Double getFundFrz() {
        return fundFrz;
    }

    public void setFundFrz(Double fundFrz) {
        this.fundFrz = fundFrz;
    }

    public Double getFundUnfrz() {
        return fundUnfrz;
    }

    public void setFundUnfrz(Double fundUnfrz) {
        this.fundUnfrz = fundUnfrz;
    }

    public Double getFundTrdFrz() {
        return fundTrdFrz;
    }

    public void setFundTrdFrz(Double fundTrdFrz) {
        this.fundTrdFrz = fundTrdFrz;
    }

    public Double getFundTrdUnfrz() {
        return fundTrdUnfrz;
    }

    public void setFundTrdUnfrz(Double fundTrdUnfrz) {
        this.fundTrdUnfrz = fundTrdUnfrz;
    }

    public Double getFundNightFrz() {
        return fundNightFrz;
    }

    public void setFundNightFrz(Double fundNightFrz) {
        this.fundNightFrz = fundNightFrz;
    }

    public Double getFundLoan() {
        return fundLoan;
    }

    public void setFundLoan(Double fundLoan) {
        this.fundLoan = fundLoan;
    }

    public Double getCreditBal() {
        return creditBal;
    }

    public void setCreditBal(Double creditBal) {
        this.creditBal = creditBal;
    }

    public Double getCreditBuySale() {
        return creditBuySale;
    }

    public void setCreditBuySale(Double creditBuySale) {
        this.creditBuySale = creditBuySale;
    }

    public String getFundFlag() {
        return fundFlag;
    }

    public void setFundFlag(String fundFlag) {
        this.fundFlag = fundFlag;
    }

    public Double getMarketValue() {
        return marketValue;
    }

    public void setMarketValue(Double marketValue) {
        this.marketValue = marketValue;
    }

    public Double getFundStandBy() {
        return fundStandBy;
    }

    public void setFundStandBy(Double fundStandBy) {
        this.fundStandBy = fundStandBy;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundBrkBuy() {
        return fundBrkBuy;
    }

    public void setFundBrkBuy(Double fundBrkBuy) {
        this.fundBrkBuy = fundBrkBuy;
    }

    public Double getFundRealBuy() {
        return fundRealBuy;
    }

    public void setFundRealBuy(Double fundRealBuy) {
        this.fundRealBuy = fundRealBuy;
    }
}
