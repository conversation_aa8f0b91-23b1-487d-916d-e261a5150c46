package com.eastmoney.service.rpc.pull;

import com.eastmoney.transport.server.NettyServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by sunyuncai on 2016/3/4.
 */
public class PullServer implements Runnable {
    private static Logger LOG = LoggerFactory.getLogger(PullServer.class);
    private final int port;
    public PullServer(int port) {
        this.port = port;
    }

    @Override
    public void run() {
        try {
            NettyServer server = new NettyServer(port,new PullServerListener());
            server.init();
            while (true) {
                try {
                    server.bind();
                }catch (Exception e){
                    LOG.error("服务启动异常", e);
                }
                Thread.sleep(5000);
            }
        } catch (Exception e) {
            LOG.error("", e);
        }
    }
}
