package com.eastmoney.accessor.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 证券类型枚举类
 *
 * Created by huachnegqi on 2017/7/6.
 */
public enum StkTypeEnum {

    STOCK("0","stock","股票","stock"),
    CDR("U","cdr","存托凭证","cdr"),
    GEB("R","geb","创业板","geb"),
    NATIONAL_DEBT("1","bond","国债","nationalDebt"),
    ENTERPRISE_BOND("2","bond","企业债券","enterpriseBond"),
    FUND("4","fund","基金","fund"),
    INVESTMENT_FUND("5","fund","投资基金","investmentFund"),
    CONVERTIBLE_BOND("8","bond","转换债券","convertibleBond"),
    BOND_STOCK("9","bond","债券转股","bondStock"),
    COMPANY_BOND("C","bond","公司债","companyBond"),
    ETF("E","fund","ETF","ETF"),
    QUOTED_STOCK("J","quotedStock","挂牌公司证券","quotedStock"),
    LOF("L","fund","LOF","LOF"),
    PRIVATE_PLACEMENT_BOND("e","bond","私募债券","privatePlacementBond"),
    CURRENCY_ETF("h","fund","货币ETF","currencyETF"),
    BOND_ETF("g","fund","债券ETF","bondETF"),
    GOLD_ETF("i","fund","黄金ETF","goldETF"),
    ASSET_BACKED_SECURITY("k","bond","资产支持证券","assetBackedSecurity"),
    SSE_LOF("o","fund","上证LOF","SSELOF"),
    FINANCIAL_RATE_LOF("r","fund","财务分级LOF","financialRateLOF"),
    EXCHANGEABLE_BOND("t","bond","可交换公司债","exchangeableBond"),
    SGB("W","sgb","科创板","sgb"),
    GEB_CDR("X","gebcdr","创业板CDR","gebcdr"),
    REITs("u","fund","基础设施公募REITs","REITs"),
    CONVERTIBLE_PRIVATE_BOND("j","bond","可转换私募债","convertiblePrivateBond"),
    SUBORDINATED_BOND("l","bond","次级债券","subordinatedBond"),
    DELISTING_CONVERTIBLE_BOND("w","bond","两网退市可转债","delistingConvertibleBond");

    private String value;//枚举值
    private String type;// 类型
    private String name;//枚举中文名称
    private String code;//枚举英文名称
    StkTypeEnum(String value, String type, String name, String code){
        this.value = value;
        this.type = type;
        this.name = name;
        this.code = code;
    }
    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
    public String getCode() {
        return code;
    }

    private static final Map<String, StkTypeEnum> STK_TYPE_TO_ENUM_MAP = new HashMap<>();

    static {
        for (StkTypeEnum stkTypeEnum : StkTypeEnum.values()) {
            STK_TYPE_TO_ENUM_MAP.put(stkTypeEnum.getValue(), stkTypeEnum);
        }
    }

    /**
     * 基于stkType获取证券类型（股债基）
     * stock、cdr、geb、gebcdr：股票
     * bond：债券
     * fund：基金
     *
     * @param stkType
     * @return
     */
    public static Optional<String> getTypeByStkType(String stkType) {
        return Optional.ofNullable(STK_TYPE_TO_ENUM_MAP.get(stkType))
                .map(StkTypeEnum::getType);
    }


    /**
     * 判断是否为 E、L、g、h、i、u、r、o 这些基金类型
     * @param stkType
     * @return
     */
    public static Boolean isShowFullNameFundType(String stkType) {
        String type = StkTypeEnum.getTypeByStkType(stkType).orElse(null);
        return Objects.equals(type, "fund") && !Objects.equals(stkType, StkTypeEnum.FUND.getValue()) &&
                !Objects.equals(stkType, StkTypeEnum.INVESTMENT_FUND.getValue());
    }
}
