package com.eastmoney.service.service.quote;

import com.eastmoney.common.entity.StkInfo;
import com.eastmoney.common.entity.StkPrice;

import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/22-10:09.
 *
 * <AUTHOR>
 */
public interface QuoteService {

    /**
     * 查询实时行情价
     * 1. 如果计算持仓收益,行情价需加利息 (positionProfit=true)
     * 2. 否则行情价不加利息 (positionProfit=false)
     * @param stkInfo
     * @param positionProfit
     * @return
     */
    StkPrice getStkPrice(StkInfo stkInfo, Boolean positionProfit);

    <T extends StkInfo> void setStkAssetQuote(List<T> stkInfoList, Boolean positionProfit);

    StkPrice getStkPrice(StkInfo stkInfo);

    /**
     * 获取行情价
     *
     * @param stkCode
     * @param market
     * @return
     */
    StkPrice getQtPrice(String stkCode, String market);

    /**
     * 获取所有股票的行情
     * @return
     * @param params
     */
    List<StkPrice> getAllQuoteList(Map params);
}
