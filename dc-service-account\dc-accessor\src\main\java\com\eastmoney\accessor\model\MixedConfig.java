package com.eastmoney.accessor.model;

/**
 * Created by Administrator on 2017/2/6.
 */
public class MixedConfig {

    private Integer serverId;
    private String paraId;
    private String paraName;
    private String paraValue;
    private String dataType;
    private Integer dataLen;
    private Integer decLen;
    private String charSet;
    private String ctrlType;
    private String show;
    private Integer bizType;
    private String paraLenl;
    private String paraClass;
    private String status;
    private String remark;
    private String nullAble;

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public String getParaId() {
        return paraId;
    }

    public void setParaId(String paraId) {
        this.paraId = paraId;
    }

    public String getParaName() {
        return paraName;
    }

    public void setParaName(String paraName) {
        this.paraName = paraName;
    }

    public String getParaValue() {
        return paraValue;
    }

    public void setParaValue(String paraValue) {
        this.paraValue = paraValue;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Integer getDataLen() {
        return dataLen;
    }

    public void setDataLen(Integer dataLen) {
        this.dataLen = dataLen;
    }

    public Integer getDecLen() {
        return decLen;
    }

    public void setDecLen(Integer decLen) {
        this.decLen = decLen;
    }

    public String getCharSet() {
        return charSet;
    }

    public void setCharSet(String charSet) {
        this.charSet = charSet;
    }

    public String getCtrlType() {
        return ctrlType;
    }

    public void setCtrlType(String ctrlType) {
        this.ctrlType = ctrlType;
    }

    public String getShow() {
        return show;
    }

    public void setShow(String show) {
        this.show = show;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getParaLenl() {
        return paraLenl;
    }

    public void setParaLenl(String paraLenl) {
        this.paraLenl = paraLenl;
    }

    public String getParaClass() {
        return paraClass;
    }

    public void setParaClass(String paraClass) {
        this.paraClass = paraClass;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNullAble() {
        return nullAble;
    }

    public void setNullAble(String nullAble) {
        this.nullAble = nullAble;
    }
}
