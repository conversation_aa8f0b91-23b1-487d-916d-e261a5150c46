package com.eastmoney.service.handler;

import com.eastmoney.service.service.StockService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/8 8:51
 */
@Service
public class StockHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockHandler.class);

    private static final ThreadPoolExecutor FLUSH_STOCK_EXECUTOR = new ThreadPoolExecutor(2, 2,
            0, TimeUnit.MICROSECONDS,
            new ArrayBlockingQueue<>(10),
            new ThreadFactoryBuilder().setNameFormat("flush-stock-thread-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());

    private static final String TOKEN = "!rsrKNLiddr69oLg";

    @Autowired
    private StockService stockService;

    public String flushStock(String token) {
        if (!TOKEN.equals(token)) {
            return "请求token错误，无法执行码表刷新，请与开发人员联系";
        }
        FLUSH_STOCK_EXECUTOR.execute(() -> stockService.loadStockMap());
        return "开始刷新码表缓存...";
    }

}
