package com.eastmoney.accessor.dao.tidb;


import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.CustCalenderDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-30 17:09
 */
public interface CustCalenderDao extends IBaseDao<CustCalenderDO, Long> {

    /**
     * 获取指定日期区间DateFrom到DateTo之间的所有阳历日期
     *
     * @param params
     * @return
     */
    List<CustCalenderDO> getByBizDateRange(Map<String, Object> params);

}
