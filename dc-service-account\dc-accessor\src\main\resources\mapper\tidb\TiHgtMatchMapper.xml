<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiHgtMatchMapper">

    <select id="getUndeliveredMatchList" resultType="as_hgtMatch">
        select a.bsflag,a.trddate,a.matchtime,a.matchamt,a.matchqty,trim(a.stkcode) stkcode,a.stkname,a.market,
               a.matchsno,a.matchprice
        from ATCENTER.hgt_match a use index (INDEX_HGT_MATCH)
        where a.matchType = '0'
          and a.fundId = #{fundId}
          and a.serverId = #{serverId}
          and a.bsflag in ('2B', '2S', '3B', '3S')
          and trim(a.stkcode) = #{stkCode}
          and a.market = #{market}
          and a.bizDate >= #{preBizDate}
        order by a.matchsno desc
    </select>

</mapper>