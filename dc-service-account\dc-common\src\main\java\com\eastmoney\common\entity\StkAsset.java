package com.eastmoney.common.entity;

import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * Created on 2016/3/1
 * 证券持仓
 *
 * <AUTHOR>
 */
public class StkAsset extends BaseEntity {
    public Long custId;/*客户代码*/
    public String orgId;/*机构编码*/
    public Long fundId;/*资金帐号*/
    public String moneyType;/*货币代码*/
    public String market;/*交易市场*/
    public String secuId;/*股东代码*/
    public String seat;/*席位代码*/
    public String stkCode;/*证券代码*/
    public Long stkBal;/*股份余额*/
    public Long stkBuySale;/*股份实时买卖差额*/
    public Long stkUnComeBuy;/*在途股份买入解冻*/
    public Long stkUnComeSale;/*在途股份卖出冻结*/
    public Double mktVal;/*证券市值*/


    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getOrgId() {
        return orgId.trim();
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType.trim();
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market.trim();
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId.trim();
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat.trim();
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode.trim();
    }

    public Long getStkBal() {
        return stkBal;
    }

    public void setStkBal(Long stkBal) {
        this.stkBal = stkBal;
    }

    public Long getStkBuySale() {
        return stkBuySale;
    }

    public void setStkBuySale(Long stkBuySale) {
        this.stkBuySale = stkBuySale;
    }

    public Long getStkUnComeBuy() {
        return stkUnComeBuy;
    }

    public void setStkUnComeBuy(Long stkUnComeBuy) {
        this.stkUnComeBuy = stkUnComeBuy;
    }

    public Long getStkUnComeSale() {
        return stkUnComeSale;
    }

    public void setStkUnComeSale(Long stkUnComeSale) {
        this.stkUnComeSale = stkUnComeSale;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    public String getStkAssetKey() {
        return StringUtils.join(StringUtils.trim(this.getStkCode()), "-", StringUtils.trim(this.getMarket()));
    }
}
