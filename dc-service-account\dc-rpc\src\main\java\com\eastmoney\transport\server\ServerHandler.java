package com.eastmoney.transport.server;

import com.eastmoney.transport.listener.Listener;
import com.eastmoney.transport.util.ChannelUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class ServerHandler extends ChannelInboundHandlerAdapter {

    private static Logger LOG = LoggerFactory.getLogger(ServerHandler.class);
    private Listener listener;

    public ServerHandler(Listener listener) {
        this.listener = listener;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        LOG.info("====服务端连接建立," + ChannelUtil.getChannelRoute(ctx.channel()));
        if (listener != null) {
            listener.channelActive(ctx);
        }
        ctx.fireChannelActive();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ctx.fireChannelRead(msg);
        if (listener != null) {
            listener.channelRead(ctx, msg);
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        LOG.info("====服务端连接断开," + ChannelUtil.getChannelRoute(ctx.channel()));
        if (listener != null) {
            listener.channelInactive(ctx);
        }
        ctx.close();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (cause != null && (cause instanceof IOException)) {
            ctx.channel().close();
            return;
        }

        LOG.error("exceptionCaught{}", ChannelUtil.getChannelRoute(ctx.channel()), cause);

        if (ctx.channel().isActive()) {
            //如果客户端还在，就发送异常消息
            return;
        }

        //最后关闭通道
        ctx.channel().close();
    }

}

