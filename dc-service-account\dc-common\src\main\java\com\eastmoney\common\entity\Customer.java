package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/2.
 */
public class Customer {
    public Integer serverId;
    public Long custId;
    public String custName;
    public String spellId;
    public String custProp;
    public String custKind;
    public String custGroup;
    public String custCard;
    public String pwdType;
    public String trdPwd;
    public String manyFundFlag;
    public String orgId;
    public String brhId;
    public Integer pwdErrTimes;
    public String status;
    public String lockFlag;
    public Integer timeOut;
    public String extProp;
    public String identitySign;
    public String servicePwd;
    public String costFlag;

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custN<PERSON>;
    }

    public String getSpellId() {
        return spellId;
    }

    public void setSpellId(String spellId) {
        this.spellId = spellId;
    }

    public String getCustProp() {
        return custProp;
    }

    public void setCustProp(String custProp) {
        this.custProp = custProp;
    }

    public String getCustKind() {
        return custKind;
    }

    public void setCustKind(String custKind) {
        this.custKind = custKind;
    }

    public String getCustGroup() {
        return custGroup;
    }

    public void setCustGroup(String custGroup) {
        this.custGroup = custGroup;
    }

    public String getCustCard() {
        return custCard;
    }

    public void setCustCard(String custCard) {
        this.custCard = custCard;
    }

    public String getPwdType() {
        return pwdType;
    }

    public void setPwdType(String pwdType) {
        this.pwdType = pwdType;
    }

    public String getTrdPwd() {
        return trdPwd;
    }

    public void setTrdPwd(String trdPwd) {
        this.trdPwd = trdPwd;
    }

    public String getManyFundFlag() {
        return manyFundFlag;
    }

    public void setManyFundFlag(String manyFundFlag) {
        this.manyFundFlag = manyFundFlag;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId;
    }

    public Integer getPwdErrTimes() {
        return pwdErrTimes;
    }

    public void setPwdErrTimes(Integer pwdErrTimes) {
        this.pwdErrTimes = pwdErrTimes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLockFlag() {
        return lockFlag;
    }

    public void setLockFlag(String lockFlag) {
        this.lockFlag = lockFlag;
    }

    public Integer getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(Integer timeOut) {
        this.timeOut = timeOut;
    }

    public String getExtProp() {
        return extProp;
    }

    public void setExtProp(String extProp) {
        this.extProp = extProp;
    }

    public String getIdentitySign() {
        return identitySign;
    }

    public void setIdentitySign(String identitySign) {
        this.identitySign = identitySign;
    }

    public String getServicePwd() {
        return servicePwd;
    }

    public void setServicePwd(String servicePwd) {
        this.servicePwd = servicePwd;
    }

    public String getCostFlag() {
        return costFlag;
    }

    public void setCostFlag(String costFlag) {
        this.costFlag = costFlag;
    }
}
