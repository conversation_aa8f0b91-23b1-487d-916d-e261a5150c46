package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitRateSectionRankMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateSectionRank;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2021/1/15-10:56.
 *
 * <AUTHOR>
 */
public class ProfitRateSectionRankDaoImpl extends BaseDao<ProfitRateSectionRankMapper, ProfitRateSectionRank, Long> implements ProfitRateSectionRankDao{
}
