package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

/**
 * Created by robin on 2016/7/18.
 * 收益率统计表
 * <AUTHOR>
 */
public class ProfitRateStat extends BaseEntityCal {

    private Long fundId;//资金帐号
    private Double profitRate;//今日收益率
    private String unit;//单位
    private Integer rankIndex;//收益率排名
    private Double rankPercent;//收益率排名百分比

    public Long getFundId() {
        return fundId;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public String getUnit() {
        return unit;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public Double getRankPercent() {
        return rankPercent;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }

    public void setRankPercent(Double rankPercent) {
        this.rankPercent = rankPercent;
    }

}
