package com.eastmoney.common.util;

/**
 * Created by sunyuncai on 2016/10/28.
 * update xiaoyongyong
 */
public class CommConstants {
    public static final Integer START_DATE = ********;//账户分析起始日

    public static String SERVER_ACCOUNT = "account-server";

    // 汇率波动提示阈值
    public static final Double RATE_THRESH = 0.01d;
    public static final String HGT_RATE_CHANGE = "hgtRateChange";

    public static final String CAL_REAL_TIME_PROFIT = "calRealTimeProfit";

    public static final String CAL_MONTH_PROFIT = "calMonthProfit";

    // fundAsset从交易历史tidb同步的开始时间
    public static final String FUNDASSET_TIDB_START_DATE_KEY = "FUNDASSET_TIDB_START_DATE";

    // 账户诊断持股风格策略
    // 最小清仓次数
    public static final Integer MIN_CLEAR_TIMES = 3;
    // 最小持股天数
    public static final Integer MIN_AVG_HOLD_DAYS = 20;
    // 分页常量
    public static final String PAGE_SIZE = "pageSize";
    public static final String PAGE_NO = "pageNo";
    public static final String START_NUM = "startNum";
    public static final int MAX_PAGE_SIZE = 1000;

    // 至今区间收益率走势图使用月收益率的临界时间：当前时间往前推3年
    public static Integer calProfitRateStartDate() {
        return Integer.valueOf(
                DateUtil.addMonth(DateUtil.getCuryyyyMMdd(), DateUtil.yyyyMMdd, -12)
        );
    }

}
