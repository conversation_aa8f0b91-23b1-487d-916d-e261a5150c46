package com.eastmoney.service.service.profit.section.concretesection;

import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.service.service.profit.section.AbstractProfitSectionService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created on 2020/8/12-19:35.
 * 整合不会跨区间的时间片(PHY,P1Y)清算 - 实时 - 临时收益
 *
 * <AUTHOR>
 */
@Service("profitSectionServiceNotCross")
public class ProfitSectionServiceNotCrossImpl extends AbstractProfitSectionService {

    @Override
    protected void setSectionParams(Map<String, Object> params, AssetNew assetNew, SectionProfitBean sectionProfitSettle) {
        Integer startDate = sectionProfitSettle.getIndexDate();
        startDate = startDate < assetNew.getStartDate() ? assetNew.getStartDate() : startDate;
        params.put("startDate", startDate);
        //最新清算日期以片收益计算时间为准
        params.put("accountBizDate", sectionProfitSettle.getBizDate());
        params.put("isProfitRateCalWindow", sectionProfitSettle.isProfitRateCalWindow());
    }
}
