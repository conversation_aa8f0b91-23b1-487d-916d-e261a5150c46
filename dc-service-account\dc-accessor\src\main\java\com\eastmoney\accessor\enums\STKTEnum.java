package com.eastmoney.accessor.enums;

/**
 * Created on 2016/11/03
 * 证券类别
 *
 * <AUTHOR>
 */
public enum STKTEnum {

    STKT_GP("0"),     //股票
    STKT_GZ("1"),     //国债
    STKT_QYZQ("2"),     //企业债券
    STKT_GZHG("3"),     //国债回购
    STKT_TZJJ("5"),     //投资基金
    STKT_QZHG("6"),     //企债回购
    STKT_ZHZQ("8"),     //转换债券
    STKT_ZQZG("9"),     //债券转股
    STKT_SWGZ("A"),     //实物国债
    STKT_SWQZ("B"),     //实物企债
    STKT_GSZ("C"),     //公司债 add by liuxr 20070921
    STKT_LOF("L"),     //LOF
    STKT_SHANGZLOF("o"),     //上证LOF add by hanyh
    STKT_SHANGZCWLOF("r"),     //上证财务分级LOF add by hanyh
    STKT_SHANGZDKLOF("s"),     //上证多空LOF add by hanyh
    STKT_SHYYHG("Y"),     //上海要约收购
    STKT_ETF("E"),     //ETF
    STKT_MDHG("D"),     //买断回购
    STKT_TP("T"),     //议案投票
    STKT_QZ("Q"),     //权证
    STKT_GZZY("G"),     //国债质押回购
    STKT_BZQ("M"),     //标准券
    STKT_QZZY("H"),     //企债质押回购
    STKT_BJZRZQ("J"),     //报价转让证券
    STKT_BJHGZQ("K"),     //报价回购证券, lids, 20110413, 报价回购
    STKT_SMZQ("e"),     //私募债券，xiaogp,20120529
    STKT_BZH("f"),     //B转H股, ganz ,20120930, B转H股
    STKT_ZQETF("g"),     //债券ETF, ganz,20130125
    STKT_HBETF("h"),     //货币ETF, ganz,20130125
    STKT_HJETF("i"),     //黄金ETF, lids, 20130522, SPB-1645
    STKT_KZHSMZQ("j"),     //可转换私募债券, lumq, 20130624, SPB-1780
    STKT_ZCZJZQ("k"),     //资产支持债券, lumq, 20130624, SPB-1780
    STKT_CJZQ("l"),     //证券公司次级债券, lumq, 20130624, SPB-1780
    STKT_ZRZGJH("m"),     //转让资管计划, lumq, 20130702, SPB-1801
    STKT_WI_BOND("n"),     //国债预发行证券, lumq, 20130717, SPB-1900
    STKT_GYXG("p"),     //公开优先股
    STKT_FGYXG("q"),     //非公开优先股
    STKT_BJZHJJ("z"),     //深圳专户基金, SPB-2514, tangxg, 20140212
    STKT_KJHGSZ("t");    //可交换公司债 SPB-4657,liyi,20151207


    private String unit;

    STKTEnum(String unit) {
        this.unit = unit;
    }

    public String getValue() {
        return this.unit;
    }

    public static STKTEnum getEnum(String value) {
        switch (value) {
            case "0":
                return STKT_GP;
            case "1":
                return STKT_GZ;
            case "2":
                return STKT_QYZQ;
            case "3":
                return STKT_GZHG;
            case "5":
                return STKT_TZJJ;
            case "6":
                return STKT_QZHG;
            case "8":
                return STKT_ZHZQ;
            case "9":
                return STKT_ZQZG;
            case "A":
                return STKT_SWGZ;
            case "B":
                return STKT_SWQZ;
            case "C":
                return STKT_GSZ;
            case "L":
                return STKT_LOF;
            case "o":
                return STKT_SHANGZLOF;
            case "r":
                return STKT_SHANGZCWLOF;
            case "s":
                return STKT_SHANGZDKLOF;
            case "Y":
                return STKT_SHYYHG;
            case "E":
                return STKT_ETF;
            case "D":
                return STKT_MDHG;
            case "T":
                return STKT_TP;
            case "Q":
                return STKT_QZ;
            case "G":
                return STKT_GZZY;
            case "M":
                return STKT_BZQ;
            case "H":
                return STKT_QZZY;
            case "J":
                return STKT_BJZRZQ;
            case "K":
                return STKT_BJHGZQ;
            case "e":
                return STKT_SMZQ;
            case "f":
                return STKT_BZH;
            case "g":
                return STKT_ZQETF;
            case "h":
                return STKT_HBETF;
            case "i":
                return STKT_HJETF;
            case "j":
                return STKT_KZHSMZQ;
            case "k":
                return STKT_ZCZJZQ;
            case "l":
                return STKT_CJZQ;
            case "m":
                return STKT_ZRZGJH;
            case "n":
                return STKT_WI_BOND;
            case "p":
                return STKT_GYXG;
            case "q":
                return STKT_FGYXG;
            case "z":
                return STKT_BJZHJJ;
            case "t":
                return STKT_KJHGSZ;
            default:
                throw new RuntimeException("not found STKT[" + value + "]");
        }
    }
}
