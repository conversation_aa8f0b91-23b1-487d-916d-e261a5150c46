package com.eastmoney.transport.util;

import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * User: hcc
 * Date: 13-9-10
 * Time: 下午6:31
 */
public class ChannelUtil {

    private static Logger LOG = LoggerFactory.getLogger(ChannelUtil.class);

    public static InetSocketAddress getLocalAddress(Channel channel) {
        return (InetSocketAddress) channel.localAddress();
    }

    public static InetSocketAddress getRemoteAddress(Channel channel) {
        return (InetSocketAddress) channel.remoteAddress();
    }

    public static String getChannelRoute(Channel channel) {
        String connectionRoute = "";
        try {
            return getLocalAddress(channel) + " -> " + getRemoteAddress(channel);
        } catch (Exception ex) {
            LOG.error("", ex);
        }
        return connectionRoute;
    }
}
