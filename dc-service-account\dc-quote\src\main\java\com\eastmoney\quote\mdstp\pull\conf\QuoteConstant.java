package com.eastmoney.quote.mdstp.pull.conf;

import org.apache.commons.configuration.CompositeConfiguration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.slf4j.LoggerFactory;

/**
 * Created by 1 on 15-7-7.
 */
public class QuoteConstant {
    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(QuoteConstant.class);

    //沪深
    public static String QUOTE_HOST;
    public static int QUOTE_PULL_PORT;
    public static int QUOTE_PUSH_PORT;
    //港股
    public static String QUOTE_HK_HOST;
    public static int QUOTE_HK_PULL_PORT;
    public static int QUOTE_HK_PUSH_PORT;

    // 沪深和港股合并版
    public static String QUOTE_COMPOSE_HOST;
    public static int QUOTE_COMPOSE_PULL_PORT;
    public static int QUOTE_COMPOSE_PUSH_HOST;

    public static String QUOTE_TYPE_HS = "1";
    public static String QUOTE_TYPE_HK = "2";
    public static String QUOTE_TYPE_HSHK = "3";
    //#初始连接数
    public static int initialSize=1;
    //#允许最大连接
    public static int maxActive=1;
    //#允许最大空闲连接数
    public static int maxIdle=5;
    //#允许最小空闲连接数
    public static int minIdle=1;

    static {
        CompositeConfiguration config = new CompositeConfiguration();
        try {
            config.addConfiguration(new PropertiesConfiguration("quote-config.properties"));
        } catch (ConfigurationException e) {
            LOG.error("quote-config.properties file err:" + e.getMessage(), e);
        }

        try {
            //沪深
            QUOTE_HOST = "*************";
            QUOTE_PULL_PORT = 4004;
            QUOTE_PUSH_PORT = 4000;
            //港股
            QUOTE_HK_HOST = "*************";
            QUOTE_HK_PULL_PORT = 4004;
            QUOTE_HK_PUSH_PORT = 4000;

            // 沪深和港股
            QUOTE_COMPOSE_HOST = "*************";
            QUOTE_COMPOSE_PULL_PORT = 4004;
            QUOTE_COMPOSE_PUSH_HOST = 4000;

            QUOTE_HOST = config.getString("quote_host");
            QUOTE_PULL_PORT = config.getInt("quote_pull_port");
            QUOTE_PUSH_PORT = config.getInt("quote_push_port");

            QUOTE_HK_HOST = config.getString("quote_hk_host");
            QUOTE_HK_PULL_PORT = config.getInt("quote_hk_pull_port");
            QUOTE_HK_PUSH_PORT = config.getInt("quote_hk_push_port");

            QUOTE_COMPOSE_HOST = config.getString("quote_compose_host");
            QUOTE_COMPOSE_PULL_PORT = config.getInt("quote_compose_pull_port");
            QUOTE_COMPOSE_PUSH_HOST = config.getInt("quote_compose_push_port");

            StringBuffer sb = new StringBuffer("");
            sb.append(" QUOTE_HOST=" + QUOTE_HOST);
            sb.append(" QUOTE_PULL_PORT=" + QUOTE_PULL_PORT);
            sb.append(" QUOTE_PUSH_PORT=" + QUOTE_PUSH_PORT);

            sb.append(" QUOTE_HK_HOST=" + QUOTE_HOST);
            sb.append(" QUOTE_HK_PULL_PORT=" + QUOTE_PULL_PORT);
            sb.append(" QUOTE_HK_PUSH_PORT=" + QUOTE_PUSH_PORT);

            sb.append(" QUOTE_COMPOSE_HOST=" + QUOTE_COMPOSE_HOST);
            sb.append(" QUOTE_COMPOSE_PULL_PORT=" + QUOTE_COMPOSE_PULL_PORT);
            sb.append(" QUOTE_COMPOSE_PUSH_HOST=" + QUOTE_COMPOSE_PUSH_HOST);

            LOG.info(sb.toString());
        } catch (Exception e) {
            LOG.error("quote-config.properties [server.ip server.port] err:" + e.getMessage(), e);
        }






    }

    public static void main(String[] args) {
        System.out.println();
    }
}
