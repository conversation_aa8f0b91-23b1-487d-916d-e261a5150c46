package com.eastmoney.common.entity;

/**
 * Created by sunyuncai on 2016/11/4.
 */
public class HgtReferRate {
    private String market;
    private String moneyType;
    private Double buyRate;
    private Double dayBuyRiseRate;
    private Double nightBuyRiseRate;
    private Double saleRate;
    private Double daySaleRiseRate;
    private Double nightSaleRiseRate;
    private Double midRate;
    /**
     * 买入结算汇率
     */
    private Double settRate;
    /**
     * 卖出结算汇率
     */
    private Double saleSettRate;
    private Integer sysDate;
    private String remark;

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public Double getBuyRate() {
        return buyRate;
    }

    public void setBuyRate(Double buyRate) {
        this.buyRate = buyRate;
    }

    public Double getDayBuyRiseRate() {
        return dayBuyRiseRate;
    }

    public void setDayBuyRiseRate(Double dayBuyRiseRate) {
        this.dayBuyRiseRate = dayBuyRiseRate;
    }

    public Double getNightBuyRiseRate() {
        return nightBuyRiseRate;
    }

    public void setNightBuyRiseRate(Double nightBuyRiseRate) {
        this.nightBuyRiseRate = nightBuyRiseRate;
    }

    public Double getSaleRate() {
        return saleRate;
    }

    public void setSaleRate(Double saleRate) {
        this.saleRate = saleRate;
    }

    public Double getDaySaleRiseRate() {
        return daySaleRiseRate;
    }

    public void setDaySaleRiseRate(Double daySaleRiseRate) {
        this.daySaleRiseRate = daySaleRiseRate;
    }

    public Double getNightSaleRiseRate() {
        return nightSaleRiseRate;
    }

    public void setNightSaleRiseRate(Double nightSaleRiseRate) {
        this.nightSaleRiseRate = nightSaleRiseRate;
    }

    public Double getMidRate() {
        return midRate;
    }

    public void setMidRate(Double midRate) {
        this.midRate = midRate;
    }

    public Double getSettRate() {
        return settRate;
    }

    public void setSettRate(Double settRate) {
        this.settRate = settRate;
    }

    public Double getSaleSettRate() {
        return saleSettRate;
    }

    public void setSaleSettRate(Double saleSettRate) {
        this.saleSettRate = saleSettRate;
    }

    public Integer getSysDate() {
        return sysDate;
    }

    public void setSysDate(Integer sysDate) {
        this.sysDate = sysDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
