package com.eastmoney.common.entity;

/**
 * Created on 2016/3/16
 * 调用存储过程计算,返回信息
 *
 * <AUTHOR>
 */
public class TxzqCalAsset {
    private String dtOrgId;// --机构编码,默认 "" 统计全部
    private Integer dtCustId;// --客户代码,默认 0 统计全部
    private Integer dtFundId;// --资金帐号,默认 0 统计全部
    private String dtKind;// --币种,默认"" 统计全部

    private Integer errorCode;// 错误编号
    private String errorMsg;

    public String getDtOrgId() {
        return dtOrgId;
    }

    public void setDtOrgId(String dtOrgId) {
        this.dtOrgId = dtOrgId.trim();
    }

    public Integer getDtCustId() {
        return dtCustId;
    }

    public void setDtCustId(Integer dtCustId) {
        this.dtCustId = dtCustId;
    }

    public Integer getDtFundId() {
        return dtFundId;
    }

    public void setDtFundId(Integer dtFundId) {
        this.dtFundId = dtFundId;
    }

    public String getDtKind() {
        return dtKind;
    }

    public void setDtKind(String dtKind) {
        this.dtKind = dtKind.trim();
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
