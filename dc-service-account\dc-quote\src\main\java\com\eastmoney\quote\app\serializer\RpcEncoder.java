package com.eastmoney.quote.app.serializer;

import com.eastmoney.quote.app.coder.Codec5059;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.LoggerFactory;
import java.nio.ByteOrder;


/**
 * Created by 1 on 15-7-7.
 */
public class RpcEncoder extends MessageToByteEncoder<Packet> {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(RpcEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, Packet packet, ByteBuf buf) throws Exception {
        try {
            buf = buf.order(ByteOrder.LITTLE_ENDIAN);
            buf.writeByte(packet.getpHead());
            buf.writeShort(packet.getType());
            buf.writeByte(packet.getAttr1());
            buf.writeByte(packet.getAttr2());
            buf.writeShort(packet.getLength());
            if(packet.getType()==5059){
                new Codec5059().encode(packet,buf);
            }
            //重置长度
            buf.setShort(5, buf.writerIndex() - 8);
        }catch (Exception e){
            LOG.error(e.getMessage(), e);
        }
    }

}
