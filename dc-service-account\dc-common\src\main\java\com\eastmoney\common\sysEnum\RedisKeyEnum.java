package com.eastmoney.common.sysEnum;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-03-28 10:49
 */
public enum RedisKeyEnum {

    GGT_EXCHANGE_RATE_KEY("account_analysis:ggt_exchange_rate:",10*60,"港股通汇率信息"),

    TRADE_CUST_CALENDER("account_analysis:cust_calender:",30*60,"盈亏日历信息")

    ;

    /**
     * redis key
     */
    private final String prefix;
    /**
     * 过期时间 单位秒，redisProxy入参是秒
     */
    private final Integer expire;
    /**
     * 描述
     */
    private final String desc;

    RedisKeyEnum(String prefix,Integer expire,String desc){
        this.prefix=prefix;
        this.expire=expire;
        this.desc=desc;
    }

    public String getPrefix() {
        return prefix;
    }

    public Integer getExpire() {
        return expire;
    }

    public String getDesc() {
        return desc;
    }
}
