<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiBusinessTaskStatusMapper">

    <select id="selectOneTaskNewestStatus" parameterType="map"
            resultType="com.eastmoney.common.entity.BusinessTaskStatusDO">
        select bizdate,executeStatus,eutime,eitime from atcenter.business_task_status
        <where>
            <if test="serverId != null">
                and serverid = #{serverId}
            </if>
            <if test="businessTaskHandle != null">
                and businessTaskHandle = #{businessTaskHandle}
            </if>
            order by bizdate desc
            limit 1
        </where>
    </select>

</mapper>