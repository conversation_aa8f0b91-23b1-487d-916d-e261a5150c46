package com.eastmoney.service.service;

import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import com.eastmoney.common.entity.cal.tProfitRank;

import java.util.List;
import java.util.Map;

public interface TProfitService {

    /**
     * 查询每日收益列表
     */
    List<TProfitDayDO> getTProfitDayList(Map<String, Object> params);

    /**
     * 做T操作汇总
     */
    TProfitBO getTProfitSection(Map<String, Object> dealParams);

    /**
     * 个股每日收益列表
     */
    List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params);

    /**
     * 个股排名
     */
    default List<tProfitRank> getSecTProfitRankList(Map<String, Object> params) {
        return null;
    }

    /**
     * 买卖记录
     */
    default List<Match> getTMatchList(Map<String, Object> params) {
        return null;
    }

    /**
     * pc 做t个股每日记录
     */
    default List<TSecProfitDayDO> getTSecProfitDayListPC(Map<String, Object> dealParams) {
        return null;
    }

    /**
     * 个股区间T收益统计
     */
    default TProfitBO getSecTProfitSection(Map<String, Object> dealParams) {
        return null;
    }

    /**
     * 自定义区间T收益统计
     */
    default TProfitBO getTProfitCustomizeSection(Map<String, Object> dealParams) {
        return null;
    }
}
