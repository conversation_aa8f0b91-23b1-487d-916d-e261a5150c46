package com.eastmoney.quote.mdstp.model;/**
 * Created by 1 on 16-10-24.
 */

/**
 * Created on 16-10-24
 *
 * <AUTHOR>
 */
public class BuySellInfo {
    //buy 0,sell 1
    private byte buyFlag;

    // 买卖盘价格
    private int dwMMp;
    // 买卖盘量
    private long xMMPVol;

    public int getDwMMp() {
        return dwMMp;
    }

    public void setDwMMp(int dwMMp) {
        this.dwMMp = dwMMp;
    }

    public long getxMMPVol() {
        return xMMPVol;
    }

    public void setxMMPVol(long xMMPVol) {
        this.xMMPVol = xMMPVol;
    }

    public byte getBuyFlag() {
        return buyFlag;
    }

    public void setBuyFlag(byte buyFlag) {
        this.buyFlag = buyFlag;
    }
}
