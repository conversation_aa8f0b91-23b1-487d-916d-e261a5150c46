package com.eastmoney.service.main;

import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.quote.mdstp.starter.QtStarter;
import com.eastmoney.service.http.HttpServerStarter;
import com.eastmoney.service.rpc.pull.PullServer;
import com.eastmoney.service.util.SpringConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * created by sunyuncai on 2016/3/8.
 */
@SpringBootApplication(scanBasePackages = {"com.eastmoney"}, exclude = {TransactionAutoConfiguration.class})
@EnableConfigurationProperties
@ImportResource(locations = {"classpath*:spring-mybatis.xml"})
@EnableScheduling
public class AccountMain {
    private static Logger LOG = LoggerFactory.getLogger(AccountMain.class);

    public static void main(String[] args) {
        String serverName = "账户分析服务";
        try {
            SpringApplication.run(AccountMain.class, args);
            QtStarter.start();
            SpringConfig springConfig = SpringContextUtil.getBean(SpringConfig.class);
            Integer pullServerPort = springConfig.getPullServerPort();
            new Thread(new PullServer(pullServerPort), serverName).start();
            HttpServerStarter.start();
        } catch (Exception e) {
            LOG.error("启动失败,请查看异常", e);
        }
    }
}
