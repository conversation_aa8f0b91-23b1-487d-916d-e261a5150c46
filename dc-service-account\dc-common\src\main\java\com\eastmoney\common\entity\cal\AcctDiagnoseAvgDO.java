package com.eastmoney.common.entity.cal;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */

import com.eastmoney.common.entity.BaseEntity;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */
public class AcctDiagnoseAvgDO extends BaseEntity {

    /**
     * 数据更新时间
     */
    private String bizTime;

    /**
     * 平均盈利分
     */
    private Integer profitScoreAvg;

    /**
     * 平均风控分
     */
    private Integer riskScoreAvg;

    /**
     * 平均选股分
     */
    private Integer holdScoreAvg;

    /**
     * 平均择时分
     */
    private Integer tradeScoreAvg;

    /**
     * 平均行业分
     */
    private Integer indAllocScoreAvg;

    public String getBizTime() {
        return bizTime;
    }

    public void setBizTime(String bizTime) {
        this.bizTime = bizTime;
    }

    public Integer getProfitScoreAvg() {
        return profitScoreAvg;
    }

    public void setProfitScoreAvg(Integer profitScoreAvg) {
        this.profitScoreAvg = profitScoreAvg;
    }

    public Integer getRiskScoreAvg() {
        return riskScoreAvg;
    }

    public void setRiskScoreAvg(Integer riskScoreAvg) {
        this.riskScoreAvg = riskScoreAvg;
    }

    public Integer getHoldScoreAvg() {
        return holdScoreAvg;
    }

    public void setHoldScoreAvg(Integer holdScoreAvg) {
        this.holdScoreAvg = holdScoreAvg;
    }

    public Integer getTradeScoreAvg() {
        return tradeScoreAvg;
    }

    public void setTradeScoreAvg(Integer tradeScoreAvg) {
        this.tradeScoreAvg = tradeScoreAvg;
    }

    public Integer getIndAllocScoreAvg() {
        return indAllocScoreAvg;
    }

    public void setIndAllocScoreAvg(Integer indAllocScoreAvg) {
        this.indAllocScoreAvg = indAllocScoreAvg;
    }
}

