package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.dao.tidb.HgtTradeDateDao;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 港股通假期收益计算标识缓存
 *
 * <AUTHOR>
 * @date 2024/8/20
 */
@Service
public class HkProfitFlagCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(HkProfitFlagCacheService.class);

    @Resource(name = "hkProfitFlagCache")
    private LoadingCache<Integer, Integer> hkProfitFlagCache;
    @Resource(name = "hgtTradeDateDao")
    private HgtTradeDateDao hgtTradeDateDao;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private TradeDateDao tradeDateDao;

    @Bean(name = "hkProfitFlagCache")
    public LoadingCache<Integer, Integer> hkProfitFlagCache() {
        LoadingCache<Integer, Integer> build = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10)
                .maximumSize(10)
                .refreshAfterWrite(300, TimeUnit.SECONDS)
                .build(new CacheLoader<Integer, Integer>() {
                    @Override
                    public Integer load(Integer key) {
                        Integer hkProfitFlag = null;
                        try {
                            hkProfitFlag = hgtTradeDateDao.calHkProfitFlag(key);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return hkProfitFlag;
                    }
                });
        return build;
    }

    /**
     * 当日是A股交易日 && 港股上一交易日大于 A股和港股上一相等交易日 && 开关打开
     * <p>
     * 当日是A股交易日这一条件是冗余的，仅为兜底
     *
     * @return 是否展示港股通假期收益
     */
    public boolean getHkProfitFlag() {
        try {
            // 数据库开关
            boolean showFlag = nodeConfigService.getHkProfitShowFlag();
            if (!showFlag) {
                return false;
            }

            // 港股上一交易日是否大于A股和港股上一相等交易日
            Integer bizDate = CommonUtil.convert(DateUtil.getCuryyyyMMdd(), Integer.class);
            Integer hkProfitFlag = hkProfitFlagCache.get(bizDate);
            if (!Objects.equals(hkProfitFlag, 1)) {
                return false;
            }

            // 当日是A股交易日
            return tradeDateDao.todayIsMarket();
        } catch (ExecutionException e) {
            LOG.error("港股通假期收益计算标识获取异常", e);
        }
        return false;
    }
}
