<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiStockCodeAlterMapper">
    <select id="getStockCodeAlterList" resultType="com.eastmoney.common.entity.StockCodeAlter">
        select OLDSTKCODE,OLDSNAME oldStkName,STKCODE,STKNAME,EFFECTIVEDATE,DEL
        from ATCENTER.B_STOCK_CODE_ALTER
        where DEL = 0
        <if test="stkCode != null">
            and STKCODE = #{stkCode}
        </if>
    </select>
</mapper>