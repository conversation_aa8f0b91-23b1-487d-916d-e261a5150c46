<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.BusinessDictMapper">

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.BusinessDict">
        select dictCode,dictKey,serverId,dictValue
        from atcenter.business_Dict
        where dictCode = #{dictCode} and dictKey = #{dictKey}
            and serverId = #{serverId}
    </select>

</mapper>