package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.service.cache.TradeTimeCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 集合竞价调度线程死亡后的修复措施
 * <AUTHOR>
@Action
@Component
public class TradeTimeRepairAction {

    @Autowired
    private TradeTimeCacheService tradeTimeCacheService;

    @FunCodeMapping("repairTradeTimeCache")
    @RequestMapping("/repairTradeTimeCache")
    public String repairTradeTimeCache(Map<String, Object> params){
        tradeTimeCacheService.repairTradeTimeCache();
        return "集合竞价时间缓存重置成功！";
    }
}
