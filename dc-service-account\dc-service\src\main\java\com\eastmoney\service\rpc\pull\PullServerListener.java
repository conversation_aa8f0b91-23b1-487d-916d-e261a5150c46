package com.eastmoney.service.rpc.pull;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.common.model.LogBean;
import com.eastmoney.common.util.ComputerAddressUtil;
import com.eastmoney.common.util.ExecutorBuilder;
import com.eastmoney.common.util.LogUtil;
import com.eastmoney.service.model.ChannelData;
import com.eastmoney.service.rpc.MessageHandler;
import com.eastmoney.service.rpc.OtherTask;
import com.eastmoney.service.util.PropertiesUtil;
import com.eastmoney.service.util.ServiceConstants;
import com.eastmoney.transport.listener.Listener;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.UnknownHostException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by sunyuncai on 2016/3/4.
 */
public class PullServerListener implements Listener {
    /**
     * 请求线程池队列任务超时时间 默认10秒
     */
    private static final int REQUEST_QUEUE_TIME_OUT = 15000;
    private static Logger LOG = LoggerFactory.getLogger(PullServerListener.class);
    private static ThreadPoolExecutor pullCoreExecutor = (ThreadPoolExecutor) ExecutorBuilder.newFixedThreadPool(
            Integer.parseInt(PropertiesUtil.readValue(ServiceConstants.PULL_SERVER_THREAD_POOL, 100)),
            Integer.parseInt(PropertiesUtil.readValue(ServiceConstants.PULL_SERVER_QUEUE_CAPACITY, 20000)),
            "pullCoreExecutor");
    private static ThreadPoolExecutor pullOtherExecutor = (ThreadPoolExecutor) ExecutorBuilder.newFixedThreadPool(1, 2000,
            "pullOtherExecutor");
    private static MessageHandler messageHandler = SpringContextUtil.getBean(MessageHandler.class);

    static {
        Thread t2 = new Thread((new MonitorScan()), "监控队列长度");
        t2.setDaemon(true);
        t2.start();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {

    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        messageHandler.channelDesKeyMap.remove(ctx.channel());
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        Message message = (Message) msg;
        try {
            byte type = message.getType();
            if (type == Constants.SOCKET_TYPE_PULL_REQ) {
                //拉取
                pullCoreExecutor.execute(new CoreTask(new ChannelData(ctx, message)));
            } else {
                pullOtherExecutor.execute(new OtherTask(new ChannelData(ctx, message)));
            }
        } catch (Throwable t) {
            messageHandler.handleException(t, new ChannelData(ctx, message));
        }
    }

    private static class MonitorScan implements Runnable {
        private static LogBean logBean = new LogBean();

        static {
            logBean.setAction("pull_queue_size");
            logBean.setLog_type("syslog");
            try {
                logBean.setS_ip(ComputerAddressUtil.getIp());
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void run() {
            long lastLogTime = 0;
            while (true) {
                long nowTime = System.currentTimeMillis();
                int queueSize = pullCoreExecutor.getQueue().size();
                int activeCount = pullCoreExecutor.getActiveCount();
                if (queueSize != 0 || activeCount != 0 || (nowTime - lastLogTime) >= 1000 * 60 * 10) {
                    logBean.setError_msg("queueSize=" + queueSize + " activeCount=" + activeCount);
                    LogUtil.info(logBean);
                    lastLogTime = nowTime;
                }
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    LOG.error("", e);
                }
            }
        }
    }

    class CoreTask implements Runnable {
        private ChannelData channelData;

        public CoreTask(ChannelData channelData) {
            this.channelData = channelData;
        }

        @Override
        public void run() {
            try {
                long waitTime = System.currentTimeMillis() - channelData.getRequestTimestamp();
                if (waitTime > REQUEST_QUEUE_TIME_OUT) {
                    throw new RuntimeException("请求超时，请稍候再试 channelData=" + JSON.toJSONString(channelData));
                }
                Message message = messageHandler.handle(channelData, waitTime);
                if (message != null) {
                    messageHandler.writeMessage(channelData.getCtx().channel(), message);
                } else {
                    throw new RuntimeException("返回空对象,channelData=" + JSON.toJSONString(channelData));
                }
            } catch (Throwable t) {
                messageHandler.handleException(t, channelData);
            }
        }
    }

}
