package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 单笔最大清仓获利&清仓总计
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearClearProfitDO {
    // 2024最大一笔清仓交易获利的日期
    private Integer maxClearProfitDate;
    // 2024最大一笔清仓交易获利的证券产品
    private String maxClearProfitStk;
    // 2024最大一笔清仓获利的金额
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal maxClearProfit;

    public Integer getMaxClearProfitDate() {
        return maxClearProfitDate;
    }

    public void setMaxClearProfitDate(Integer maxClearProfitDate) {
        this.maxClearProfitDate = maxClearProfitDate;
    }

    public String getMaxClearProfitStk() {
        return maxClearProfitStk;
    }

    public void setMaxClearProfitStk(String maxClearProfitStk) {
        this.maxClearProfitStk = maxClearProfitStk;
    }

    public BigDecimal getMaxClearProfit() {
        return maxClearProfit;
    }

    public void setMaxClearProfit(BigDecimal maxClearProfit) {
        this.maxClearProfit = maxClearProfit;
    }
}
