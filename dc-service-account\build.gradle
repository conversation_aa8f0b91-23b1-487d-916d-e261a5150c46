ext {
    version = [
            'fastjson'                                   : '1.2.83',
            "slf4j"                                      : "1.7.35",
            "logback"                                    : "1.2.10",
            "spring-boot"                                : "2.4.13",
            "guava"                                      : "31.0.1-jre",
            "dom4j"                                      : "1.6.1",
            "commons-lang3"                              : "3.12.0",
            "commons-io"                                 : "2.11.0",
            "commons-beanutils"                          : "1.9.4",
            "commons-text"                               : "1.9",
            "httpclient"                                 : "4.5.13",
            "commons-collections4"                       : "4.4",
            "ojdbc6"                                     : "11.2.0.3",
            "druid"                                      : "1.2.8",
            "netty-all"                                  : "4.1.73.Final",
            "netty-tcnative"                             : "2.0.48.Final",
            "mybatis-spring-boot"                        : "2.1.4",
            "mockito-core"                               : "3.5.15",
            "mysql"                                      : "5.1.49",
            "joda"                                       : "2.9.7",
            "jtds"                                       : "1.3.1",
            'commons-math3'                              : "3.6",
            'mail'                                       : "1.4",
            "commons-pool"                               : "1.6",
            "commons-configuration"                      : "1.10",
            "jeromq"                                     : "0.4.3",
            "lombok"                                     : "1.16.20",
            'redis-cluster-client'                       : '1.0.0.7',
            'kryo'                                       : '5.0.0',
    ]

    dependencies = ["fastjson"                                   : "com.alibaba:fastjson:${ext.version['fastjson']}",
                    "slf4j-api"                                  : "org.slf4j:slf4j-api:${ext.version['slf4j']}",
                    "logback"                                    : "ch.qos.logback:logback-classic:${ext.version['logback']}",

                    "spring-boot-starter"                        : "org.springframework.boot:spring-boot-starter:${ext.version['spring-boot']}",
                    "spring-boot-starter-aop"                    : "org.springframework.boot:spring-boot-starter-aop:${ext.version['spring-boot']}",
                    "spring-boot-configuration-processor"        : "org.springframework.boot:spring-boot-configuration-processor:${ext.version['spring-boot']}",
                    "spring-boot-autoconfigure"                  : "org.springframework.boot:spring-boot-autoconfigure:${ext.version['spring-boot']}",
                    "spring-boot-starter-test"                   : "org.springframework.boot:spring-boot-starter-test:${ext.version['spring-boot']}",
                    "spring-boot-starter-jdbc"                   : "org.springframework.boot:spring-boot-starter-jdbc:${ext.version['spring-boot']}",

                    "guava"                                      : "com.google.guava:guava:${ext.version['guava']}",
                    "httpclient"                                 : "org.apache.httpcomponents:httpclient:${ext.version['httpclient']}",
                    "dom4j"                                      : "dom4j:dom4j:${ext.version['dom4j']}",
                    "commons-lang3"                              : "org.apache.commons:commons-lang3:${ext.version['commons-lang3']}",
                    "commons-io"                                 : "commons-io:commons-io:${ext.version['commons-io']}",
                    "commons-collections"                        : "org.apache.commons:commons-collections4:${ext.version['commons-collections4']}",
                    "commons-text"                               : "org.apache.commons:commons-text:${ext.version['commons-text']}",
                    "commons-beanutils"                          : "commons-beanutils:commons-beanutils:${ext.version['commons-beanutils']}",
                    "ojdbc6"                                     : "com.oracle:ojdbc6:${ext.version['ojdbc6']}",
                    "druid"                                      : "com.alibaba:druid:${ext.version['druid']}",
                    "netty-all"                                  : "io.netty:netty-all:${ext.version['netty-all']}",
                    "netty-tcnative"                             : "io.netty:netty-tcnative-boringssl-static:${ext.version['netty-tcnative']}",
                    "mybatis-spring-boot"                        : "org.mybatis.spring.boot:mybatis-spring-boot-starter:${ext.version['mybatis-spring-boot']}",
                    "mockito-core"                               : "org.mockito:mockito-core:${ext.version['mockito-core']}",
                    "mockito-inline"                             : "org.mockito:mockito-inline:${ext.version['mockito-core']}",
                    "mysql"                                      : "mysql:mysql-connector-java:${ext.version['mysql']}",
                    "joda-time"                                  : "joda-time:joda-time:${ext.version['joda']}",
                    "jtds"                                       : "net.sourceforge.jtds:jtds:${ext.version['jtds']}",
                    "commons-math3"                              : "org.apache.commons:commons-math3:${ext.version['commons-math3']}",
                    "mail"                                       : "javax.mail:mail:${ext.version['mail']}",
                    "commons-pool"                               : "commons-pool:commons-pool:${ext.version['commons-pool']}",
                    "commons-configuration"                      : "commons-configuration:commons-configuration:${ext.version['commons-configuration']}",
                    "jeromq"                                     : "org.zeromq:jeromq:${ext.version['jeromq']}",
                    "lombok"                                     : "org.projectlombok:lombok:${ext.version['lombok']}",
                    "redis-cluster-client"                       : "com.eastmoney.dccp:redis-cluster-client:${ext.version['redis-cluster-client']}",
                    "kryo"                                       : "com.esotericsoftware:kryo:${ext.version['kryo']}"
    ]
}

buildscript {
    repositories {
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        maven { url 'https://repo.spring.io/snapshot' }
        maven { url 'https://repo.spring.io/milestone' }
        mavenCentral()
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:2.4.13")
        classpath("io.spring.gradle:dependency-management-plugin:1.0.8.RELEASE")
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'idea'
    apply plugin: 'maven'
    apply plugin: 'io.spring.dependency-management'

    group 'dc-service-account'

    project(":dc-accessor") {
        version = '1.0-SNAPSHOT'
    }
    project(":dc-common") {
        version = '1.0-SNAPSHOT'
    }
    project(":dc-rpc") {
        version = '1.0-SNAPSHOT'
    }
    project(":dc-service") {
        version = '1.0-SNAPSHOT'
    }
    project(":dc-quote") {
        version = '1.0-SNAPSHOT'
    }

    // JVM 版本号要求
    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    // java编译的时候缺省状态下会因为中文字符而失败
    [compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }


    ext {
        if (!project.hasProperty('buildType')) {
            buildType = "local"
        }
        if (project.hasProperty('branch')) {
            if (branch == null || branch == '') {
                versionStr = version + rootDir.name.replace(rootProject.name, "")
            } else {
                versionStr = version + '-' + branch
            }
        } else {
            versionStr = version
        }
    }

    repositories {
        maven { url "http://maven.aliyun.com/nexus/content/groups/public/" }
        maven {
            // 凭证
            credentials {
                username 'service' // 仓库发布用户名
                password 'service123' // 仓库发布用户密码
            }
            // 地址
            url 'http://172.30.64.112:8081/repository/srv-releases/'
            allowInsecureProtocol true
        }
        maven { url "http://61.129.129.185:8081/nexus/content/repositories/releases/" }
        maven { url "http://61.129.129.185:8081/nexus/content/repositories/central/" }
        maven { url "http://61.129.129.185:8081/nexus/content/repositories/thirdparty/" }
        maven { url "http://clojars.org/repo/" }
        mavenCentral()
    }

    configurations {
        // 所有需要忽略的包定义在此
        //all*.exclude group: 'commons-logging'
        implementation.exclude module: 'log4j-to-slf4j'
    }

    dependencies {
        // 通用依赖
        implementation rootProject.ext.dependencies['slf4j-api']
        implementation rootProject.ext.dependencies['logback']
        implementation rootProject.ext.dependencies['fastjson']
        implementation rootProject.ext.dependencies['commons-lang3']
        implementation rootProject.ext.dependencies['commons-beanutils']
        implementation rootProject.ext.dependencies['commons-text']
        implementation rootProject.ext.dependencies['guava']
        implementation rootProject.ext.dependencies['mybatis-spring-boot']
        implementation rootProject.ext.dependencies['mysql']
        implementation rootProject.ext.dependencies['jtds']
        testImplementation rootProject.ext.dependencies['mockito-core']
        testImplementation rootProject.ext.dependencies['mockito-inline']

        implementation (rootProject.ext.dependencies['spring-boot-starter']) {
            exclude group: 'ch.qos.logback', module: 'logback-core'
            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        }

        implementation rootProject.ext.dependencies['spring-boot-starter-aop']
        implementation rootProject.ext.dependencies['spring-boot-configuration-processor']
        implementation rootProject.ext.dependencies['spring-boot-starter-test']
        implementation rootProject.ext.dependencies['netty-all']
        implementation rootProject.ext.dependencies['druid']
        implementation rootProject.ext.dependencies['ojdbc6']
        implementation rootProject.ext.dependencies['lombok']
        implementation rootProject.ext.dependencies['redis-cluster-client']
        implementation rootProject.ext.dependencies['kryo']
    }

    //打包源代码
    task sourcesJar(type: Jar, dependsOn: classes) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }

    task javadocJar(type: Jar, dependsOn: javadoc) {
        classifier = 'javadoc'
        from javadoc.destinationDir
    }

    //打包上传包含的功能
    artifacts {
        archives jar
        archives sourcesJar
        // archives javadocJar
    }

    def configFile = file('config.groovy')
    Map configObject = new HashMap();
    if (configFile.exists()) {
        BufferedInputStream bufferedInputStream = configFile.newInputStream()
        byte[] bs = new byte[bufferedInputStream.available()]
        bufferedInputStream.read(bs, 0, bufferedInputStream.available())
        bufferedInputStream.close()
        String str = new String(bs, "utf-8")
        configObject = new ConfigSlurper("${buildType}").parse(str)
    }
    //noinspection GroovyAssignabilityCheck
    configObject.put("jarFileName", "${project.name}-${project.version}")
    //noinspection GroovyAssignabilityCheck
    configObject.put("projectName", "${project.name}")

    //编译时处理资源的时候替换 ${}
    processResources() {
        filteringCharset = 'UTF-8'
        println "${project.name} buildType:${buildType}"
        eachFile { fileCopyDetails ->
            if (fileCopyDetails.name.endsWith(".properties") || fileCopyDetails.name.endsWith(".yml")) {
                expand(configObject)
            }
        }
    }

    task zipAll(type: Zip) {
        destinationDir = file("${buildDir}/${buildType}")
        archiveName = "${project.name}-${buildType}-${versionStr}.zip"
        from "${buildDir}/${buildType}"
        exclude("${project.name}-${buildType}-${versionStr}.zip")
        into "${project.name}-${versionStr}"
    }

    task copyStartShell(type: Copy) {
        filteringCharset = 'UTF-8'
        from "${buildDir}/resources/main"
        into "${buildDir}/${buildType}"
        include("*.bat")
        include("*.sh")
        expand(configObject)
    }

    task copyLibs(type: Copy) {
        from configurations.runtimeClasspath
        into "${buildDir}/${buildType}/libs"
    }

    task copyJar(type: Copy) {
        from "${buildDir}/libs/${project.name}-${project.version}.jar"
        into "${buildDir}/${buildType}"
    }

    task copyConfig(type: Copy) {
        from("${project.rootProject.rootDir}/dc-accessor/${project.buildDir.name}/resources/main")
        from("${project.rootProject.rootDir}/dc-common/${project.buildDir.name}/resources/main")
        from("${project.rootProject.rootDir}/dc-quote/${project.buildDir.name}/resources/main")
        from("${project.rootProject.rootDir}/dc-rpc/${project.buildDir.name}/resources/main")
        from("${buildDir}/resources/main")
        into "${buildDir}/${buildType}/config"
        include("**/*.properties", "**/*.xml", "*.properties", "*.xml", "**/*.json")
        exclude("mapper", "spring*", "spring-mybatis.xml", "mybatis-config.xml","META-INF")
    }

    task publish() {
    }

    task remove(type: Delete) {
        delete 'out', 'build'
    }

    //执行顺序
    publish.dependsOn zipAll
    zipAll.dependsOn copyStartShell
    copyStartShell.dependsOn copyLibs
    copyLibs.dependsOn copyJar
    copyJar.dependsOn copyConfig
    copyConfig.dependsOn build

    compileJava.dependsOn remove

}




