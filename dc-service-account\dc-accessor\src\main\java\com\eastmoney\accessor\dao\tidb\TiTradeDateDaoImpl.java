package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.mapper.tidb.TiTradeDateMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.cache.LocalCache;
import com.eastmoney.common.entity.TradeDate;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * A股交易日历tidb Dao实现类
 * <AUTHOR>
 * @create 2023/5/16
 */
@Service("tradeDateDao")
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
public class TiTradeDateDaoImpl extends BaseDao<TiTradeDateMapper, Object, Integer> implements TradeDateDao {
    @Override
    public boolean isMarket(Integer bizdate) {
        Boolean flag = LocalCache.IsMarket(bizdate);
        if(flag == null){
            Map<String,Object> params = new HashMap<>();
            if (bizdate != null) {
                params.put("bizdate", String.valueOf(bizdate));
            }
            if(getMapper().todayIsMarket(params) == 1){
                flag = true;
            }else{
                flag = false;
            }
            LocalCache.addIsMarket(bizdate,flag);
        }
        return flag;
    }

    @Override
    public boolean todayIsMarket() {
        return isMarket(DateUtil.getCuryyyyMMddInteger());
    }

    @Override
    public String getPreMarketDay(Integer bizdate) {
        String preMarketDay = LocalCache.getPreMarketDay(bizdate);
        if(!StringUtils.isNoneBlank(preMarketDay)){
            Map<String,Object> params = new HashMap<>();
            if (bizdate != null) {
                params.put("bizdate", String.valueOf(bizdate));
            }
            preMarketDay = getMapper().getPreMarketDay(params);
            preMarketDay = DateUtil.strToStr(preMarketDay, "yyyy-MM-dd", "yyyyMMdd");
            LocalCache.addPreMarketDay(bizdate,preMarketDay);
        }
        return preMarketDay;
    }

    @Override
    public int getNextMarketDay(Integer bizdate) {
        String nextMarketDay = LocalCache.getNextMarketDay(bizdate);
        if(!StringUtils.isNoneBlank(nextMarketDay)){
            Map<String,Object> params = new HashMap<>();
            if (bizdate != null) {
                params.put("bizdate", String.valueOf(bizdate));
            }
            nextMarketDay = getMapper().getNextMarketDay(params);
            nextMarketDay = DateUtil.strToStr(nextMarketDay, "yyyy-MM-dd", "yyyyMMdd");
            LocalCache.addNextMarketDay(bizdate,nextMarketDay);
        }
        return Integer.parseInt(nextMarketDay);
    }

    @Override
    public List<TradeDate> getTradeDateList(Map<String, Object> params) {
        return getMapper().getTradeDateList(params);
    }


    @Override
    public List<TradeDate> getAllTradeDateList(Map<String, Object> params) {
        return getMapper().getAllTradeDateList(params);
    }

    @Override
    public List<TradeDate> getLastTradeDateOfMonth(String startDate, String endDate) {
        Map<String,Object> params = new HashMap<>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return getMapper().getLastTradeDateOfMonth(params);
    }

    @Override
    public Integer getLatestTrdDateOfSpecifyDate(Integer specifyDate) {
        if (isMarket(specifyDate)) {
            // 当日未清算完成，入参为最新交易日时需要计算实时指标
            return specifyDate;
        } else {
            return Integer.valueOf(getPreMarketDay(specifyDate));
        }
    }
}
