package com.eastmoney.common.entity;

/**
 * Created on 2016/3/1
 * 沪港通 委托流水
 *
 * <AUTHOR>
 */
public class HgtOrderRec extends PushEntity {
    private Long orderSno;
    private Long orderGroup;
    private String orderId; /*订单合同号*/
    private Integer orderDate;
    private Integer operDate;/*操作日期*/
    private Integer operTime;
    private Long custId;
    private String custName;
    private String orgId;
    private String brhId;
    private Long fundId; /*资金帐号*/
    private String moneyType;
    private String fundKind;
    private String fundLevel;
    private String fundGroup;
    private String secuId;
    private String rptSecuId;
    private String market;
    private String seat;
    private String stkCode;/*证券代码*/
    private String stkName;
    private String stkType;
    private Double orderPrice; /*委托价格*/
    private Long orderQty; /*委托数量*/
    private Long matchQty;
    private Long cancelQty;
    private Long reportQty;
    private Double matchAmt;
    private Double orderFrzAmt_rmb;   /*委托冻结金额*/
    private Double clearAmt_rmb;
    private Double tradeFee_rmb;   /*交易费用*/
    private String nightFlag;
    private String bsFlag;       /*交易类别*/
    private String timeInForce;
    private String cancelFlag;
    private Long reportTime;
    private String orderStatus;/*委托状态*/
    private Long recNum;
    private String jysOrderId;
    private String cancelOrderId; /*撤单合同号*/
    private String channel;
    private String mkt_branchId;
    private Long agentId;
    private Long operId;
    private String netAddr;
    private String operWay;
    private String reportKind;
    private String remark;
    private Double referRate;
    private Double aFundAmt;
    private Double hFundAmt;
    private String remark1;

    public Integer getOperDate() {
        return operDate;
    }

    public void setOperDate(Integer operDate) {
        this.operDate = operDate;
    }

    public Long getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Long orderSno) {
        this.orderSno = orderSno;
    }

    public Long getOrderGroup() {
        return orderGroup;
    }

    public void setOrderGroup(Long orderGroup) {
        this.orderGroup = orderGroup;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId.trim();
    }

    public Integer getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Integer orderDate) {
        this.orderDate = orderDate;
    }

    public Integer getOperTime() {
        return operTime;
    }

    public void setOperTime(Integer operTime) {
        this.operTime = operTime;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName.trim();
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId.trim();
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType.trim();
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind.trim();
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel.trim();
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup.trim();
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId.trim();
    }

    public String getRptSecuId() {
        return rptSecuId;
    }

    public void setRptSecuId(String rptSecuId) {
        this.rptSecuId = rptSecuId.trim();
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market.trim();
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat.trim();
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode.trim();
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName.trim();
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType.trim();
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Long getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Long orderQty) {
        this.orderQty = orderQty;
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Long getCancelQty() {
        return cancelQty;
    }

    public void setCancelQty(Long cancelQty) {
        this.cancelQty = cancelQty;
    }

    public Long getReportQty() {
        return reportQty;
    }

    public void setReportQty(Long reportQty) {
        this.reportQty = reportQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public Double getOrderFrzAmt_rmb() {
        return orderFrzAmt_rmb;
    }

    public void setOrderFrzAmt_rmb(Double orderFrzAmt_rmb) {
        this.orderFrzAmt_rmb = orderFrzAmt_rmb;
    }

    public Double getClearAmt_rmb() {
        return clearAmt_rmb;
    }

    public void setClearAmt_rmb(Double clearAmt_rmb) {
        this.clearAmt_rmb = clearAmt_rmb;
    }

    public Double getTradeFee_rmb() {
        return tradeFee_rmb;
    }

    public void setTradeFee_rmb(Double tradeFee_rmb) {
        this.tradeFee_rmb = tradeFee_rmb;
    }

    public String getNightFlag() {
        return nightFlag;
    }

    public void setNightFlag(String nightFlag) {
        this.nightFlag = nightFlag.trim();
    }

    public String getBsFlag() {
        return bsFlag;
    }

    public void setBsFlag(String bsFlag) {
        this.bsFlag = bsFlag.trim();
    }

    public String getTimeInForce() {
        return timeInForce;
    }

    public void setTimeInForce(String timeInForce) {
        this.timeInForce = timeInForce.trim();
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag.trim();
    }

    public Long getReportTime() {
        return reportTime;
    }

    public void setReportTime(Long reportTime) {
        this.reportTime = reportTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus.trim();
    }

    public Long getRecNum() {
        return recNum;
    }

    public void setRecNum(Long recNum) {
        this.recNum = recNum;
    }

    public String getJysOrderId() {
        return jysOrderId;
    }

    public void setJysOrderId(String jysOrderId) {
        this.jysOrderId = jysOrderId.trim();
    }

    public String getCancelOrderId() {
        return cancelOrderId;
    }

    public void setCancelOrderId(String cancelOrderId) {
        this.cancelOrderId = cancelOrderId.trim();
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel.trim();
    }

    public String getMkt_branchId() {
        return mkt_branchId;
    }

    public void setMkt_branchId(String mkt_branchId) {
        this.mkt_branchId = mkt_branchId.trim();
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getNetAddr() {
        return netAddr;
    }

    public void setNetAddr(String netAddr) {
        this.netAddr = netAddr.trim();
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay.trim();
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = reportKind.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark.trim();
    }

    public Double getReferRate() {
        return referRate;
    }

    public void setReferRate(Double referRate) {
        this.referRate = referRate;
    }

    public Double getaFundAmt() {
        return aFundAmt;
    }

    public void setaFundAmt(Double aFundAmt) {
        this.aFundAmt = aFundAmt;
    }

    public Double gethFundAmt() {
        return hFundAmt;
    }

    public void sethFundAmt(Double hFundAmt) {
        this.hFundAmt = hFundAmt;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1.trim();
    }

}
