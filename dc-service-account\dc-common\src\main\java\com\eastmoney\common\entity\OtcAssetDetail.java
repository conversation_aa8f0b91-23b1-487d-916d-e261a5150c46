package com.eastmoney.common.entity;

/**
 * <AUTHOR>
 * @date 2018-08-02 17:36
 * OTC 资产类
 */
public class OtcAssetDetail {
    private Long custCode;//客户代码
    private String orgid;//分支机构
    private Long cuacctCode;//资金账号
    private String custName;//客户姓名
    private String taCode;//登记机构
    private String taName;//登记名称
    private String taacc;//基金账号
    private String transacc;//交易账号
    private String instType;//产品大类
    private String instCls;//产品子类
    private String instCode;//产品代码
    private String instName;//产品名称
    private String currency;//币种
    private Double mktVal;//市值
    private String remark;//备注
    private Double emptyDdd;//虚增资产
    private Double emptySub;//虚减资产
    private Integer taSno;//基司内码
    private Double mktXjbBal;//现金宝实时余额
    private Double mktValBal;//现金宝实时市值
    private Double otcAsset;    //OTC 资产 sum(mkt_val) - sum(empty_add) + sum(empty_sub)

    public Long getCustCode() {
        return custCode;
    }

    public void setCustCode(Long custCode) {
        this.custCode = custCode;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public Long getCuacctCode() {
        return cuacctCode;
    }

    public void setCuacctCode(Long cuacctCode) {
        this.cuacctCode = cuacctCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

    public String getTaacc() {
        return taacc;
    }

    public void setTaacc(String taacc) {
        this.taacc = taacc;
    }

    public String getTransacc() {
        return transacc;
    }

    public void setTransacc(String transacc) {
        this.transacc = transacc;
    }

    public String getInstType() {
        return instType;
    }

    public void setInstType(String instType) {
        this.instType = instType;
    }

    public String getInstCls() {
        return instCls;
    }

    public void setInstCls(String instCls) {
        this.instCls = instCls;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getInstName() {
        return instName;
    }

    public void setInstName(String instName) {
        this.instName = instName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getMktVal() {
        return mktVal;
    }

    public void setMktVal(Double mktVal) {
        this.mktVal = mktVal;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getEmptyDdd() {
        return emptyDdd;
    }

    public void setEmptyDdd(Double emptyDdd) {
        this.emptyDdd = emptyDdd;
    }

    public Double getEmptySub() {
        return emptySub;
    }

    public void setEmptySub(Double emptySub) {
        this.emptySub = emptySub;
    }

    public Integer getTaSno() {
        return taSno;
    }

    public void setTaSno(Integer taSno) {
        this.taSno = taSno;
    }

    public Double getMktXjbBal() {
        return mktXjbBal;
    }

    public void setMktXjbBal(Double mktXjbBal) {
        this.mktXjbBal = mktXjbBal;
    }

    public Double getMktValBal() {
        return mktValBal;
    }

    public void setMktValBal(Double mktValBal) {
        this.mktValBal = mktValBal;
    }

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }
}
