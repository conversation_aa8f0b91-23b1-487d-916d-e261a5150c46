package com.eastmoney.common.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/3/8.
 */
public class Output {
    private String funCode;
    private int errCode = 0;
    private String msg = "";
    private Map<String,Object> extraInfo;
    private Object data = new ArrayList();

    public Output() {

    }
    public Output(String funCode) {
        this.funCode = funCode;
    }

    public Map<String, Object> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, Object> extraInfo) {
        this.extraInfo = extraInfo;
    }

    public int getErrCode() {
        return errCode;
    }

    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }

    public String getFunCode() {
        return funCode;
    }

    public void setFunCode(String funCode) {
        this.funCode = funCode;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getSize() {
        int size = 0;
        if (data != null && (data instanceof List)) {
            size = ((List) data).size();
        }
        return size;
    }
}
