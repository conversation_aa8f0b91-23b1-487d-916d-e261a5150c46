package com.eastmoney.service.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.eastmoney.accessor.util.AccessorConstants;
import com.eastmoney.common.model.Output;
import com.eastmoney.common.serializer.AbstractSerializeFilter;
import com.eastmoney.common.serializer.NumberFormatFilter;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.LogUtil;
import com.eastmoney.service.Dispatcher;
import com.eastmoney.service.exception.ChannelException;
import com.eastmoney.service.exception.UnSatisfiedConditionException;
import com.eastmoney.service.model.ChannelData;
import com.eastmoney.service.util.*;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import com.eastmoney.transport.util.DesUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.sql.SQLSyntaxErrorException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by sunyuncai on 2016/3/26.
 */
@Service
public class MessageHandler {
    private static Logger LOG = LoggerFactory.getLogger(MessageHandler.class);
    public final ConcurrentHashMap<Channel, String> channelDesKeyMap = new ConcurrentHashMap<>();
    @Autowired
    private Dispatcher dispatcher;
    /**
     * 判断sql注入需要过滤的非业务字段
     */
    private static final Set<String> SQL_FILTER_IGNORE = new HashSet<>();

    static {
        SQL_FILTER_IGNORE.add("czxt");
        SQL_FILTER_IGNORE.add("version");
        SQL_FILTER_IGNORE.add("sblx");
        SQL_FILTER_IGNORE.add("type");
        SQL_FILTER_IGNORE.add("sjhm");
        SQL_FILTER_IGNORE.add("yybdm");
        SQL_FILTER_IGNORE.add("svrid");
        SQL_FILTER_IGNORE.add("emtoken");
    }

    /**
     * 拉取返回
     *
     * @param channelData
     * @return
     * @throws Exception
     */
    public Message handle(ChannelData channelData, long waitTime) throws Throwable {
        long startTime = System.currentTimeMillis();
        int errCode = AccessorConstants.ERRCODE_FAIL;
        String errorType = "";
        String errorMsg = "";
        Message message = channelData.getMessage();
        Map<String, Object> params = getParamFromMessage(message);
        params.put("requestId", new String(message.getRequestId()));
        Integer count = CommonUtil.convert(params, "count", Integer.class);
        if (count == null || count <= 0) {
            count = Integer.parseInt(PropertiesUtil.readValue(ServiceConstants.PAGE_SIZE, 500));
            params.put("count", count);
        }
        LOG.debug("input:" + JSON.toJSONString(params));
        channelData.setParams(params);
        String funCode = (String) params.get("funCode");
        Output output = new Output(funCode);
        //封装返回信息
        Message replyMsg = new Message();
        try {
            checkCondition(channelData);
            Object data = dispatcher.service(params);
            if (data != null) {
                output.setData(data);
            }
            errCode = AccessorConstants.ERRCODE_SUCCESS;
        } catch (Throwable t) {
            if (t instanceof InvocationTargetException) {
                t = ((InvocationTargetException) t).getTargetException();
            }
            if (t instanceof MyBatisSystemException || t instanceof SQLSyntaxErrorException || t instanceof DataAccessException) {
                errorType = "数据库查询异常";
            }
            errorMsg = t.getMessage();
            LOG.error("", t);
            ExceptionUtil.handle(t, channelData);
        } finally {
            replyMsg.setRequestId(message.getRequestId());
            output.setErrCode(errCode);
            if (!StringUtils.isEmpty(errorType)) {
                output.setMsg(errorType);
            } else {
                output.setMsg(errorMsg);
            }
            if (errCode == AccessorConstants.ERRCODE_FAIL) {
                output.setMsg("数据请求超时，请稍后再试。");
            }
            String content;
            AbstractSerializeFilter serializeFilter = dispatcher.getSerializeFilter(funCode);
            if (serializeFilter != null) {
                SerializeFilter[] serializeFilters = new SerializeFilter[2];
                serializeFilters[0] = serializeFilter;
                serializeFilters[1] = NumberFormatFilter.getInstance();
                content = JSON.toJSONString(output, serializeFilters);
            } else {
                content = JSON.toJSONString(output);
            }
            //是否加密由入参决定
            String desKey = channelDesKeyMap.get(channelData.getCtx().channel());
            if (message.getReplyCipher() == 1) {
                replyMsg.setContent(DesUtil.encrypt(content, desKey).getBytes());
                replyMsg.setCipher((byte) 1);
            } else {
                replyMsg.setContent(content.getBytes());
            }
            replyMsg.setReplyCipher(message.getReplyCipher());
            replyMsg.setType(getReplyType(message.getType()));
            //日志系统
            try {
                LogUtil.info(ServiceUtil.composeAccessLogBean(channelData, output, errorType, errorMsg, waitTime,
                        System.currentTimeMillis() - startTime));
            } catch (Throwable t) {
                LOG.error("", t);
            }
        }
        return replyMsg;

    }


    /**
     * 条件检查
     *
     * @param channelData
     */
    public void checkCondition(ChannelData channelData) {
        Message message = channelData.getMessage();
        /** 1、检查密钥是否生成**/
        if (message.getReplyCipher() == 1) {
            String desKey = channelDesKeyMap.get(channelData.getCtx().channel());
            if (StringUtils.isEmpty(desKey)) {
                throw new UnSatisfiedConditionException("不存在密钥，请发送生成密钥请求");
            }
        }
        /** 2 防止SQL注入 */
        Map<String, Object> params = channelData.getParams();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            if (StringUtils.isEmpty(key)) {
                continue;
            }
            if (SQL_FILTER_IGNORE.contains(key.toLowerCase())) {
                continue;
            }
            if (entry.getValue() instanceof String) {
                String value = (String) entry.getValue();
                if (SQLPreventAttackUtil.hasAttackStr(value)) {
                    throw new UnSatisfiedConditionException("存在非法字符，key=" + entry.getKey() + " value=" + value);
                }
            }
        }
        /** 3 推送服务是否传beginId **/
        if (message.getType() == Constants.SOCKET_TYPE_PUSH_REQ) {
            if (channelData.getParams().get("beginId") == null) {
                throw new RuntimeException("请传入beginId参数");
            }
        }
    }

    /**
     * 秘钥返回
     *
     * @param ctx
     * @param message
     */
    public void replyDesKey(ChannelHandlerContext ctx, Message message) throws Throwable {
        Message replyMessage = new Message();
        String desKey = DesUtil.genDesKey();
        channelDesKeyMap.put(ctx.channel(), desKey);
        replyMessage.setType(Constants.SOCKET_TYPE_KEY_RES);
        if (message != null) {
            replyMessage.setRequestId(message.getRequestId());
        } else {
            replyMessage.setRequestId(CommonUtil.generateUUID().getBytes());
        }
        replyMessage.setContent(desKey.getBytes());
        writeMessage(ctx.channel(), replyMessage);
    }

    /**
     * 心跳返回
     *
     * @param ctx
     * @param message
     */
    public void replyHeartbeat(ChannelHandlerContext ctx, Message message) throws Throwable {
        Message replyMessage = new Message();
        replyMessage.setType(Constants.SOCKET_TYPE_HTBT_RES);
        replyMessage.setRequestId(message.getRequestId());
        writeMessage(ctx.channel(), replyMessage);
    }


    /**
     *
     * @param t
     * @param channelData
     * @throws Throwable
     */
    public void replyFailMsg(Throwable t, ChannelData channelData) throws Throwable {
        Message replyMessage = new Message();
        replyMessage.setRequestId(channelData.getMessage().getRequestId());
        replyMessage.setType(getReplyType(channelData.getMessage().getType()));
        replyMessage.setCipher((byte) 0);
        replyMessage.setCompress((byte) 0);
        replyMessage.setReplyCipher(channelData.getMessage().getReplyCipher());
        Output output = new Output();
        String errMsg = t.getMessage();
        //队列满的情况
        if (t instanceof RejectedExecutionException) {
            errMsg += "系统繁忙，请稍后再试";
        }
        String funCode = "exception";
        if(!StringUtils.isEmpty(channelData.getParams().get("funCode"))) {
            funCode = channelData.getParams().get("funCode").toString();
        }
        output.setFunCode(funCode);
        output.setErrCode(AccessorConstants.ERRCODE_FAIL);
        //空指针异常无信息
        output.setMsg(errMsg);
        replyMessage.setContent(JSON.toJSONString(output).getBytes());
        writeMessage(channelData.getCtx().channel(), replyMessage, true);
    }

    /**
     * 获取返回的type
     *
     * @param type
     * @return
     */
    public byte getReplyType(byte type) {
        byte replyType = 0;
        if (Constants.SOCKET_TYPE_PULL_REQ == type) {
            replyType = Constants.SOCKET_TYPE_PULL_RES;
        } else if (Constants.SOCKET_TYPE_PUSH_REQ == type) {
            replyType = Constants.SOCKET_TYPE_PUSH_RES;
        } else if (Constants.SOCKET_TYPE_KEY_REQ == type) {
            replyType = Constants.SOCKET_TYPE_KEY_RES;
        } else if (Constants.SOCKET_TYPE_HTBT_REQ == type) {
            replyType = Constants.SOCKET_TYPE_HTBT_RES;
        }
        return replyType;
    }

    public Map<String, Object> getParamFromMessage(Message message) {
        Map<String, Object> params0 = JSON.parseObject(new String(message.getContent()), Map.class);
        Map<String, Object> params = new HashMap<>();
        for (Map.Entry<String, Object> entry : params0.entrySet()) {
            String key = entry.getKey();
            if (!StringUtils.isEmpty(key) && Character.isLowerCase(entry.getKey().charAt(0))) {
                params.put(key, entry.getValue());
            }
        }
        return params;
    }

    public void writeMessage(Channel channel, Message message) throws Throwable {
        writeMessage(channel, message, false);
    }

    public void writeMessage(Channel channel, Message message, boolean failed) throws Throwable {
        if (channel == null) {
            throw new ChannelException(ChannelException.ERRCODE_CHANNEL_NULL, "channel is null");
        } else if (!channel.isActive()) {
            throw new ChannelException(ChannelException.ERRCODE_CHANNEL_NOT_ACTIVE,
                    "channel is not active channel=" + channel);
        }
        int i = 0;
        while (!channel.isWritable()) {
            LOG.error("failed={} channel is not writable channel={} rid={}  i={}",
                    failed, channel, new String(message.getRequestId()), i);
            if (!channel.isActive()) {
                throw new ChannelException(ChannelException.ERRCODE_CHANNEL_NOT_ACTIVE,
                        "channel is not active channel=" + channel);
            }
            try {
                i++;
                if (!failed && i > 5000) {
                    LOG.warn("channel is not writable channel={} rid={} i={}",
                            channel, new String(message.getRequestId()), i);
                    throw new ChannelException(ChannelException.ERRCODE_CHANNEL_NOT_WRITABLE,
                            "channel is not writable channel=" + channel);
                }
                TimeUnit.MICROSECONDS.sleep(10);
            } catch (InterruptedException e) {
                LOG.error("", e);
            }
        }
        channel.writeAndFlush(message);
    }


    public void handleException(Throwable t, ChannelData channelData) {
        ExceptionUtil.handle(t, channelData);
        if (t instanceof ChannelException) {
            int errCode = ((ChannelException) t).getErrCode();
            if (errCode == ChannelException.ERRCODE_CHANNEL_NOT_ACTIVE) {
                channelData.getCtx().channel().close();
                return;
            } else if (errCode == ChannelException.ERRCODE_CHANNEL_NOT_WRITABLE) {
                byte type = channelData.getMessage().getType();
                //如果是请求秘钥或是心跳包
                if (type == 1 || type == 3) {
                    return;
                }
            }
        }
        try {
            replyFailMsg(t, channelData);
        } catch (Throwable t1) {
            ExceptionUtil.handle(t1, channelData);
            if (t instanceof ChannelException) {
                int errCode = ((ChannelException) t).getErrCode();
                if (errCode == ChannelException.ERRCODE_CHANNEL_NOT_ACTIVE) {
                    channelData.getCtx().channel().close();
                }
            }
        }
    }

}
