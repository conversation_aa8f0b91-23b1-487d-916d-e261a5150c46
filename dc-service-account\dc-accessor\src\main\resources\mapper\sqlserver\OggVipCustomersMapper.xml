<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggVipCustomersMapper">

    <select id="selectInterceptFundIds" resultType="long">
        select
        t.fundid
        from
        (
        select
        a.*,
        b.singleflag
        from
        run.dbo.vip_customers a with (nolock)
        inner join run.dbo.custbaseinfo b with (nolock)
        on
        a.custid = b.custid) t
         <where>
             t.singleflag != '0'
             <if test="fundId != null">
                 and t.fundid = #{fundId}
             </if>
         </where>
    </select>

</mapper>