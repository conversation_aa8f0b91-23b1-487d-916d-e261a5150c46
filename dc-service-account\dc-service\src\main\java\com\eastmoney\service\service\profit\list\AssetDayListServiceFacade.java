package com.eastmoney.service.service.profit.list;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.cal.AssetHis;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.TradeDateCacheService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.asset.AssetHisService;
import com.eastmoney.service.service.asset.base.AssetService;

import java.util.*;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/2/21
 */
@Service("assetDayListServiceFacade")
public class AssetDayListServiceFacade {

    @Resource(name = "assetServiceRealTime")
    protected AssetService assetServiceRealTime;
    @Autowired
    private AssetHisService assetHisService;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Resource
    private CommonService commonService;
    @Autowired
    private TradeDateCacheService tradeDateCacheService;

    /**
     * 总资产走势
     * @param params
     * @return
     */
    public List<AssetDay> getAssetDayList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        Integer assetStartDate = CommonUtil.convert(params.get("assetStartDate"), Integer.class);
        Boolean calMonthAsset = CommonUtil.convert(params.getOrDefault("calMonthAsset", false), Boolean.class);

        List<AssetDay> assetDayList;
        if (BooleanUtils.isTrue(calMonthAsset)) {
            // 先查询每个月最后一天有资产的日期
            Integer settleDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
            List<Integer> tradeDateList = tradeDateCacheService.getLastTradeDateOfMonth(startDate, settleDate);

            // 再查询每个月最后一天的资产
            assetDayList = assetHisService.getMonthAssetDayTrend(fundId, tradeDateList);
        } else {
            Integer lastTradeDate = Integer.valueOf(tradeDateDao.getPreMarketDay(startDate));
            assetDayList = assetHisService.getAssetDayTrend(fundId, lastTradeDate, endDate);
        }

        /*-------------begin-------------计算实时资产-----------------------------*/
        Boolean calRealTimeAsset = CommonUtil.convert(params.get("calRealTimeAsset"), Boolean.class);
        if (BooleanUtils.isTrue(calRealTimeAsset)) {
            int accountBizDate;
            if (BooleanUtils.isTrue(calMonthAsset)) {
                accountBizDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
            } else {
                if (!CollectionUtils.isEmpty(assetDayList)) {
                    accountBizDate = assetDayList.get(assetDayList.size() - 1).getBizDate();
                } else {
                    //跨周期时，取不到收益率，获取前一个交易日的日期作为清算日期
                    accountBizDate = Integer.parseInt(tradeDateDao.getPreMarketDay(startDate));
                }
            }
            params.put("accountBizDate", accountBizDate);
            int calRealTimeProfitBizDate = commonService.calRealTimeProfitBizDate(accountBizDate);
            if (calRealTimeProfitBizDate != 0) {
                AssetHis assetRealTime = assetServiceRealTime.getAsset(params);
                AssetDay assetDayRealTime = new AssetDay();
                Integer bizDate = BooleanUtils.isTrue(calMonthAsset) ? calRealTimeProfitBizDate / 100 : calRealTimeProfitBizDate;
                assetDayRealTime.setBizDate(bizDate);
                assetDayRealTime.setMktVal(ArithUtil.round(assetRealTime.getMktval()));

                assetDayRealTime.setAsset(
                        ArithUtil.round(
                                ArithUtil.add(assetRealTime.getAsset(), assetRealTime.getOtcAsset())
                        )
                );

                if (BooleanUtils.isTrue(calMonthAsset)) {
                    assetDayList = assetDayList.stream()
                            .filter(assetDay -> !Objects.equals(assetDay.getBizDate(), bizDate)
                    ).collect(Collectors.toList());
                }

                assetDayList.add(assetDayRealTime);
            }
        }
        /*-------------end-------------计算实时资产-----------------------------*/
        // 对于本周、本月、本年第一天9:25之前的情况（结果集只有一条，且bizDate比区间首日还早），返回空，前端展示：暂无数据
        if (CollectionUtils.isEmpty(assetDayList) ||
                (!BooleanUtils.isTrue(calMonthAsset) && assetDayList.size() == 1 && assetDayList.get(0).getBizDate() < startDate)) {
            return Collections.emptyList();
        }

        // 如果前端需要区间上一个交易日的记录（beginIndex = -1） 并且 上一交易日没有数据需要补0
        Integer beginIndex = CommonUtil.convert(params.get("beginIndex"), Integer.class);
        if (beginIndex != null && beginIndex == -1 && Objects.equals(startDate, assetStartDate)) {
            AssetDay firstAssetDay = new AssetDay();
            String lastTradeDate = BooleanUtils.isTrue(calMonthAsset) ?
                    DateUtil.addMonth(String.valueOf(startDate), DateUtil.yyyyMMdd, -1).substring(0,6)
                    : tradeDateDao.getPreMarketDay(startDate);
            firstAssetDay.setBizDate(Integer.valueOf(lastTradeDate));
            assetDayList.add(0, firstAssetDay);
        }

        return assetDayList;
    }
}
