<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.oracle.ProfitRateContrastMapper">
    <resultMap id="BaseResultMap" type="as_profitRateContrast" >
        <result column="UNIT" property="unit"/>
        <result column="ALL_NUM" property="allNum"/>
        <result column="HIGH_AVG" property="highAvg"/>
        <result column="ALL_AVG" property="allAvg"/>
        <result column="BIZDATE" property="bizDate"/>
        <result column="GAIN_NUM" property="gainNum"/>
        <result column="MIDDLE_RATE" property="middleRate"/>
        <result column="LOSS_NUM" property="lossNum"/>
    </resultMap>

    <sql id="All_Column">
        UNIT, ALL_NUM, HIGH_AVG, ALL_AVG,BIZDATE,GAIN_NUM,MIDDLE_RATE,LOSS_NUM
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="All_Column"/> FROM ATCENTER.PROFIT_RATE_CONTRAST
        <where>
            <if test="bizDate != null">
                AND BIZDATE = #{bizDate}
            </if>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
            <if test="fundIds != null">
                AND fundid IN
                <foreach item="item" index="index" collection="fundIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>