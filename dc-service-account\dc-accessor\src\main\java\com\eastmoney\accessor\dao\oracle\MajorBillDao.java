package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.MajorBill;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/31.
 */
public interface MajorBillDao extends IBaseDao<MajorBill,Long> {
    List<MajorBill> getBestMonth(Map<String, Object> params);

    /**
     * 查询区间内月收益率
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    Map<Integer,List<ProfitRateDay>> selectMonthProfitRate(Long fundId, Integer startDate, Integer endDate);

    /**
     * 查询区间内月收益
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProfitDay> selectMonthProfit(Long fundId, Integer startDate, Integer endDate);

    /**
     * 查询区间内年收益额
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProfitDay> selectYearProfit(Long fundId, Integer startDate, Integer endDate);

    /**
     * 查询区间内年收益率
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProfitRateDay> selectYearProfitRate(Long fundId, Integer startDate, Integer endDate);

    List<MajorBillStrInfo> selectByYearBill(Map<String, Object> params);

}
