package com.eastmoney.common.entity;

/**
 * Created by sunyuncai on 2016/10/19.
 */
public class StkInfo {
    public Long fundId;//资金账号
    public String moneyType;//货币
    public String market;//市场
    public String stkCode; //证券代码
    public String stkType;//证券类型
    public Double buyCost;//当前成本
    public Long stkQty;//证券数量
    public Double bondIntr; //债券应计利息
    public Double closePrice;
    public Double openPrice;
    public Double lastPrice;//最新价格
    public String lofMoneyFlag;
    public String mtkCalFlag;//市值计算标识
    public String stkLevel;
    public Double settRate;  //购买的费率
    public Double saleSettRate; //卖出费率
    public Double ticketPrice;  //私募
    public Long stkBal;//股份余额
    public Long stkAvl;//可用数量
    public Long stkUnComeBuy;//在途买入
    public Long stkUnComeSale;//在途卖出
    public Long stkBuySale;//股份实时买卖差额
    public String stkName;//证券名称
    public Integer quitDate;//退市日期
    private StkPrice stkPrice;
    private String expandNameAbbr; // 扩位简称
    /**
     * 交易类型
     * 0 正常交易
     */
    private String trdId;

    public StkInfo() {
    }

    public StkInfo(String stkCode, String market) {
        this.stkCode = stkCode;
        this.market = market;
    }

    public StkInfo(String stkCode, String market, double settRate) {
        this.stkCode = stkCode;
        this.market = market;
        this.settRate = settRate;
    }

    public StkInfo(String market, String stkCode, String stkType, Double buyCost) {
        this.market = market;
        this.stkCode = stkCode;
        this.stkType = stkType;
        this.buyCost = buyCost;
    }

    public StkInfo(String market, String stkCode, String stkType, Double bondIntr, Double buyCost) {
        this.market = market;
        this.stkCode = stkCode;
        this.stkType = stkType;
        this.bondIntr = bondIntr;
        this.buyCost = buyCost;
    }

    public Double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(Double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public StkPrice getStkPrice() {
        return stkPrice;
    }

    public void setStkPrice(StkPrice stkPrice) {
        this.stkPrice = stkPrice;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Double getBuyCost() {
        return buyCost;
    }

    public void setBuyCost(Double buyCost) {
        this.buyCost = buyCost;
    }

    public Long getStkQty() {
        return stkQty;
    }

    public void setStkQty(Long stkQty) {
        this.stkQty = stkQty;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public Double getClosePrice() {
        return closePrice;
    }

    public void setClosePrice(Double closePrice) {
        this.closePrice = closePrice;
    }

    public Double getOpenPrice() {
        return openPrice;
    }

    public void setOpenPrice(Double openPrice) {
        this.openPrice = openPrice;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public String getLofMoneyFlag() {
        return lofMoneyFlag;
    }

    public void setLofMoneyFlag(String lofMoneyFlag) {
        this.lofMoneyFlag = lofMoneyFlag;
    }

    public String getMtkCalFlag() {
        return mtkCalFlag;
    }

    public void setMtkCalFlag(String mtkCalFlag) {
        this.mtkCalFlag = mtkCalFlag;
    }

    public String getStkLevel() {
        return stkLevel;
    }

    public void setStkLevel(String stkLevel) {
        this.stkLevel = stkLevel;
    }

    public Double getSettRate() {
        return settRate;
    }

    public void setSettRate(Double settRate) {
        this.settRate = settRate;
    }

    public Double getSaleSettRate() {
        return saleSettRate;
    }

    public void setSaleSettRate(Double saleSettRate) {
        this.saleSettRate = saleSettRate;
    }

    public String getExpandNameAbbr() {
        return expandNameAbbr;
    }

    public void setExpandNameAbbr(String expandNameAbbr) {
        this.expandNameAbbr = expandNameAbbr;
    }

    public Long getStkBal() {
        return stkBal;
    }

    public void setStkBal(Long stkBal) {
        this.stkBal = stkBal;
    }

    public Long getStkAvl() {
        return stkAvl;
    }

    public void setStkAvl(Long stkAvl) {
        this.stkAvl = stkAvl;
    }

    public Long getStkUnComeBuy() {
        return stkUnComeBuy;
    }

    public void setStkUnComeBuy(Long stkUnComeBuy) {
        this.stkUnComeBuy = stkUnComeBuy;
    }

    public Long getStkUnComeSale() {
        return stkUnComeSale;
    }

    public void setStkUnComeSale(Long stkUnComeSale) {
        this.stkUnComeSale = stkUnComeSale;
    }

    public Long getStkBuySale() {
        return stkBuySale;
    }

    public void setStkBuySale(Long stkBuySale) {
        this.stkBuySale = stkBuySale;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Integer getQuitDate() {
        return quitDate;
    }

    public void setQuitDate(Integer quitDate) {
        this.quitDate = quitDate;
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = trdId;
    }
}
