package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiIpoSettleStatMapper;
import com.eastmoney.common.entity.cal.IPOProfitInfoBO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class TiIpoSettleStatDaoImpl extends BaseDao<TiIpoSettleStatMapper, IPOProfitInfoBO, Long> implements IpoSettleStatDao {
    @Override
    public IPOProfitInfoBO getIpoSettleStatus(Integer serverId, String businessCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("serverId", serverId);
        param.put("businessCode", businessCode);
        return getMapper().selectSettleStatus(param);
    }
}
