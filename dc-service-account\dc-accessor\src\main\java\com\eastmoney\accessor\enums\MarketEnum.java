package com.eastmoney.accessor.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created on 2020/7/20-10:17.
 *
 * <AUTHOR>
 */
public enum MarketEnum {
    /**
     * 深A
     */
    SZ_A("0","深A","szA"),
    /**
     * 沪A
     */
    SH_A("1","沪A","shA"),
    /**
     * 深B
     */
    SZ_B("2","深B","szB"),
    /**
     * 沪B
     */
    SH_B("3","沪B","shB"),
    /**
     * 沪港通
     */
    SH_HK("5","沪港通","shhk"),
    /**
     * 股转A
     */
    STOCK_A("6","股转A","stockA"),
    /**
     * 股转B
     */
    STOCK_B("7","股转B","shockB"),
    /**
     * 深港通
     */
    SZ_HK("S","深港通","szhk"),
    /**
     * 北交所
     */
    BJ_A("B","京A","BJA");

    private static final Map<String, MarketEnum> MARKET_MAP;
    static  {
        MARKET_MAP = Arrays.stream(MarketEnum.values()).collect(Collectors.toMap(MarketEnum::getValue, Function.identity(),(o1, o2)->o2));
    }

    private String value;//枚举值
    private String name;//枚举中文名称
    private String code;//枚举英文名称
    MarketEnum(String value, String name, String code){
        this.value = value;
        this.name = name;
        this.code = code;
    }
    public String getValue() {
        return value;
    }
    public String getName() {
        return name;
    }
    public String getCode() {
        return code;
    }

    /**
     * 获取market对应的名称
     * @return
     */
    public static String getMarketName(String market){
        return MARKET_MAP.get(market).getName();
    }
}
