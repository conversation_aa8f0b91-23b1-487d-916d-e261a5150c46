package com.eastmoney.service.service.profit.base;

import com.eastmoney.accessor.dao.oracle.FundAssetDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.enums.MoneyTypeEnum;
import com.eastmoney.accessor.enums.STKTEnum;
import com.eastmoney.accessor.service.CoreConfigService;
import com.eastmoney.accessor.service.FundStockInnovateService;
import com.eastmoney.accessor.service.OggLogAssetService;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.cal.AssetDetailDO;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.*;
import com.eastmoney.service.service.AssetDetailService;
import com.eastmoney.service.service.asset.AssetHisService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.quote.QuoteService;
import com.eastmoney.service.util.BusinessUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.eastmoney.common.util.ArithUtil.*;

/**
 * Created on 2020/8/12-13:58.
 *
 * <AUTHOR>
 */
@Service("profitServiceRealTime")
public class ProfitServiceRealTimeImpl implements ProfitService {
    private static final String CAL_PROFIT_RATE = "calProfitRate";
    private static final String LOFTYPE_MONEY_OLD = "1";
    private static final String LOFTYPE_MONEY_NEW = "4";
    @Autowired
    private AssetHisService assetHisService;
    @Resource(name = "fundAssetDao")
    private FundAssetDao fundAssetDao;
    @Resource
    private AssetNewService assetNewService;
    @Autowired
    private OggLogAssetService logAssetService;

    @Autowired
    private QuoteService quoteService;
    @Autowired
    private HgtReferRateService hgtReferRateService;
    @Autowired
    private HgtTradeDateService hgtTradeDateService;
    @Autowired
    private FundStockInnovateService fundStockInnovateService;
    @Autowired
    private StkPriceCacheService stkPriceCacheService;
    @Autowired
    private AssetDetailService assetDetailService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private CoreConfigService coreConfigService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    /**
     * 计算实时收益额
     *
     * @param params fundId
     * @return
     */
    @Override
    public DayProfitBean getProfitDay(Map<String, Object> params) {
        DayProfitBean dayProfitBean = new DayProfitBean();
        Boolean filterHgtProfit = CommonUtil.convert(params.get("filterHgtProfit"), Boolean.class);
        if (filterHgtProfit == null) {
            filterHgtProfit = false;
        }
        //获取实时计算收益额
        Map dayProfitResult = getDayProfitData(params, filterHgtProfit, null);
        List<DayStkProfit> dayStkProfitList = (List<DayStkProfit>) dayProfitResult.get("dayStkProfitList");
        if (!CollectionUtils.isEmpty(dayStkProfitList)) {
            //根据每支股票的收益额获取日收益
            double profit = calDayProfit(dayStkProfitList);
            dayProfitBean.setProfit(profit);
            long fundId = CommonUtil.convert(params, "fundId", Long.class);
            dayProfitBean.setFundId(fundId);

            if (BooleanUtils.isTrue(CommonUtil.convert(params.get(CAL_PROFIT_RATE), Boolean.class))) {
                Integer accountBizDate = CommonUtil.convert(params.get("accountBizDate"), Integer.class);
                Integer bizDate = CommonUtil.convert(params.get("realBizDate"), Integer.class);
                if (bizDate != null) {
                    dayProfitBean.setBizDate(bizDate);
                }

                // 计算日收益率
                calDayProfitRate(accountBizDate, dayProfitBean, filterHgtProfit);
            }
            dayProfitBean.isRealtimeCalculated = true;
        }
        return dayProfitBean;
    }

    /**
     * AAA
     * 计算最新未清算交易日实时收益额
     * 结果集中港股通均以换算成人民币收益额
     *
     * @param params
     * @return
     */
    public Map getDayProfitData(Map params, boolean filterHgtProfit, QryFund qryFund) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        List<PositionInfo> positionInfoList;
        List<LogAsset> logAssetList;
        // 计算当日收益
        List<PositionInfo> profitPositionInfoList;
        List<LogAsset> profitLogAssetList;

        if (Objects.isNull(qryFund)) {
            // funcId=1002080: 获取持仓、交易流水
            params.put("funcId", 1002080);
            //PCMP-******** 【账户分析】北交所存量代码段改造 柜台行情在切换过程中是830  mdstp是920
            Integer serverId = coreConfigService.getServerId(fundId);
            PosDBItem item;
            if (nodeConfigService.getHgtProfitCalOptimizeFlag(serverId)) {
                //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notpnew1，这个存过不返回港股通交易记录
                item = fundStockInnovateService.getDBResultInfo(params);
            } else {
                //集中交易资产,三合一存过 up_xzperqryfund_stk_maxdraw_notp
                item = fundStockInnovateService.getDBResultInfoOld(params);
            }

            profitPositionInfoList = item.getProfitStkInfoList();
            profitLogAssetList = item.getProfitLogAssetList();
            positionInfoList = item.getStkInfoList();
            logAssetList = item.getLogAssetList();
        } else {
            profitPositionInfoList = Optional.ofNullable(qryFund.getProfitStkInfoList()).orElse(new ArrayList<>());
            profitLogAssetList = Optional.ofNullable(qryFund.getProfitLogAssetList()).orElse(new ArrayList<>());
            positionInfoList = Optional.ofNullable(qryFund.getStkInfoList()).orElse(new ArrayList<>());
            logAssetList = Optional.ofNullable(qryFund.getLogAssetList()).orElse(new ArrayList<>());
        }

        //查询个股收益
        String stkCode = CommonUtil.convert(params.get("stkCode"), String.class);
        String market = CommonUtil.convert(params.get("market"), String.class);

        //需要兼容 830代码传进来 但是 柜台此时是920的信息
        String alterStkCode = bseCodeAlterService.getCodeAlterOrReverse(stkCode, market);
        List<String> codeList = Arrays.asList(StringUtils.trim(stkCode), alterStkCode);

        if (!StringUtils.isEmpty(stkCode) && !StringUtils.isEmpty(market)) {
            positionInfoList = positionInfoList.stream()
                    .filter(o -> codeList.contains(o.getStkCode().trim())
                            && o.getMarket().trim().equals(market.trim()))
                    .collect(Collectors.toList());

            logAssetList = logAssetList.stream()
                    .filter(o -> codeList.contains(o.getStkCode().trim())
                            && o.getMarket().trim().equals(market.trim()))
                    .collect(Collectors.toList());

            profitPositionInfoList = profitPositionInfoList.stream()
                    .filter(o -> codeList.contains(o.getStkCode().trim())
                            && o.getMarket().trim().equals(market.trim()))
                    .collect(Collectors.toList());

            profitLogAssetList = profitLogAssetList.stream()
                    .filter(o -> codeList.contains(o.getStkCode().trim())
                            && o.getMarket().trim().equals(market.trim()))
                    .collect(Collectors.toList());
        }

        Iterator<LogAsset> iter1 = logAssetList.iterator();
        while (iter1.hasNext()) {
            LogAsset logAsset = iter1.next();
            logAsset.setFundId(fundId);
            if (ArithUtil.eq(logAsset.getBuyMatchAmt(), 0.0) && ArithUtil.eq(logAsset.getSaleMatchAmt(), 0.0)) {
                iter1.remove();
            }
        }

        Iterator<LogAsset> iter2 = profitLogAssetList.iterator();
        while (iter2.hasNext()) {
            LogAsset logAsset = iter2.next();
            logAsset.setFundId(fundId);
            if (ArithUtil.eq(logAsset.getBuyMatchAmt(), 0.0) && ArithUtil.eq(logAsset.getSaleMatchAmt(), 0.0)) {
                iter2.remove();
            }
        }

        // 查询stkPrice缓存
        // PCMP-******** 【账户分析】北交所存量代码段改造 positionInfoList、profitPositionInfoList这些前面都是柜台查出来的数据，这里也是去查柜台，不用转换
        positionInfoList = stkPriceCacheService.updateStkInfoList(fundId, positionInfoList);
        profitPositionInfoList = stkPriceCacheService.updateStkInfoList(fundId, profitPositionInfoList);

        addClearStkAsset(positionInfoList, logAssetList);
        addClearStkAsset(profitPositionInfoList, profitLogAssetList);

        //计算每支股票成交情况
        List<DayStkProfit> dayStkProfitList = getDayStkProfitList(fundId, profitPositionInfoList, profitLogAssetList, filterHgtProfit);
        Map result = new HashMap(2);
        result.put("dayStkProfitList", dayStkProfitList);
        result.put("positionInfoList", positionInfoList);
        return result;
    }

    /**
     * #10032936 普通账户分析-当日盈亏增加费用
     *    当日收益额 = (最新价格 + 应计利息)*买入结算汇率*持仓数量
     *    -(昨日收盘价+应计利息)*买入结算汇率 (持仓数量 + 当日卖出总量 - 当日买入总量 )
     *    -买入清算金额(match.clearamt = sum(买入数量*（最新价+应计利息）*卖出结算汇率+买入费用))
     *     +卖出清算金额(match.clearamt = sum(卖出数量*（最新价+应计利息）*买入结算汇率-卖出费用))
     */
    private List<DayStkProfit> getDayStkProfitList(Long fundId, List<PositionInfo> positionInfoList, List<LogAsset> logAssetList, Boolean filterHgtProfit) {
        List<DayStkProfit> profitList = new ArrayList<>();

        //处理可转债
        quoteService.setStkAssetQuote(positionInfoList, false);
        boolean dayProfitCalFeeFlag = nodeConfigService.getDayProfitCalFeeFlag();

        // PCMP-10616646 【柜台配合】港股通当日收益计算优化 - 增加开关
        boolean hgtProfitCalOptimizeFlag = nodeConfigService.getHgtProfitCalOptimizeFlag(coreConfigService.getServerId(fundId));

        for (PositionInfo pstInfo : positionInfoList) {
            if (StringUtils.isBlank(pstInfo.getMarket())
                    || "2".equals(pstInfo.getMarket()) || "3".equals(pstInfo.getMarket())) {
                continue;
            }
            //L LOF基金，1 老式货币基金（盘中价格不变），4 新式货币基金（价格固定0.01）
            if (STKTEnum.STKT_LOF.getValue().equals(pstInfo.stkType) &&
                    (LOFTYPE_MONEY_OLD.equals(pstInfo.lofMoneyFlag) || LOFTYPE_MONEY_NEW.equals(pstInfo.lofMoneyFlag))) {
                continue;
            }
            //排除深市的 F 非交易开放基金
            if (STKTEnum.STKT_LOF.getValue().equals(pstInfo.stkType) && "F".equals(pstInfo.stkLevel) && MarketEnum.SZ_A.getValue().equals(pstInfo.market)) {
                continue;
            }
            //退市代码不计算当日盈亏
            if ("Z".equals(pstInfo.stkLevel) && DateUtil.getCuryyyyMMddInteger() > pstInfo.quitDate) {
                continue;
            }

            //如果是港股通，且 当天是港股通非交易日， 或当天是港股通交易日但上午不开盘，且时间为上午跳过。
            //如果是港股通，且传入了filterHgtProfit=true 跳过（APPAGILE-88833 柜台分享页持仓盈亏比例）
            if (BusinessUtil.isGgtMarket(pstInfo.getMarket()) && ((!hgtTradeDateService.isMarket(DateUtil.getCuryyyyMMddInteger())) || filterHgtProfit)) {
                continue;
            }
            DayStkProfit dsp = new DayStkProfit();
            dsp.setMarket(pstInfo.getMarket());
            dsp.setMoneyType(pstInfo.getMoneyType());
            dsp.setStkCode(pstInfo.getStkCode());
            dsp.setStkType(pstInfo.getStkType());
            //交易类型
            dsp.setTrdId(pstInfo.getTrdId());
            // 三合一存过新增集合的持仓份额字段stkQty
            long qty = addIgnoreNull(pstInfo.stkBal, pstInfo.stkBuySale, pstInfo.stkUnComeBuy,
                    null == pstInfo.stkUnComeSale ? null : -pstInfo.stkUnComeSale);
            dsp.setStkQty(Objects.isNull(pstInfo.stkQty) ? qty : pstInfo.stkQty);

            StkPrice stkPrice = pstInfo.getStkPrice();
            // 当mdstp查询不到stkPrice行情 这里会跳过的，然后不会放入dayStkProfitList
            if (!stkPrice.isQtPrice) {
                continue;
            }
            pstInfo.setClosePrice(stkPrice.getClosePrice());
            pstInfo.setLastPrice(stkPrice.getLastPrice());
            if (pstInfo.getLastPrice() == null || pstInfo.getLastPrice() == 0.0 || pstInfo.getClosePrice() == 0.0) {
                continue;
            }

            dsp.dwClose = stkPrice.getClosePrice();
            dsp.dwPrice = ArithUtil.round(stkPrice.getPrice(), 3);
            //获取债券利息
            Double bondIntr = pstInfo.getBondIntr();
            if (dayProfitCalFeeFlag) {
                // #10032936 普通账户分析-当日盈亏增加费用 计算费用开关打开需要加上应计利息
                dsp.dwClose = add(dsp.dwClose, bondIntr);
                dsp.dwPrice = add(dsp.dwPrice, bondIntr);
            }
            HgtReferRate hgtReferRateInfo = hgtReferRateService.getOggHgtReferRateService(pstInfo.getFundId(), pstInfo.getMarket());
            Double sellSettRate = Objects.nonNull(hgtReferRateInfo) ? hgtReferRateInfo.getSaleSettRate() : 1d;
            // 柜台开始清算后,存过返回结算汇率
            sellSettRate = Objects.nonNull(pstInfo.getSaleSettRate()) ? pstInfo.getSaleSettRate() : sellSettRate;
            for (LogAsset logAsset : logAssetList) {
                String market = logAsset.getMarket();
                if (dsp.getStkCode().trim().equals(logAsset.getStkCode().trim()) && dsp.market.equals(market)) {
                    if (!ArithUtil.eq(logAsset.getBuystkqty(), 0)) {
                        //累加同一只股票所有买入数量
                        dsp.buyCount = dsp.buyCount + logAsset.getBuystkqty();
                        //dsp.buyAmt = dsp.buyAmt + logAsset.getMatchPrice() * logAsset.getMatchQty();
                        //累加同一只股票的所有买入金额
                        dsp.buyAmt = add(dsp.buyAmt, logAsset.getBuyMatchAmt());

                        //盈亏比例分母中的买入金额需包含利息 --APPAGILE-89983
                        Double matchAmt = logAsset.getBuyMatchAmt();
                        if (BusinessUtil.isGgtMarket(market)) {
                            //盈亏比例分母中买入金额需要乘上卖出结算汇率
                            matchAmt = ArithUtil.mul(matchAmt, sellSettRate);
                        }
                        if (matchAmt != null) {
                            dsp.buyAmtWithIntr = add(dsp.buyAmtWithIntr, matchAmt);
                        } else {
                            // 走不进来 有买入数量必然有买入金额不会为空
                            dsp.buyAmtWithIntr = add(dsp.buyAmtWithIntr, add(matchAmt, mul(bondIntr, logAsset.getBuystkqty())));
                        }
                    }
                    if (!ArithUtil.eq(logAsset.getSalestkqty(), 0)) {
                        //累加同一只股票所有卖出数量
                        dsp.sellCount = dsp.sellCount + logAsset.getSalestkqty();
                        //dsp.sellAmt = dsp.sellAmt + logAsset.getMatchPrice() * logAsset.getMatchQty();
                        //累加同一只股票的所有卖出金额
                        dsp.sellAmt = add(dsp.sellAmt, logAsset.getSaleMatchAmt());
                    }
                }
            }
            //当日参考盈亏=（最新价-昨收价）*（证券数量-当日买入数量）+（最新价-成交价）*当日买入数量+（成交价-昨收价）*当日卖出数量；
            //当日参考盈亏=（最新价-昨收价）*（证券数量-当日买入数量）+ (最新价*当日买入数量-当日买入金额)+（当日卖出金额-昨收价*当日卖出数量）；
//            dsp.profit = (dsp.dwPrice - dsp.dwClose) * (dsp.stkQty - dsp.buyCount)
//                    + (dsp.dwPrice * dsp.buyCount - dsp.buyAmt)
//                    + (dsp.sellAmt - dsp.dwClose * dsp.sellCount);
            // 买入/卖出数量使用存过返回的当日总买入/卖出数量 PCMP-10616646 【柜台配合】港股通当日收益计算优化
            if (BusinessUtil.isGgtMarket(pstInfo.getMarket())) {
                if (hgtProfitCalOptimizeFlag) {
                    dsp.buyCount = pstInfo.getTotalBuyQty();
                    dsp.sellCount = pstInfo.getTotalSaleQty();
                }
            }
            //持仓盈亏
            double profit1 = mul(sub(dsp.dwPrice, dsp.dwClose), sub(dsp.stkQty, dsp.buyCount));
            double buyVal = mul(dsp.dwPrice, dsp.buyCount);
            double sellLastVal = mul(dsp.sellCount, dsp.dwClose);
            // 港股通收益计算:（最新价-昨收价）*（证券数量-当日买入数量）*买入结算汇率
            // +当日买入数量*最新价*买入结算汇率-当日港股通买入成交金额（HK$）*卖出结算汇率
            // +当日港股通卖出成交金额（HK$）*买入结算汇率-当日卖出数量*昨收价*买入结算汇率 --- DATACENTER-3723
            if (BusinessUtil.isGgtMarket(pstInfo.getMarket())) {
                Double buySettRate = Objects.nonNull(hgtReferRateInfo) ? hgtReferRateInfo.getSettRate() : 1d;
                // 柜台开始清算后,存过返回结算汇率
                buySettRate = Objects.nonNull(pstInfo.getSettRate()) ? pstInfo.getSettRate() : buySettRate;
                //保存T-1日买入结算汇率
                dsp.setBuySettRate(buySettRate);
                //如果是港股通，对持有盈亏乘上买入结算汇率
                profit1 = mul(profit1, buySettRate);
                //对买入市值乘上买入结算汇率
                buyVal = mul(buyVal, buySettRate);
                //对买入金额乘上卖出结算汇率
                dsp.buyAmt = mul(dsp.buyAmt, sellSettRate);
                //对卖出金额乘上买入结算汇率
                dsp.sellAmt = mul(dsp.sellAmt, buySettRate);
                if (hgtProfitCalOptimizeFlag) {
                    // 买卖金额使用存过返回的总买入金额 PCMP-10616646 【柜台配合】港股通当日收益计算优化
                    dsp.buyAmt = pstInfo.getTotalBuyAmt();
                    dsp.sellAmt = pstInfo.getTotalSaleAmt();
                    dsp.buyAmtWithIntr = pstInfo.getTotalBuyAmt();
                }
                //对卖出昨收市值乘上买入结算汇率
                sellLastVal = mul(sellLastVal, buySettRate);
            }
            //买入盈亏
            double profit2 = sub(buyVal, dsp.buyAmt);
            //卖出盈亏
            double profit3 = sub(dsp.sellAmt, sellLastVal);
            //ACCTANAL-106 当日实时盈亏取2位小数
            dsp.profit = round(add(profit1, profit2, profit3));
            profitList.add(dsp);
        }
        return profitList;
    }

    /**
     * 当日某支股票清仓，在清算后查不到持仓
     */
    private void addClearStkAsset(List<PositionInfo> positionInfoList, List<LogAsset> logAssetList) {
        for (LogAsset logAsset : logAssetList) {
            boolean isClear = true;
            for (StkInfo pstInfo : positionInfoList) {
                if (pstInfo.getStkCode().trim().equals(logAsset.getStkCode().trim())) {
                    isClear = false;
                    break;
                }
            }
            if (isClear && (!ArithUtil.eq(logAsset.getBuystkqty(), 0) || !ArithUtil.eq(logAsset.getSalestkqty(), 0))) {
                PositionInfo pstInfo = new PositionInfo();
                pstInfo.isClear = isClear;
                pstInfo.setStkCode(logAsset.getStkCode());
                pstInfo.setStkBal(0L);
                pstInfo.setStkBuySale(0L);
                pstInfo.setStkUnComeBuy(0L);
                pstInfo.setStkUnComeSale(0L);
                pstInfo.setMarket(logAsset.getMarket());
                pstInfo.setFundId(logAsset.getFundId());
                pstInfo.setMoneyType(MoneyTypeEnum.RMB.getValue());
                positionInfoList.add(pstInfo);
            }
        }
    }

    /**
     * 计算当日收益率
     *
     * @param accountBizDate
     */
    private DayProfitBean calDayProfitRate(Integer accountBizDate, DayProfitBean dayProfitBean, boolean filterHgtProfit) {
        Long fundId = dayProfitBean.getFundId();
        double dayProfit = dayProfitBean.getProfit();
        Map<String, Object> params = new HashedMap(4);
        params.put("fundId", fundId);
        params.put("moneyType", "0");
        //当日收益率=当日收益额/（当日期初持仓总市值+ 当日期初可用资金 +广义净入金）
        double dayProfitRate = 0.0;

        if (!ArithUtil.eq(dayProfit, 0)) {
            params.put("assetDate", accountBizDate);
            //上一个交易日的持仓
            AssetNew assetNew = assetNewService.getAssetInfo(params);
            if (assetNew != null) {
                params.putIfAbsent("serverId", assetNew.getServerId());
            }
            //当日期初持仓总市值
            Double lastMktVal = assetHisService.getOpenMktVal(fundId, accountBizDate, assetNew.getServerId(), filterHgtProfit)
                    .values()
                    .stream()
                    .reduce(0.0, Double::sum);

            //上一个交易日的可用
            Double lastFundAvl = 0.0;

            // 判断fundAsset是否从交易历史tidb同步
            // 如果从交易历史tidb同步,期初可用资金查询asset_detail: ifnull(fundasset,0)+ifnull(fundbuysale,0)
            // 否则,查询fundAsset: add(fundBal, fundBuySale, -fundUncomeBuy, fundUncomeSale,fundCashPro)
            if (useFundAssetByTidb(accountBizDate, fundId)) {
                AssetDetailDO assetDetail = assetDetailService.getAllFundAsset(accountBizDate, fundId, MoneyTypeEnum.RMB.getValue());
                lastFundAvl = assetDetail.getAsset();
            } else {
                List<FundAsset> list = fundAssetDao.query(params);
                if (list.size() == 1) {
                    //期初可用资金fundAvl=add(fundBal,fundBuySale, -fundUncomeBuy, fundUncomeSale,fundcashpro)
                    FundAsset fundAssetInfo = list.get(0);
                    lastFundAvl = ArithUtil.add(fundAssetInfo.getFundBal(), fundAssetInfo.getFundBuySale(),
                            fundAssetInfo.getFundUncomeBuy() == null ? null : -fundAssetInfo.getFundUncomeBuy(), fundAssetInfo.getFundUncomeSale(), fundAssetInfo.getFundCashPro());
                }
            }

            //查询该日的转账记录
            params.put("bizDate", dayProfitBean.getBizDate());
            //银证划转、台账间划转记录
            List<LogAsset> transferList = logAssetService.selectTransferAmt(params);

            //广义出入金
            double broadMoney = 0.0;
            if (transferList.size() > 0) {
                double sumFundEffet = 0.0;
                List<Double> sumfundEffetList = new ArrayList<>();
                for (LogAsset logAsset : transferList) {
                    sumFundEffet += logAsset.getFundEffect();
                    sumfundEffetList.add(sumFundEffet);
                }
                for (Double sumFundEffet1 : sumfundEffetList) {
                    if (sumFundEffet1 > broadMoney) {
                        broadMoney = sumFundEffet1;
                    }
                }
            }

            //当日收益率=当日收益额/（当日期初持仓总市值+ 当日期初可用资金 +广义净入金）
            double fenmu = ArithUtil.add(lastFundAvl, lastMktVal, broadMoney);

            if (fenmu > 0) {
                dayProfitRate = ArithUtil.div(dayProfit, fenmu);
            }

            //计算当日转入转出
            for (LogAsset logAsset : transferList) {
                double fundEffect = logAsset.getFundEffect();
                if (fundEffect > 0) {
                    dayProfitBean.setShiftIn(ArithUtil.add(dayProfitBean.getShiftIn(), fundEffect));
                } else {
                    dayProfitBean.setShiftOut(ArithUtil.sub(dayProfitBean.getShiftOut(), fundEffect));
                }
            }
        }
        dayProfitBean.setProfitRate(dayProfitRate);
        return dayProfitBean;
    }


    /**
     * 根据每支股票的收益额获取日收益
     * 如果stockFlag 为 "0" 则只计算沪深A股的收益 否则计算所有股票收益
     *
     * @param dayStkProfitList
     * @return
     */
    private double calDayProfit(List<DayStkProfit> dayStkProfitList) {
        return dayStkProfitList.stream()
                .reduce(0.0, (profit, dayStkProfit) -> profit + dayStkProfit.profit, Double::sum);
    }

    /**
     * 判断fundAsset是否从交易历史tidb同步
     * 根据资金账号所在核心判断fundasset同步逻辑
     *
     * @param bizDate
     * @return
     */
    private Boolean useFundAssetByTidb(Integer bizDate, Long fundId) {
        Integer serverId = coreConfigService.getServerId(fundId);
        if (serverId == -1) {
            serverId = 1;
        }
        String configKey = String.join("_", CommConstants.FUNDASSET_TIDB_START_DATE_KEY, serverId.toString());
        String fundAssetConfig = nodeConfigService.getNodeConfig(configKey);
        if (!StringUtils.isEmpty(fundAssetConfig) && Integer.parseInt(fundAssetConfig) <= bizDate) {
            return true;
        }
        return false;
    }
}
