package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.HgtExchangeRate;

import java.util.List;
import java.util.Map;

/**
 * 港股通结算汇率
 *
 * <AUTHOR>
 * @create 2022/11/20
 */
public interface HgtExchangeRateDao extends IBaseDao<HgtExchangeRate, String> {
    /**
     * 查询深市最新两日的结算汇率
     *
     * @param params
     * @return
     */
    List<HgtExchangeRate> getLastestExchangeRate(Map<String, Object> params);

    /**
     * 获取指定日期的港股通汇率信息  bizdate
     *
     * @param params
     * @return
     */
    List<HgtExchangeRate> getRateListByBizdate(Map<String, Object> params);

    /**
     * 获取最近日期的最新的汇率数据
     *
     * @param params
     * @return
     */
    List<HgtExchangeRate> getLatestRateList(Map<String, Object> params);
}
