package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitSectionDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitSectionMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitSection;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by xiaoyongyong on 2016/7/19.
 * ProfitSectionServiceImpl
 *
 * update on 2016/07/26
 *
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitSectionDao")
public class TiProfitSectionDaoImpl extends BaseDao<TiProfitSectionMapper, ProfitSection, Long> implements ProfitSectionDao {

}
