package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.PositionSectionDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.PositionSection;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class PositionSectionCacheService {
    private static Logger LOG = LoggerFactory.getLogger(PositionSectionCacheService.class);
    private static final String SPLIT_FLAG = "-";
    @Resource(name = "positionSectionCache")
    private LoadingCache<String, Optional<List<PositionSection>>> positionSectionCache;
    @Autowired
    private PositionSectionDao positionSectionDao;
    @Autowired
    protected AssetNewService assetNewService;

    @Bean(name = "positionSectionCache")
    public LoadingCache<String, Optional<List<PositionSection>>> positionSectionCache() {
        LoadingCache<String, Optional<List<PositionSection>>> positionSectionCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(100000)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<List<PositionSection>>>() {
                    @Override
                    public Optional<List<PositionSection>> load(String key) throws Exception {
                        try {
                            String[] splitArray = key.split(SPLIT_FLAG);
                            Long fundId = CommonUtil.convert(splitArray[0], Long.class);
                            String unit = CommonUtil.convert(splitArray[1], String.class);

                            DateRange dateRange = getDateRange(fundId, unit);

                            return Optional.ofNullable(positionSectionDao.getPositionProfitStatistics(fundId,
                                    dateRange.getStartDate(), dateRange.getEndDate()));
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.empty();
                    }
                });
        return positionSectionCache;
    }
    @RedisCache(keyGenerator = "'jzjy_positionsection_' + #params.get('fundId') + '_' + #params.get('unit')")
    public List<PositionSection> getPositionSection(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        try {
            return positionSectionCache.get(fundId + SPLIT_FLAG + unit).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取持仓盈亏失败", e);
        }
        return null;
    }

    /**
     * 根据unit，计算查询的起止时间
     *
     * @param fundId
     * @param unit
     * @return
     */
    private DateRange getDateRange(final Long fundId, final String unit) {
        Integer intEndDate = null;
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        // 获取该用户最新的清算日期，作为unit区间的endDate
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (Objects.nonNull(assetNew)) {
            intEndDate = assetNew.getBizDate();
        }
        // 如果获取不到该用户最新的清算日期，则endDate为查询当天
        intEndDate = intEndDate == null ? Integer.valueOf(DateUtil.getCuryyyyMMdd()) : intEndDate;

        DateRange dateRange = CommonUtil.getDateRange(intEndDate, unit);
        // ALL区间特殊处理
        if (unit.equals(DateUnitEnum.ALL.getValue())) {
            dateRange.setStartDate(null);
        }
        return dateRange;
    }
}
