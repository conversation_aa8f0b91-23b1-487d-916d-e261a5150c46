<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiBseCodeAlterMapper">

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.BseCodeAlterDO">
        select * from atcenter.bse_code_alter
        <where>
            market='B' and del=0
            <if test="bizDate != null">
                and bizdate = #{bizDate}
            </if>
        </where>
    </select>

</mapper>