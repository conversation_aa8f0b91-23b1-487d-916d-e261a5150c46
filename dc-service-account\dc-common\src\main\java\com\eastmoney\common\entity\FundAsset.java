package com.eastmoney.common.entity;

/**
 * Created by Administrator on 2017/5/25.
 */
public class FundAsset {
    private String orgId;  // 机构编码
    private int fundSeq;  // 资金序号0代表是是客户下该币种的主资金帐号
    private long fundId;  // 资金帐号
    private String moneyType;  // 货币代码
    private String custId;  // 客户代码
    private Double fundLastBal;  // 昨日余额
    private Double fundBal;  // 到帐余额
    private Double fundAvl;  // 可用资金
    private Double overdRaw;  // 透支限额
    private Double fundBuy;  // 买入冻结(委托买入计加、撤单计减)
    private Double fundSale;  // 卖出解冻(成交卖出计加)
    private Double fundUncomeBuy;  // 在途买入(日终更新)
    private Double fundUncomeSale;  // 在途卖出(日终更新)
    private Double fundFrz;  // 冻结总金额(同时写资金冻结控制表logfrzctrl)
    private Double fundUnFrz;  // 解冻总金额(同时写资金冻结控制表logfrzctrl)
    private Double fundTrdFrz;  // 交易冻结金额(资金复核，银证转帐）
    private Double fundTrdUnFrz;  // 交易解冻金额(银证通）
    private Double fundNightFrz;  // 夜市冻结资金
    private Double fundLoan;  // 融资总金额
    private Double creditBal;   // 融券卖出资金可用额, 等于sum(creditfund.creditbal)
    private Double creditBuySale;  // 当日融券卖出和买券还券成交量的差额
    private String fundFlag;  // '0' 资金未修改'1' 资金已修改
    private Double marketValue;  // 证券市值
    private Double fundStandby;  // 备用金额
    private Double fundBuySale;  // 买卖差额（卖出成交增加、买入成交减少）
    private Double fundBrkBuy;  // 报价回购终止买入差额

    private Double fundAssetAdjamt;//补偿资金资产
    private Double stkAssetAdjamt;//补偿证券资产

    private Double fundCashPro;//otc资产

    public Double getFundCashPro() {
        return fundCashPro;
    }

    public void setFundCashPro(Double fundCashPro) {
        this.fundCashPro = fundCashPro;
    }

    public Double getFundAssetAdjamt() {
        return fundAssetAdjamt;
    }

    public void setFundAssetAdjamt(Double fundAssetAdjamt) {
        this.fundAssetAdjamt = fundAssetAdjamt;
    }

    public Double getStkAssetAdjamt() {
        return stkAssetAdjamt;
    }

    public void setStkAssetAdjamt(Double stkAssetAdjamt) {
        this.stkAssetAdjamt = stkAssetAdjamt;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public int getFundSeq() {
        return fundSeq;
    }

    public void setFundSeq(int fundSeq) {
        this.fundSeq = fundSeq;
    }

    public long getFundId() {
        return fundId;
    }

    public void setFundId(long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public Double getFundLastBal() {
        return fundLastBal;
    }

    public void setFundLastBal(Double fundLastBal) {
        this.fundLastBal = fundLastBal;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public Double getFundAvl() {
        return fundAvl;
    }

    public void setFundAvl(Double fundAvl) {
        this.fundAvl = fundAvl;
    }

    public Double getOverdRaw() {
        return overdRaw;
    }

    public void setOverdRaw(Double overdRaw) {
        this.overdRaw = overdRaw;
    }

    public Double getFundBuy() {
        return fundBuy;
    }

    public void setFundBuy(Double fundBuy) {
        this.fundBuy = fundBuy;
    }

    public Double getFundSale() {
        return fundSale;
    }

    public void setFundSale(Double fundSale) {
        this.fundSale = fundSale;
    }

    public Double getFundUncomeBuy() {
        return fundUncomeBuy;
    }

    public void setFundUncomeBuy(Double fundUncomeBuy) {
        this.fundUncomeBuy = fundUncomeBuy;
    }

    public Double getFundUncomeSale() {
        return fundUncomeSale;
    }

    public void setFundUncomeSale(Double fundUncomeSale) {
        this.fundUncomeSale = fundUncomeSale;
    }

    public Double getFundFrz() {
        return fundFrz;
    }

    public void setFundFrz(Double fundFrz) {
        this.fundFrz = fundFrz;
    }

    public Double getFundUnFrz() {
        return fundUnFrz;
    }

    public void setFundUnFrz(Double fundUnFrz) {
        this.fundUnFrz = fundUnFrz;
    }

    public Double getFundTrdFrz() {
        return fundTrdFrz;
    }

    public void setFundTrdFrz(Double fundTrdFrz) {
        this.fundTrdFrz = fundTrdFrz;
    }

    public Double getFundTrdUnFrz() {
        return fundTrdUnFrz;
    }

    public void setFundTrdUnFrz(Double fundTrdUnFrz) {
        this.fundTrdUnFrz = fundTrdUnFrz;
    }

    public Double getFundNightFrz() {
        return fundNightFrz;
    }

    public void setFundNightFrz(Double fundNightFrz) {
        this.fundNightFrz = fundNightFrz;
    }

    public Double getFundLoan() {
        return fundLoan;
    }

    public void setFundLoan(Double fundLoan) {
        this.fundLoan = fundLoan;
    }

    public Double getCreditBal() {
        return creditBal;
    }

    public void setCreditBal(Double creditBal) {
        this.creditBal = creditBal;
    }

    public Double getCreditBuySale() {
        return creditBuySale;
    }

    public void setCreditBuySale(Double creditBuySale) {
        this.creditBuySale = creditBuySale;
    }

    public String getFundFlag() {
        return fundFlag;
    }

    public void setFundFlag(String fundFlag) {
        this.fundFlag = fundFlag;
    }

    public Double getMarketValue() {
        return marketValue;
    }

    public void setMarketValue(Double marketValue) {
        this.marketValue = marketValue;
    }

    public Double getFundStandby() {
        return fundStandby;
    }

    public void setFundStandby(Double fundStandby) {
        this.fundStandby = fundStandby;
    }

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public Double getFundBrkBuy() {
        return fundBrkBuy;
    }

    public void setFundBrkBuy(Double fundBrkBuy) {
        this.fundBrkBuy = fundBrkBuy;
    }
}
