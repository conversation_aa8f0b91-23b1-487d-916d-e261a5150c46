package com.eastmoney.accessor.datasource;

import com.eastmoney.common.util.ClassUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * Created on 2016/3/2
 * 动态数据源
 *
 * <AUTHOR>
 */
public class DynamicDataSource extends AbstractRoutingDataSource {
    private static final Logger LOG = LoggerFactory.getLogger(DynamicDataSource.class);

    /**
     * 默认配置文件名
     */
    private static final String DEFAULT_DS_CONFIG_FILE = "dataSource.xml";
    /**
     * 数据源配置文件
     */
    private String dsConfigFile;

    @Override
    protected Object determineCurrentLookupKey() {
        return DataSourceContextHolder.getDataSourceType();
    }

    @Override
    public void afterPropertiesSet() {
        this.initDataSources();
    }

    /**
     * 初始化数据源
     */
    public void initDataSources() {
        List<Map<String, String>> dataSourceList = DynamicDataSourceUtils.parseDataSources(this.getDsConfigFile());
        this.initDataSources(dataSourceList);
    }

    /**
     * 初始化数据源
     *
     * @param dataSourceList
     */
    public void initDataSources(List<Map<String, String>> dataSourceList) {
        LOG.info("开始初始化动态数据源");
        Map<Object, Object> targetDataSource = new HashMap<>();
        Object defaultTargetDataSource = null;
        for (Map<String, String> map : dataSourceList) {
            String dataSourceId = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_ID);
            String dataSourceClass = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_CLASS);
            String isDefaultDataSource = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_DEFAULT, "false");

            String dbType = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_DB_TYPE);
            String flag = null;
            Integer serverId = null;
            if (dbType.equals(DataSourceConstant.SQL_SERVER)) {
                flag = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_FLAG);
                String sid = DynamicDataSourceUtils.getAndRemoveValue(map, DataSourceConstant.ATTR_SERVER_ID);
                if (sid != null) {
                    serverId = Integer.parseInt(sid);
                }
            }

            DataSource dataSource = (DataSource) ClassUtil.newInstance(dataSourceClass);
            DynamicDataSourceUtils.setDsProperties(map, dataSource);
            targetDataSource.put(dataSourceId, dataSource);
            if (Boolean.valueOf(isDefaultDataSource)) {
                defaultTargetDataSource = dataSource;
            }
            LOG.info("dataSourceId={},dataSourceClass={},isDefaultDataSource={},dbType={},flag={},serverId={}",
                    new Object[]{dataSourceId, dataSourceClass, isDefaultDataSource, dbType, flag, serverId});
        }
        this.setTargetDataSources(targetDataSource);
        this.setDefaultTargetDataSource(defaultTargetDataSource);//设置默认数据源
        super.afterPropertiesSet();
        LOG.info("初始化动态数据源完成");
    }

    public String getDsConfigFile() {
        if (StringUtils.isBlank(this.dsConfigFile)) {
            return DEFAULT_DS_CONFIG_FILE;
        }
        return dsConfigFile;
    }

    public void setDsConfigFile(String dsConfigFile) {
        this.dsConfigFile = dsConfigFile;
    }

}
