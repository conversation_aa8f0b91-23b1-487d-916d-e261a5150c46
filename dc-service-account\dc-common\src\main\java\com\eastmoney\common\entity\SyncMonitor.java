package com.eastmoney.common.entity;

import com.eastmoney.common.util.DateUtil;

/**
 * Created on 2016/3/3
 * 数据同步监控 每天比对一次
 *
 * <AUTHOR>
 */
public class SyncMonitor extends BaseEntity {
    //    private Long eid;//系统物理主键
//    private Date eiTime;//数据入库时间
//    private Date euTime;//数据修改时间
    private String schedule_name;//任务名
    private Long row_num;//源表记录数
    private Long sync_num;//同步表记录数
    private String send_error;//发送失败信息
    private Long batch;//批次号
    private Integer status; //监控状态 1：初始 2：正常 3：异常
    private String sys_flag;//所属系统标识
    public final static int STATUS_EXCEPTION = -1;
    private String timestamp;
    private Object message;

    public static SyncMonitor createExceptionResult(Object message) {
        return createMonitorResult(STATUS_EXCEPTION,message);
    }
    public static SyncMonitor createMonitorResult(int status,Object message){
        SyncMonitor result = new SyncMonitor();
        result.setStatus(status);
        result.setMessage(message);
        result.setTimestamp(DateUtil.getCurDateTime());
        return result;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSchedule_name() {
        return schedule_name;
    }

    public void setSchedule_name(String schedule_name) {
        this.schedule_name = schedule_name.trim();
    }

    public Long getRow_num() {
        return row_num;
    }

    public void setRow_num(Long row_num) {
        this.row_num = row_num;
    }

    public Long getSync_num() {
        return sync_num;
    }

    public void setSync_num(Long sync_num) {
        this.sync_num = sync_num;
    }

    public String getSend_error() {
        return send_error;
    }

    public void setSend_error(String send_error) {
        this.send_error = send_error;
    }

    public Long getBatch() {
        return batch;
    }

    public void setBatch(Long batch) {
        this.batch = batch;
    }

    public String getSys_flag() {
        return sys_flag;
    }

    public void setSys_flag(String sys_flag) {
        this.sys_flag = sys_flag.trim();
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }
}
