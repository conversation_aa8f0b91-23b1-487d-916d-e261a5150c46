package com.eastmoney.service.service.profit.base;

import com.eastmoney.accessor.dao.tidb.SecProfitDayDao;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 个股日收益明细清算service
 * <AUTHOR>
 * @create 2024/3/17
 */
@Service("secProfitServiceSettle")
public class SecProfitServiceSettleImpl implements SecProfitService {
    private static Logger LOG = LoggerFactory.getLogger(SecProfitServiceSettleImpl.class);

    private static String separator = "-";

    @Autowired
    private SecProfitDayDao secProfitDayDao;

    @Resource(name = "cashProfitCache")
    private LoadingCache<String, Optional<Double>> cashProfitCache;

    @Resource(name = "secProfitTotalNumCache")
    private LoadingCache<String, Optional<Integer>> secProfitTotalNumCache;

    /**
     * 查询某一天的个股日收益明细
     * @return
     */
    public List<SecProfitDayDO> getSecProfitDay(Map<String, Object> params) {
        return secProfitDayDao.getSecProfitDay(params);
    }

    @Override
    public List<SecProfitDayDO> getSecProfitDayByRange(Map<String, Object> params) {
        return secProfitDayDao.getSecProfitDayByRange(params);
    }

    /**
     * 查询总条数
     * @param params
     * @return
     */
    public Integer queryCount(Map<String, Object> params) {
        return secProfitDayDao.queryCount(params);
    }

    @Bean(name = "cashProfitCache")
    public LoadingCache<String, Optional<Double>> cashProfitCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1000)
                .maximumSize(50000)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<Double>>() {
                    @Override
                    public Optional<Double> load(String key){
                        Double cashProfit = null;
                        try {
                            Map<String, Object> param = new HashMap<>();
                            String[] keys = key.split(separator);
                            param.put("fundId", keys[0]);
                            param.put("startDate", keys[1]);
                            param.put("endDate", keys[2]);
                            cashProfit = secProfitDayDao.getCashProfit(param);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(cashProfit);
                    }
                });
    }

    @Bean(name = "secProfitTotalNumCache")
    public LoadingCache<String, Optional<Integer>> secProfitTotalNumCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(1000)
                .maximumSize(50000)
                .refreshAfterWrite(5, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<Integer>>() {
                    @Override
                    public Optional<Integer> load(String key){
                        Integer totalNum = null;
                        try {
                            Map<String, Object> param = new HashMap<>();
                            String[] keys = key.split(separator);
                            param.put("fundId", keys[0]);
                            param.put("startDate", keys[1]);
                            param.put("endDate", keys[2]);
                            totalNum = secProfitDayDao.queryCount(param);
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(totalNum);
                    }
                });
    }


    /**
     * 查询账户级别收益
     * @param params
     * @return
     */
    public Double getCashProfitDay(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        // topSize 有值说明是查询首页
        Integer topSize = CommonUtil.convert(params.get("topSize"), Integer.class);
        // cashProfitFlag-是否返回其它收益，0-否，1-是
        Integer cashProfitFlag = CommonUtil.convert(params.get("cashProfitFlag"), Integer.class);

        if (topSize != null || !Objects.equals(cashProfitFlag, 1)) {
            return null;
        }

        String key = fundId + separator + startDate + separator + endDate;
        // 只有明细详情页才会展示账户级别收益
        try {
            return cashProfitCache.get(key).orElse(0d);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取现金收益[key:{}]总条数失败, 失败原因：{}", key, e);
        }
        return null;
    }

    /**
     * 查询个股明细总条数
     * @param params
     * @return
     */
    public Integer getSecProfitTotalNum(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        // topSize 有值说明是查询首页
        Integer topSize = CommonUtil.convert(params.get("topSize"), Integer.class);
        // totalNumFlag-是否返回记录总数，0-否，1-是
        Integer totalNumFlag = CommonUtil.convert(params.get("totalNumFlag"), Integer.class);

        if (topSize != null || !Objects.equals(totalNumFlag, 1)) {
            return null;
        }

        String key = fundId + separator + startDate + separator + endDate;
        // 只有明细详情页才需要总条数
        try {
            return secProfitTotalNumCache.get(key).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取个股明细[key:{}]总条数失败, 失败原因：{}", key, e);
        }
        return null;
    }
}
