package com.eastmoney.common.serializer;

import com.alibaba.fastjson.serializer.ValueFilter;
import com.eastmoney.common.util.CommonUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/6.
 */
public class NumberFormatFilter implements ValueFilter {
    private int[] precisions = new int[]{2, 3, 6};
    private Map<Integer, NumberFormat> numberFormatMap = new HashMap<>();
    private Map<String, Integer> fieldPrecisionMap = new HashMap<>();
    private static NumberFormatFilter numberFormatFilter = new NumberFormatFilter();

    private NumberFormatFilter() {
        for (int precision : precisions) {
            NumberFormat nf = getIntegerInstance();
            nf.setMinimumFractionDigits(precision);
            numberFormatMap.put(precision, nf);
        }

        fieldPrecisionMap.put("costPrice", 3);
        fieldPrecisionMap.put("profitCost", 3);
        fieldPrecisionMap.put("costPriceEx", 3);
        fieldPrecisionMap.put("lastPrice", 3);
        fieldPrecisionMap.put("profitRate", 6);
        fieldPrecisionMap.put("profitRateTotal", 6);
        fieldPrecisionMap.put("price", 3);
        fieldPrecisionMap.put("fundAll", 3);
        fieldPrecisionMap.put("positionRate", 6);
        fieldPrecisionMap.put("fundAllWithOtc", 3);
    }

    public static NumberFormatFilter getInstance() {
        return numberFormatFilter;
    }

    @Override
    public Object process(Object object, String name, Object value) {
        if (value instanceof Double || value instanceof Float) {
            Integer precision = fieldPrecisionMap.get(name);
            if (precision == null) {
                precision = 2;
            }
            return numberFormatMap.get(precision).format(new BigDecimal(String.valueOf(value)));
        }
        if (value instanceof Integer || value instanceof Long || value instanceof Short) {
            return CommonUtil.convert(value, String.class);
        }
        return value;
    }

    private NumberFormat getIntegerInstance() {
        NumberFormat nf = NumberFormat.getIntegerInstance();
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        return nf;
    }

}
