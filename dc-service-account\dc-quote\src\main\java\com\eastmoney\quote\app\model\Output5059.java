package com.eastmoney.quote.app.model;/**
 * Created by 1 on 16-10-20.
 */

import com.eastmoney.quote.app.serializer.Packet;

/**
 * Created on 16-10-20
 *
 * <AUTHOR>
 */
public class Output5059 extends Packet {

    private short clientID;
    private byte reqIDLength;
    private byte[] reqIDs;
    private short reqDataTotalNum;
    private short returnDataNum;
    private DataId5059[] dataId5059;
    private DataIdRecord5059[] dataIdRecord5059;

    public short getClientID() {
        return clientID;
    }

    public void setClientID(short clientID) {
        this.clientID = clientID;
    }

    public byte getReqIDLength() {
        return reqIDLength;
    }

    public void setReqIDLength(byte reqIDLength) {
        this.reqIDLength = reqIDLength;
    }

    public byte[] getReqIDs() {
        return reqIDs;
    }

    public void setReqIDs(byte[] reqIDs) {
        this.reqIDs = reqIDs;
    }

    public short getReqDataTotalNum() {
        return reqDataTotalNum;
    }

    public void setReqDataTotalNum(short reqDataTotalNum) {
        this.reqDataTotalNum = reqDataTotalNum;
    }

    public short getReturnDataNum() {
        return returnDataNum;
    }

    public void setReturnDataNum(short returnDataNum) {
        this.returnDataNum = returnDataNum;
    }

    public DataId5059[] getDataId5059() {
        return dataId5059;
    }

    public void setDataId5059(DataId5059[] dataId5059) {
        this.dataId5059 = dataId5059;
    }

    public DataIdRecord5059[] getDataIdRecord5059() {
        return dataIdRecord5059;
    }

    public void setDataIdRecord5059(DataIdRecord5059[] dataIdRecord5059) {
        this.dataIdRecord5059 = dataIdRecord5059;
    }
}
