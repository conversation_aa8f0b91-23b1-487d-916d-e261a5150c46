package com.eastmoney.common.entity;

/**
 * Created by sunyuncai on 2016/11/5.
 */
public class StkPrice {
    private String market;
    private String stkCode;
    private Double closePrice;
    private Double lastClosePrice;
    private Double openPrice;
    private Double lastPrice;
    private Double highPrice;
    private Double lowPrice;
    private Long matchQty;
    private Double matchAmt;
    private Double bondIntr;
    private String mtkCalFlag;
    private Double xrPrice;
    private Double price;

    //price是否来自行情
    public boolean isQtPrice = false;

    //交易时间hhmmss
    private int dwTime;

    private String stkType;

    private String lofMoneyFlag;

    private String stkLevel;

    private Double ticketPrice;  //私募

    private String stkName;

    private Integer quitDate;//退市日期

    /**
     * 交易类型
     * 0 正常交易
     */
    private String trdId;

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public boolean isQtPrice() {
        return isQtPrice;
    }

    public void setQtPrice(boolean isQtPrice) {
        this.isQtPrice = isQtPrice;
    }

    public Double getClosePrice() {
        return closePrice;
    }

    public void setClosePrice(Double closePrice) {
        this.closePrice = closePrice;
    }

    public Double getLastClosePrice() {
        return lastClosePrice;
    }

    public void setLastClosePrice(Double lastClosePrice) {
        this.lastClosePrice = lastClosePrice;
    }

    public Double getOpenPrice() {
        return openPrice;
    }

    public void setOpenPrice(Double openPrice) {
        this.openPrice = openPrice;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public Double getHighPrice() {
        return highPrice;
    }

    public void setHighPrice(Double highPrice) {
        this.highPrice = highPrice;
    }

    public Double getLowPrice() {
        return lowPrice;
    }

    public void setLowPrice(Double lowPrice) {
        this.lowPrice = lowPrice;
    }

    public Long getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Long matchQty) {
        this.matchQty = matchQty;
    }

    public Double getMatchAmt() {
        return matchAmt;
    }

    public void setMatchAmt(Double matchAmt) {
        this.matchAmt = matchAmt;
    }

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public String  getMtkCalFlag() {
        return mtkCalFlag;
    }

    public void setMtkCalFlag(String mtkCalFlag) {
        this.mtkCalFlag = mtkCalFlag;
    }

    public Double getXrPrice() {
        return xrPrice;
    }

    public void setXrPrice(Double xrPrice) {
        this.xrPrice = xrPrice;
    }

    public int getDwTime() {
        return dwTime;
    }

    public void setDwTime(int dwTime) {
        this.dwTime = dwTime;
    }

    public String getStkType() {
        return stkType;
    }

    public void setStkType(String stkType) {
        this.stkType = stkType;
    }

    public String getLofMoneyFlag() {
        return lofMoneyFlag;
    }

    public void setLofMoneyFlag(String lofMoneyFlag) {
        this.lofMoneyFlag = lofMoneyFlag;
    }

    public String getStkLevel() {
        return stkLevel;
    }

    public void setStkLevel(String stkLevel) {
        this.stkLevel = stkLevel;
    }

    public Double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(Double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Integer getQuitDate() {
        return quitDate;
    }

    public void setQuitDate(Integer quitDate) {
        this.quitDate = quitDate;
    }

    public String getTrdId() {
        return trdId;
    }

    public void setTrdId(String trdId) {
        this.trdId = trdId;
    }
}
