package com.eastmoney.common.util;

import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.stat.descriptive.moment.Mean;
import org.apache.commons.math3.stat.descriptive.moment.StandardDeviation;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * sunyuncai 2016/3/16
 */

public class ArithUtil {
    private static final int DEF_DIV_SCALE = 10;
    private static final double DOUBLE_THRESHOLD = 0.000001;

    public static Double notNull(Double x) {
        return x == null ? 0.0 : x;
    }

    public static Long notNull(Long x) {
        return x == null ? 0L : x;
    }

    public static Integer notNull(Integer x) {
        return x == null ? 0 : x;
    }

    /**
     * 两个Double数相加
     */
    public static Double add(Number v1, Number v2) {
        v1 = convertIfNullTo0(v1);
        v2 = convertIfNullTo0(v2);
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.add(b2).doubleValue();
    }

    /**
     * 多个数相加
     */
    public static Double add(Number... values) {
        if (values.length == 1) {
            throw new RuntimeException("只有一个入参");
        }
        BigDecimal result = new BigDecimal(new Double(0.0).toString());
        BigDecimal bd;
        for (Number value : values) {
            value = convertIfNullTo0(value);
            bd = new BigDecimal(value.toString());
            result = result.add(bd);
        }
        return result.doubleValue();
    }

    /**
     * 多个数相加
     */
    public static long addIgnoreNull(Long... values) {
        long result = 0l;
        for (Long value : values) {
            if (value != null)
                result += value;
        }
        return result;
    }

    /**
     * 两个Double数相减
     */
    public static Double sub(Number v1, Number v2) {
        v1 = convertIfNullTo0(v1);
        v2 = convertIfNullTo0(v2);
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.subtract(b2).doubleValue();
    }


    /**
     * 多个数相减
     * 第一个参数为被减数
     */
    public static Double sub(Number... values) {
        if (values.length == 1) {
            throw new RuntimeException("只有一个入参");
        }
        //第一个参数为被减数
        BigDecimal result = new BigDecimal(values[0].toString());
        BigDecimal bd;
        for (int i = 1; i < values.length; i++) {
            bd = new BigDecimal(values[i].toString());
            result = result.subtract(bd);
        }
        return result.doubleValue();
    }

    /**
     * 两个Double数相乘
     */
    public static Double mul(Number v1, Number v2) {
        v1 = convertIfNullTo0(v1);
        v2 = convertIfNullTo0(v2);
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 多个数相乘
     */
    public static Double mul(Number... values) {
        if (values.length == 1) {
            throw new RuntimeException("只有一个入参");
        }
        BigDecimal result = new BigDecimal(new Double(1d).toString());
        BigDecimal bd;
        for (Number value : values) {
            if (value == null || 0 == value.doubleValue())
                return 0d;
            bd = new BigDecimal(value.toString());
            result = result.multiply(bd);
        }
        return result.doubleValue();
    }

    /**
     * 两个Double数相除
     */
    public static Double div(Number v1, Number v2) {
        v1 = convertIfNullTo0(v1);
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.divide(b2, DEF_DIV_SCALE, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 两个Double数相除，并保留scale位小数
     */
    public static Double div(Number v1, Number v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        if (v2.doubleValue() == 0) {
            return 0d;
        }
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * BigDecimal转Double
     */
    public static Double toBigDecimal(BigDecimal number, Double defaultVal) {
        if(number == null) {
            return defaultVal;
        }
        return number.doubleValue();
    }


    static Number convertIfNullTo0(Number v1) {
        if (v1 == null) {
            return 0.0;
        } else {
            return v1;
        }
    }

    /**
     * 四舍五入
     *
     * @param ratio 小数点后的位数
     */
    public static double round(Number number, int ratio) {
        number = convertIfNullTo0(number);
        BigDecimal b1 = new BigDecimal(number.toString());
        return b1.setScale(ratio, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 四舍五入,保留2位小数
     */
    public static double round(Number number) {
        return round(number, 2);
    }

    public static double[] getNormalDistributionValues(double[] values) {
        //求平均值
        Mean mean = new Mean(); // 算术平均值
        double meanValue = mean.evaluate(values);
        //求标准方差
        StandardDeviation standardDeviation = new StandardDeviation(false);
        double standardDeviationValue = standardDeviation.evaluate(values);

        NormalDistribution normalDistribution = new NormalDistribution(meanValue, standardDeviationValue);
        double[] normalDistributioinValues = new double[values.length];
        for (int i = 0; i < values.length; i++) {
            normalDistributioinValues[i] = normalDistribution.density(values[i]);
        }
        return normalDistributioinValues;
    }

    public static double[] getNormalDistributionValues(double[] values, double sd) {
        //求平均值
        Mean mean = new Mean(); // 算术平均值
        double meanValue = mean.evaluate(values);

        NormalDistribution normalDistribution = new NormalDistribution(meanValue, sd);
        double[] normalDistributioinValues = new double[values.length];
        for (int i = 0; i < values.length; i++) {
            normalDistributioinValues[i] = normalDistribution.density(values[i]);
        }
        return normalDistributioinValues;
    }

    public static boolean eq(Number number1, Number number2) {
        return Math.abs(sub(number1, number2)) < DOUBLE_THRESHOLD;
    }

    /**
     * number1 <= number2  ,允许有DOUBLE_THRESHOLD的误差
     * @param number1
     * @param number2
     * @return
     */
    public static boolean le(Number number1, Number number2){
        return sub(number1, number2) <= DOUBLE_THRESHOLD;
    }


    /**
     * number1 >= number2  ,允许有DOUBLE_THRESHOLD的误差
     * @param number1
     * @param number2
     * @return
     */
    public static boolean ge(Number number1, Number number2){
        return sub(number1, number2) >= -DOUBLE_THRESHOLD;
    }


    // ********************************************* BigDecimal 相关计算 *********************************************

    /**
     * 两个BigDecimal相加
     */
    public static BigDecimal add(BigDecimal v1, BigDecimal v2) {
        v1 = Optional.ofNullable(v1).orElse(BigDecimal.ZERO);
        v2 = Optional.ofNullable(v2).orElse(BigDecimal.ZERO);
        return v1.add(v2);
    }

    /**
     * 两个BigDecimal数相减
     */
    public static BigDecimal sub(BigDecimal v1, BigDecimal v2) {
        v1 = convertBigDecimalIfNullTo0(v1);
        v2 = convertBigDecimalIfNullTo0(v2);
        return v1.subtract(v2);
    }

    static BigDecimal convertBigDecimalIfNullTo0(BigDecimal v1) {
        if (v1 == null) {
            return BigDecimal.ZERO;
        } else {
            return v1;
        }
    }

    /**
     * 四舍五入
     *
     * @param ratio 小数点后的位数
     */
    public static BigDecimal round(BigDecimal number, int ratio) {
        number = Optional.ofNullable(number).orElse(BigDecimal.ZERO);
        return number.setScale(ratio, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal mul(BigDecimal v1, BigDecimal v2) {
        v1 = convertBigDecimalIfNullTo0(v1);
        v2 = convertBigDecimalIfNullTo0(v2);
        return v1.multiply(v2);
    }

    /**
     * Double转BigDecimal
     */
    public static BigDecimal toBigDecimal(Double number, BigDecimal defaultVal) {
        if(number == null) {
            return defaultVal;
        }
        return new BigDecimal(Double.toString(number));
    }

    

    public static void main(String[] args) {
//		System.out.println(div(100d,3d,4));
//
//        float f = Float.parseFloat("1.0");
//        int bits = Float.floatToIntBits(f);
//        System.out.println("bits: " + bits);
//        System.out.println("back to float: " + Float.intBitsToFloat(bits));
//        System.out.println(add(3.1,3.2,-4.0));
//        System.out.println(add(3.1,3.2));
////        System.out.println(add(3.2));
//        System.out.println(div(18792d,1000d));
//        System.out.println(72d/1000);
//        System.out.println(72.1+1000.1);
        Number num = 123456789987654321L;
        System.out.println(num.doubleValue());
        if (num.intValue() == 123456789987654321L) {
            System.out.println("相等");
        }
        System.out.println(div(8.0104690432, 8.79, 6));
        System.out.println(le(0.0,0.0));
    }

    public static double addProfitRate(Number v1, Number v2) {
        v1 = convertIfNullTo0(v1);
        v2 = convertIfNullTo0(v2);
        return sub(mul(add(v1, 1), (add(ArithUtil.round(v2, 4), 1))), 1);
    }
}
