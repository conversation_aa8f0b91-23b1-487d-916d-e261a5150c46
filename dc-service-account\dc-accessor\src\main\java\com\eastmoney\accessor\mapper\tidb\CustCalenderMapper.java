package com.eastmoney.accessor.mapper.tidb;


import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.CustCalenderDO;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-30 17:13
 */
public interface CustCalenderMapper extends BaseMapper<CustCalenderDO, Long> {

    /**
     * 获取指定日期区间DateFrom到DateTo之间的所有阳历日期
     *
     * @param params
     * @return
     */
    List<CustCalenderDO> getByBizDateRange(Map<String, Object> params);
}
