package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 1. 牛市提前布局（前瞻性）
 * 2. 10月8日未站岗
 * 3. 最高月收益
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearBullTradeDO {

    // 09/24（含）仓位≥50%的具体仓位比例
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal mvalRatio0924;
    // 09/24（含）仓位≥50%的用户超越的股友比
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal mvalRatio0924Percent;
    // 普通账户有持仓，但10月8日未有任何的“买入交易”成交
    private Integer holdNobuy1008;

    public BigDecimal getMvalRatio0924() {
        return mvalRatio0924;
    }

    public void setMvalRatio0924(BigDecimal mvalRatio0924) {
        this.mvalRatio0924 = mvalRatio0924;
    }

    public BigDecimal getMvalRatio0924Percent() {
        return mvalRatio0924Percent;
    }

    public void setMvalRatio0924Percent(BigDecimal mvalRatio0924Percent) {
        this.mvalRatio0924Percent = mvalRatio0924Percent;
    }

    public Integer getHoldNobuy1008() {
        return holdNobuy1008;
    }

    public void setHoldNobuy1008(Integer holdNobuy1008) {
        this.holdNobuy1008 = holdNobuy1008;
    }

}
