package com.eastmoney.accessor.enums;

/**
 * Created on 2016/11/03
 * 证券类别
 *
 * <AUTHOR>
 */
public enum LofTypeEnum {
    LOFTYPE_STK(0), //股票基金
    LOFTYPE_MONEY_OLD(1), //老式货币基金
    LOFTYPE_BOND(2), //债券基金
    LOFTYPE_MIXED(3), //混合基金
    LOFTYPE_MONEY_NEW(4); //新式货币基金

    private Integer unit;

    LofTypeEnum(Integer unit) {
        this.unit = unit;
    }

    public Integer getValue() {
        return this.unit;
    }

    public static LofTypeEnum getEnum(Integer value) {
        switch (value) {
            case 0:
                return LOFTYPE_STK;
            case 1:
                return LOFTYPE_MONEY_OLD;
            case 2:
                return LOFTYPE_BOND;
            case 3:
                return LOFTYPE_MIXED;
            case 5:
                return LOFTYPE_MONEY_NEW;
            default:
                throw new RuntimeException("not found LofType[" + value + "]");
        }
    }
}
