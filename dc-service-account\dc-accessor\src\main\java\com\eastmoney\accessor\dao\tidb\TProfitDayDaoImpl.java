package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiTProfitDayMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/30 11:07
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("tProfitDayDao")
public class TProfitDayDaoImpl extends BaseDao<TiTProfitDayMapper, TProfitDayDO, Long> implements TProfitDayDao {

    @Override
    public List<TProfitDayDO> getTProfitDayList(Map<String, Object> params) {
        return getMapper().getTProfitDayList(params);
    }

    @Override
    public TProfitBO getTProfitSection(Map<String, Object> dealParams) {
        return getMapper().getTProfitSection(dealParams);
    }
}
