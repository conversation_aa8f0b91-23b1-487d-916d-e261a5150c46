package com.eastmoney.service.serializer;

import com.eastmoney.common.serializer.AbstractSerializeFilter;
import com.google.common.collect.ImmutableSet;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * Created by Administrator on 2017/4/1.
 */
@Component
public class ProfitSectionStatFilter extends AbstractSerializeFilter {
    @Override
    protected Collection<String> genIncludes() {
        return ImmutableSet.of(
                "profitTotal", "profitRateTotal", "statList", "profit", "profitRate", "bizDate"
        );
    }

}
