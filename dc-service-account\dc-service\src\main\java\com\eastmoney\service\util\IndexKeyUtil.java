package com.eastmoney.service.util;

import com.eastmoney.common.model.DateRange;
import org.springframework.lang.NonNull;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * <AUTHOR>
 * @description 账单中的IndexKey解析工具类
 * @date 2025/5/7 10:12
 */
public final class IndexKeyUtil {

    private IndexKeyUtil() {
        throw new AssertionError("不能实例化工具类");
    }

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 如果是年，则返回[某年第一天，某年最后一天]
     * <p>
     * 如果是月，则返回[某月第一天，某月最后一天]
     * <p>
     * 如果是当月，则返回[当月第一天，今天]
     *
     * @param indexKey 账单中的IndexKey
     * @return 日期区间
     */
    @NonNull
    public static DateRange getDateRange(@NonNull String indexKey) {
        try {
            if (indexKey.length() == 4) {
                // 处理年份格式，如 "2025"
                int year = Integer.parseInt(indexKey);
                String firstDay = LocalDate.of(year, 1, 1).format(FORMATTER);
                String lastDay = LocalDate.of(year, 12, 31).format(FORMATTER);
                return new DateRange(Integer.parseInt(firstDay), Integer.parseInt(lastDay));

            } else if (indexKey.length() == 6) {
                // 处理年月格式，如 "202501"
                DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyyMM");
                YearMonth yearMonth = YearMonth.parse(indexKey, monthFormatter);

                String firstDay = yearMonth.atDay(1).format(FORMATTER);
                String lastDay = yearMonth.atEndOfMonth().format(FORMATTER);

                // 如果是当前月份，则使用今天作为结束日期
                LocalDate today = LocalDate.now();
                if (yearMonth.equals(YearMonth.from(today))) {
                    lastDay = today.format(FORMATTER);
                }

                return new DateRange(Integer.parseInt(firstDay), Integer.parseInt(lastDay));
            }
        } catch (NumberFormatException | DateTimeParseException e) {
            throw new IllegalArgumentException("IndexKey格式化失败：" + indexKey, e);
        }

        throw new IllegalArgumentException("IndexKey必须为4位或者6位");
    }
}
