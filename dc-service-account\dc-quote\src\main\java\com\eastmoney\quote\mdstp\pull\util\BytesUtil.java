package com.eastmoney.quote.mdstp.pull.util;/**
 * Created by 1 on 16-10-20.
 */

import java.io.UnsupportedEncodingException;

/**
 * Created on 16-10-20
 *
 * <AUTHOR>
 */
public class BytesUtil {
    /**
     * long类型转成byte数组
     *
     * @param l
     * @return
     */
    public static byte[] longToByte(long l) {
        long temp = l;
        byte[] b = new byte[8];
        for (int i = 0; i < b.length; i++) {
//            b[i] = new Long(temp & 0xff).byteValue();//将最低位保存在最低位
            b[i] = (byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return b;
    }


    /**
     * long类型转成byte数组
     *
     * @param l
     * @return
     */
    public static byte[] longToByte(long l, byte[] dest, int index) {
        long temp = l;
        for (int i = index; i < index + 8; i++) {
//            dest[i] = new Long(temp & 0xff).byteValue();//将最低位保存在最低位
            dest[i] = (byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return dest;
    }


    /**
     * byte数组转成long
     *
     * @param b
     * @return
     */
    public static long byteToLong(byte[] b) {
        long s = 0;
        long s0 = b[0] & 0xff;// 最低位
        long s1 = b[1] & 0xff;
        long s2 = b[2] & 0xff;
        long s3 = b[3] & 0xff;
        long s4 = b[4] & 0xff;// 最低位
        long s5 = b[5] & 0xff;
        long s6 = b[6] & 0xff;
        long s7 = b[7] & 0xff;

        // s0不变
        s1 <<= 8;
        s2 <<= 16;
        s3 <<= 24;
        s4 <<= 8 * 4;
        s5 <<= 8 * 5;
        s6 <<= 8 * 6;
        s7 <<= 8 * 7;
        s = s0 | s1 | s2 | s3 | s4 | s5 | s6 | s7;
        return s;
    }

    /**
     * int到字节数组的转换！
     *
     * @param i
     * @return
     */
    public static byte[] intToByte(int i) {
        int temp = i;
        byte[] b = new byte[4];
        for (int j = 0; j < b.length; j++) {
//            b[j] = new Integer(temp & 0xff).byteValue();//将最低位保存在最低位
            b[j] =(byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return b;

    }

    /**
     * int到字节数组的转换！
     *
     * @param i
     * @return
     */
    public static byte[] intToByte(int i, byte[] dest, int index) {
        int temp = i;
        for (int j = index; j < index + 4; j++) {
//            dest[j] = new Integer(temp & 0xff).byteValue();//将最低位保存在最低位
            dest[j] =(byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return dest;

    }

    /**
     * 字节数组到int的转换！
     *
     * @param b
     * @return
     */
    public static int byteToInt(byte[] b) {
        int s = 0;
        int s0 = b[0] & 0xff;// 最低位
        int s1 = b[1] & 0xff;
        int s2 = b[2] & 0xff;
        int s3 = b[3] & 0xff;
        s3 <<= 24;
        s2 <<= 16;
        s1 <<= 8;
        s = s0 | s1 | s2 | s3;
        return s;
    }

    /**
     * @param str
     * @return byte[]
     */
    public static byte[] stringToByte(String str, int length) throws UnsupportedEncodingException {

        byte[] codeBytes = new byte[length];
        for (short i = 0; i < length; i++) {
            codeBytes[i] = 0;
        }
        byte[] tmpCode = null;
        tmpCode = str.substring(0, str.length() < 8 ? str.length() : 8).getBytes("gb2312"); //当字符串超过8个字符时（16字节）只截取前面8个字符
        System.arraycopy(tmpCode, 0, codeBytes, 0, tmpCode.length);
        return codeBytes;

    }

    /**
     * short到字节数组的转换！
     *
     * @param s
     * @return
     */
    public static byte[] shortToByte(short s) {
        int temp = s;
        byte[] b = new byte[2];
        for (int i = 0; i < b.length; i++) {
//            b[i] = new Integer(temp & 0xff).byteValue();//将最低位保存在最低位
            b[i] = (byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return b;
    }

    /**
     * short到字节数组的转换！
     *
     * @param s
     * @return
     */
    public static byte[] shortToByte(short s, byte[] dest, int index) {
        int temp = s;
        for (int i = index; i < index + 2; i++) {
//            dest[i] = new Integer(temp & 0xff).byteValue();//将最低位保存在最低位
            dest[i] = (byte)(temp & 0xff);//将最低位保存在最低位

            temp = temp >> 8; // 向右移8位
        }
        return dest;
    }

    /**
     * 字节数组到short的转换！
     *
     * @param b
     * @return
     */
    public static short byteToShort(byte[] b) {
        short s = 0;
        short s0 = (short) (b[0] & 0xff);// 最低位
        short s1 = (short) (b[1] & 0xff);
        s1 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    /**
     * boolean 转byte 数组
     *
     * @param b
     * @return
     */
    public static byte[] booleanToByte(boolean b) {
        byte[] bytes = new byte[1];
        bytes[0] = b ? (byte) 1 : 0;
        return bytes;
    }

    /**
     * boolean 转byte 数组
     *
     * @param b
     * @return
     */
    public static byte[] booleanToByte(boolean b, byte[] dest, int index) {
        dest[index] = b ? (byte) 1 : 0;
        return dest;
    }

    /**
     * byte转boolean
     *
     * @param b
     * @return
     */
    public static boolean byteToBoolean(byte[] b) {

        return b[0] == 1;
    }

    /**
     * byte转char
     *
     * @param b
     * @return
     */
    public static char byteToChar(byte[] b) {
        return (char) b[0];
    }

    public static byte[] charToByte(char c) {
        byte[] bytes = new byte[1];
        bytes[0] = (byte) c;
        return bytes;
    }

    /**
     * 获取 bytes 从start开始，长度为length的子数组
     *
     * @param bytes
     * @param start
     * @param length
     * @return
     */
    public static byte[] sub(byte[] bytes, int start, int length) {
        byte[] sub = new byte[length];
        System.arraycopy(bytes, start, sub, 0, length);
        return sub;
    }

    /**
     * 将字节型数据转换为0~255 (0xFF 即BYTE)。
     *
     * @param b
     * @return
     */
    public static int getUnsignedByte(byte b) {
        return b & 0x0FF;
    }

    /**
     * float转换byte
     *
     * @param f
     */
    public static byte[] floatToByte(float f) {
        byte[] b = new byte[4];
        int l = Float.floatToIntBits(f);
        for (int i = 0; i < 4; i++) {
            b[i] = new Integer(l).byteValue();
            l = l >> 8;
        }
        return b;
    }

    /**
     * float转换byte
     *
     * @param f
     */
    public static byte[] floatToByte(float f, byte[] dest, int index) {
        int l = Float.floatToIntBits(f);
        for (int i = index; i < index + 4; i++) {
//            dest[i] = new Integer(l).byteValue();
            dest[i] = (byte)l;

            l = l >> 8;
        }
        return dest;
    }

    /**
     * byte转float
     *
     * @param b
     * @return
     */
    public static float byteToFloat(byte[] b) {
        int l;
        l = b[0];
        l &= 0xff;
        l |= ((long) b[1] << 8);
        l &= 0xffff;
        l |= ((long) b[2] << 16);
        l &= 0xffffff;
        l |= ((long) b[3] << 24);
        return Float.intBitsToFloat(l);
    }


    /**
     * double 转byte
     *
     * @param d
     * @return
     * @throws java.io.IOException
     */
    public static byte[] doubleToByte(double d) {
        byte[] b = new byte[8];
        long temp = Double.doubleToLongBits(d);
        for (int i = 0; i < 8; i++) {
//            b[i] = new Long(temp & 0xff).byteValue();
            b[i] = (byte)(temp & 0xff);

            temp = temp >> 8; // 向右移8位
        }

        return b;
    }

    /**
     * double 转byte
     *
     * @param d
     * @return
     * @throws java.io.IOException
     */
    public static byte[] doubleToByte(double d, byte[] dest, int index) {
        long temp = Double.doubleToLongBits(d);
        for (int i = index; i < index + 8; i++) {
//            dest[i] = new Long(temp & 0xff).byteValue();
            dest[i] = (byte)(temp & 0xff);

            temp = temp >> 8; // 向右移8位
        }

        return dest;
    }


    /**
     * byte转double
     *
     * @param b
     * @return
     */
    public static double byteToDouble(byte[] b) {
        long l;
        l = b[0];
        l &= 0xff;
        l |= ((long) b[1] << 8);
        l &= 0xffff;
        l |= ((long) b[2] << 16);
        l &= 0xffffff;
        l |= ((long) b[3] << 24);
        l &= 0xffffffffl;
        l |= ((long) b[4] << 32);
        l &= 0xffffffffffl;
        l |= ((long) b[5] << 40);
        l &= 0xffffffffffffl;
        l |= ((long) b[6] << 48);
        l &= 0xffffffffffffffl;
        l |= ((long) b[7] << 56);
        return Double.longBitsToDouble(l);
    }

    public static byte[] longTo4Byte(long value) {
        byte[] data = new byte[4];
        for (int i = 0; i < data.length; i++) {
            data[i] = (byte) (value >> (8 * i));
        }
        return data;
    }

    // 将C/C++的无符号 DWORD类型转换为java的long型
    public static long getLong(byte buf[], int index) {
        int firstByte = (0x000000FF & ((int) buf[index]));
        int secondByte = (0x000000FF & ((int) buf[index + 1]));
        int thirdByte = (0x000000FF & ((int) buf[index + 2]));
        int fourthByte = (0x000000FF & ((int) buf[index + 3]));
        long unsignedLong = ((long) (firstByte | secondByte << 8 | thirdByte << 16 | fourthByte << 24)) & 0xFFFFFFFFL;
        return unsignedLong;
    }
}
