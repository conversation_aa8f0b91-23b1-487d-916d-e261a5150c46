package com.eastmoney.accessor.aspect;

import com.eastmoney.accessor.datasource.DataSourceContextHolder;
import com.eastmoney.accessor.datasource.SqlServerForwarder;
import com.eastmoney.common.util.CommonUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2015/12/3
 */
@Aspect
@Order(1)
@Component
public class MultipleDataSourceAspect {

    @Autowired
    private SqlServerForwarder sqlServerForwarder;

    @Before("execution(* com.eastmoney.accessor.dao..*(..))")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getName();
        if (className.contains("oracle")) {
            DataSourceContextHolder.setDataSourceType(CommonUtil.getOraDbDataSourceType());
        } else if (className.contains("kgdb")) {
            DataSourceContextHolder.setDataSourceType(CommonUtil.getOtcKGDbDataSourceType());
        } else if (className.contains("tidb")) {
            DataSourceContextHolder.setDataSourceType(CommonUtil.getTidbDataSourceType());
        } else if (className.contains("fundia")) {
            DataSourceContextHolder.setDataSourceType(CommonUtil.getFundIaDataSourceType());
        } else if (className.contains("sqlserver")) {
                DataSourceContextHolder.setDataSourceType(sqlServerForwarder.forward(joinPoint));
        }
    }

    @After("execution(* com.eastmoney.accessor.dao..*(..))")
    public void doAfter(JoinPoint joinPoint) throws Throwable {
        DataSourceContextHolder.clearDataSourceType();
    }

}
