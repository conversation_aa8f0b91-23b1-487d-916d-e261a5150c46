package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.VO.ExchangeRateInfoVO;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.TProfitDetail;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.handler.TOperationAnalyzeHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 做T分析接口
 * @date 2024/4/29 17:17
 */
@Action
@Component
public class TOperationAnalyzeAction {

    @Resource
    private TOperationAnalyzeHandler tOperationAnalyzeHandler;


    /**
     * PC APP 通用接口 获取个股汇总做T收益 T-1
     */
    @FunCodeMapping("getSecTProfitRankList")
    @RequestMapping("/getSecTProfitRankList")
    public TProfitBO getSecTProfitRankList(Map<String, Object> params) {
        Map<String, Object> dealParams = dealParamsNeedPageAndSort(params);
        return tOperationAnalyzeHandler.getSecTProfitRankList(dealParams);
    }

    /**
     * PC APP 通用接口 获取全量汇总做T收益 有实时
     */
    @FunCodeMapping("getTProfitSection")
    @RequestMapping("/getTProfitSection")
    public TProfitBO getTProfitSection(Map<String, Object> params) {
        Map<String, Object> dealParams = checkAndDealCommonParams(params);
        return tOperationAnalyzeHandler.getTProfitSection(dealParams);
    }


    /**
     * 每日做T收益列表 有实时(带stkCode即为  个股维度)
     */
    @FunCodeMapping("getTProfitDayList")
    @RequestMapping("/getTProfitDayList")
    public List<TProfitDayDO> getTProfitDaySectionList(Map<String, Object> params) {
        Map<String, Object> dealParams = dealParamsNeedPage(params);
        return tOperationAnalyzeHandler.getTProfitDayList(dealParams);
    }

    /**
     * 每日做T收益列表 PC使用 有实时  PC前端暂无stkCode维度进来
     */
    @FunCodeMapping("getTProfitDayListPC")
    @RequestMapping("/getTProfitDayListPC")
    public List<TSecProfitDayDO> getTProfitDayListPC(Map<String, Object> params) {
        // 通用入参检查并处理
        Map<String, Object> dealParams = dealParamsNeedPage(params);
        return tOperationAnalyzeHandler.getTProfitDayListPC(dealParams);
    }

    /**
     * 个股每日做T收益列表 PC使用 无实时  PC前端只做在下拉展示，无详情跳转
     */
    @FunCodeMapping("getSecTProfitDayListPC")
    @RequestMapping("/getSecTProfitDayListPC")
    public List<TSecProfitDayDO> getSecTProfitDayListPC(Map<String, Object> params) {
        // todo通用入参检查并处理 参数检查放action
        CommonUtil.checkParamNotNull(params, new String[]{"stkCode", "market"});
        Map<String, Object> dealParams = dealParamsNeedPage(params);
        return tOperationAnalyzeHandler.getSecTProfitDayListPC(dealParams);
    }

    /**
     * 做T每日收益个股明细
     */
    @FunCodeMapping("getSecTDetailList")
    @RequestMapping("/getSecTDetailList")
    public List<TProfitDetail> getTProfitDetailList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "bizDate"});
        return tOperationAnalyzeHandler.getSecTDetailList(params);
    }

    /**
     * 做T每日收益个股明细
     */
    @FunCodeMapping("getSecTDetailListPC")
    @RequestMapping("/getSecTDetailListPC")
    public List<Match> getTProfitDetailListPC(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "stkCode", "market", "bizDate"});
        return tOperationAnalyzeHandler.getSecTDetailListPC(params);
    }

    /**
     * 做T分析查询指定日期港股通汇率信息  app\pc通用
     */
    @FunCodeMapping("getExchangeRateInfo")
    @RequestMapping("/getExchangeRateInfo")
    public ExchangeRateInfoVO getExchangeRateInfo(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"bizDate","source"});
        return tOperationAnalyzeHandler.getExchangeRateInfo(params);
    }

    /**
     * 参数检查 默认参数处理 需要分页
     */
    private Map<String, Object> dealParamsNeedPageAndSort(Map<String, Object> params) {
        Map<String, Object> resultParams = dealParamsNeedPage(params);
        // 排序参数
        String orderFlag = CommonUtil.convert(params, "orderFlag", String.class);
        if (StringUtils.isEmpty(orderFlag)) {
            orderFlag = "2";
        }
        String sortFlag = CommonUtil.convert(params, "sortFlag", String.class);
        if (StringUtils.isEmpty(sortFlag)) {
            sortFlag = "2";
        }
        resultParams.put("orderFlag", orderFlag);
        resultParams.put("sortFlag", sortFlag);
        // 处理startDate endDate
        return resultParams;
    }

    /**
     * 参数检查 默认参数处理 需要分页
     */
    private Map<String, Object> dealParamsNeedPage(Map<String, Object> params) {
        Map<String, Object> resultParams = checkAndDealCommonParams(params);
        // 分页参数
        CommonUtil.checkParamNotNull(params, new String[]{CommConstants.PAGE_NO, CommConstants.PAGE_SIZE});
        Integer pageSize = CommonUtil.convert(params.get(CommConstants.PAGE_SIZE), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get(CommConstants.PAGE_NO), Integer.class);
        pageNo = pageNo - 1 >= 0 ? pageNo : 1;
        Integer startNum = pageSize * (pageNo - 1);
        resultParams.put(CommConstants.PAGE_NO, pageNo);
        resultParams.put(CommConstants.START_NUM, startNum);
        resultParams.put(CommConstants.PAGE_SIZE, pageSize >= 999 ? 999 : pageSize);
        // 处理startDate endDate
        return resultParams;
    }

    /**
     * 处理startDate endDate 参数
     */
    private Map<String, Object> dealDateRange(Map<String, Object> params) {
        String unit = CommonUtil.convert(params, "unit", String.class);
        // 如果获取不到该用户最新的清算日期，则endDate为查询当天
        Integer endDate = Integer.valueOf(DateUtil.getCuryyyyMMdd());
        DateRange dateRange = CommonUtil.getDateRange(endDate, unit);
        params.put("endDate", endDate);
        // ALL区间特殊处理
        if (!unit.equals(DateUnitEnum.ALL.getValue())) {
            params.put("startDate", dateRange.getStartDate());
        }
        return params;
    }

    /**
     * 参数检查 默认参数处理
     */
    private Map<String, Object> checkAndDealCommonParams(Map<String, Object> params) {
        Map<String, Object> resultParams = new HashMap<>();
        CommonUtil.checkParamNotNull(params, new String[]{"fundId"});
        String fundId = CommonUtil.convert(params, "fundId", String.class);
        resultParams.put("fundId", fundId);
        // 传个股查询需要传stkCode market
        String stkCode = CommonUtil.convert(params, "stkCode", String.class);
        String market = CommonUtil.convert(params, "market", String.class);
        if (StringUtils.isNotEmpty(stkCode) || StringUtils.isNotEmpty(market)) {
            CommonUtil.checkParamNotNull(params, new String[]{"stkCode", "market"});
            resultParams.put("stkCode", stkCode);
            resultParams.put("market", market);
        }
        Integer startDate = CommonUtil.convert(params, "startDate", Integer.class);
        Integer endDate = CommonUtil.convert(params, "endDate", Integer.class);
        if (startDate != null || endDate != null) {
            CommonUtil.checkParamNotNull(params, new String[]{"startDate", "endDate"});
            resultParams.put("startDate", startDate);
            resultParams.put("endDate", endDate);
            // 自定义区间直接返回
            return resultParams;
        }
        String unit = CommonUtil.convert(params, "unit", String.class);
        resultParams.put("unit", unit);
        if (StringUtils.isEmpty(unit)) {
            // 默认M本月展示
            resultParams.put("unit", DateUnitEnum.MONTH.getValue());
        }
        // 处理unit
        return dealDateRange(resultParams);
    }


}
