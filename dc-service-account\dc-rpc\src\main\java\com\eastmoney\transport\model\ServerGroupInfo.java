package com.eastmoney.transport.model;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/24
 * Time: 13:50
 */
public class ServerGroupInfo {
    private final AtomicInteger seq = new AtomicInteger(0);
    private List<ServerInfo> serverInfos = new ArrayList<ServerInfo>();

    public List<ServerInfo> getServerInfos() {
        return serverInfos;
    }

    public void setServerInfos(List<ServerInfo> serverInfos) {
        this.serverInfos = serverInfos;
    }

    public AtomicInteger getSeq() {
        return seq;
    }
}
