package com.eastmoney.quote.mdstp.serializer;

import java.util.concurrent.*;

/**
 * Created on 2019/5/29.
 *
 * @auther user
 */
public class QtDecoderWorker {

    /**
     * 解包线程池
     */
    private static ExecutorService quoteExecutorService = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            0, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(100000));

    public static Future<?> submit(QtDecoderRunner runner) {
        return quoteExecutorService.submit(runner);
    }

}
