package com.eastmoney.service.service;

import com.eastmoney.common.entity.cal.ProfitDay;

import java.util.List;

/**
 * 日收益service
 * <AUTHOR>
 * @create 2024/2/7
 */
public interface ProfitDayService {

    /**
     * 查询指定区间的收益数据
     * @param fundId
     * @param startDate
     * @param endDate
     * @param unit
     * @return
     */
    List<ProfitDay> queryProfitDayByUnit(Long fundId, Integer startDate, Integer endDate, String unit);

    /**
     * 查询指定日期收益额
     * @param fundId
     * @param bizDate
     * @return
     */
    List<ProfitDay> queryProfitByBizDate(Long fundId, Integer bizDate);
}
