package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.sqlserver.OggLogBankTranDao;
import com.eastmoney.common.entity.LogBankTran;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
@Service
public class LogBankTranServiceImpl implements LogBankTranService {
    @Autowired
    private OggLogBankTranDao oggLogBankTranDao;


    @Override
    public List<LogBankTran> query(Map<String, Object> params) {
        return oggLogBankTranDao.query(params);
    }

    @Override
    public List<LogBankTran> selectBySysDate(Map<String, Object> params) {
        return oggLogBankTranDao.selectBySysDate(params);
    }

    @Override
    public Double selectFundIaAdjustFundAsset(Map<String, Object> params) {
        return oggLogBankTranDao.selectFundIaAdjustFundAsset(params);
    }
}
