package com.eastmoney.accessor.service;

import com.eastmoney.common.entity.LogBankTran;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
public interface LogBankTranService {

    List<LogBankTran> query(Map<String, Object> params);

    List<LogBankTran> selectBySysDate(Map<String, Object> params);

    Double selectFundIaAdjustFundAsset(Map<String, Object> params);
}
