package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ReferenceProfitRateMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ReferenceProfitRate;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by Administrator on 2017/4/14.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("referenceProfitRateDao")
public class ReferenceProfitRateDaoImpl extends BaseDao<ReferenceProfitRateMapper,ReferenceProfitRate,Long> implements ReferenceProfitRateDao {
}
