package com.eastmoney.service.http;

import com.alibaba.fastjson.JSON;
import com.eastmoney.accessor.util.SpringContextUtil;
import com.eastmoney.common.model.LogBean;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.GzipUtil;
import com.eastmoney.common.util.LogUtil;
import com.eastmoney.service.util.SpringConfig;
import com.eastmoney.transport.util.ChannelUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.codec.http.*;
import io.netty.handler.codec.http.multipart.DefaultHttpDataFactory;
import io.netty.handler.codec.http.multipart.HttpPostRequestDecoder;
import io.netty.handler.codec.http.multipart.InterfaceHttpData;
import io.netty.handler.codec.http.multipart.MemoryAttribute;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataAccessException;

import java.lang.reflect.InvocationTargetException;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.sql.SQLSyntaxErrorException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static io.netty.handler.codec.http.HttpHeaders.Names.*;
import static io.netty.handler.codec.http.HttpResponseStatus.OK;
import static io.netty.handler.codec.http.HttpVersion.HTTP_1_1;

/**
 * Created on 2016/7/23.
 * <p>
 * http请求处理类r
 *
 * <AUTHOR>   updater: xiaoyongyong
 */
public class HttpServerHandler extends ChannelInboundHandlerAdapter {

    private static final Log LOG = LogFactory.getLog(HttpServerHandler.class);
    private static ScheduledExecutorService executor = null;
    private static volatile BlockingQueue<QueueBean> queue = null;
    private static volatile String serverIp = null;
    // 请求流水号
    private static final String EM_PID = "em_pid";
    // 接口编号
    private static final String TYPE = "type";
    private static SpringConfig springConfig = SpringContextUtil.getBean(SpringConfig.class);

    // 与tcp请求线程池配置保持一致,用于压测场景
    static {
        Integer threadPool = springConfig.getHttp_server_thread_pool();
        Integer queueCapacity = springConfig.getHttp_server_queue_capacity();
        executor = Executors.newScheduledThreadPool(Objects.isNull(threadPool) ? 1 : threadPool);
        queue = new ArrayBlockingQueue<>(Objects.isNull(queueCapacity) ? 20 : queueCapacity);
    }

    static {
        Thread thread = new Thread(new ChannelReadScan(), "ServerChannelReadScan");
        thread.setDaemon(true);
        thread.start();
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (serverIp == null) {
            serverIp = ((InetSocketAddress) ctx.channel().localAddress()).getAddress().getHostAddress();
        }
        queue.put(new QueueBean(ctx, msg));
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        LOG.error("ctx close!", cause);
        ctx.close();
    }

    private static class ChannelReadScan implements Runnable {
        @Override
        public void run() {
            try {
                while (true) {
                    final QueueBean queueBean = queue.take();
                    executor.execute(new Runnable() {
                        @Override
                        public void run() {
                            String errorType = "";
                            String errorMsg = "";
                            long startTime = System.currentTimeMillis();
                            HttpResult httpResult = new HttpResult();
                            ChannelHandlerContext ctx = queueBean.getCtx();
                            boolean keepAlive = false;
                            // 请求流水号
                            String emPid = "";
                            try {
                                Object msg = queueBean.getMsg();
                                if (msg instanceof HttpRequest) {

                                    HttpRequest req = (HttpRequest) msg;
                                    if (HttpHeaders.is100ContinueExpected(req)) {
                                        ctx.write(new DefaultFullHttpResponse(HTTP_1_1, HttpResponseStatus.CONTINUE));
                                    }
                                    keepAlive = HttpHeaders.isKeepAlive(req);

                                    // 解析http头部
                                    HttpHeaders headers = req.headers();
                                    if(headers.contains(EM_PID)){
                                        emPid = headers.get(EM_PID);
                                    }

                                    String uri = req.getUri();
                                    LOG.debug("uri:" + uri);
                                    if (uri.endsWith("/favicon.ico")) {
                                        return;
                                    }
                                    if (uri.startsWith("http")) {
                                        uri = uri.replaceAll("http://[^/]+", "");
                                    }
                                    String requestPath = uri.trim().split("\\?")[0];

                                    Map<String, String> params = convertToMap(uri, req);
                                    requestPath = (!StringUtils.isEmpty(requestPath) && Objects.equals(requestPath.length(), 1))
                                            ? requestPath + params.getOrDefault(TYPE, "") : requestPath;
                                    Object data = service(requestPath, params, req, ctx);
                                    httpResult.setData(data);
                                }
                            } catch (Throwable t) {
                                if (t instanceof InvocationTargetException) {
                                    t = ((InvocationTargetException) t).getTargetException();
                                }
                                if (t instanceof MyBatisSystemException || t instanceof SQLSyntaxErrorException || t instanceof DataAccessException) {
                                    errorType = "数据库查询异常";
                                }
                                errorMsg = t.getMessage();
                                t.printStackTrace();
                                httpResult.setStatus(-1);
                                LOG.error(t.getMessage());
                            } finally {
                                if (StringUtils.isNotBlank(errorType)) {
                                    httpResult.setMessage(errorType);
                                } else {
                                    httpResult.setMessage(errorMsg);
                                }
                                byte[] httpResultBytes = JSON.toJSONString(httpResult).getBytes();
                                FullHttpResponse response = new DefaultFullHttpResponse(HTTP_1_1, OK, Unpooled.wrappedBuffer(GzipUtil.compress(httpResultBytes)));
                                response.headers().set(CONTENT_TYPE, "text/html; charset=UTF-8");
                                response.headers().set(CONTENT_LENGTH, response.content().readableBytes());
                                response.headers().set(CONTENT_ENCODING, HttpHeaders.Values .GZIP);
                                response.headers().set(EM_PID, emPid);
                                if (!keepAlive) {
                                    ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
                                } else {
                                    response.headers().set(CONNECTION, HttpHeaders.Values.KEEP_ALIVE);
                                    ctx.writeAndFlush(response);
                                }

                                try {
                                    LogUtil.info(composeAccessLogBean(queueBean, httpResult, errorType, errorMsg, System.currentTimeMillis() - startTime));
                                } catch (Throwable t) {
                                    t.printStackTrace();
                                }
                            }
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private LogBean composeAccessLogBean(QueueBean queueBean, HttpResult httpResult, String errorType, String errorMsg, long spentTime) {
            LogBean logBean = new LogBean();
            ChannelHandlerContext ctx = queueBean.getCtx();
            Object msg = queueBean.getMsg();
            if (msg instanceof HttpRequest) {
                HttpRequest req = (HttpRequest) msg;
                String uri = req.getUri();
                if (uri.startsWith("http")) {
                    uri = uri.replaceAll("http://[^/]+", "");
                }
                String requestPath = uri.trim().split("\\?")[0];
                Map<String, String> params = convertToMap(uri, req);
                logBean.setAction(requestPath.replaceAll("/", ""));
                logBean.setIn_param(JSON.toJSONString(params));

                String clientIp = getClientIp(req, ctx);
                if (StringUtils.isNoneBlank(clientIp)) {
                    logBean.setC_ip(clientIp);
                }
                InetSocketAddress sip = ChannelUtil.getLocalAddress(ctx.channel());
                if (sip != null) {
                    String serverIp = CommonUtil.convert(sip, String.class).replaceAll("/", "");
                    logBean.setS_ip(serverIp.substring(0, serverIp.indexOf(":")));
                }

                logBean.setU_id(CommonUtil.convert(params.get("fundId"), String.class));
                logBean.setT_id(CommonUtil.convert(params.get("requestId"), String.class));

                logBean.setResult(httpResult.getStatus() + "");
                if (httpResult.getStatus() == -1) {
                    logBean.setError_no(errorType);
                    logBean.setError_msg(errorMsg);
                } else {
                    String message = "size=" + httpResult.getSize();
                    logBean.setError_msg(message);
                }

                if (httpResult.getData() != null) {
                    String jsonString = JSON.toJSONString(httpResult.getData());
                    logBean.setOut_param(jsonString);
                }
                logBean.setSpent_time(spentTime + "");
                logBean.setLog_type("info");
            }
            return logBean;
        }

        private Map<String, String> convertToMap(String uri, HttpRequest req) {
            Map<String, String> params = new HashMap<>();

            // 是GET请求
            if (HttpMethod.GET.equals(req.getMethod())) {
                // 解析请求参数
                QueryStringDecoder queryStringDecoder = new QueryStringDecoder(uri);
                Map<String, List<String>> paramMap = queryStringDecoder.parameters();
                for (Map.Entry<String, List<String>> entry : paramMap.entrySet()) {
                    String value = CommonUtil.convert(entry.getValue().get(0), String.class);
                    if (StringUtils.isNotBlank(value)) {
                        params.put(entry.getKey(), entry.getValue().get(0));
                    }
                }
            }

            if (HttpMethod.POST.equals(req.getMethod())) {
                String content = ((FullHttpRequest) req).content().toString(StandardCharsets.UTF_8);
                // 是POST请求
                HttpPostRequestDecoder decoder = new HttpPostRequestDecoder(new DefaultHttpDataFactory(false), req);
                List<InterfaceHttpData> postList = decoder.getBodyHttpDatas();
                if (postList.size() == 0) {
                    //解析JSON入参
                    if (content != null && !content.isEmpty()) {
                        params = JSON.parseObject(content, Map.class);
                    }
                } else {
                    for (InterfaceHttpData data : postList) {
                        if (data.getHttpDataType() == InterfaceHttpData.HttpDataType.Attribute) {
                            MemoryAttribute attribute = (MemoryAttribute) data;
                            params.put(attribute.getName(), attribute.getValue());
                        }
                    }
                }
            }
            return params;
        }

        private Object service(String requestPath, Map<String, String> params, HttpRequest req, ChannelHandlerContext ctx) throws Exception {
            return HttpDispatcher.service(requestPath, params);
        }


        private String getClientIp(HttpRequest req, ChannelHandlerContext ctx) {
            String clientIP = req.headers().get("X-Forwarded-For");
            if (clientIP == null) {
                InetSocketAddress insocket = (InetSocketAddress) ctx.channel()
                        .remoteAddress();
                clientIP = insocket.getAddress().getHostAddress();
            }
            return clientIP;
        }


    }

    class QueueBean {
        private final ChannelHandlerContext ctx;
        private final Object msg;

        QueueBean(ChannelHandlerContext ctx, Object msg) {
            this.ctx = ctx;
            this.msg = msg;
        }

        public ChannelHandlerContext getCtx() {
            return ctx;
        }

        public Object getMsg() {
            return msg;
        }
    }
}