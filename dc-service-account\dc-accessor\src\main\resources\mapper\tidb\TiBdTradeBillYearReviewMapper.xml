<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiBdTradeBillYearReviewMapper">
    <resultMap id="BaseResultMap" type="com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearReviewDO">
        <result column="INDEXKEY" property="indexKey"/>
        <result column="SZZS_INDEX_CHG" property="szzsIndexChg"/>
        <result column="CYBZ_INDEX_CHG" property="cybzIndexChg"/>
        <result column="KC50_INDEX_CHG" property="kc50IndexChg"/>
        <result column="BZ50_INDEX_CHG" property="bz50IndexChg"/>
        <result column="PUBLISH_CNT" property="publishCnt"/>
        <result column="PUBLISH_RISE_CNT" property="publishRiseCnt"/>
        <result column="TOP1_PUBLISH_NAME" property="top1PublishName"/>
        <result column="TOP1_PUBLISH_CHG" property="top1PublishChg"/>
        <result column="TOP2_PUBLISH_NAME" property="top2PublishName"/>
        <result column="TOP2_PUBLISH_CHG" property="top2PublishChg"/>
        <result column="TOP3_PUBLISH_NAME" property="top3PublishName"/>
        <result column="TOP3_PUBLISH_CHG" property="top3PublishChg"/>
        <result column="STOCK_RISE_CNT" property="stockRiseCnt"/>
        <result column="STOCK_FALL_CNT" property="stockFallCnt"/>
        <result column="RISE_TOP1_STK_NAME" property="riseTop1StkName"/>
        <result column="RISE_TOP1_STK_CHG" property="riseTop1StkChg"/>
        <result column="LISTING_TOP1_STK_NAME" property="listingTop1StkName"/>
        <result column="LISTING_TOP1_STK_CHG" property="listingTop1StkChg"/>
        <result column="CONSECUTIVE_STK_NAME" property="consecutiveStkName"/>
        <result column="CONSECUTIVE_STK_CHG" property="consecutiveStkChg"/>
        <result column="CONSECUTIVE_DAYS" property="consecutiveDays"/>
        <result column="LIMIT_UP_DAYS" property="limitUpDays"/>
        <result column="OPTIONAL_STK_NAME" property="optionalStkName"/>
        <result column="OPTIONAL_STK_CNT" property="optionalStkCnt"/>
    </resultMap>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
            INDEXKEY,
            SZZS_INDEX_CHG,
            CYBZ_INDEX_CHG,
            KC50_INDEX_CHG,
            BZ50_INDEX_CHG,
            PUBLISH_CNT,
            PUBLISH_RISE_CNT,
            TOP1_PUBLISH_NAME,
            TOP1_PUBLISH_CHG,
            TOP2_PUBLISH_NAME,
            TOP2_PUBLISH_CHG,
            TOP3_PUBLISH_NAME,
            TOP3_PUBLISH_CHG,
            STOCK_RISE_CNT,
            STOCK_FALL_CNT,
            RISE_TOP1_STK_NAME,
            RISE_TOP1_STK_CHG,
            LISTING_TOP1_STK_NAME,
            LISTING_TOP1_STK_CHG,
            CONSECUTIVE_STK_NAME,
            CONSECUTIVE_STK_CHG,
            CONSECUTIVE_DAYS,
            LIMIT_UP_DAYS,
            OPTIONAL_STK_NAME,
            OPTIONAL_STK_CNT
        FROM
            ATCENTER.BD_TRADE_BILL_YEAR_REVIEW
        <where>
            <if test="indexKey != null and indexKey != 0">
                AND INDEXKEY = #{indexKey}
            </if>
        </where>
    </select>

</mapper>
