package com.eastmoney.common.serializer;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;

import java.io.IOException;
import java.lang.reflect.Type;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * double类型数据不使用科学计数法并转为String类型
 */
public class DoubleToStringSerializer implements ObjectSerializer {

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {

        if(Objects.nonNull(object)) {
            DecimalFormat df = new DecimalFormat();
            df.setGroupingUsed(false);

            Double value = (Double) object;
            //默认保留3位小数,前端只显示2位小数
            serializer.write(df.format(value));
        }
    }
}
