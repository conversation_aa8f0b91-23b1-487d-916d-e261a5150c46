package com.eastmoney.quote.mdstp.pull.client;


import com.eastmoney.quote.mdstp.pull.conf.QuoteConstant;
import org.apache.commons.pool.PoolableObjectFactory;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by 1 on 15-7-7.
 */
public class PoolableRpcConnectionFactory implements RpcConnFactInterface,
        PoolableObjectFactory<RpcConnection> {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(PoolableRpcConnectionFactory.class);

    //connectionFactory
    private RpcConnFactInterface connectionFactory;

    //org.apache.commons.pool
    private GenericObjectPool<RpcConnection> pool;




    public PoolableRpcConnectionFactory(String quoteType,String host,int port){
        this.connectionFactory =new RpcConnectionFactory(quoteType,host,port);
        GenericObjectPool.Config cfg = new GenericObjectPool.Config();
        cfg.maxActive= QuoteConstant.maxActive;
        cfg.maxIdle= QuoteConstant.maxIdle;
        cfg.minIdle= QuoteConstant.minIdle;
        pool = new GenericObjectPool<RpcConnection>(this,cfg);
        initConnPool();

        pool.setTestOnBorrow(true);
        pool.setTestWhileIdle(true);



    }

    public void initConnPool(){
        long startTime = System.currentTimeMillis();
        final int num = QuoteConstant.initialSize;
        CountDownLatch latch=new CountDownLatch(num);
        for (int i = 1; i <= num; i++) {
            try {
                new Thread(new InitPoolWorker(i, latch,pool)).start();
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        try {
            latch.await();
            long endTime = System.currentTimeMillis();
            LOG.info("***********init conn pool size:"+num+",spend times:"+(endTime-startTime)+"ms.**********");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /* get Connection
     * @see org.stefan.snrpc.SnRpcConnectionFactory#getConnection()
     */
    public RpcConnection getConnection() throws Throwable {
        LOG.debug("borrow connection ...");
        return pool.borrowObject();
    }

    /* recycle connection pool
     * @see org.stefan.snrpc.SnRpcConnectionFactory#recycle(org.stefan.snrpc.SnRpcConnection)
     */
    public void recycle(RpcConnection connection) throws Throwable {
        if (null != connection) {
            pool.returnObject(connection);
        }
        LOG.debug("recycle connection ...");
    }

    /**
     * destroy connection  pool
     * @throws Throwable
     */
    public void destroy() throws Throwable {

        pool.close();
        LOG.debug("destroy connection ...");
    }

    /* activate connection
     * @see org.apache.commons.pool.PoolableObjectFactory#activateObject(java.lang.Object)
     */
    public void activateObject(RpcConnection connection) throws Exception {
        try {
            connection.connection();
        } catch (Throwable e) {
            throw new Exception(e);
        }finally {
            LOG.debug("activateObject connection ...");
        }
    }

    /*destroy connection
     * @see org.apache.commons.pool.PoolableObjectFactory#destroyObject(java.lang.Object)
     */
    public void destroyObject(RpcConnection connection) throws Exception {
        try {
            connection.close();
        } catch (Throwable e) {
            throw new Exception(e);
        }finally {
            LOG.debug("destroyObject connection ...");
        }
    }

    /* make connection
     * @see org.apache.commons.pool.PoolableObjectFactory#makeObject()
     */
    public RpcConnection makeObject() throws Exception {
        RpcConnection connection = null;
        try {
            connection = connectionFactory.getConnection();
            connection.connection();
            return connection;
        } catch (Throwable e) {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
            }
            throw new Exception(e);
        }finally {
            LOG.debug("makeObject connection ...");
        }
    }

    /* passivateconnection
     * @see org.apache.commons.pool.PoolableObjectFactory#passivateObject(java.lang.Object)
     */
    public void passivateObject(RpcConnection connection) throws Exception {
        LOG.debug("passivateObject connection ...");
    }

    /* validate connection
     * @see org.apache.commons.pool.PoolableObjectFactory#validateObject(java.lang.Object)
     */
    public boolean validateObject(RpcConnection connection) {
        LOG.debug("validateObject connection ...");
        return connection.isConnected() && !connection.isClosed();
    }

    public void setLifo(boolean lifo) {
        pool.setLifo(lifo);
    }

    public void setMaxActive(int maxActive) {
        pool.setMaxActive(maxActive);
    }

    public void setMaxIdle(int maxIdle) {
        pool.setMaxIdle(maxIdle);
    }

    public void setMaxWait(long maxWait) {
        pool.setMaxWait(maxWait);
    }

    public void setMinEvictableIdleTimeMillis(long minEvictableIdleTimeMillis) {
        pool.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
    }

    public void setMinIdle(int minIdle) {
        pool.setMinIdle(minIdle);
    }

    public void setNumTestsPerEvictionRun(int numTestsPerEvictionRun) {
        pool.setNumTestsPerEvictionRun(numTestsPerEvictionRun);
    }

    public void setSoftMinEvictableIdleTimeMillis(
            long softMinEvictableIdleTimeMillis) {
        pool.setSoftMinEvictableIdleTimeMillis(softMinEvictableIdleTimeMillis);
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        pool.setTestOnBorrow(testOnBorrow);
    }

    public void setTestOnReturn(boolean testOnReturn) {
        pool.setTestOnReturn(testOnReturn);
    }

    public void setTestWhileIdle(boolean testWhileIdle) {
        pool.setTestWhileIdle(testWhileIdle);
    }

    public void setTimeBetweenEvictionRunsMillis(
            long timeBetweenEvictionRunsMillis) {
        pool.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
    }

    public void setWhenExhaustedAction(byte whenExhaustedAction) {
        pool.setWhenExhaustedAction(whenExhaustedAction);
    }
}

