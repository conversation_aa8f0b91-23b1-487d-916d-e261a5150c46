package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Created on 2020/8/18-20:20.
 *
 * <AUTHOR>
 */
@Action
@Component
public class TimeControlAction {
//    @Autowired
//    private SysConfigService sysConfigService;
//
//    @RequestMapping("/updateCurrentDateTime")
//    @FunCodeMapping("updateCurrentDateTime")
//    public Objects updateCurrentDateTime(Map<String, Object> params){
//        DateUtil.currentTime = CommonUtil.convert(params.get("currentTime"), String.class);
//        DateUtil.currentDate = CommonUtil.convert(params.get("currentDate"), String.class);
//        return null;
//    }
//
//    @RequestMapping("/updateSysConfig")
//    @FunCodeMapping("updateSysConfig")
//    public Objects updateCSysConfig(Map<String, Object> params){
//        Integer status = CommonUtil.convert(params.get("status"), Integer.class);
//        Integer sysDate = CommonUtil.convert(params.get("sysDate"), Integer.class);
//        sysConfigService.setSysConfig(status, sysDate);
//        return null;
//    }
}
