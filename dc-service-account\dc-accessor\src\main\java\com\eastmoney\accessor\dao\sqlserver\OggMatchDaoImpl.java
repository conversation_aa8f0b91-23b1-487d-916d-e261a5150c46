package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.sqlserver.OggMatchMapper;
import com.eastmoney.common.entity.Match;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/11/8
 */
@SqlServerTarget()
@Service
public class OggMatchDaoImpl extends BaseDao<OggMatchMapper, Match, Integer> implements OggMatchDao {
    @Override
    public List<Match> getRealTimeMatchList(Map<String, Object> params) {
        return mapper.getRealTimeMatchList(params);
    }

    @Override
    public List<Match> getAllRealTimeMatchList(Map<String, Object> params) {
        return mapper.getAllRealTimeMatchList(params);
    }
}
