package com.eastmoney.common.entity.cal.dw.bill;


import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 1. 抓牛股
 * 2. ETF收益
 * 3. 做T相关指标
 * 4. 优优投顾
 * <p>
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearBullProfitDO {
    // 2024全年抓住的牛股个数
    private Long catchBullCnt;
    // 2024全年抓住的牛股个数超越的股友百分比
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal catchBullCntPercent;
    // 全年抓住的牛股中用户赚钱最多的个股名称
    private String catchBullMaxpName;
    // 全年抓住的牛股中用户赚钱最多的个股的全年涨幅（前复权）
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal catchBullMaxpChg;
    // 全年抓住的牛股中用户赚钱最多的个股的收益金额
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal catchBullMaxpProfit;
    // 2024全年持有（清仓和持仓）过的ETF总只数
    private Long holdEtfCnt;
    // 2024全年持有（清仓和持仓）过的ETF获得的总收益
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal holdEtfProfit;
    // 2024全年持有（清仓和持仓）过的ETF中赚钱最多的1只产品名称
    private String holdEtfMaxpName;
    // 2024全年持有（清仓和持仓）过的ETF中赚钱最多的ETF的全年收益
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal holdEtfMaxpProfit;
    // 2024.09.24以来的牛市收益金额（普通账户）
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal bullProfit;
    // 2024.09.24以来的牛市收益率（普通账户）
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal bullProfitRate;
    // 2024.09.24以来牛市收益率超越普通交易用户百分比
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal bullProfitPercent;

    private Integer bizDate;

    public Long getCatchBullCnt() {
        return catchBullCnt;
    }

    public void setCatchBullCnt(Long catchBullCnt) {
        this.catchBullCnt = catchBullCnt;
    }

    public BigDecimal getCatchBullCntPercent() {
        return catchBullCntPercent;
    }

    public void setCatchBullCntPercent(BigDecimal catchBullCntPercent) {
        this.catchBullCntPercent = catchBullCntPercent;
    }

    public String getCatchBullMaxpName() {
        return catchBullMaxpName;
    }

    public void setCatchBullMaxpName(String catchBullMaxpName) {
        this.catchBullMaxpName = catchBullMaxpName;
    }

    public BigDecimal getCatchBullMaxpChg() {
        return catchBullMaxpChg;
    }

    public void setCatchBullMaxpChg(BigDecimal catchBullMaxpChg) {
        this.catchBullMaxpChg = catchBullMaxpChg;
    }

    public BigDecimal getCatchBullMaxpProfit() {
        return catchBullMaxpProfit;
    }

    public void setCatchBullMaxpProfit(BigDecimal catchBullMaxpProfit) {
        this.catchBullMaxpProfit = catchBullMaxpProfit;
    }

    public Long getHoldEtfCnt() {
        return holdEtfCnt;
    }

    public void setHoldEtfCnt(Long holdEtfCnt) {
        this.holdEtfCnt = holdEtfCnt;
    }

    public BigDecimal getHoldEtfProfit() {
        return holdEtfProfit;
    }

    public void setHoldEtfProfit(BigDecimal holdEtfProfit) {
        this.holdEtfProfit = holdEtfProfit;
    }

    public String getHoldEtfMaxpName() {
        return holdEtfMaxpName;
    }

    public void setHoldEtfMaxpName(String holdEtfMaxpName) {
        this.holdEtfMaxpName = holdEtfMaxpName;
    }

    public BigDecimal getHoldEtfMaxpProfit() {
        return holdEtfMaxpProfit;
    }

    public void setHoldEtfMaxpProfit(BigDecimal holdEtfMaxpProfit) {
        this.holdEtfMaxpProfit = holdEtfMaxpProfit;
    }

    public BigDecimal getBullProfit() {
        return bullProfit;
    }

    public void setBullProfit(BigDecimal bullProfit) {
        this.bullProfit = bullProfit;
    }

    public BigDecimal getBullProfitRate() {
        return bullProfitRate;
    }

    public void setBullProfitRate(BigDecimal bullProfitRate) {
        this.bullProfitRate = bullProfitRate;
    }

    public BigDecimal getBullProfitPercent() {
        return bullProfitPercent;
    }

    public void setBullProfitPercent(BigDecimal bullProfitPercent) {
        this.bullProfitPercent = bullProfitPercent;
    }

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }
}
