<?mapper.xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- 开启mybatis二级缓存
    <settings>
        <setting name="cacheEnabled" value="true"/>
    </settings>
    -->
<!--    <settings>-->
<!--        &lt;!&ndash; 打印查询语句 &ndash;&gt;-->
<!--        <setting name="logImpl" value="STDOUT_LOGGING" />-->
<!--    </settings>-->
    <!-- 别名列表 -->
    <typeAliases>
        <!--  数据同步 实体别名-->
        <typeAlias alias="as_hgtMatch" type="com.eastmoney.common.entity.HgtMatch"/>
        <typeAlias alias="as_logAsset" type="com.eastmoney.common.entity.LogAsset"/>
        <typeAlias alias="as_logBankTran" type="com.eastmoney.common.entity.LogBankTran"/>
        <typeAlias alias="as_match" type="com.eastmoney.common.entity.Match"/>
        <typeAlias alias="as_ofAsset" type="com.eastmoney.common.entity.OfAsset"/>
        <typeAlias alias="as_stkAsset" type="com.eastmoney.common.entity.StkAsset"/>
        <typeAlias alias="as_sysConfig" type="com.eastmoney.common.entity.SysConfig"/>
        <!--  系统控制 实体别名-->
        <!--  数据计算 实体别名-->
        <typeAlias alias="as_assetHis" type="com.eastmoney.common.entity.cal.AssetHis"/>
        <typeAlias alias="as_assetNew" type="com.eastmoney.common.entity.cal.AssetNew"/>
        <typeAlias alias="as_positionProfit" type="com.eastmoney.common.entity.cal.PositionProfit"/>
        <typeAlias alias="as_positionSection" type="com.eastmoney.common.entity.cal.PositionSection"/>
        <typeAlias alias="as_profitDay" type="com.eastmoney.common.entity.cal.ProfitDay"/>
        <typeAlias alias="as_profitRateContrast" type="com.eastmoney.common.entity.cal.ProfitRateContrast"/>
        <typeAlias alias="as_profitRateContrastChart" type="com.eastmoney.common.entity.cal.ProfitRateContrastChart"/>
        <typeAlias alias="as_profitRateDay" type="com.eastmoney.common.entity.cal.ProfitRateDay"/>
        <typeAlias alias="as_profitRateSection" type="com.eastmoney.common.entity.cal.ProfitRateSection"/>
        <typeAlias alias="as_profitRateStat" type="com.eastmoney.common.entity.cal.ProfitRateStat"/>
        <typeAlias alias="as_profitSection" type="com.eastmoney.common.entity.cal.ProfitSection"/>
        <typeAlias alias="as_profitStat" type="com.eastmoney.common.entity.cal.ProfitStat"/>
        <!-- 数据计算 值对象-->
        <typeAlias alias="as_stkTrade" type="com.eastmoney.common.entity.cal.StkTrade"/>
        <!--可取 实体别名-->
        <typeAlias alias="as_stkPrice" type="com.eastmoney.common.entity.StkPrice"/>
        <typeAlias alias="as_exchangeRate" type="com.eastmoney.common.entity.HgtExchangeRate"/>

        <typeAlias alias="as_calDigest" type="com.eastmoney.common.entity.cal.CalDigestDO"/>
        <typeAlias alias="as_profitRateSectionRank" type="com.eastmoney.common.entity.cal.ProfitRateSectionRank"/>
        <typeAlias alias="as_hgtTradeDay" type="com.eastmoney.common.entity.HgtTradeDayDO"/>
        <typeAlias alias="as_ipoTradeBill" type="com.eastmoney.common.entity.cal.IPOTradeBill"/>
        <typeAlias alias="as_positionProfitBO" type="com.eastmoney.common.entity.cal.PositionProfitBO"/>
        <typeAlias alias="as_coreConfig" type="com.eastmoney.common.entity.CoreConfig"/>

        <typeAlias alias="as_secProfitDay" type="com.eastmoney.common.entity.cal.SecProfitDayDO"/>
    </typeAliases>

</configuration>
