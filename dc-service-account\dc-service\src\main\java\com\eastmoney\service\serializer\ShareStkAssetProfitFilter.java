package com.eastmoney.service.serializer;

import com.eastmoney.common.serializer.AbstractSerializeFilter;
import com.google.common.collect.ImmutableSet;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class ShareStkAssetProfitFilter extends AbstractSerializeFilter {
    @Override
    protected Collection<String> genIncludes() {
        return ImmutableSet.of(
                "fundAll","fundMktVal","fundAvl","fundBal","fundFrz","moneyType","dayProfit","maxDraw","fundType","sumIncome",
                "positionInfoList","stkType","stkName","stkCode","stkQty","costPrice","income","publishName","price","mktVal","market","otcAsset",
                "holdDays", "labels", "positionRate", "fundAllWithOtc", "fundIaAsset", "dayProfitRate","profitRate", "expandNameAbbr"
        );
    }
}
