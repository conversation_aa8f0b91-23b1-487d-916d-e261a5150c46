package com.eastmoney.common.entity.cal.dw.bill;

/**
 * @Project dc-service-account
 * @Description 单日交易次数最多-----2023年账单新增表,由数据中心提供
 * <AUTHOR>
 * @Date 2023/11/17 14:00
 * @Version 1.0
 */
public class BdTradeBillMostTradeDO {
    /**
     * 单日交易次数最多的日期
     */
    private Integer mostTradesDate;
    /**
     * 单日交易次数最多的次数
     */
    private Long mostTradesCnt;

    /**
     * 2024全年单日交易次数最多的日期是否在09/24-10/11疯牛期间
     * 2024年账单-新增（数据中心提供）
     */
    private Integer bullMostTradesDateFlag;

    public Integer getMostTradesDate() {
        return mostTradesDate;
    }

    public void setMostTradesDate(Integer mostTradesDate) {
        this.mostTradesDate = mostTradesDate;
    }

    public Long getMostTradesCnt() {
        return mostTradesCnt;
    }

    public void setMostTradesCnt(Long mostTradesCnt) {
        this.mostTradesCnt = mostTradesCnt;
    }

    public Integer getBullMostTradesDateFlag() {
        return bullMostTradesDateFlag;
    }

    public void setBullMostTradesDateFlag(Integer bullMostTradesDateFlag) {
        this.bullMostTradesDateFlag = bullMostTradesDateFlag;
    }
}
