package com.eastmoney.service.handler;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.PositionInfo;
import com.eastmoney.common.entity.QryFund;
import com.eastmoney.common.entity.RealAsset;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitSection;
import com.eastmoney.common.model.DateRange;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommConstants;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.service.CommonService;
import com.eastmoney.service.service.asset.base.AssetServiceRealTimeImpl;
import com.eastmoney.service.service.profit.list.AssetDayListServiceFacade;
import com.eastmoney.service.service.profit.list.ProfitDayListServiceFacade;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.eastmoney.common.util.ArithUtil.notNull;

/**
 * Created by Administrator on 2017/5/25.
 */
@Service
public class FundAssetHandler {
    private static final String CAL_STK_ASSET_FLAG = "0";
    @Resource(name = "assetServiceRealTime")
    private AssetServiceRealTimeImpl assetServiceRealTime;
    @Resource(name = "profitDayListServiceFacade")
    private ProfitDayListServiceFacade profitDayListServiceFacade;
    @Autowired
    private StkAssetService stkAssetService;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private ProfitHandler profitHandler;
    @Autowired
    private TradeDateDao tradeDateDao;
    @Resource(name = "assetDayListServiceFacade")
    private AssetDayListServiceFacade assetDayListServiceFacade;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    /**
     * 资产查询接口
     * flag  是否获取最大可取 接口请求时传入""
     * flag1 是否计算当日参考盈亏 接口请求时传入"0"
     * flag2 是否根据持仓计算持仓收益 接口请求时传入"0"
     * stockFlag 是否只计算沪深A股的收益 接口请求传入"0"
     * moneyType 接口请求传入"0"
     *
     * @param params
     * @return
     */
    public QryFund getRealTimeAsset(Map<String, Object> params) {
        //总资产信息
        List<QryFund> qryFunds = assetServiceRealTime.getAssetRealTime(params, false);
        if (qryFunds.size() == 0) {
            return null;
        }
        QryFund qryFund = qryFunds.get(0);

        //获取最大可取：三合一存过会返回 maxDraw
        String moneyType = CommonUtil.convert(params.get("moneyType"), String.class);
        params.put("startDate", DateUtil.getCuryyyyMMdd());
        params.put("endDate", DateUtil.getCuryyyyMMdd());
        List<ProfitDay> profitDay = profitDayListServiceFacade.getDayProfitList(params);
        if (!CollectionUtils.isEmpty(profitDay)) {
            qryFund.dayProfit = profitDay.stream()
                    .reduce(0.0, (profit, dayStkProfit) -> ArithUtil.add(profit, dayStkProfit.getProfit()), ArithUtil::add);
        }

        //持仓
        String calStkAssetFlag = CommonUtil.convert(params.get("flag2"), String.class);
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        if (CAL_STK_ASSET_FLAG.equals(calStkAssetFlag)) {
            List<PositionInfo> positionInfoList = qryFund.getStkInfoList();
            if (positionInfoList.size() > 0) {
                //计算持仓收益
                stkAssetService.calPositionIncome(positionInfoList, moneyType, fundId, notNull(qryFund.getFundAllWithOtc()));
                for (PositionInfo pstInfo : positionInfoList) {
                    qryFund.sumIncome = ArithUtil.add(qryFund.sumIncome, pstInfo.getIncome());
                    pstInfo.setCorResCode(bseCodeAlterService.getCodeAlterXsbAndBjs(pstInfo.stkCode, pstInfo.market));
                }
                stkAssetService.setExtInfo(positionInfoList, fundId);
                // 计算总仓位
                double allPositionRate = positionInfoList.stream()
                        .map(PositionInfo::getPositionRate)
                        .reduce(0.0, ArithUtil::add);
                qryFund.setPositionRate(allPositionRate);
            }
            qryFund.setPositionInfoList(positionInfoList);
        }
        qryFund.setStkInfoList(Collections.emptyList());
        return qryFund;
    }


    /**
     * 总资产走势查询
     * @param params
     * @return
     */
    public List<AssetDay> getAssetDayTrend(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        int today = DateUtil.getCuryyyyMMddInteger();
        String unit = CommonUtil.convert(params.get("unit"), String.class);

        //确定账户分析数据计算到了哪天
        int startDate;
        int endDate;
        if (Objects.equals(DateUnitEnum.ALL.getValue(), unit)) {
            startDate = assetNew.getStartDate();
            endDate = assetNew.getBizDate();
        } else {
            ProfitSection profitSection = profitHandler.getProfitSection(params);
            if (profitSection == null) {
                return null;
            }
            startDate = profitSection.getIndexDate();
            endDate = tradeDateDao.getNextMarketDay(profitSection.getBakBizDate());
        }

        if (unit.equals(DateUnitEnum.MONTH.getValue()) || unit.equals(DateUnitEnum.WEEK.getValue()) || unit.equals(DateUnitEnum.YEAR.getValue())) {
            DateRange dateRange = CommonUtil.getDateRange(unit);
            //如果大于说明已经跨月或跨周，使用新的起止时间
            if (dateRange.getStartDate() > startDate) {
                startDate = dateRange.getStartDate();
                endDate = dateRange.getEndDate();
            }
        }
        //查询的开始时间不得小于首次拥有资产时间
        if (unit.equals(DateUnitEnum.ALL.getValue()) || startDate < assetNew.getStartDate()) {
            startDate = assetNew.getStartDate();

            //至今区间startDate小于设置的日期,使用每个月最后一天的总资产
            if (unit.equals(DateUnitEnum.ALL.getValue()) && startDate < CommConstants.calProfitRateStartDate()) {
                params.put("calMonthAsset", true);
                params.put("settleDate", assetNew.getBizDate());
            }
        }

        if (today > endDate) {
            endDate = today;
        }

        params.put("assetStartDate", assetNew.getStartDate());
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("calRealTimeAsset", true);

        return assetDayListServiceFacade.getAssetDayList(params);
    }

    /**
     * 自定义区间总资产走势查询
     *
     * @param params
     * @return
     */
    public List<AssetDay> getAssetDayTrendCustomize(Map<String, Object> params) {
        if (!commonService.formatCustomizeQueryTime(params)) {
            return null;
        }

        Integer settleDate = CommonUtil.convert(params.get("settleDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);
        if (endDate > settleDate) {
            params.put("calRealTimeAsset", true);
        }

        return assetDayListServiceFacade.getAssetDayList(params);
    }

    public RealAsset getOnlyRealTimeAsset(Map<String, Object> params){
        RealAsset realAsset = new RealAsset();
        //总资产信息
        List<QryFund> qryFunds = assetServiceRealTime.getAssetRealTime(params, false);
        if (CollectionUtils.isNotEmpty(qryFunds)) {
            realAsset.of(qryFunds.get(0));
        }
        return realAsset;
    }
}
