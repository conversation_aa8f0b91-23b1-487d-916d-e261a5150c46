package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.HgtExchangeRate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/11/20
 */
@Repository
public interface TiHgtExchangeRateMapper extends BaseMapper<HgtExchangeRate, String> {
    List<HgtExchangeRate> getLastestExchangeRate(Map<String, Object> params);

    List<HgtExchangeRate> getRateListByBizdate(Map<String, Object> params);
    /**
     * 获取最近日期的最新的汇率数据
     *
     * @param params
     * @return
     */
    List<HgtExchangeRate> getLatestRateList(Map<String, Object> params);

}
