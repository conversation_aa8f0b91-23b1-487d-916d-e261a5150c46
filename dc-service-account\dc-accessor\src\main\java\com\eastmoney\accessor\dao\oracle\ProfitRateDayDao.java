
package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.ProfitRateDay;

/**
 * Created by xiaoyongyong on 2016/7/19.
 * ProfitRateDayService
 */
public interface ProfitRateDayDao extends IBaseDao<ProfitRateDay, Long> {
    /**
     * 计算区间收益率
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    Double getSectionProfitRate(Long fundId,Integer startDate,Integer endDate);
}



