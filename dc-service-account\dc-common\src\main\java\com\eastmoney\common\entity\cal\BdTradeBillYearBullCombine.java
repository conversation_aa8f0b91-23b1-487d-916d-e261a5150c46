package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearBullProfitDO;
import com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearBullTradeDO;
import com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearHoldDO;
import com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearTProfitDO;

/**
 * 牛市指标集合
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearBullCombine {
    private BdTradeBillYearBullProfitDO bullProfitInfo;
    private BdTradeBillYearBullTradeDO bullTradeInfo;
    private BdTradeBillYearHoldDO holdInfo;
    private BdTradeBillYearTProfitDO tProfitInfo;

    public BdTradeBillYearBullProfitDO getBullProfitInfo() {
        return bullProfitInfo;
    }

    public void setBullProfitInfo(BdTradeBillYearBullProfitDO bullProfitInfo) {
        this.bullProfitInfo = bullProfitInfo;
    }

    public BdTradeBillYearBullTradeDO getBullTradeInfo() {
        return bullTradeInfo;
    }

    public void setBullTradeInfo(BdTradeBillYearBullTradeDO bullTradeInfo) {
        this.bullTradeInfo = bullTradeInfo;
    }

    public BdTradeBillYearHoldDO getHoldInfo() {
        return holdInfo;
    }

    public void setHoldInfo(BdTradeBillYearHoldDO holdInfo) {
        this.holdInfo = holdInfo;
    }

    public BdTradeBillYearTProfitDO gettProfitInfo() {
        return tProfitInfo;
    }

    public void settProfitInfo(BdTradeBillYearTProfitDO tProfitInfo) {
        this.tProfitInfo = tProfitInfo;
    }
}
