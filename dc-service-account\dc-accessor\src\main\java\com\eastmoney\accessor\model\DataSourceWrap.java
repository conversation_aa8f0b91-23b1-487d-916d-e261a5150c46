package com.eastmoney.accessor.model;

import java.io.Serializable;

/**
 * Created on 2016/4/2
 *
 * <AUTHOR>
 */
public class DataSourceWrap implements Serializable {
    private static final long serialVersionUID = -7928535958241244378L;
    private String dbType;/*数据库类型 oracle sqlServer mysql等*/
    private String dataSourceId;/*数据源id*/
    private String flag;/*区分run库和history库*/
    private Integer serverId;/*机器编号*/

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(String dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }
}
