package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.StockCodeAlterMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.StockCodeAlter;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("stockCodeAlterDao")
public class StockCodeAlterDaoImpl extends BaseDao<StockCodeAlterMapper, StockCodeAlter, Integer> implements StockCodeAlterDao {


    @Override
    public List<StockCodeAlter> getStockCodeAlterList(Map<String, Object> params) {
        return getMapper().getStockCodeAlterList(params);
    }
}
