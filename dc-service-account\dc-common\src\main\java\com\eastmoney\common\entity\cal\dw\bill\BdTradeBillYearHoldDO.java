package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 累计持仓
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillYearHoldDO {
    // 2024累计持有过的个股数量（持仓+清仓）
    private Long tradesStockCnt;
    // 2024累计持有过的个股总数中盈利的数量
    private Long profitStockCnt;
    // 投资胜率=全年盈利个股/全年累计持清仓个股
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal stockWinRate;
    // 当天是否有持仓 1-是 0-否
    private Integer currentHoldFlag;
    public Long getTradesStockCnt() {
        return tradesStockCnt;
    }

    public void setTradesStockCnt(Long tradesStockCnt) {
        this.tradesStockCnt = tradesStockCnt;
    }

    public Long getProfitStockCnt() {
        return profitStockCnt;
    }

    public void setProfitStockCnt(Long profitStockCnt) {
        this.profitStockCnt = profitStockCnt;
    }

    public BigDecimal getStockWinRate() {
        return stockWinRate;
    }

    public void setStockWinRate(BigDecimal stockWinRate) {
        this.stockWinRate = stockWinRate;
    }

    public Integer getCurrentHoldFlag() {
        return currentHoldFlag;
    }

    public void setCurrentHoldFlag(Integer currentHoldFlag) {
        this.currentHoldFlag = currentHoldFlag;
    }
}
