
package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitStat;

import java.util.List;
import java.util.Map;

/**
 * Created by xiaoyongyong on 2016/7/19.
 * ProfitDayService
 */
public interface ProfitDayDao extends IBaseDao<ProfitDay, Long> {
    List<ProfitStat> getProfitStatList(Map<String, Object> params);

    Double getSumProfit(Map<String, Object> params);

    Double getSumProfit(Long fundId,Integer startDate,Integer endDate);
}



