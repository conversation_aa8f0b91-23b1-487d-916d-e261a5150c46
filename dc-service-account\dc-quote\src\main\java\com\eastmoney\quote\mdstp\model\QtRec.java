package com.eastmoney.quote.mdstp.model;/**
 * Created by 1 on 16-10-24.
 */

import java.util.List;

/**
 * Created on 16-10-24
 *
 * <AUTHOR>
 */
public class QtRec extends Rec{
    // 股票唯一标识
    private int dwStockId;
    private String code;
    // 类型
    private byte type;
    // 流通股数
    private long xLTG;
    // 总发行量
    private long xZFX;
    // 涨停价
    private int dwPriceZT;
    // 跌停价
    private int dwPriceDT;
    // 两日前收盘价
    private int dw2Close;
    // 昨持仓量 (仅Type = 168,169,173,174有该字段)
    private int xPreOpenInterest;
    // 昨结算   (仅Type = 168,169,173,174有该字段
    private int dwPreSettlementPrice;
    // 最高价
    private int dwHigh;
    // 最低价
    private int dwLow;
    // 成交量
    private long xVolume;
    // 成交额
    private double xAmount;
    // 成交笔数
    private int dwTradeNum;
    // 买盘个数
    private byte cNumBuy;
    // 卖盘个数
    private byte cNumSell;
    // 买卖盘信息
    private List<BuySellInfo> buySellInfos;
    // 委托买入均价
    private int dwPBuy;
    // 委托买入总量
    private long xVBuy;
    // 委托卖出均价
    private int dwPSell;
    // 委托买入总量
    private long xVSell;
    // 外盘
    private long xWaiPan;
    // 现手
    private long xCurVol;
    // 现手方向
    private byte cCurVol;
    // 5分钟前的价格
    private int dwPrice5;
    // 理论价格
    private int dwNorminal;
    // 集合竞价
    private byte cVirtual;
    // 集合竞价成交额
    private double xVirtualAmt;

    private byte cFundFlowFlag;
    // (仅m_cFundflowFlag = 1 有该字段)
    private List<FundFlow> fundFlows;

    // 持仓量变化 (仅Type = 168,169有该字段)
    private long xCurOI;
    // 持仓量 (仅Type = 168,169有该字段)
    private long xOpenInterest;
    // 今结算 (仅Type = 168,169有该字段)
    private int dwSettlementPrice;

    public int getDwStockId() {
        return dwStockId;
    }

    public void setDwStockId(int dwStockId) {
        this.dwStockId = dwStockId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    public long getxLTG() {
        return xLTG;
    }

    public void setxLTG(long xLTG) {
        this.xLTG = xLTG;
    }

    public long getxZFX() {
        return xZFX;
    }

    public void setxZFX(long xZFX) {
        this.xZFX = xZFX;
    }

    public int getDwPriceZT() {
        return dwPriceZT;
    }

    public void setDwPriceZT(int dwPriceZT) {
        this.dwPriceZT = dwPriceZT;
    }

    public int getDwPriceDT() {
        return dwPriceDT;
    }

    public void setDwPriceDT(int dwPriceDT) {
        this.dwPriceDT = dwPriceDT;
    }

    public int getxPreOpenInterest() {
        return xPreOpenInterest;
    }

    public void setxPreOpenInterest(int xPreOpenInterest) {
        this.xPreOpenInterest = xPreOpenInterest;
    }

    public int getDwPreSettlementPrice() {
        return dwPreSettlementPrice;
    }

    public void setDwPreSettlementPrice(int dwPreSettlementPrice) {
        this.dwPreSettlementPrice = dwPreSettlementPrice;
    }

    public int getDwHigh() {
        return dwHigh;
    }

    public void setDwHigh(int dwHigh) {
        this.dwHigh = dwHigh;
    }

    public int getDwLow() {
        return dwLow;
    }

    public void setDwLow(int dwLow) {
        this.dwLow = dwLow;
    }

    public long getxVolume() {
        return xVolume;
    }

    public void setxVolume(long xVolume) {
        this.xVolume = xVolume;
    }

    public double getxAmount() {
        return xAmount;
    }

    public void setxAmount(double xAmount) {
        this.xAmount = xAmount;
    }

    public int getDwTradeNum() {
        return dwTradeNum;
    }

    public void setDwTradeNum(int dwTradeNum) {
        this.dwTradeNum = dwTradeNum;
    }

    public byte getcNumBuy() {
        return cNumBuy;
    }

    public void setcNumBuy(byte cNumBuy) {
        this.cNumBuy = cNumBuy;
    }

    public byte getcNumSell() {
        return cNumSell;
    }

    public void setcNumSell(byte cNumSell) {
        this.cNumSell = cNumSell;
    }

    public int getDwPBuy() {
        return dwPBuy;
    }

    public void setDwPBuy(int dwPBuy) {
        this.dwPBuy = dwPBuy;
    }

    public long getxVBuy() {
        return xVBuy;
    }

    public void setxVBuy(long xVBuy) {
        this.xVBuy = xVBuy;
    }

    public int getDwPSell() {
        return dwPSell;
    }

    public void setDwPSell(int dwPSell) {
        this.dwPSell = dwPSell;
    }

    public long getxVSell() {
        return xVSell;
    }

    public void setxVSell(long xVSell) {
        this.xVSell = xVSell;
    }

    public long getxWaiPan() {
        return xWaiPan;
    }

    public void setxWaiPan(long xWaiPan) {
        this.xWaiPan = xWaiPan;
    }

    public long getxCurVol() {
        return xCurVol;
    }

    public void setxCurVol(long xCurVol) {
        this.xCurVol = xCurVol;
    }

    public byte getcCurVol() {
        return cCurVol;
    }

    public void setcCurVol(byte cCurVol) {
        this.cCurVol = cCurVol;
    }

    public int getDwPrice5() {
        return dwPrice5;
    }

    public void setDwPrice5(int dwPrice5) {
        this.dwPrice5 = dwPrice5;
    }

    public int getDwNorminal() {
        return dwNorminal;
    }

    public void setDwNorminal(int dwNorminal) {
        this.dwNorminal = dwNorminal;
    }

    public byte getcVirtual() {
        return cVirtual;
    }

    public void setcVirtual(byte cVirtual) {
        this.cVirtual = cVirtual;
    }

    public double getxVirtualAmt() {
        return xVirtualAmt;
    }

    public void setxVirtualAmt(double xVirtualAmt) {
        this.xVirtualAmt = xVirtualAmt;
    }

    public byte getcFundFlowFlag() {
        return cFundFlowFlag;
    }

    public void setcFundFlowFlag(byte cFundFlowFlag) {
        this.cFundFlowFlag = cFundFlowFlag;
    }

    public long getxCurOI() {
        return xCurOI;
    }

    public void setxCurOI(long xCurOI) {
        this.xCurOI = xCurOI;
    }

    public long getxOpenInterest() {
        return xOpenInterest;
    }

    public void setxOpenInterest(long xOpenInterest) {
        this.xOpenInterest = xOpenInterest;
    }

    public int getDwSettlementPrice() {
        return dwSettlementPrice;
    }

    public void setDwSettlementPrice(int dwSettlementPrice) {
        this.dwSettlementPrice = dwSettlementPrice;
    }

    public List<BuySellInfo> getBuySellInfos() {
        return buySellInfos;
    }

    public void setBuySellInfos(List<BuySellInfo> buySellInfos) {
        this.buySellInfos = buySellInfos;
    }

    public List<FundFlow> getFundFlows() {
        return fundFlows;
    }

    public void setFundFlows(List<FundFlow> fundFlows) {
        this.fundFlows = fundFlows;
    }

    public int getDw2Close() {
        return dw2Close;
    }

    public void setDw2Close(int dw2Close) {
        this.dw2Close = dw2Close;
    }
}
