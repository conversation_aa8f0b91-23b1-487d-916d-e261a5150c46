package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggVipCustomersMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/26 11:06
 */
@Service("vipCustomersDao")
@SqlServerTarget()
public class OggVipCustomersDaoImpl implements OggVipCustomersDao {

    @Autowired
    private OggVipCustomersMapper oggVipCustomersMapper;

    @Override
    public Set<Long> selectInterceptFundIds(Map<String, Object> params) {
        return oggVipCustomersMapper.selectInterceptFundIds(params);
    }
}
