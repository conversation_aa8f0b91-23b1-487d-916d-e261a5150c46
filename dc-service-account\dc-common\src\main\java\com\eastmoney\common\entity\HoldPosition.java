package com.eastmoney.common.entity;

import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/22 14:26
 */
public class HoldPosition {

    private Long fundId;

    private Integer serverId;

    private String secuId;

    private String stkCode;

    private String market;

    private Integer startDate;

    private Integer clearStartDate;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getClearStartDate() {
        return clearStartDate;
    }

    public void setClearStartDate(Integer clearStartDate) {
        this.clearStartDate = clearStartDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HoldPosition)) return false;
        HoldPosition that = (HoldPosition) o;
        return Objects.equals(fundId, that.fundId) && Objects.equals(serverId, that.serverId) && Objects.equals(secuId, that.secuId) && Objects.equals(stkCode, that.stkCode) && Objects.equals(market, that.market) && Objects.equals(startDate, that.startDate) && Objects.equals(clearStartDate, that.clearStartDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fundId, serverId, secuId, stkCode, market, startDate, clearStartDate);
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", HoldPosition.class.getSimpleName() + "[", "]")
                .add("fundId=" + fundId)
                .add("serverId=" + serverId)
                .add("secuId='" + secuId + "'")
                .add("stkCode='" + stkCode + "'")
                .add("market='" + market + "'")
                .add("startDate=" + startDate)
                .add("clearDate=" + clearStartDate)
                .toString();
    }
}
