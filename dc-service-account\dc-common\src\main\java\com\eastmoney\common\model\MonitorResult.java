package com.eastmoney.common.model;

import com.alibaba.fastjson.JSON;
import com.eastmoney.common.util.DateUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/6/22.
 */
public class MonitorResult {
    private String metric;
    private Object message;
    private Object sourceStore;
    private Object targetStore;
    private int status;
    public final static int STATUS_NORMAL = 0;
    public final static int STATUS_EXCEPTION = -1;
    private String timestamp;

    public static MonitorResult createNormalResult(Object message) {
        return createMonitorResult(STATUS_NORMAL,message);
    }

    public static MonitorResult createExceptionResult(Object message) {
        return createMonitorResult(STATUS_EXCEPTION,message);
    }
    public static MonitorResult createMonitorResult(int status,Object message){
        MonitorResult result = new MonitorResult();
        result.setStatus(status);
        result.setMessage(message);
        result.setTimestamp(DateUtil.getCurDateTime());
        return result;
    }
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }

    public String getMetric() {
        return metric;
    }

    public void setMetric(String metric) {
        this.metric = metric;
    }


    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Object getSourceStore() {
        return sourceStore;
    }

    public void setSourceStore(Object sourceStore) {
        this.sourceStore = sourceStore;
    }

    public Object getTargetStore() {
        return targetStore;
    }

    public void setTargetStore(Object targetStore) {
        this.targetStore = targetStore;
    }

    public static void main(String[] args) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("name", "sun");
        msg.put("age", 18);
        MonitorResult result = createNormalResult(msg);
        System.out.println(JSON.toJSONString(result));
    }
}
