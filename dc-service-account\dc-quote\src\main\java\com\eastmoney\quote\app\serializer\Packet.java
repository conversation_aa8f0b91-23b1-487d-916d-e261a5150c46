package com.eastmoney.quote.app.serializer;



/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-10-20.
 */
public class Packet {

    public static final byte pHead = '{';

    public static final byte pTail = '}';

    public short type; //包类型

    public byte attr1; //包属性 固定输出两个字节

    public byte attr2; //包属性 固定输出两个字节

    public short length; //正文长度

    public static byte getpHead() {
        return pHead;
    }

    public short getType() {
        return type;
    }

    public void setType(short type) {
        this.type = type;
    }

    public byte getAttr1() {
        return attr1;
    }

    public void setAttr1(byte attr1) {
        this.attr1 = attr1;
    }

    public byte getAttr2() {
        return attr2;
    }

    public void setAttr2(byte attr2) {
        this.attr2 = attr2;
    }

    public short getLength() {
        return length;
    }

    public void setLength(short length) {
        this.length = length;
    }

    public static byte getpTail() {
        return pTail;
    }

}
