package com.eastmoney.common.sysEnum;

/**
 * Created on 2016/4/6
 * 调度任务状态枚举
 *
 * <AUTHOR>
 */
public enum StatusEnum {
    INIT(0),/*初始化*/
    RUN(1),/*运行中*/
    FINISH(2),/*任务成功*/
    TIMEOUT(3),/*任务失败：超时中断*/
    TIMEOUT_FINISH(9),/*任务成功：超时成功*/
    FAIL(4),/*任务失败:同步数据执行中程序异常*/
    EXIST(5),/*任务失败：任务已存在*/
    INIT_HANDLE_INSTANCE(6),/*任务失败：初始handle实例失败*/
    UPDATE_TASK_EXCEPTION(7),/*任务失败:同步数据执行中程序异常,更新任务状态失败异常*/
    SAME_TASK_EXECUTE(8);/*任务失败:有同样的任务正在执行*/
    private Integer value;

    StatusEnum(Integer i) {
        this.value = i;
    }

    public Integer getValue() {
        return value;
    }

    public static StatusEnum valueOf(int value){
        switch (value){
            case 0:
                return INIT;
            case 1:
                return RUN;
            case 2:
                return FINISH;
            case 3:
                return TIMEOUT;
            case 4:
                return FAIL;
            case 5:
                return EXIST;
            case 6:
                return INIT_HANDLE_INSTANCE;
            case 7:
                return UPDATE_TASK_EXCEPTION;
            case 8:
                return SAME_TASK_EXECUTE;
            default:
                return INIT;
        }
    }
}
