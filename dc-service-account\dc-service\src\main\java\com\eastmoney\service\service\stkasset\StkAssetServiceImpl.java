package com.eastmoney.service.service.stkasset;

import com.eastmoney.accessor.dao.oracle.StkAssetDao;
import com.eastmoney.accessor.dao.oracle.TpseStSechyreDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.enums.MoneyTypeEnum;
import com.eastmoney.accessor.enums.StkFcEnum;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.*;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.*;
import com.eastmoney.service.service.StockService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.quote.QuoteService;
import com.eastmoney.service.util.BusinessUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.eastmoney.common.util.ArithUtil.*;
import static java.util.Optional.ofNullable;

/**
 * Created on 2020/8/12-14:38.
 *
 * <AUTHOR>
 */
@Service
public class StkAssetServiceImpl implements StkAssetService {
    private static final String SECU_ID = "secuId";
    private static final String STK_CODE = "stkCode";
    private static final String QRY_FLAG = "qryFlag";
    private static final String COUNT = "count";
    private static final String POST_STR = "postStr";
    @Autowired
    private TpseStSechyreDao tpseStSechyreDao;
    @Autowired
    private QuoteService quoteService;
    @Autowired
    private StockService stockService;
    @Autowired
    private HoldPositionService holdPositionService;
    @Resource(name = "stkAssetDao")
    private StkAssetDao stkAssetDao;
    @Autowired
    private HgtReferRateService hgtReferRateService;
    @Autowired
    private AssetNewService assetNewService;
    @Autowired
    private MixedConfigService mixedConfigService;
    @Autowired
    private StkPriceCacheService stkPriceCacheService;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    @Override
    public void calPositionIncome(List<PositionInfo> positionInfoList, String moneyType, Long fundId, double fundAll) {
        //获取成本价标识
        Map<String, Object> mixedConfigParams = new HashMap<>(2);
        mixedConfigParams.put("paraId", "cbjbz");
        mixedConfigParams.put("fundId", fundId);
        /**
         *  paraid   paraName     paraValue
         *  cbjbz   成本价标志	 1
         */
        String priceFlag = mixedConfigService.getMixedConfigParaValue(mixedConfigParams, "1");

        Iterator<PositionInfo> iter = positionInfoList.iterator();
        while (iter.hasNext()) {
            PositionInfo pstInfo = iter.next();
            if (!Objects.equals(moneyType, pstInfo.getMoneyType()) || pstInfo.isClear) {
                iter.remove();
                continue;
            }
            //计算成本单价
            if ("0".equals(priceFlag) && pstInfo.buyCost != 0 && pstInfo.stkQty != 0) {
                double costPrice = div(pstInfo.buyCost, pstInfo.stkQty);
                pstInfo.setCostPrice(costPrice);
            }

            pstInfo.stkQty = ArithUtil.add(pstInfo.stkBal, pstInfo.stkBuySale, pstInfo.stkUnComeBuy,
                    ArithUtil.mul(-1, pstInfo.stkUnComeSale)).longValue();

            //SPB-4216, shil, 20150803, 外围接口增加买入均价的输出0.13947
            double costPriceEx = pstInfo.stkQty != 0 ? div(pstInfo.buyCost, pstInfo.stkQty, 3) : 0;
            pstInfo.setCostPriceEx(costPriceEx);
            pstInfo.setClosePrice(pstInfo.getStkPrice().getClosePrice());
            pstInfo.setLastPrice(pstInfo.getStkPrice().getLastPrice());

            //集合竞价期间的虚拟匹配价格调整为昨收价
            //APPAGILE-120248
            Double price = pstInfo.getStkPrice().getPrice();
            double mktVal = notNull(pstInfo.getMktVal());
            double income = 0.0;    //累计盈亏
            double proIncome = 0.0; //参考盈亏

            if (Objects.equals(pstInfo.mtkCalFlag, "1")) {
                if (price > 0.0) {
                    mktVal = mul(pstInfo.stkQty, price);
                    if (BusinessUtil.isGgtMarket(pstInfo.getMarket())) {
                        //根据汇率将港股市值转换为人民币市值
                        //APPAGILE-73382 持仓收益的港股通采用ogg买入结算汇率，与柜台保持一致
                        HgtReferRate hgtReferRateInfo = hgtReferRateService.getOggHgtReferRateService(pstInfo.getFundId(), pstInfo.getMarket());
                        Double hgtReferRate = Objects.nonNull(hgtReferRateInfo) ? hgtReferRateInfo.getSettRate() : 1d;
                        pstInfo.setSettRate(hgtReferRate);
                        mktVal = round(mul(mktVal, hgtReferRate), 2);
                        price = round(mul(price, hgtReferRate), 3);
                    }
                    //Todo proIncome的计算为重复计算：从数据字典上看，这块应该计算的是成本而不是盈亏
                    //ogg存过：costprice = case when (isnull(a.stkbal,0) + isnull(a.stkbuysale,0) + isnull(a.stkuncomebuy,0) - isnull(a.stkuncomesale,0)) = 0 then 0
                    //else a.profitcost/(isnull(a.stkbal,0) + isnull(a.stkbuysale,0) + isnull(a.stkuncomebuy,0) - isnull(a.stkuncomesale,0)) end
                    pstInfo.costPrice = !ArithUtil.eq(pstInfo.stkQty, 0) ? ArithUtil.div(pstInfo.profitCost, pstInfo.stkQty) : 0;
                    //costPrice可能存在精度问题,所以直接用profitCost计算
                    income = round(sub(mul(pstInfo.stkQty, price), pstInfo.profitCost));
                    // APPAGILE-115182 income和proIncome为double，其计算使用ArithUtil
                    // ACCTANAL-106 对个股参考收益结果保留两位小数
                    proIncome = round(sub(mktVal, pstInfo.profitCost));
                    //DATACENTER-3723 如果是港股通，优先使用市值 * 汇率 - 成本价，避免(price * 汇率 - costPrice)*stkqty导致误差
                    if (ArithUtil.eq(income, 0) || BusinessUtil.isGgtMarket(pstInfo.getMarket())) {
                        income = proIncome;
                    }
                } else {
                    if (ArithUtil.ge(pstInfo.getBuyCost(), 0)) {
                        mktVal = pstInfo.getBuyCost();
                    } else {
                        //没有行情，且没有buyCost显示为0
                        mktVal = 0.0;
                    }
                }
            }

            pstInfo.price = price;
            pstInfo.setMktVal(mktVal);
            pstInfo.setIncome(income);
            pstInfo.setProIncome(proIncome);
            //盈亏比例 pstInfo.lastPrice = 0
            double profitRate = 0.0;
            if (pstInfo.costPrice != null && pstInfo.costPrice >= 0.0005 && pstInfo.price != null) {
                //ACCTANAL-106 现价、成本价保留三位小数
                pstInfo.price = ArithUtil.round(pstInfo.price, 3);
                pstInfo.costPrice = ArithUtil.round(pstInfo.costPrice, 3);
                profitRate = ArithUtil.sub(div(pstInfo.price, pstInfo.costPrice, 6), 1);
            }
            pstInfo.setProfitRate(profitRate);
            if (!eq(fundAll, 0.0)) {
                pstInfo.setPositionRate(div(mktVal, fundAll));
            }
        }
        if (positionInfoList.size() > 1) {
            //最后一条保存完整信息
            for (int i = 0; i < positionInfoList.size() - 1; i++) {
                positionInfoList.get(i).setPostStr(null);
            }
        }
    }

    /**
     * APPAGILE-80350
     * 设置一些额外信息 比如持仓天数 行业 标签
     *
     * @param positionInfoList
     * @param fundId
     */
    @Override
    public void setExtInfo(List<PositionInfo> positionInfoList, Long fundId) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        Map<String, Integer> startDateMap = holdPositionService.getHoldPositionStartDate(fundId);
        int today = DateUtil.getCuryyyyMMddInteger();
        for (PositionInfo positionInfo : positionInfoList) {
            if (positionInfo == null) {
                continue;
            }
            String alert830StkCode = bseCodeAlterService.getCodeAlterReverse(positionInfo.getStkCode(), positionInfo.getMarket());
            int startDate = ofNullable(startDateMap.get(positionInfo.getMarket() + "-" + StringUtils.trim(positionInfo.getStkCode())))
                    .orElse(ofNullable(startDateMap.get(positionInfo.getMarket() + "-" + alert830StkCode)).orElse(today));
            positionInfo.setStartDate(startDate);
            // 持仓天数 feature-ACCTANAL-196 修改为自然日
            Integer holdDays = DateUtil.getDiffDay(startDate, today).intValue();
            if (holdDays == 0 && (MarketEnum.SZ_HK.getValue().equals(positionInfo.getMarket()) ||
                    MarketEnum.SH_HK.getValue().equals(positionInfo.getMarket()))) {
                holdDays = null;
            }
            positionInfo.setHoldDays(holdDays);
            // 行业
            String pulishName = tpseStSechyreDao.getPublishName(positionInfo.getStkCode());
            if (StringUtils.isBlank(pulishName)) {
                pulishName = tpseStSechyreDao.getDefaultPublishName();
            }
            positionInfo.setPublishName(pulishName);
            positionInfo.setLabels(getLabels(positionInfo));
        }
    }

    @Override
    public List<StkAsset> getStkAsset(Long fundId, Integer bizDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        AssetNew assetInfo = assetNewService.getAssetInfo(params);
        if (assetInfo != null) {
            params.put("serverId", assetInfo.getServerId());
        }
        params.put("assetDate", bizDate);
        List<String> markets = Arrays.asList(MarketEnum.SH_HK.getValue(), MarketEnum.SZ_HK.getValue());
        params.put("markets", markets);
        return stkAssetDao.selectOne(params);
    }

    @Override
    @RedisCache(keyGenerator = "'jzjy_stkasset_'+ #fundId + '_' + #bizDate")
    public List<StkAsset> getStkAsset(Long fundId, Integer bizDate, Integer serverId) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("fundId", fundId);
        queryParam.put("assetDate", bizDate);
        queryParam.put("moneyType", MoneyTypeEnum.RMB.getValue());
        queryParam.put("serverId", serverId);
        //查询指定日期持仓
        return stkAssetDao.query(queryParam);
    }

    @Override
    public Double getMktVal(StkAsset stkAsset, boolean dayProfitCalFeeFlag) {
        Double lastMktVal = 0.0;
        //期初市值计算=期初份额*昨收价
        long stkQty = ArithUtil.addIgnoreNull(stkAsset.stkBal, stkAsset.stkBuySale, stkAsset.stkUnComeBuy,
                stkAsset.stkUnComeSale == null ? null : -stkAsset.stkUnComeSale);

        HgtReferRate hgtReferRateInfo = hgtReferRateService.getOggHgtReferRateService(stkAsset.getFundId(), stkAsset.getMarket());
        Double settRate = Objects.nonNull(hgtReferRateInfo) ? hgtReferRateInfo.getSettRate() : 1d;

        if (stkQty > 0) {
            String stkCode = stkAsset.getStkCode();
            // PCMP-10028770 【账户分析】北交所存量代码段改造 处理持仓 切换后的第一个交易日 stkAsset为830  柜台和mdstp均是920
            stkCode = bseCodeAlterService.getCodeAlterWithBizDate(stkCode, stkAsset.getMarket(), DateUtil.getCuryyyyMMddInteger());
            List<StkInfo> stkInfoList = Collections.singletonList(new StkInfo(stkCode, stkAsset.getMarket()));
            // 查询stkPrice缓存
            stkInfoList = stkPriceCacheService.updateStkInfoList(stkAsset.getFundId(), stkInfoList);
            //PCMP-10028770 【账户分析】北交所存量代码段改造 柜台行情830 mdstp920  此时开关打开 此处进行切换
            //先转为920进行行情查询
            bseCodeAlterService.transStkInfoCode(stkInfoList.get(0), true);
            StkPrice stkPrice = quoteService.getStkPrice(stkInfoList.get(0));
            if(!stkPrice.isQtPrice) {
                //查不到再转为830进行行情查询
                bseCodeAlterService.transStkInfoCode(stkInfoList.get(0), false);
                stkPrice = quoteService.getStkPrice(stkInfoList.get(0));
            }
            //昨收价
            Double closePrice = stkPrice.getClosePrice();
            //收盘价
            Double lastPrice = stkPrice.getLastPrice() == null ? 0d : stkPrice.getLastPrice();

            if (closePrice != null && lastPrice > 0.0001) {
                if (BusinessUtil.isGgtMarket(stkAsset.getMarket())) {
                    closePrice = ArithUtil.mul(closePrice, settRate);
                }
                lastMktVal = ArithUtil.mul(!dayProfitCalFeeFlag ? closePrice : add(closePrice, stkPrice.getBondIntr()), stkQty);
            } else {
                lastMktVal = stkAsset.getMktVal();
            }
        }
        return lastMktVal;
    }

    /**
     * 股票（除港股通）：所属东财行业
     * 港股通：深港通/沪港通+所属行业（两地均上市个股取A股所属行业）
     * 股转：股转+所属层级（基础层/创新层/精选层）
     * 基金：基金
     * 债券：债券
     * <p>
     * 判断方法：
     * 股票：0-股票（且市场不为5-沪港通、6-深港通）、U-存托凭证、R-创业板、W-科创板、X-创业板CDR
     * 港股通：0-股票且所属市场为5-沪港通、6-深港通
     * 股转：stock.stkFc
     * 基金：o-上证LOF、r-财务分级LOF、L-LOF、E-ETF、h-货币ETF、5-投资基金、i-黄金ETF、g-债券ETF、u-基础设施基金
     * 债券：8-转换债券、C-公司债、e-私募债券、2-企业债券、t-可交换公司债券、1-国债、k-资产支持证券
     *
     * @param positionInfo
     * @return
     */
    private List<String> getLabels(PositionInfo positionInfo) {
        String stkType = positionInfo.getStkType();
        if (stkType == null) {
            stkType = stockService.getStkType(positionInfo.getStkCode(), positionInfo.getMarket());
        }
        String market = positionInfo.getMarket();
        List<String> labels = new ArrayList<>();
        // 首先处理股转数据 兼容北交所的临时修改 后续等柜台的完整逻辑
        String stkLayerInfo = stockService.getStkFc(positionInfo.getStkCode(), positionInfo.getMarket());
        if (StkFcEnum.JCC.getValue().equals(stkLayerInfo)) {
            labels.add("股转");
            labels.add(StkFcEnum.JCC.getName());
        } else if (StkFcEnum.CXC.getValue().equals(stkLayerInfo)) {
            labels.add("股转");
            labels.add(StkFcEnum.CXC.getName());
        } else if (StkFcEnum.JXC.getValue().equals(stkLayerInfo)) {
            String publishName = tpseStSechyreDao.getPublishName(positionInfo.getStkCode());
            if (StringUtils.isBlank(publishName)) {
                publishName = tpseStSechyreDao.getDefaultPublishName();
            }
            labels.add(publishName);
        } else {
            if (stkType != null) {
                switch (stkType) {
                    case "0":
                    case "U":
                    case "R":
                    case "W":
                    case "X":
                        if (MarketEnum.SH_HK.getValue().equals(market)) {
                            labels.add("沪港通");
                        } else if (MarketEnum.SZ_HK.getValue().equals(market)) {
                            labels.add("深港通");
                        }
                        String publishName = tpseStSechyreDao.getPublishName(positionInfo.getStkCode());
                        if (StringUtils.isBlank(publishName)) {
                            publishName = tpseStSechyreDao.getDefaultPublishName();
                        }
                        labels.add(publishName);
                        break;
                    case "o":
                    case "r":
                    case "L":
                    case "h":
                    case "5":
                    case "i":
                    case "g":
                    case "u":
                        labels.add("基金");
                        break;
                    case "E":
                        if (MarketEnum.SH_HK.getValue().equals(market)) {
                            labels.add("沪港通");
                        } else if (MarketEnum.SZ_HK.getValue().equals(market)) {
                            labels.add("深港通");
                        } else {
                            labels.add("基金");
                        }
                        break;
                    case "8":
                    case "C":
                    case "e":
                    case "2":
                    case "t":
                    case "1":
                    case "k":
                        labels.add("债券");
                        break;
                    default:
                        break;
                }
            }
        }
        return labels;
    }


    /**
     * 获取个股期初市值
     *
     * @param fundId
     * @param bizDate
     * @param moneyType
     * @param serverId
     * @param market
     * @param stkCode
     * @param dayProfitExtend
     * @return
     */
    public double getOpenMktVal(Long fundId, Integer bizDate, String moneyType, Integer serverId, String market, String stkCode, DayProfitExtend dayProfitExtend) {
        //上一个交易日的持仓
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("assetDate", bizDate);
        params.put("market", market);
        params.put("stkCode", stkCode);
        params.put("moneyType", moneyType);
        params.put("serverId", serverId);
        bseCodeAlterService.getCodeAlterParams(params);
        List<StkAsset> stkAssets = stkAssetDao.query(params);


        boolean dayProfitCalFeeFlag = nodeConfigService.getDayProfitCalFeeFlag();
        Double lastMktVal = 0.0;
        for (StkAsset stkAsset : stkAssets) {
            lastMktVal = ArithUtil.add(lastMktVal, getMktVal(stkAsset, dayProfitCalFeeFlag));
        }
        return lastMktVal;
    }

}
