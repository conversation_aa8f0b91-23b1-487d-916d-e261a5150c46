package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * 最高日收益
 * 2024年账单-新增（数据中心提供）
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
public class BdTradeBillMaxProfitDO {
    // 2024全年单日最高收益的日期
    private Integer bizDate;
    // 2024全年单日最高收益金额
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal profit;
    // 2024全年单日最高收益的日期是否在09/24-10/11疯牛期间
    private Integer bullHighestProfitDateFlag;


    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public BigDecimal getProfit() {
        return profit;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

    public Integer getBullHighestProfitDateFlag() {
        return bullHighestProfitDateFlag;
    }

    public void setBullHighestProfitDateFlag(Integer bullHighestProfitDateFlag) {
        this.bullHighestProfitDateFlag = bullHighestProfitDateFlag;
    }

}
