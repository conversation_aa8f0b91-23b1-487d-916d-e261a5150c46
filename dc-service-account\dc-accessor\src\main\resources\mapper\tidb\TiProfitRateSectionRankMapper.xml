<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiProfitRateSectionRankMapper">
    <select id="selectByCondition" resultType="as_profitRateSectionRank">
        SELECT
        unit, ifnull(RANKPERCENT, 0) rankPercent, EUTIME, profitRate
        FROM ATCENTER.PROFIT_RATE_SECTION_RANK
        <where>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
            <if test="profitRate != null">
                AND profitRate >= #{profitRate}
            </if>
        </where>
        ORDER BY PROFITRATE

    </select>

</mapper>
