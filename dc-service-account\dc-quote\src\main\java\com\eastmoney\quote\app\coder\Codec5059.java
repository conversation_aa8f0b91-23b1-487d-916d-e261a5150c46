package com.eastmoney.quote.app.coder;/**
 * Created by 1 on 16-10-20.
 */

import com.eastmoney.quote.app.model.DataId5059;
import com.eastmoney.quote.app.model.DataIdRecord5059;
import com.eastmoney.quote.app.model.Input5059;
import com.eastmoney.quote.app.model.Output5059;
import com.eastmoney.quote.app.serializer.Packet;
import io.netty.buffer.ByteBuf;

import java.util.List;

/**
 * Created on 16-10-20
 *
 * <AUTHOR>
 */
public class Codec5059 {

    public void decode(ByteBuf in, List<Object> out, Packet packet) {
        if ((packet.getAttr1() & 0x10)==0) {
            Output5059 res = new Output5059();
            res.setType(packet.getType());
            res.setAttr1(packet.getAttr1());
            res.setAttr2(packet.getAttr2());
            res.setLength(packet.getLength());
            res.setClientID(in.readShort());
            byte reqIDLength = in.readByte();
            res.setReqIDLength(reqIDLength);
            byte[] reqIDs = new byte[reqIDLength];
            for (int i = 0; i < reqIDLength; i++){
                reqIDs[i] = in.readByte();
            }
            res.setReqIDs(reqIDs);
            res.setReqDataTotalNum(in.readShort());
            short returnDataNum = in.readShort();
            res.setReturnDataNum(returnDataNum);
            DataId5059[] dataId5059s = new DataId5059[returnDataNum];
            for (short i = 0; i < returnDataNum; i++) {
                DataId5059 dataId5059 = new DataId5059();
                setAllDataID(dataId5059, in, reqIDs, reqIDLength);
                dataId5059s[i] = dataId5059;
            }
            res.setDataId5059(dataId5059s);
            out.add(res);
        }else{
            Output5059 res = new Output5059();
            res.setType(packet.getType());
            res.setAttr1(packet.getAttr1());
            res.setAttr2(packet.getAttr2());
            res.setLength(packet.getLength());
            res.setClientID(in.readShort());
            res.setReqDataTotalNum(in.readShort());
            short returnDataNum = in.readShort();
            res.setReturnDataNum(returnDataNum);
            DataIdRecord5059[] dataIDRecords = new DataIdRecord5059[returnDataNum];
            for (short i = 0; i < returnDataNum; i++) {
                DataIdRecord5059 dataIdRecord5059 = new DataIdRecord5059();
                byte reqIDLength = in.readByte();
                dataIdRecord5059.setReqIDLength(reqIDLength);
                byte[] reqIDs = new byte[reqIDLength];
                for (int j = 0; j < reqIDLength; j++){
                    reqIDs[j] = in.readByte();
                }
                dataIdRecord5059.setReqIDs(reqIDs);
                setAllDataID(dataIdRecord5059, in, reqIDs, reqIDLength);
                dataIDRecords[i] = dataIdRecord5059;
            }

            res.setDataId5059(dataIDRecords);
        }
    }

    private void setAllDataID(DataId5059 allDataID, ByteBuf in, byte[] reqIDs, byte reqIDLength) {
        for (byte b2 = 0; b2 < reqIDLength; b2++) {
            if (reqIDs[b2] == 1) {
                allDataID.setiPriceLast(in.readInt());
            }
            if (reqIDs[b2] == 2) {
                allDataID.setiPriceHigh(in.readInt());
            }
            if (reqIDs[b2] == 3) {
                allDataID.setiPriceLow(in.readInt());
            }
            if (reqIDs[b2] == 4) {
                allDataID.setiPreClose(in.readInt());
            }
            if (reqIDs[b2] == 5) {
                allDataID.setLiVolume(in.readLong());
            }
            if (reqIDs[b2] == 6) {
                allDataID.setLiAmount(in.readLong());
            }
            if (reqIDs[b2] == 7) {
                allDataID.setLiTotalShares(in.readLong());
            }
            if (reqIDs[b2] == 8) {
                allDataID.setLiNetShare(in.readLong());
            }
            if (reqIDs[b2] == 9) {
                allDataID.setiNetCapital(in.readInt());
            }
            if (reqIDs[b2] == 10) {
                // 代码       NString
                short nsMarketCodeLength = in.readShort();
                allDataID.setNsMarketCodeLength(nsMarketCodeLength);
                byte[] nsMarketCodeContext = new byte[nsMarketCodeLength];
                for (int i = 0; i < nsMarketCodeLength; i++) {
                    nsMarketCodeContext[i] = in.readByte();
                }
                allDataID.setNsMarketCodeContext(nsMarketCodeContext);
                allDataID.setNsMarket(new String(nsMarketCodeContext));
            }
            if (reqIDs[b2] == 11) {
                // 代码       NString
                short nsNameLength = in.readShort();
                allDataID.setNsNameLength(nsNameLength);
                byte[] nsNameContext = new byte[nsNameLength];
                for (int i = 0; i < nsNameLength; i++) {
                    nsNameContext[i] = in.readByte();
                }
                allDataID.setNsNameContext(nsNameContext);
                allDataID.setNsName(new String(nsNameContext));
            }
            if (reqIDs[b2] == 12) {
                allDataID.setcDecimalNum(in.readByte());
            }
            if (reqIDs[b2] == 13) {
                allDataID.setLiDrIEPS(in.readLong());
            }
            if (reqIDs[b2] == 14) {
                allDataID.setcSeason(in.readByte());
            }
            if (reqIDs[b2] == 15) {
                allDataID.setiUpPercent(in.readInt());
            }
            if (reqIDs[b2] == 16) {
                allDataID.setiUpDown(in.readInt());
            }
            if (reqIDs[b2] == 17) {
                allDataID.setiTurnoverRatio(in.readInt());
            }
            if (reqIDs[b2] == 18) {
                allDataID.setUliTotalValue(in.readLong());
            }
            if (reqIDs[b2] == 19) {
                allDataID.setUliCurrentValue(in.readLong());
            }
            if (reqIDs[b2] == 20) {
                allDataID.setLiNetShareEx(in.readLong());
            }
            if (reqIDs[b2] == 21) {
                allDataID.setcStockStatus(in.readByte());
            }

        }
    }


    public void encode(Packet packet, ByteBuf buf) {

        Input5059 req = (Input5059) packet;
        buf.writeShort(req.getClientID());
        buf.writeByte(req.getPushType());
        buf.writeByte(req.getSortID());
        buf.writeByte(req.getSortType());
        buf.writeShort(req.getStartPlace());
        buf.writeShort(req.getReqNum());
        buf.writeByte(req.getReqIdNums());
        buf.writeBytes(req.getReqId());
        buf.writeByte(req.getReqType());
        buf.writeShort(req.getReqDataNum());
        int num = req.getReqDataNum();
        for (int i = 0; i < num; i++) {
            buf.writeShort(req.getReqDataContext()[i].getReqDataLength());
            buf.writeBytes(req.getReqDataContext()[i].getReqDataContext());
        }
        buf.writeByte(req.getpTail());
    }




}
