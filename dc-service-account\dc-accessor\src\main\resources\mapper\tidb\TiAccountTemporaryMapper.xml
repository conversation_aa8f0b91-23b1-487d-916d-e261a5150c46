<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiAccountTemporaryMapper">
    <select id="selectByCondition" resultType="com.eastmoney.common.entity.AccountTemporary">
        SELECT fundid, profit, profitrate, shiftout, shiftin, asset, bizdate,eutime,IFNULL(otcAsset,0), serverId
        FROM ATCENTER.B_ACCOUNT_TEMPORARY t use index(pk_lg_b_account_temporary)
        where
        FUNDID = #{fundId}
    </select>

</mapper>