package com.eastmoney.accessor.mapper.fundia;

import com.eastmoney.common.entity.FundSysStatus;
import com.eastmoney.common.entity.fundia.CombAsset;
import com.eastmoney.common.entity.fundia.CombProfit;
import com.eastmoney.common.entity.fundia.FundInvestBizApplyOrderInfo;
import com.eastmoney.common.entity.fundia.InvestFeeDetail;

import java.util.List;
import java.util.Map;

public interface FundInvestMapper {
    List<CombAsset> queryCombAsset(Map<String, Object> params);

    List<InvestFeeDetail> queryInvestFee(Map<String, Object> params);
    FundSysStatus querySystemStatus();

    List<FundInvestBizApplyOrderInfo>queryLatestBizApplyOrders(Map<String, Object> params);

    List<CombProfit> queryCombProfit(Map<String, Object> params);

}
