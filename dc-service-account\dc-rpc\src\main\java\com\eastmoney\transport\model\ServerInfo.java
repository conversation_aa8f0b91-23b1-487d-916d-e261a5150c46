package com.eastmoney.transport.model;
/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/24
 * Time: 9:14
 */
public class ServerInfo {
    private final String ip;
    private final int port;
    private final int connectNum;
//    private NettyClient nettyClient ;
    public ServerInfo(String ip,int port, int connectNum) {
        this.ip = ip;
        this.port = port;
        this.connectNum = connectNum;
    }

    public String getIp() {
        return ip;
    }

    public int getPort() {
        return port;
    }

    public int getConnectNum() {
        return connectNum;
    }

    //    public NettyClient getNettyClient() {
//        return nettyClient;
//    }
//
//    public void setNettyClient(NettyClient nettyClient) {
//        this.nettyClient = nettyClient;
//    }
}
