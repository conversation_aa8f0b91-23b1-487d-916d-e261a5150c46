package com.eastmoney.service.util;

import java.io.IOException;

/**
 * Created by sunyuncai on 2016/8/22.
 */
public class CodisConfig {
    public static volatile boolean isCodisOpen = true;
    static {
        try {
            isCodisOpen = getCodisOpen();
            Thread t = new Thread(new CodisConfigScan());
            t.setDaemon(true);
            t.setName("监控codis配置项:" + ServiceConstants.CODIS_OPEN_FLAG);
            t.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static boolean getCodisOpen() throws IOException {
        String codisOpenFlag = PropertiesUtil.readValue(ServiceConstants.CODIS_OPEN_FLAG, "0");
        boolean isCodisOpen = true;
        if ("0".equals(codisOpenFlag)) {
            isCodisOpen = false;
        }
        return isCodisOpen;
    }
    private static class CodisConfigScan implements Runnable {
        @Override
        public void run() {
            while (true) {
                try {
                    boolean status = getCodisOpen();
                    if (status != CodisConfig.isCodisOpen) {
                        System.out.println("codis open status" + " : " + status);
                        CodisConfig.isCodisOpen = status;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(CodisConfig.isCodisOpen);
    }
}
