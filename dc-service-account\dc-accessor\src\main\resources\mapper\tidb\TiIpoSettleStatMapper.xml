<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiIpoSettleStatMapper">
    <select id="selectSettleStatus" resultType="com.eastmoney.common.entity.cal.IPOProfitInfoBO">
        SELECT bizDate
        FROM ATCENTER.IPO_SETTLE_STAT
        <where>
            <if test="serverId != null">
                and SERVERID= #{serverId}
            </if>
            <if test="businessCode != null">
                AND BUSINESS_CODE = #{businessCode}
            </if>
        </where>
    </select>
</mapper>