package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.IpoPositionInfoHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 新股新债打新收益接口
 */
@Action
@Component
@Service
public class IpoPositionInfoAction {
    @Autowired
    private IpoPositionInfoHandler ipoPositionInfoHandler;

    /**
     * 查询资金账号区间内的打新收益数据、最新清算日期
     * @param params
     * @return
     */
    @FunCodeMapping("getIpoPositionProfitList")
    @RequestMapping("/getIpoPositionProfitList")
    public Object getIpoPositionProfitList(Map<String, Object> params) {
        CommonUtil.checkParamNotNull(params, new String[]{"fundId", "startDate", "endDate"});

        return ipoPositionInfoHandler.getIpoPositionProfitList(params);
    }
}
