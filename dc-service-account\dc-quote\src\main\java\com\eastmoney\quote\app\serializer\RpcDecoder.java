package com.eastmoney.quote.app.serializer;

import com.eastmoney.quote.app.coder.Codec5059;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.LoggerFactory;

import java.nio.ByteOrder;
import java.util.List;

/**
 * Created by 1 on 15-7-7.
 */
public class RpcDecoder extends ByteToMessageDecoder {

    public static org.slf4j.Logger LOG = LoggerFactory.getLogger(RpcDecoder.class);

    private String filename;

    public RpcDecoder(){}

    @Override
    protected void decode(ChannelHandlerContext channelHandlerContext, ByteBuf in, List<Object> out) throws Exception {
        try {
            if (in.readableBytes() < 7) {
                return;
            }
            in=in.order(ByteOrder.LITTLE_ENDIAN);
            in.markReaderIndex();
            byte aHead = in.readByte();
            if(aHead!=Packet.getpHead()) {
                throw new Exception("串包了");
            }
            Packet packet = new Packet();
            packet.setType(in.readShort());
            packet.setAttr1(in.readByte());
            packet.setAttr2(in.readByte());
            packet.setLength(in.readShort());
            if (in.readableBytes() < packet.length) {
                in.resetReaderIndex();
                return;
            }
            byte[] con = null;
            if ((packet.getAttr1() & 0x02)==1) {
                int originalSize = in.readUnsignedShort();
                int compressedSize = in.readUnsignedShort();
                con = new byte[compressedSize];
                in.readBytes(con);
                con = ZLibUtils.decompress(con);
            }else{
                con = new byte[packet.getLength()];
            }
            if(packet.getType()==5059){
                in.readBytes(con);
                ByteBuf deConBuf = Unpooled.wrappedBuffer(con);
                deConBuf=deConBuf.order(ByteOrder.LITTLE_ENDIAN);
                new Codec5059().decode(deConBuf,out,packet);
                in.readByte();
            }
        } catch (Exception e){
            LOG.error(e.getMessage(), e);
        }
    }

}
