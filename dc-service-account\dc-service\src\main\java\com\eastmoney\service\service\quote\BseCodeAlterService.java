package com.eastmoney.service.service.quote;


import com.eastmoney.common.entity.*;

import java.util.List;
import java.util.Map;

public interface BseCodeAlterService {

    /**
     * 加载代码转换缓存
     */
    void doLoadInfo();

    /**
     * stkInfo中的 stkCode 830 -> 920  true 正向
     * stkInfo中的 stkCode 920 -> 830  false  反向
     * @return
     */
    StkInfo transStkInfoCode(StkInfo stkInfo, boolean direction);

    /**
     * 入参中可能的830转为920，如果入参中的code并非要转的代码，则返回原值
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlter(String stkCode, String market);

    /**
     * 日期和bizDate一致表示 切换后的第一个交易日，行情那块需要进行切换
     * 入参中可能的830转为920，如果入参中的code并非要转的代码，则返回原值
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlterWithBizDate(String stkCode, String market, Integer date);


    /**
     * 入参中可能的830-B转为920-b，如果入参中的code并非要转的代码，则返回原值
     * 入参中可能的830-6转为920-b，如果入参中的code并非要转的代码，则返回原值
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlterXsbAndBjs(String stkCode, String market);

    /**
     * 入参中可能的920转为830，如果入参中的code并非要转的代码，则返回原值
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlterReverse(String stkCode, String market);

    /**
     * 日期和bizDate一致表示 切换后的第一个交易日，行情那块需要进行切换
     * 入参中可能的920转为830，如果入参中的code并非要转的代码，则返回原值
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlterReverseWithBizDate(String stkCode, String market, Integer date);



    /**
     * param入参中添加转换代码
     * @param params
     * @return
     */
    Map<String, Object> getCodeAlterParams(Map<String, Object> params);


    /**
     * 进行代码转换
     * 830 -> 920  或 920 -> 830
     * @param stkCode
     * @param market
     * @return
     */
    String getCodeAlterOrReverse(String stkCode, String market);

}
