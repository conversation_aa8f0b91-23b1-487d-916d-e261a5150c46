dependencies {
    implementation project(':dc-common')
    implementation rootProject.ext.dependencies['commons-pool']
    implementation rootProject.ext.dependencies['commons-configuration']
    implementation rootProject.ext.dependencies['jeromq']
}

jar {
    manifest {
        attributes 'Manifest-Version': '1.0'
        attributes 'Created-By': 'Gradle'
        attributes 'Class-Path': ". config/ libs/" + configurations.runtime.collect { it.name }.join(' libs/')
    }
}