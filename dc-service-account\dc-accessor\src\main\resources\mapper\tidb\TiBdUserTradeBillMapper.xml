<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiBdUserTradeBillMapper">

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.BdUserTradeBillDO">
        select fund_code fundId,
               open_date openDate,
               sale_ceil saleCeil,
               buy_floor buyFloor,
               sname_sale snameSale,
               down_perc downPerc,
               sname_buy snameBuy,
               up_perc upPerc,
               before_20150106 beforeStartDate
        from ATCENTER.BD_trade_bill
        where fund_code = #{fundId}
    </select>

</mapper>