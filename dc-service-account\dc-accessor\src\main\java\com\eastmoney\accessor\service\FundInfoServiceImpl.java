package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.sqlserver.OggFundInfoDao;
import com.eastmoney.common.entity.FundInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/9
 */
@Service
public class FundInfoServiceImpl implements FundInfoService{
    @Autowired
    private OggFundInfoDao oggFundInfoDao;

    @Override
    public FundInfo getFundInfo(Map<String, Object> params) {
        return oggFundInfoDao.getFundInfo(params);
    }
}
