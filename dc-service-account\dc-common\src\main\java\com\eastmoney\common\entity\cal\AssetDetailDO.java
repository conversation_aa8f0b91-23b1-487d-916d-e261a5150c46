package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntityCal;

/**
 * <AUTHOR>
 * @description 新资产明细表DO
 * @date 2024/1/12 13:46
 */
public class AssetDetailDO extends BaseEntityCal {

    private Long custId;
    private Long fundId;
    private String moneyType;
    private Double fundAsset;
    private Double stkAsset;
    private Double bbContract;
    private Double bjContractSz;
    private Double gpzyDebt; // 股票质押暂时不用
    private Double bondAsset;
    private Double zqzyContract;// 债券质押资产暂时不用
    private Double asset;
    private Double fundBuySale;// 到账资金 只给接口计算日收益使用 历史资产不需要

    public Double getFundBuySale() {
        return fundBuySale;
    }

    public void setFundBuySale(Double fundBuySale) {
        this.fundBuySale = fundBuySale;
    }

    public AssetDetailDO() {
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public Double getFundAsset() {
        return fundAsset;
    }

    public void setFundAsset(Double fundAsset) {
        this.fundAsset = fundAsset;
    }

    public Double getStkAsset() {
        return stkAsset;
    }

    public void setStkAsset(Double stkAsset) {
        this.stkAsset = stkAsset;
    }

    public Double getBbContract() {
        return bbContract;
    }

    public void setBbContract(Double bbContract) {
        this.bbContract = bbContract;
    }

    public Double getBjContractSz() {
        return bjContractSz;
    }

    public void setBjContractSz(Double bjContractSz) {
        this.bjContractSz = bjContractSz;
    }

    public Double getGpzyDebt() {
        return gpzyDebt;
    }

    public void setGpzyDebt(Double gpzyDebt) {
        this.gpzyDebt = gpzyDebt;
    }

    public Double getBondAsset() {
        return bondAsset;
    }

    public void setBondAsset(Double bondAsset) {
        this.bondAsset = bondAsset;
    }

    public Double getZqzyContract() {
        return zqzyContract;
    }

    public void setZqzyContract(Double zqzyContract) {
        this.zqzyContract = zqzyContract;
    }

    public Double getAsset() {
        return asset;
    }

    public void setAsset(Double asset) {
        this.asset = asset;
    }
}
