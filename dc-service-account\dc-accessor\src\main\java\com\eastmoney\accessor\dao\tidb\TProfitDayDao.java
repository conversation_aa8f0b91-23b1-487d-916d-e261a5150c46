package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.IBaseDao;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;

import java.util.List;
import java.util.Map;


public interface TProfitDayDao extends IBaseDao<TProfitDayDO, Long> {

    List<TProfitDayDO> getTProfitDayList(Map<String, Object> params);

    TProfitBO getTProfitSection(Map<String, Object> dealParams);
}
