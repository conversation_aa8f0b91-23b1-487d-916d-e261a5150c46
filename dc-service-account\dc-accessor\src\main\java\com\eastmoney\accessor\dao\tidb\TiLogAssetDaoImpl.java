package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.LogAssetDao;
import com.eastmoney.accessor.mapper.tidb.TiLogAssetMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.LogAsset;
import com.eastmoney.common.util.CommonUtil;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/3
 *
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("logAssetDao")
public class TiLogAssetDaoImpl extends BaseDao<TiLogAssetMapper, LogAsset, Integer> implements LogAssetDao {

    @Override
    public List<Object> getLogAssetSum(Map<String, Object> params) {
        return getMapper().getLogAssetSum(params);
    }

    @Override
    public List<LogAsset> getStkTradeList(Map<String, Object> params) {

        return getMapper().getStkTradeList(params);
    }

    @Override
    public List<LogAsset> getStkShiftList(Map<String, Object> params) {
        return getMapper().getStkShiftList(params);
    }

    @Override
    public List<LogAsset> getOtherDetailList(Map<String, Object> params) {
        Integer pageSize = CommonUtil.convert(params.get("pageSize"), Integer.class);
        Integer pageNo = CommonUtil.convert(params.get("pageNo"), Integer.class);
        Integer startNum = pageSize * (pageNo - 1);
        params.put("startNum", startNum);
        params.put("pageSize", pageSize);
        return getMapper().getOtherDetailList(params);
    }

    @Override
    public List<LogAsset> getTradeShiftList(Map<String, Object> params) {
        return getMapper().getTradeShiftList(params);
    }

    @Override
    public List<LogAsset> getHoldTradeShiftList(Map<String, Object> params) {
        return getMapper().getHoldTradeShiftList(params);
    }

    @Override
    public List<LogAsset> getBSTradeList(Map<String, Object> params) {
        return getMapper().getBSTradeList(params);
    }
}
