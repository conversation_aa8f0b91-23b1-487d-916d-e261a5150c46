package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.AssetNewDao;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.CommonUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2020/8/17-8:52.
 *
 * <AUTHOR>
 */
@Service
public class AssetNewService {
    private static Logger LOG = LoggerFactory.getLogger(AssetNewService.class);
    @Resource(name = "assetNewCache")
    private LoadingCache<Long, Optional<AssetNew>> assetNewCache;
    @Autowired
    private AssetNewDao assetNewDao;

    @Bean(name = "assetNewCache")
    public LoadingCache<Long, Optional<AssetNew>> assetNewCache() {
        LoadingCache<Long, Optional<AssetNew>> assetNewLoadingCache = CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(10000)
                .maximumSize(100000)
                .refreshAfterWrite(60, TimeUnit.SECONDS)
                .build(new CacheLoader<Long, Optional<AssetNew>>() {
                    @Override
                    public Optional<AssetNew> load(Long key) throws Exception {
                        AssetNew assetNew = null;
                        try {
                            Map<String, Object> param = new HashMap<>(1);
                            param.put("fundId", key);
                            List<AssetNew> assetNewList = assetNewDao.query(param);
                            if (!CollectionUtils.isEmpty(assetNewList)) {
                                assetNew = assetNewList.get(0);
                            }
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }
                        return Optional.ofNullable(assetNew);
                    }
                });
        return assetNewLoadingCache;
    }

    public AssetNew getAssetInfo(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        try {
            return assetNewCache.get(fundId).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取最新资产失败", e);
        }
        return null;
    }

}
