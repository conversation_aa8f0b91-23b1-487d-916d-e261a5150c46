package com.eastmoney.common.entity;

/**
 * Created on 2016/3/1
 * 转帐流水
 *
 * <AUTHOR>
 */
public class LogBankTran implements Comparable<LogBankTran>{
    private Integer sysDate;/*操作的交易日期 sqlServer*/
    private Integer syDate;/*操作的交易日期 oracle*/
    private Long operDate;/*发生日期*/
    private Integer operTime;/*操作时间*/
    private Long sno;/*流水号*/
    private Long linkSno;/*其它关联流水号(资金和帐户流水号)*/
    private Long strikeSno;/*冲正流水号*/
    private String moneyType;/*货币代码*/
    private String orgId;/*机构编码*/
    private String brhId;/*机构分支*/
    private String bankCode;/*银行代码*/
    private String bankBranch;/*银行支行(冗余方便查询)*/
    private String bankNetPlace;/*银行网点*/
    private String bankTranId;/*业务类型, 开户销户存入取出 冲正*/
    private Long custId;/*客户类别*/
    private String custName;/*客户姓名*/
    private Long fundId;/*资金帐号*/
    private String sfFundId;/*三方存管帐号*/
    private String reportKind;/*客户报表分类*/
    private String custKind;/*客户类别*/
    private String custGroup;/*客户分组*/
    private String fundKind;/*资金分类*/
    private String fundLevel;/*资金室号*/
    private String fundGroup;/*资金分组*/
    private String linkFlag;/*转帐标识 交易市场或其他标识*/
    private String linkAccId;/*关联帐号*/
    private String idType;/*证件类型, '证件类型:身份证、护照、军官证、士兵证、营业执照…'*/
    private String idNo;/*证件号码*/
    private String bankId;/*银行帐号*/
    private String sourceType;/*发起方*/
    private Double fundEffect;/*转帐发生金额*/
    private Double fundBal;/*发生后余额*/
    private String status;/*交易状态*/
    private String bankPwd;/*银行帐号密码*/
    private String bankMsgId;/*外部信息代码*/
    private String bankMsg;/*外部信息内容*/
    private Long sysErrId;/*信息代码*/
    private Long dealTime;/*外部机构处理时间*/
    private String bankSno;/*外部机构流水号*/
    private Long strikeNum;/*冲正次数*/
    private String strikeFlag;/*冲正标志 0'流水冲正’（实时） 1’流水调整‘（事后）*/
    private String doHistFlag;/*归档标志 '0' 归档到历史 '1' 不归档到历史*/
    private Long agentId;/*代理人代码*/
    private Long operId;/*操作人代码*/
    private String operWay;/*操作方式*/
    private String operOrg;/*操作营业部代码*/
    private String operLevel;/*操作员级别*/
    private String netAddr;/*操作站点*/
    private Long chkOper;/*审核柜员*/
    private Long agentOper;/*代理柜员*/
    private String remark;/*备注信息*/
    private String remark1;/*MAC地址*/
    private String extSno;/*外部流水号,用于外部反查转账流水*/
    private Long eid;

    public Long getOperDate() {
        return operDate;
    }

    public void setOperDate(Long operDate) {
        this.operDate = operDate;
    }

    public Long getSno() {
        return sno;
    }

    public void setSno(Long sno) {
        this.sno = sno;
    }

    public Integer getSysDate() {
        return sysDate;
    }

    public void setSysDate(Integer sysDate) {
        this.sysDate = sysDate;
    }

    public Integer getSyDate() {
        if (syDate == null && sysDate != null) {
            syDate = sysDate;
        }
        return syDate;
    }

    public void setSyDate(Integer syDate) {
        this.syDate = syDate;
    }

    public Integer getOperTime() {
        return operTime;
    }

    public void setOperTime(Integer operTime) {
        this.operTime = operTime;
    }

    public Long getLinkSno() {
        return linkSno;
    }

    public void setLinkSno(Long linkSno) {
        this.linkSno = linkSno;
    }

    public Long getStrikeSno() {
        return strikeSno;
    }

    public void setStrikeSno(Long strikeSno) {
        this.strikeSno = strikeSno;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType.trim();
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId.trim();
    }

    public String getBrhId() {
        return brhId;
    }

    public void setBrhId(String brhId) {
        this.brhId = brhId.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode.trim();
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch.trim();
    }

    public String getBankNetPlace() {
        return bankNetPlace;
    }

    public void setBankNetPlace(String bankNetPlace) {
        this.bankNetPlace = bankNetPlace.trim();
    }

    public String getBankTranId() {
        return bankTranId;
    }

    public void setBankTranId(String bankTranId) {
        this.bankTranId = bankTranId.trim();
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName.trim();
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getSfFundId() {
        return sfFundId;
    }

    public void setSfFundId(String sfFundId) {
        this.sfFundId = sfFundId.trim();
    }

    public String getReportKind() {
        return reportKind;
    }

    public void setReportKind(String reportKind) {
        this.reportKind = reportKind.trim();
    }

    public String getCustKind() {
        return custKind;
    }

    public void setCustKind(String custKind) {
        this.custKind = custKind.trim();
    }

    public String getCustGroup() {
        return custGroup;
    }

    public void setCustGroup(String custGroup) {
        this.custGroup = custGroup.trim();
    }

    public String getFundKind() {
        return fundKind;
    }

    public void setFundKind(String fundKind) {
        this.fundKind = fundKind.trim();
    }

    public String getFundLevel() {
        return fundLevel;
    }

    public void setFundLevel(String fundLevel) {
        this.fundLevel = fundLevel.trim();
    }

    public String getFundGroup() {
        return fundGroup;
    }

    public void setFundGroup(String fundGroup) {
        this.fundGroup = fundGroup.trim();
    }

    public String getLinkFlag() {
        return linkFlag;
    }

    public void setLinkFlag(String linkFlag) {
        this.linkFlag = linkFlag.trim();
    }

    public String getLinkAccId() {
        return linkAccId;
    }

    public void setLinkAccId(String linkAccId) {
        this.linkAccId = linkAccId.trim();
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType.trim();
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo.trim();
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId.trim();
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType.trim();
    }

    public Double getFundEffect() {
        return fundEffect;
    }

    public void setFundEffect(Double fundEffect) {
        this.fundEffect = fundEffect;
    }

    public Double getFundBal() {
        return fundBal;
    }

    public void setFundBal(Double fundBal) {
        this.fundBal = fundBal;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status.trim();
    }

    public String getBankPwd() {
        return bankPwd;
    }

    public void setBankPwd(String bankPwd) {
        this.bankPwd = bankPwd.trim();
    }

    public String getBankMsgId() {
        return bankMsgId;
    }

    public void setBankMsgId(String bankMsgId) {
        this.bankMsgId = bankMsgId.trim();
    }

    public String getBankMsg() {
        return bankMsg;
    }

    public void setBankMsg(String bankMsg) {
        this.bankMsg = bankMsg.trim();
    }

    public Long getSysErrId() {
        return sysErrId;
    }

    public void setSysErrId(Long sysErrId) {
        this.sysErrId = sysErrId;
    }

    public Long getDealTime() {
        return dealTime;
    }

    public void setDealTime(Long dealTime) {
        this.dealTime = dealTime;
    }

    public String getBankSno() {
        return bankSno;
    }

    public void setBankSno(String bankSno) {
        this.bankSno = bankSno.trim();
    }

    public Long getStrikeNum() {
        return strikeNum;
    }

    public void setStrikeNum(Long strikeNum) {
        this.strikeNum = strikeNum;
    }

    public String getStrikeFlag() {
        return strikeFlag;
    }

    public void setStrikeFlag(String strikeFlag) {
        this.strikeFlag = strikeFlag.trim();
    }

    public String getDoHistFlag() {
        return doHistFlag;
    }

    public void setDoHistFlag(String doHistFlag) {
        this.doHistFlag = doHistFlag.trim();
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public String getOperWay() {
        return operWay;
    }

    public void setOperWay(String operWay) {
        this.operWay = operWay.trim();
    }

    public String getOperOrg() {
        return operOrg;
    }

    public void setOperOrg(String operOrg) {
        this.operOrg = operOrg.trim();
    }

    public String getOperLevel() {
        return operLevel;
    }

    public void setOperLevel(String operLevel) {
        this.operLevel = operLevel.trim();
    }

    public String getNetAddr() {
        return netAddr;
    }

    public void setNetAddr(String netAddr) {
        this.netAddr = netAddr.trim();
    }

    public Long getChkOper() {
        return chkOper;
    }

    public void setChkOper(Long chkOper) {
        this.chkOper = chkOper;
    }

    public Long getAgentOper() {
        return agentOper;
    }

    public void setAgentOper(Long agentOper) {
        this.agentOper = agentOper;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark.trim();
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1.trim();
    }

    public String getExtSno() {
        return extSno;
    }

    public void setExtSno(String extSno) {
        this.extSno = extSno.trim();
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    @Override
    public int compareTo(LogBankTran obj) {
        return this.eid.compareTo(obj.getEid());
    }
}
