package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.ProfitRateSectionRankDao;
import com.eastmoney.common.entity.cal.ProfitRateSectionRank;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 16:07
 */
@Service
public class ProfitRateSectionRankCacheService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProfitRateSectionRankCacheService.class);

    /**
     * 收益率排名信息map
     * key = stkCode
     * value = 分层信息
     */
    private Map<String, List<ProfitRateSectionRank>> profitRateSectionRankMap = new HashMap<>();


    @Autowired
    private ProfitRateSectionRankDao profitRateSectionRankDao;

    /**
     * 收益率排名
     *
     * @param unit
     * @param profitRate
     * @return
     */
    public Optional<Double> getProfitRateSectionRank(String unit, Double profitRate) {
        if (StringUtils.isEmpty(unit) || profitRate == null || profitRateSectionRankMap.get(unit) == null) {
            return Optional.empty();
        }
        List<ProfitRateSectionRank> profitRateSectionRankList = profitRateSectionRankMap.get(unit).stream()
                .filter(profitRateSectionRank ->
                        profitRateSectionRank.getProfitRate() != null &&
                                profitRateSectionRank.getRankPercent() != null)
                .collect(Collectors.toList());
        int startIndex = 0;
        int endIndex = profitRateSectionRankList.size() - 1;
        while (startIndex < endIndex) {
            int mid = (startIndex + endIndex) / 2;
            ProfitRateSectionRank profitRateSectionRank = profitRateSectionRankList.get(mid);
            if (ArithUtil.eq(profitRate, profitRateSectionRank.getProfitRate())) {
                return Optional.ofNullable(profitRateSectionRank.getRankPercent());
            }
            if (profitRate > profitRateSectionRank.getProfitRate()) {
                startIndex = mid + 1;
            } else {
                endIndex = mid;
            }
        }
        return Optional.ofNullable(profitRateSectionRankList.get(startIndex).getRankPercent());

    }

    @PostConstruct
    public void loadInfo() {
        LOGGER.info("------------------------正在加载收益率排名信息，请等待-------------------------" + DateUtil.getCurDateTime());
        doLoadInfo();
        LOGGER.info("------------------------收益率排名加载完成-------------------------" + DateUtil.getCurDateTime());
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        executor.scheduleAtFixedRate(this::doLoadInfo, 600, 10 * 60, TimeUnit.SECONDS);
    }

    private void doLoadInfo() {
        Map<String, Object> params = new HashMap<>();
        List<ProfitRateSectionRank> profitRateSectionRankList = profitRateSectionRankDao.query(params);
        profitRateSectionRankMap = profitRateSectionRankList.stream()
                .filter(profitRateSectionRank -> StringUtils.isNotEmpty(profitRateSectionRank.getUnit())
                        && profitRateSectionRank.getProfitRate() != null
                        && profitRateSectionRank.getRankPercent() != null)
                .peek(profitRateSectionRank -> profitRateSectionRank.setRankPercent(ArithUtil.div(profitRateSectionRank.getRankPercent(), 10000)))
                .sorted(Comparator.comparing(ProfitRateSectionRank::getProfitRate))
                .collect(Collectors.groupingBy(ProfitRateSectionRank::getUnit, LinkedHashMap::new, Collectors.toList()));

    }
}
