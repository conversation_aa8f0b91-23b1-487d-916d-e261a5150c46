package com.eastmoney.service.action;

import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.cal.*;
import com.eastmoney.service.handler.TOperationAnalyzeHandler;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TOperationAnalyzeActionTest {

    @Mock
    private TOperationAnalyzeHandler mockTOperationAnalyzeHandler;

    @InjectMocks
    private TOperationAnalyzeAction tOperationAnalyzeActionUnderTest;

    @Test
    void testGetTProfitStockList() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Configure TOperationAnalyzeHandler.getTProfitStockList(...).
        final TProfitBO tProfitBO = new TProfitBO();
        tProfitBO.setEndDate("20240718");
        tProfitRank tProfitRank = new tProfitRank();
        tProfitRank.setMarket("0");
        tProfitRank.setStkCode("000001");
        tProfitRank.setStkName("平安银行");
        tProfitRank.setTProfit(555.2);
        tProfitRank.setTSuccess(11);
        tProfitRank.setTSuccessRate(0.4074);
        tProfitRank.setTTimes(27);
        tProfitBO.setTProfitStockList(Arrays.asList(tProfitRank));
        when(mockTOperationAnalyzeHandler.getSecTProfitRankList(new HashMap<>())).thenReturn(tProfitBO);

        // Run the test
        final TProfitBO result = tOperationAnalyzeActionUnderTest.getSecTProfitRankList(params);

        // Verify the results
        assertThat(result).isEqualTo(tProfitBO);

    }

    @Test
    void testGetTotalTProfit() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Configure TOperationAnalyzeHandler.getTotalTProfit(...).
        final TProfitBO tProfitBO = new TProfitBO();
        tProfitBO.setTotalTSuccess(124L);
        tProfitBO.setTotalTTimes(258L);
        tProfitBO.setTotalTSuccessRate(0.4806);
        tProfitBO.setTotalTProfit(1628073.94);
        tProfitBO.setEndDate("9511569");
        when(mockTOperationAnalyzeHandler.getTProfitSection(new HashMap<>())).thenReturn(tProfitBO);

        // Run the test
        final TProfitBO result = tOperationAnalyzeActionUnderTest.getSecTProfitRankList(params);

        // Verify the results
        assertThat(result).isEqualTo(tProfitBO);
    }

    @Test
    void testGetTProfitDayList() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Configure TOperationAnalyzeHandler.getTProfitDayList(...).
        final TProfitDayDO tProfitDayDO = new TProfitDayDO();
        tProfitDayDO.setBizDate(20240722);
        tProfitDayDO.setTProfit(10.0);
        final List<TProfitDayDO> tProfitDayDOList = Arrays.asList(tProfitDayDO);
        when(mockTOperationAnalyzeHandler.getTProfitDayList(new HashMap<>())).thenReturn(tProfitDayDOList);

        // Run the test
        final List<TProfitDayDO> result = tOperationAnalyzeActionUnderTest.getTProfitDaySectionList(params);

        // Verify the results
        assertThat(result).isEqualTo(tProfitDayDOList);
    }

    @Test
    void testGetTProfitDayList_TOperationAnalyzeHandlerReturnsNoItems() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        when(mockTOperationAnalyzeHandler.getTProfitDayList(new HashMap<>())).thenReturn(Collections.emptyList());

        // Run the test
        final List<TProfitDayDO> result = tOperationAnalyzeActionUnderTest.getTProfitDaySectionList(params);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetTProfitDayListPC() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Configure TOperationAnalyzeHandler.getTProfitDayListPC(...).
        final TSecProfitDayDO tSecProfitDayDO = new TSecProfitDayDO();
        tSecProfitDayDO.setMoneyType("0");
        tSecProfitDayDO.setHoldFlag("0");
        tSecProfitDayDO.setLastMatchTime(9511569L);
        tSecProfitDayDO.setStkName("神州高铁");
        tSecProfitDayDO.setFundId(540700265069L);
        tSecProfitDayDO.setStkCode("000008");
        tSecProfitDayDO.setMarket("0");
        tSecProfitDayDO.setTProfit(5.0);
        tSecProfitDayDO.setTDifferences(0.01);
        final List<TSecProfitDayDO> tSecProfitDayDOList = Arrays.asList(tSecProfitDayDO);
        when(mockTOperationAnalyzeHandler.getTProfitDayListPC(new HashMap<>())).thenReturn(tSecProfitDayDOList);

        // Run the test
        final List<TSecProfitDayDO> result = tOperationAnalyzeActionUnderTest.getTProfitDayListPC(params);

        // Verify the results
        assertThat(result).isEqualTo(tSecProfitDayDOList);
    }

    @Test
    void testGetTProfitDayListPC_TOperationAnalyzeHandlerReturnsNoItems() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        when(mockTOperationAnalyzeHandler.getTProfitDayListPC(new HashMap<>())).thenReturn(Collections.emptyList());

        // Run the test
        final List<TSecProfitDayDO> result = tOperationAnalyzeActionUnderTest.getTProfitDayListPC(params);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetTProfitDetailList() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Configure TOperationAnalyzeHandler.getTProfitDetailList(...).
        final TProfitDetail tProfitDetail = new TProfitDetail();
        tProfitDetail.setHoldFlag("0");
        tProfitDetail.setMarket("0");
        tProfitDetail.setStkCode("000008");
        tProfitDetail.setStkName("神州高铁");
        tProfitDetail.setTDifferences(-0.1);

        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);

        final Match match1 = new Match();
        match.setBsFlag("S");
        match.setMatchPrice(1.93);
        match.setMatchQty(100L);
        match.setMatchTime(9481829L);
        ArrayList<Match> matches = Lists.newArrayList(match, match1);
        tProfitDetail.setTMatchList(matches);
        final List<TProfitDetail> tProfitDetailList =new ArrayList<>();
        tProfitDetailList.add(tProfitDetail);
        when(mockTOperationAnalyzeHandler.getSecTDetailList(new HashMap<>())).thenReturn(tProfitDetailList);

        // Run the test
        final List<TProfitDetail> result = tOperationAnalyzeActionUnderTest.getTProfitDetailList(params);

        // Verify the results
        assertThat(result).isEqualTo(tProfitDetailList);
    }

    @Test
    void testGetTProfitDetailList_TOperationAnalyzeHandlerReturnsNoItems() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        when(mockTOperationAnalyzeHandler.getSecTDetailList(new HashMap<>())).thenReturn(Collections.emptyList());

        // Run the test
        final List<TProfitDetail> result = tOperationAnalyzeActionUnderTest.getTProfitDetailList(params);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetTProfitDetailListPC() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        final Match match = new Match();
        match.setBsFlag("B");
        match.setMatchPrice(1.94);
        match.setMatchQty(100L);
        match.setMatchTime(9483653L);

        final Match match1 = new Match();
        match.setBsFlag("S");
        match.setMatchPrice(1.93);
        match.setMatchQty(100L);
        match.setMatchTime(9481829L);
        ArrayList<Match> matches = Lists.newArrayList(match, match1);
        when(mockTOperationAnalyzeHandler.getSecTDetailListPC(new HashMap<>())).thenReturn(matches);

        // Run the test
        final List<Match> result = tOperationAnalyzeActionUnderTest.getTProfitDetailListPC(params);

        // Verify the results
        assertThat(result).isEqualTo(matches);
    }

    @Test
    void testGetTProfitDetailListPC_TOperationAnalyzeHandlerReturnsNoItems() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        when(mockTOperationAnalyzeHandler.getSecTDetailListPC(new HashMap<>())).thenReturn(Collections.emptyList());

        // Run the test
        final List<Match> result = tOperationAnalyzeActionUnderTest.getTProfitDetailListPC(params);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
