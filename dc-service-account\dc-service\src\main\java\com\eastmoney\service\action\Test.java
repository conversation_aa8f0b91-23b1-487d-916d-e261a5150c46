package com.eastmoney.service.action;

import com.eastmoney.common.entity.cal.StockBill;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Administrator on 2017/4/1.
 */
public class Test {
    public static void main(String[] args) {
        List<StockBill> list = new ArrayList<>();
        StockBill s = new StockBill();
        s.setFundId(123L);
        StockBill s1 = new StockBill();
        s1.setFundId(456L);
        StockBill s2 = new StockBill();
        s2.setFundId(789L);
        list.add(s);
        list.add(s1);
        list.add(s2);
        for (StockBill sd : list) {
            System.out.println(sd.getFundId());
            sd.setFundId(147L);
        }
        for (StockBill stockBill : list) {
            System.out.println(stockBill.getFundId());
        }
    }

    private static void getNPE(String x) {
        System.out.println("当前字母为：" + x.toString());
    }
}
