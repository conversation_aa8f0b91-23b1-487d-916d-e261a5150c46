package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.StockLayeredInfoDao;
import com.eastmoney.common.entity.StockLayeredInfo;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 16:07
 */
@Service
public class StockLayeredInfoService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockLayeredInfoService.class);

    /**
     * 股转分层信息map
     * key = stkCode
     * value = 分层信息
     */
    private Map<String, String> stockLayeredInfoMap = new HashMap<>();

    @Autowired
    private StockLayeredInfoDao stockLayeredInfoDao;

    /**
     * 获取股转分层信息
     *
     * @param stkCode
     * @return
     */
    public Optional<String> getLayeredInfo(String stkCode) {
        if (StringUtils.isEmpty(stkCode)) {
            return Optional.empty();
        }
        return Optional.ofNullable(stockLayeredInfoMap.get(StringUtils.trim(stkCode)));
    }

    @PostConstruct
    public void loadInfo() {
        LOGGER.info("------------------------正在加载股转分层信息，请等待-------------------------" + DateUtil.getCurDateTime());
        doLoadInfo();
        LOGGER.info("------------------------股转分层加载完成-------------------------"  + DateUtil.getCurDateTime());
        Calendar date = Calendar.getInstance();
        date.set(date.get(Calendar.YEAR), date.get(Calendar.MONTH), date.get(Calendar.DATE), 6, 0, 0);
        Date now = new Date();
        if (now.after(date.getTime())) {
            date.add(Calendar.DATE, 1);
        }
        long diff = (date.getTime().getTime() - now.getTime()) / 1000;
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        executor.scheduleAtFixedRate(this::doLoadInfo, diff, 60 * 60, TimeUnit.SECONDS);
    }

    private void doLoadInfo() {
        List<StockLayeredInfo> stockLayeredInfoList = stockLayeredInfoDao.selectAll();
        this.stockLayeredInfoMap = stockLayeredInfoList.stream()
                .filter(stockLayeredInfo -> StringUtils.isNotEmpty(stockLayeredInfo.getStkCode()) && StringUtils.isNotEmpty(stockLayeredInfo.getSubLevel()))
                .collect(Collectors.toMap(StockLayeredInfo::getStkCode, StockLayeredInfo::getSubLevel, (one, two) -> two));
    }
}
