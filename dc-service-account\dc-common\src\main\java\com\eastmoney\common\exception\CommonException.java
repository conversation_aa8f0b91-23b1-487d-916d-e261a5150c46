package com.eastmoney.common.exception;

/**
 * Created by sunyuncai on 2016/10/24.
 */
public class CommonException extends RuntimeException{
    private int errCode = -1;
    private String errMsg;

    public CommonException() {
    }

    public CommonException(int errCode, String errMsg){
        super(errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }
    public CommonException(Throwable e, int errCode, String errMsg){
        super(errMsg,e);
        this.errCode = errCode;
    }

    public int getErrCode() {
        return errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

}
