package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitSectionMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitSection;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by xiaoyongyong on 2016/7/19.
 * ProfitSectionServiceImpl
 *
 * update on 2016/07/26
 *
 * <AUTHOR>
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitSectionDao")
public class ProfitSectionDaoImpl extends BaseDao<ProfitSectionMapper, ProfitSection, Long> implements ProfitSectionDao {

}
