package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.IPOTradeBillDao;
import com.eastmoney.accessor.mapper.tidb.TiIPOTradeBillMapper;
import com.eastmoney.common.entity.cal.IPOTradeBill;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/30
 */
@Service("ipoTradeBillDao")
public class TiIPOTradeBillDaoImpl extends BaseDao<TiIPOTradeBillMapper, IPOTradeBill, Long> implements IPOTradeBillDao {
    @Override
    public List<IPOTradeBill> selectYearBill(Long fundId, Integer indexKey) {
        Map<String, Object> param = new HashMap<>();
        param.put("fundId", fundId);
        param.put("indexKey", indexKey);
        return getMapper().selectYearBill(param);
    }
}
