package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiChoiceStockMapper;
import com.eastmoney.common.entity.ChoiceStock;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/1/18
 */
@Service("choiceStockDao")
public class ChoiceStockDaoImpl extends BaseDao<TiChoiceStockMapper, ChoiceStock, Integer> implements ChoiceStockDao {
    @Override
    public List<ChoiceStock> queryAllStock() {
        return getMapper().queryAllStock();
    }
}
