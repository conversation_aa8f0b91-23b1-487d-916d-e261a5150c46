package com.eastmoney.common.entity;

/**
 * 集中交易总资产
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
public class RealAsset {
    /**
     * 资金账号
     */
    private Long fundId;
    /**
     * 总资产（包含otc）
     */
    private Double fundAllWithOtc;
    /**
     * 不含otc
     */
    public Double fundAll;
    /**
     * OTC资产（包含天天宝）
     */
    public Double otcAsset;
    /**
     * 基金投顾总资产
     */
    public Double fundIaAsset;

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Double getFundAllWithOtc() {
        return fundAllWithOtc;
    }

    public void setFundAllWithOtc(Double fundAllWithOtc) {
        this.fundAllWithOtc = fundAllWithOtc;
    }

    public Double getFundAll() {
        return fundAll;
    }

    public void setFundAll(Double fundAll) {
        this.fundAll = fundAll;
    }

    public Double getOtcAsset() {
        return otcAsset;
    }

    public void setOtcAsset(Double otcAsset) {
        this.otcAsset = otcAsset;
    }

    public Double getFundIaAsset() {
        return fundIaAsset;
    }

    public void setFundIaAsset(Double fundIaAsset) {
        this.fundIaAsset = fundIaAsset;
    }

    public void of(QryFund qryFund){
        this.setFundId(qryFund.getFundId());
        this.setFundAllWithOtc(qryFund.getFundAllWithOtc());
        this.setFundAll(qryFund.getFundAll());
        this.setOtcAsset(qryFund.getOtcAsset());
        this.setFundIaAsset(qryFund.getFundIaAsset());
    }
}
