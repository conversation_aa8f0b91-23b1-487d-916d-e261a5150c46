package com.eastmoney.test;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.service.handler.ProfitHandler;
import com.eastmoney.service.cache.SysConfigService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;


import java.util.HashMap;
import java.util.Map;

@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class TradeActionTest {
    private Long fundId;
    private Map<String, Object> params = new HashMap<>();
    @InjectMocks
    ProfitHandler profitHandler;
    @InjectMocks
    private TradeDateDao tradeDateDao;

    @InjectMocks
    SysConfigService sysConfigService;

    @BeforeEach
    public void init(){
        fundId = 540700265069L;
        params.put("fundId", fundId);
    }

    @Test
    public void testRealTimeProfit(){
        DayProfitBean realTimeProfitInfo = profitHandler.getRealTimeProfitInfo(params);

        Assertions.assertNotNull(realTimeProfitInfo);
    }

}
