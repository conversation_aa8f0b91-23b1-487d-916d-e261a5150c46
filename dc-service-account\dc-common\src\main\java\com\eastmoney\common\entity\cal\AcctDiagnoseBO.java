package com.eastmoney.common.entity.cal;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */

import com.eastmoney.common.entity.BaseEntity;

import java.util.List;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */
public class AcctDiagnoseBO extends BaseEntity {

    /**
     * 是否提供诊断数据 0-No 1-Yes
     */
    private Integer hasPosition;
    /**
     * 诊断起始日
     */
    private Integer startDate;
    /**
     * 诊断截止日
     */
    private Integer endDate;
    /**
     * 数据更新时间
     */
    private String bizTime;
    /**
     * 数据时间标识 (1:昨天,2:今天,3:前天及更早以前)
     */
    private Integer endFlag;
    /**
     * 资产起始日
     */
    private Integer assetStartDate;
    /**
     * 综合评分
     */
    private Integer totalScore;
    /**
     * 综合评分日差
     */
    private Integer totalScoreDiff;
    /**
     * 历史最高综合评分
     */
    private Integer totalScoreMax;
    /**
     * 综合评分超越股友比
     */
    private Double totalScorePct;
    /**
     * 盈利能力分
     */
    private Integer profitScore;
    /**
     * 盈利评分日差
     */
    private Integer profitScoreDiff;
    /**
     * 平均盈利分
     */
    private Integer profitScoreAvg;
    /**
     * 收益额
     */
    private Double profit;
    /**
     * 收益率
     */
    private Double profitRate;
    /**
     * 击败沪深300幅度
     */
    private Double beatIndex300;
    /**
     * 风控能力分
     */
    private Integer riskScore;
    /**
     * 风控评分日差
     */
    private Integer riskScoreDiff;
    /**
     * 平均风控分
     */
    private Integer riskScoreAvg;
    /**
     * 最大回撤
     */
    private Double maxDrawdown;
    /**
     * 超额回撤
     */
    private Double overDrawdown;
    /**
     * 波动率
     */
    private Double volatility;
    /**
     * 夏普率
     */
    private Double sharpeRatio;
    /**
     * 选股能力分
     */
    private Integer holdScore;
    /**
     * 选股评分日差
     */
    private Integer holdScoreDiff;
    /**
     * 平均选股分
     */
    private Integer holdScoreAvg;
    /**
     * 持仓胜率
     */
    private Double holdWinRate;
    /**
     * 持仓胜率超越股友比
     */
    private Double holdWinRatePct;
    /**
     * 实时TOP持仓
     */
    private List<StkTopLabel> curHoldStkList;
    /**
     * 择时能力分
     */
    private Integer tradeScore;
    /**
     * 择时评分日差
     */
    private Integer tradeScoreDiff;
    /**
     * 平均择时分
     */
    private Integer tradeScoreAvg;
    /**
     * 择时胜率
     */
    private Double tradeWinRate;
    /**
     * 择时胜率超越股友比
     */
    private Double tradeWinRatePct;
    /**
     * 清仓总次数
     */
    private Integer clearTimes;
    /**
     * 平均持股天数
     */
    private Integer avgHoldDays;
    /**
     * 投资风格 S-短线投资 L-长线投资
     */
    private String investStyle;
    /**
     * 行业配置分
     */
    private Integer indAllocScore;
    /**
     * 行业评分日差
     */
    private Integer indAllocScoreDiff;
    /**
     * 平均行业分
     */
    private Integer indAllocScoreAvg;
    /**
     * 偏好行业列表
     */
    private List<IndPct> preferIndPctList;

    public static class StkTopLabel {
        String stkName;
        String stkCode;
        String label;
        String market;

        //北交所行情适配 830 -> 920
        String corResCode;

        public StkTopLabel() {
        }

        public StkTopLabel(String stkName, String stkCode, String market, String label) {
            this.stkName = stkName;
            this.stkCode = stkCode;
            this.market = market;
            this.label = label;
        }

        public String getStkName() {
            return stkName;
        }

        public void setStkName(String stkName) {
            this.stkName = stkName;
        }

        public String getStkCode() {
            return stkCode;
        }

        public void setStkCode(String stkCode) {
            this.stkCode = stkCode;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getMarket() {
            return market;
        }

        public void setMarket(String market) {
            this.market = market;
        }

        public String getCorResCode() {
            return corResCode;
        }

        public void setCorResCode(String corResCode) {
            this.corResCode = corResCode;
        }
    }

    public static class IndPct {
        String indName;
        Double percent;

        public IndPct() {
        }

        public IndPct(String indName, Double percent) {
            this.indName = indName;
            this.percent = percent;
        }

        public String getIndName() {
            return indName;
        }

        public void setIndName(String indName) {
            this.indName = indName;
        }

        public Double getPercent() {
            return percent;
        }

        public void setPercent(Double percent) {
            this.percent = percent;
        }
    }

    public Integer getHasPosition() {
        return hasPosition;
    }

    public void setHasPosition(Integer hasPosition) {
        this.hasPosition = hasPosition;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }

    public String getBizTime() {
        return bizTime;
    }

    public void setBizTime(String bizTime) {
        this.bizTime = bizTime;
    }

    public Integer getEndFlag() {
        return endFlag;
    }

    public void setEndFlag(Integer endFlag) {
        this.endFlag = endFlag;
    }

    public Integer getAssetStartDate() {
        return assetStartDate;
    }

    public void setAssetStartDate(Integer assetStartDate) {
        this.assetStartDate = assetStartDate;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getTotalScoreMax() {
        return totalScoreMax;
    }

    public void setTotalScoreMax(Integer totalScoreMax) {
        this.totalScoreMax = totalScoreMax;
    }

    public Double getTotalScorePct() {
        return totalScorePct;
    }

    public void setTotalScorePct(Double totalScorePct) {
        this.totalScorePct = totalScorePct;
    }

    public Integer getProfitScore() {
        return profitScore;
    }

    public void setProfitScore(Integer profitScore) {
        this.profitScore = profitScore;
    }

    public Integer getProfitScoreAvg() {
        return profitScoreAvg;
    }

    public void setProfitScoreAvg(Integer profitScoreAvg) {
        this.profitScoreAvg = profitScoreAvg;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public Double getBeatIndex300() {
        return beatIndex300;
    }

    public void setBeatIndex300(Double beatIndex300) {
        this.beatIndex300 = beatIndex300;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Integer getRiskScoreAvg() {
        return riskScoreAvg;
    }

    public void setRiskScoreAvg(Integer riskScoreAvg) {
        this.riskScoreAvg = riskScoreAvg;
    }

    public Double getMaxDrawdown() {
        return maxDrawdown;
    }

    public void setMaxDrawdown(Double maxDrawdown) {
        this.maxDrawdown = maxDrawdown;
    }

    public Double getOverDrawdown() {
        return overDrawdown;
    }

    public void setOverDrawdown(Double overDrawdown) {
        this.overDrawdown = overDrawdown;
    }

    public Double getVolatility() {
        return volatility;
    }

    public void setVolatility(Double volatility) {
        this.volatility = volatility;
    }

    public Double getSharpeRatio() {
        return sharpeRatio;
    }

    public void setSharpeRatio(Double sharpeRatio) {
        this.sharpeRatio = sharpeRatio;
    }

    public Integer getHoldScore() {
        return holdScore;
    }

    public void setHoldScore(Integer holdScore) {
        this.holdScore = holdScore;
    }

    public Integer getHoldScoreAvg() {
        return holdScoreAvg;
    }

    public void setHoldScoreAvg(Integer holdScoreAvg) {
        this.holdScoreAvg = holdScoreAvg;
    }

    public Double getHoldWinRate() {
        return holdWinRate;
    }

    public void setHoldWinRate(Double holdWinRate) {
        this.holdWinRate = holdWinRate;
    }

    public Double getHoldWinRatePct() {
        return holdWinRatePct;
    }

    public void setHoldWinRatePct(Double holdWinRatePct) {
        this.holdWinRatePct = holdWinRatePct;
    }

    public List<StkTopLabel> getCurHoldStkList() {
        return curHoldStkList;
    }

    public void setCurHoldStkList(List<StkTopLabel> curHoldStkList) {
        this.curHoldStkList = curHoldStkList;
    }

    public Integer getTradeScore() {
        return tradeScore;
    }

    public void setTradeScore(Integer tradeScore) {
        this.tradeScore = tradeScore;
    }

    public Integer getTradeScoreAvg() {
        return tradeScoreAvg;
    }

    public void setTradeScoreAvg(Integer tradeScoreAvg) {
        this.tradeScoreAvg = tradeScoreAvg;
    }

    public Double getTradeWinRate() {
        return tradeWinRate;
    }

    public void setTradeWinRate(Double tradeWinRate) {
        this.tradeWinRate = tradeWinRate;
    }

    public Double getTradeWinRatePct() {
        return tradeWinRatePct;
    }

    public void setTradeWinRatePct(Double tradeWinRatePct) {
        this.tradeWinRatePct = tradeWinRatePct;
    }

    public Integer getClearTimes() {
        return clearTimes;
    }

    public void setClearTimes(Integer clearTimes) {
        this.clearTimes = clearTimes;
    }

    public Integer getAvgHoldDays() {
        return avgHoldDays;
    }

    public void setAvgHoldDays(Integer avgHoldDays) {
        this.avgHoldDays = avgHoldDays;
    }

    public String getInvestStyle() {
        return investStyle;
    }

    public void setInvestStyle(String investStyle) {
        this.investStyle = investStyle;
    }

    public Integer getIndAllocScore() {
        return indAllocScore;
    }

    public void setIndAllocScore(Integer indAllocScore) {
        this.indAllocScore = indAllocScore;
    }

    public Integer getIndAllocScoreAvg() {
        return indAllocScoreAvg;
    }

    public void setIndAllocScoreAvg(Integer indAllocScoreAvg) {
        this.indAllocScoreAvg = indAllocScoreAvg;
    }

    public List<IndPct> getPreferIndPctList() {
        return preferIndPctList;
    }

    public void setPreferIndPctList(List<IndPct> preferIndPctList) {
        this.preferIndPctList = preferIndPctList;
    }

    public Integer getTotalScoreDiff() {
        return totalScoreDiff;
    }

    public void setTotalScoreDiff(Integer totalScoreDiff) {
        this.totalScoreDiff = totalScoreDiff;
    }

    public Integer getProfitScoreDiff() {
        return profitScoreDiff;
    }

    public void setProfitScoreDiff(Integer profitScoreDiff) {
        this.profitScoreDiff = profitScoreDiff;
    }

    public Integer getRiskScoreDiff() {
        return riskScoreDiff;
    }

    public void setRiskScoreDiff(Integer riskScoreDiff) {
        this.riskScoreDiff = riskScoreDiff;
    }

    public Integer getHoldScoreDiff() {
        return holdScoreDiff;
    }

    public void setHoldScoreDiff(Integer holdScoreDiff) {
        this.holdScoreDiff = holdScoreDiff;
    }

    public Integer getTradeScoreDiff() {
        return tradeScoreDiff;
    }

    public void setTradeScoreDiff(Integer tradeScoreDiff) {
        this.tradeScoreDiff = tradeScoreDiff;
    }

    public Integer getIndAllocScoreDiff() {
        return indAllocScoreDiff;
    }

    public void setIndAllocScoreDiff(Integer indAllocScoreDiff) {
        this.indAllocScoreDiff = indAllocScoreDiff;
    }

    public void combineOf(AcctDiagnoseDO acctDiagnoseDO, AcctDiagnoseAvgDO acctDiagnoseAvgDO){
        this.hasPosition = acctDiagnoseDO.getHasPosition();
        this.totalScore = acctDiagnoseDO.getTotalScore();
        this.totalScoreMax = acctDiagnoseDO.getTotalScoreMax();
        this.totalScorePct = acctDiagnoseDO.getTotalScorePct();
        this.profitScore = acctDiagnoseDO.getProfitScore();
        this.riskScore = acctDiagnoseDO.getRiskScore();
        this.maxDrawdown = acctDiagnoseDO.getMaxDrawdown();
        this.overDrawdown = acctDiagnoseDO.getOverDrawdown();
        this.volatility = acctDiagnoseDO.getVolatility();
        this.sharpeRatio = acctDiagnoseDO.getSharpeRatio();
        this.holdScore = acctDiagnoseDO.getHoldScore();
        this.holdWinRate = acctDiagnoseDO.getHoldWinRate();
        this.holdWinRatePct = acctDiagnoseDO.getHoldWinRatePct();
        this.tradeScore = acctDiagnoseDO.getTradeScore();
        this.tradeWinRate = acctDiagnoseDO.getTradeWinRate();
        this.tradeWinRatePct = acctDiagnoseDO.getTradeWinRatePct();
        this.indAllocScore = acctDiagnoseDO.getIndAllocScore();

        this.profitScoreAvg = acctDiagnoseAvgDO.getProfitScoreAvg();
        this.riskScoreAvg = acctDiagnoseAvgDO.getRiskScoreAvg();
        this.holdScoreAvg = acctDiagnoseAvgDO.getHoldScoreAvg();
        this.tradeScoreAvg = acctDiagnoseAvgDO.getTradeScoreAvg();
        this.indAllocScoreAvg = acctDiagnoseAvgDO.getIndAllocScoreAvg();
    }

    public static boolean isAvailable(AcctDiagnoseBO acctDiagnoseBO){
        return (acctDiagnoseBO != null && acctDiagnoseBO.getHasPosition() != null && acctDiagnoseBO.getHasPosition() == 1);
    }
}

