package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.ProfitDayMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitDay;
import com.eastmoney.common.entity.cal.ProfitStat;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/19.
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("profitDayDao")
public class ProfitDayDaoImpl extends BaseDao<ProfitDayMapper, ProfitDay, Long> implements ProfitDayDao {
    @Override
    public List<ProfitStat> getProfitStatList(Map<String, Object> params) {
        return mapper.getProfitStatList(params);
    }

    @Override
    public Double getSumProfit(Map<String, Object> params) {
        return mapper.getSumProfit(params);
    }

    @Override
    public Double getSumProfit(Long fundId, Integer startDate, Integer endDate) {
        Map<String,Object> param=new HashMap<>();
        param.put("fundId",fundId);
        param.put("startDate",startDate);
        param.put("endDate",endDate);
        return mapper.getSumProfit(param);
    }
}
