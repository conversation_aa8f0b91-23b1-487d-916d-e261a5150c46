package com.eastmoney.service.cache;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.util.CommConstants;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 交易日历缓存
 * <AUTHOR>
 * @create 2024/3/27
 */
@Service
public class TradeDateCacheService {
    private static Logger LOG = LoggerFactory.getLogger(TradeDateCacheService.class);

    @Resource
    private LoadingCache<String, Optional<List<Integer>>> lastTradeDateOfMonthCache;

    @Autowired
    private TradeDateDao tradeDateDao;

    @Bean(name = "lastTradeDateOfMonthCache")
    public LoadingCache<String, Optional<List<Integer>>> lastTradeDateOfMonthCache() {
        return CacheBuilder.newBuilder()
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .initialCapacity(5)
                .maximumSize(100)
                .refreshAfterWrite(10, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Optional<List<Integer>>>() {
                    @Override
                    public Optional<List<Integer>> load(String key) throws Exception {
                        String[] keys = key.split("-");
                        List<Integer> result = null;
                        try {
                            result = tradeDateDao.getLastTradeDateOfMonth(keys[0], keys[1])
                                    .stream()
                                    .map(t -> Integer.valueOf(t.getTradeDate()))
                                    .collect(Collectors.toList());
                        } catch (Exception ex) {
                            LOG.error(ex.getMessage(), ex);
                        }

                        return Optional.ofNullable(result);
                    }
                });
    }

    /**
     * 获取每个月最后一个交易日
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Integer> getLastTradeDateOfMonth(Integer startDate, Integer endDate) {
        try {
            if (startDate == null || endDate == null) {
                return new ArrayList<>();
            }

            String key = CommConstants.START_DATE + "-" + endDate;
            List<Integer> tradeDateList = lastTradeDateOfMonthCache.get(key).orElse(new ArrayList<>());

            return tradeDateList.stream()
                    .filter(t -> t >= startDate && t <= endDate)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("错误通过guava获取交易日历每个月最后一个交易日失败:{}, startDate:{}, endDate:{}", e, startDate, endDate);
        }

        return new ArrayList<>();
    }
}
