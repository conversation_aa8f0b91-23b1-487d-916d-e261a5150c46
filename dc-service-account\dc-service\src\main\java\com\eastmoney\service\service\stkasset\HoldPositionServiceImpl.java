package com.eastmoney.service.service.stkasset;

import com.google.common.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 13:57
 */
@Service
public class HoldPositionServiceImpl implements HoldPositionService {

    @Autowired
    private LoadingCache<Long, Map<String, Integer>> holdStartDateCache;

    /**
     * 查询持仓的建仓日期
     *
     * @param fundId
     * @return key = market-stkCode
     */
    @Override
    public Map<String, Integer> getHoldPositionStartDate(Long fundId) {
        return holdStartDateCache.getUnchecked(fundId);
    }
}
