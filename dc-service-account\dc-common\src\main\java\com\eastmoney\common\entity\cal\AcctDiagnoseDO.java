package com.eastmoney.common.entity.cal;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */

import com.eastmoney.common.entity.BaseEntity;

/**
 * Created on 2024-04-30
 * Description
 *
 * <AUTHOR>
 */
public class AcctDiagnoseDO extends BaseEntity {

    /**
     * 是否提供诊断数据 0-No 1-Yes
     */
    private Integer hasPosition;
    /**
     * 综合评分
     */
    private Integer totalScore;
    /**
     * 前一日综合评分
     */
    private Integer totalScorePre;
    /**
     * 历史最高综合评分
     */
    private Integer totalScoreMax;
    /**
     * 综合评分超越股友比
     */
    private Double totalScorePct;
    /**
     * 盈利能力分
     */
    private Integer profitScore;
    /**
     * 前一日盈利分
     */
    private Integer profitScorePre;
    /**
     * 风控能力分
     */
    private Integer riskScore;
    /**
     * 前一日风控分
     */
    private Integer riskScorePre;
    /**
     * 最大回撤
     */
    private Double maxDrawdown;
    /**
     * 超额回撤
     */
    private Double overDrawdown;
    /**
     * 波动率
     */
    private Double volatility;
    /**
     * 夏普率
     */
    private Double sharpeRatio;
    /**
     * 选股能力分
     */
    private Integer holdScore;
    /**
     * 前一日选股分
     */
    private Integer holdScorePre;
    /**
     * 持仓胜率
     */
    private Double holdWinRate;
    /**
     * 持仓胜率超越股友比
     */
    private Double holdWinRatePct;
    /**
     * 择时能力分
     */
    private Integer tradeScore;
    /**
     * 前一日择时分
     */
    private Integer tradeScorePre;
    /**
     * 择时胜率
     */
    private Double tradeWinRate;
    /**
     * 择时胜率超越股友比
     */
    private Double tradeWinRatePct;
    /**
     * 行业配置分
     */
    private Integer indAllocScore;
    /**
     * 前一日行业分
     */
    private Integer indAllocScorePre;
    /**
     * 偏好行业汇总(数据库)
     */
    private String preferIndList;

    public Integer getHasPosition() {
        return hasPosition;
    }

    public void setHasPosition(Integer hasPosition) {
        this.hasPosition = hasPosition;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getTotalScorePre() {
        return totalScorePre;
    }

    public void setTotalScorePre(Integer totalScorePre) {
        this.totalScorePre = totalScorePre;
    }

    public Integer getTotalScoreMax() {
        return totalScoreMax;
    }

    public void setTotalScoreMax(Integer totalScoreMax) {
        this.totalScoreMax = totalScoreMax;
    }

    public Double getTotalScorePct() {
        return totalScorePct;
    }

    public void setTotalScorePct(Double totalScorePct) {
        this.totalScorePct = totalScorePct;
    }

    public Integer getProfitScore() {
        return profitScore;
    }

    public void setProfitScore(Integer profitScore) {
        this.profitScore = profitScore;
    }

    public Integer getProfitScorePre() {
        return profitScorePre;
    }

    public void setProfitScorePre(Integer profitScorePre) {
        this.profitScorePre = profitScorePre;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Integer getRiskScorePre() {
        return riskScorePre;
    }

    public void setRiskScorePre(Integer riskScorePre) {
        this.riskScorePre = riskScorePre;
    }

    public Double getMaxDrawdown() {
        return maxDrawdown;
    }

    public void setMaxDrawdown(Double maxDrawdown) {
        this.maxDrawdown = maxDrawdown;
    }

    public Double getOverDrawdown() {
        return overDrawdown;
    }

    public void setOverDrawdown(Double overDrawdown) {
        this.overDrawdown = overDrawdown;
    }

    public Double getVolatility() {
        return volatility;
    }

    public void setVolatility(Double volatility) {
        this.volatility = volatility;
    }

    public Double getSharpeRatio() {
        return sharpeRatio;
    }

    public void setSharpeRatio(Double sharpeRatio) {
        this.sharpeRatio = sharpeRatio;
    }

    public Integer getHoldScore() {
        return holdScore;
    }

    public void setHoldScore(Integer holdScore) {
        this.holdScore = holdScore;
    }

    public Integer getHoldScorePre() {
        return holdScorePre;
    }

    public void setHoldScorePre(Integer holdScorePre) {
        this.holdScorePre = holdScorePre;
    }

    public Double getHoldWinRate() {
        return holdWinRate;
    }

    public void setHoldWinRate(Double holdWinRate) {
        this.holdWinRate = holdWinRate;
    }

    public Double getHoldWinRatePct() {
        return holdWinRatePct;
    }

    public void setHoldWinRatePct(Double holdWinRatePct) {
        this.holdWinRatePct = holdWinRatePct;
    }

    public Integer getTradeScore() {
        return tradeScore;
    }

    public void setTradeScore(Integer tradeScore) {
        this.tradeScore = tradeScore;
    }

    public Integer getTradeScorePre() {
        return tradeScorePre;
    }

    public void setTradeScorePre(Integer tradeScorePre) {
        this.tradeScorePre = tradeScorePre;
    }

    public Double getTradeWinRate() {
        return tradeWinRate;
    }

    public void setTradeWinRate(Double tradeWinRate) {
        this.tradeWinRate = tradeWinRate;
    }

    public Double getTradeWinRatePct() {
        return tradeWinRatePct;
    }

    public void setTradeWinRatePct(Double tradeWinRatePct) {
        this.tradeWinRatePct = tradeWinRatePct;
    }

    public Integer getIndAllocScore() {
        return indAllocScore;
    }

    public void setIndAllocScore(Integer indAllocScore) {
        this.indAllocScore = indAllocScore;
    }

    public Integer getIndAllocScorePre() {
        return indAllocScorePre;
    }

    public void setIndAllocScorePre(Integer indAllocScorePre) {
        this.indAllocScorePre = indAllocScorePre;
    }

    public String getPreferIndList() {
        return preferIndList;
    }

    public void setPreferIndList(String preferIndList) {
        this.preferIndList = preferIndList;
    }
}

