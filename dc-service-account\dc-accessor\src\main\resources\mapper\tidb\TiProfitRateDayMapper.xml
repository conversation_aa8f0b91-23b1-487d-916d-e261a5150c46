<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiProfitRateDayMapper">
    <resultMap id="BaseResultMap" type="as_profitRateDay" >
        <result column="FUNDID" property="fundId"/>
        <result column="PROFIT_RATE" property="profitRate"/>
        <result column="BIZDATE" property="bizDate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID, PROFIT_RATE, BIZDATE
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
            <include refid="All_Column"/>FROM ATCENTER.PROFIT_RATE_DAY t use index(PK_LG_PROFIT_RATE_DAY_NEW)
        <where>
            <if test="fundId != null">
                AND FUNDID = #{fundId}
            </if>
            <if test="startDate != null">
                AND BIZDATE >= #{startDate}
            </if>
            <if test="endDate != null">
                AND BIZDATE &lt;= #{endDate}
            </if>
            <if test="bizDate != null">
                AND BIZDATE = #{bizDate}
            </if>
        </where>
    </select>

    <select id="getSectionProfitRate" resultType="double">
        SELECT
        ifnull(POWER(10,SUM(LOG(10,(1 +
        CASE SIGN(PROFIT_RATE + 1)
        when 1 then  profit_rate
        when 0 then 0
        when -1 then 0
        end )
        )))-1,0)
        FROM ATCENTER.PROFIT_RATE_DAY t use index(PK_LG_PROFIT_RATE_DAY_NEW)
        where fundId = #{fundId}
        and bizDate BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>