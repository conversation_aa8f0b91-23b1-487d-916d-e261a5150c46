package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

import java.util.Date;

/**
 * Created by robin on 2016/7/18.
 * 收益时间片表
 * <AUTHOR>
 */
public class ProfitSection extends BaseEntityCal {
    private Date euTime;
    private Long fundId;//资金帐号
    private Double profit;//收益
    private String unit;//单位
    private Integer indexDate;  //对应查询周期的起始时间
    private int bakBizDate;   //最新计算时间前一天日期

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public int getBakBizDate() {
        return bakBizDate;
    }

    public void setBakBizDate(int bakBizDate) {
        this.bakBizDate = bakBizDate;
    }

    public Integer getIndexDate() {
        return indexDate;
    }

    public void setIndexDate(Integer indexDate) {
        this.indexDate = indexDate;
    }

    public Long getFundId() {
        return fundId;
    }

    public Double getProfit() {
        return profit;
    }

    public String getUnit() {
        return unit;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public void setUnit(String unit) {
        this.unit = unit.trim();
    }

}
