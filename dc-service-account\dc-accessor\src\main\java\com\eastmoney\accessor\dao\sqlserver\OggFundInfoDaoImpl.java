package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggFundInfoMapper;
import com.eastmoney.common.entity.FundInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created by sunyuncai on 2017/2/3
 */
@SqlServerTarget()
@Service
public class OggFundInfoDaoImpl implements OggFundInfoDao {

    @Autowired
    private OggFundInfoMapper oggFundInfoMapper;

    @Override
    public FundInfo getFundInfo(Map<String, Object> params) {
        return oggFundInfoMapper.getFundInfo(params);
    }
}
