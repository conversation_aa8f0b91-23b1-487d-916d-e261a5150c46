package com.eastmoney.service.service.stkasset;

import com.eastmoney.common.entity.DayProfitExtend;
import com.eastmoney.common.entity.PositionInfo;
import com.eastmoney.common.entity.StkAsset;

import java.util.List;

/**
 * Created on 2020/8/22-10:45.
 *
 * <AUTHOR>
 */
public interface StkAssetService {

    /**
     * 计算收益
     *
     * @param positionInfoList
     * @param moneyType
     * @param fundId
     * @param fundAll          实时总资产 用来计算实时仓位
     */
    void calPositionIncome(List<PositionInfo> positionInfoList, String moneyType, Long fundId, double fundAll);

    /**
     * APPAGILE-80350
     * 设置一些额外信息 比如持仓天数 行业 标签
     *
     * @param positionInfoList
     * @param fundId
     */
    void setExtInfo(List<PositionInfo> positionInfoList, Long fundId);

    /**
     * 获取港股通用户的持仓信息
     *
     * @param fundId
     * @param bizDate
     * @return
     */
    List<StkAsset> getStkAsset(Long fundId, Integer bizDate);

    List<StkAsset> getStkAsset(Long fundId, Integer bizDate, Integer serverId);

    Double getMktVal(StkAsset stkAsset, boolean dayProfitCalFeeFlag);

    /**
     * 查询个股期初市值
     */
    double getOpenMktVal(Long fundId, Integer bizDate, String moneyType, Integer serverId, String market, String stkCode, DayProfitExtend dayProfitExtend);
}

