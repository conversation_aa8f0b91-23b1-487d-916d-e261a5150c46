<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggMatchMapper">

    <select id="getRealTimeMatchList" resultType="as_match">
        select a.bsflag,a.trddate,a.matchtime,a.matchamt,a.matchqty, ltrim(rtrim(a.stkcode)) stkcode,a.stkname,a.market,
        a.matchsno,a.matchprice
        from run.dbo.match a with (nolock)
        <where> a.matchtype = '0'
            and a.fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND ltrim(rtrim(a.stkcode)) IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and ltrim(rtrim(a.stkcode)) = #{stkCode}
                    </if>
                </otherwise>
            </choose>
            <if test="market != null">
            and a.market = #{market}
            </if>
            <if test="bsFlagList!=null">
                and a.bsflag in
                <foreach collection="bsFlagList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.matchsno desc
    </select>

    <select
            id="getAllRealTimeMatchList" resultType="as_match">
        select bsflag,trddate,matchtime,matchamt,matchqty, ltrim(rtrim(stkcode)) stkcode,stkname,market,
        matchsno,matchprice
        from run.dbo.match with (nolock)
        <where> matchtype = '0'
            and fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND ltrim(rtrim(stkcode)) IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and ltrim(rtrim(stkcode)) = #{stkCode}
                    </if>
                </otherwise>
            </choose>
            <if test="market != null">
                and market = #{market}
            </if>
            <if test="bsFlagList!=null">
                and bsflag in
                <foreach collection="bsFlagList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        union all
        select bsflag,trddate,matchtime,matchamt,matchqty, ltrim(rtrim(stkcode)) stkcode,stkname,market,
        matchsno,matchprice
        from run.dbo.hgt_match with(index = hgt_match_custid, nolock)
        <where> matchtype = '0'
            <if test="custId != null">
                and custid = #{custId}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND ltrim(rtrim(stkcode)) IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and ltrim(rtrim(stkcode)) = #{stkCode}
                    </if>
                </otherwise>
            </choose>
            <if test="market != null">
                and market = #{market}
            </if>
            <if test="bsFlagList!=null">
                and bsflag in
                <foreach collection="bsFlagList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>