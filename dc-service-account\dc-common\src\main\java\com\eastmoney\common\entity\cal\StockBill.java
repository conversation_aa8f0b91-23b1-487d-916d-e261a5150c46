package com.eastmoney.common.entity.cal;


import com.eastmoney.common.entity.BaseEntity;

/**
 * 股票账单表
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/31.
 */
public class StockBill extends BaseEntity {

    private Long fundId; //资金账号
    private Integer indexKey; //时间槽索引key，年:yyyy,月：yyyyMM
    private String timeSlot; //时间槽类型，年:Y，月:M
    private String profitType; //收益类型：1-盈利 2-亏损
    private Integer rankIndex; //类型排名序号：盈利降序，亏损降序
    private String stkCode; //股票代码
    private String market; //市场代码
    private String stkName; //股票名称
    private Double profit; //收益，人民币
    private Integer positionDays; //持仓天数
    private Integer positionTimes; //实现持仓次数
    private Double totalCost; //总费用
    private Double totalOtherExpend; //其他总支出
    private Double totalOtherIncome; //其他总收入
    private Double totalBuy; //总买入金额
    private Double totalSale; //总卖出金额
    private Long totalMatchQtyBuy; //证券总买入数量
    private Long totalMatchQtySale; //证券总卖出数量
    private Double avgSpread; //平均买卖价差
    private Double chgAmtRate; //变化金额比例

    public Double getChgAmtRate() {
        return chgAmtRate;
    }

    public void setChgAmtRate(Double chgAmtRate) {
        this.chgAmtRate = chgAmtRate;
    }

    public Double getAvgSpread() {
        return avgSpread;
    }

    public void setAvgSpread(Double avgSpread) {
        this.avgSpread = avgSpread;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public Integer getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Integer indexKey) {
        this.indexKey = indexKey;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public String getProfitType() {
        return profitType;
    }

    public void setProfitType(String profitType) {
        this.profitType = profitType;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Double getProfit() {
        return profit;
    }

    public void setProfit(Double profit) {
        this.profit = profit;
    }

    public Integer getPositionDays() {
        return positionDays;
    }

    public void setPositionDays(Integer positionDays) {
        this.positionDays = positionDays;
    }

    public Integer getPositionTimes() {
        return positionTimes;
    }

    public void setPositionTimes(Integer positionTimes) {
        this.positionTimes = positionTimes;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public Double getTotalOtherExpend() {
        return totalOtherExpend;
    }

    public void setTotalOtherExpend(Double totalOtherExpend) {
        this.totalOtherExpend = totalOtherExpend;
    }

    public Double getTotalOtherIncome() {
        return totalOtherIncome;
    }

    public void setTotalOtherIncome(Double totalOtherIncome) {
        this.totalOtherIncome = totalOtherIncome;
    }

    public Double getTotalBuy() {
        return totalBuy;
    }

    public void setTotalBuy(Double totalBuy) {
        this.totalBuy = totalBuy;
    }

    public Double getTotalSale() {
        return totalSale;
    }

    public void setTotalSale(Double totalSale) {
        this.totalSale = totalSale;
    }

    public Long getTotalMatchQtyBuy() {
        return totalMatchQtyBuy;
    }

    public void setTotalMatchQtyBuy(Long totalMatchQtyBuy) {
        this.totalMatchQtyBuy = totalMatchQtyBuy;
    }

    public Long getTotalMatchQtySale() {
        return totalMatchQtySale;
    }

    public void setTotalMatchQtySale(Long totalMatchQtySale) {
        this.totalMatchQtySale = totalMatchQtySale;
    }
}
