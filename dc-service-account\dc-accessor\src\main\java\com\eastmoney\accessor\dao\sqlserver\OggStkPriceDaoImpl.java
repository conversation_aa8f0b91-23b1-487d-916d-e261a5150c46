package com.eastmoney.accessor.dao.sqlserver;

import com.eastmoney.accessor.annotation.SqlServerTarget;
import com.eastmoney.accessor.mapper.sqlserver.OggStkPriceMapper;
import com.eastmoney.common.entity.StkPrice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by sunyuncai on 2016/11/4
 */
@SqlServerTarget()
@Service
public class OggStkPriceDaoImpl implements OggStkPriceDao {
    @Autowired
    private OggStkPriceMapper mapper;
    @Override
    public List<StkPrice> query(Map params){
        return mapper.query(params);
    }

    @Override
    public Map<String, StkPrice> getStkPriceMap(Map<String, Object> param) {
        return mapper.query(param)
                .stream()
                .filter(o -> o.getStkCode() != null && o.getMarket() != null)
                .collect(Collectors.toMap(o -> String.join("-", o.getMarket().trim(), o.getStkCode().trim()),
                        Function.identity(), (a, b) -> a));
    }
}
