package com.eastmoney.common.entity;

import com.eastmoney.common.util.DateUtil;

import java.util.Date;

/**
 * Created on 2016/3/3
 * 定时任务日志
 *
 * <AUTHOR>
 */
public class ScheduleLog extends BaseEntity {
    //    private String eid;//系统物理主键
//    private String eiTime;//数据入库时间
//    private String euTime;//数据修改时间
    private String schedule_name;//定时任务名称
    private Integer result;//执行结果
    private String error;//错误信息
    private Date start_time;//开始时间
    private Date end_time;//结束时间
    private Integer bizDate;//日志操作日期
    private String sys_flag;//所属系统标识



    public String getSchedule_name() {
        return schedule_name;
    }

    public void setSchedule_name(String schedule_name) {
        this.schedule_name = schedule_name.trim();
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getError() {
        return error;
    }

    public void setError(String error){
        try{
            int length = error.getBytes("GBK").length;
            if (length > 100) {
                byte[] errorByte = error.getBytes("GBK");
                for(int i = 0;i < 100; i++){
                    error += errorByte[i];
                }
            }
            this.error = error;
        }catch (Exception e){
            this.error = "error字符串字节转换失败";
        }

    }
    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public Date getStart_time() {
        return start_time;
    }

    public void setStart_time(Date start_time) {
        this.start_time = start_time;
    }

    public Date getEnd_time() {
        return end_time;
    }

    public void setEnd_time(Date end_time) {
        this.end_time = end_time;
    }

    public ScheduleLog(String sys_flag) {
        this.sys_flag = sys_flag;
    }
    public ScheduleLog(){

    }

    public ScheduleLog(String schedule_name,Integer sourceServerId,String sys_flag) {
        this.schedule_name = schedule_name.trim();
        this.start_time = new Date();
        this.bizDate = Integer.parseInt(DateUtil.dateToStr(start_time, DateUtil.yyyyMMdd));
        this.sys_flag =sys_flag;
        this.setServerId(sourceServerId);
    }

    public ScheduleLog(String schedule_name,String sys_flag) {
        this.schedule_name = schedule_name.trim();
        this.start_time = new Date();
        this.sys_flag = sys_flag;
        this.bizDate = Integer.parseInt(DateUtil.dateToStr(start_time, DateUtil.yyyyMMdd));
    }

    public void setResultAndError(Integer result,String error){
        this.setResult(result);
        this.setError(error);
    }



    public String getSys_flag() {
        return sys_flag;
    }

    public void setSys_flag(String sys_flag) {
        this.sys_flag = sys_flag.trim();
    }
}
