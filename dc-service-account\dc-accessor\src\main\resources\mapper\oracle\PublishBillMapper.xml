<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.PublishBillMapper">

    <sql id="All_Column">
        fundId,indexKey,timeSlot,publishCode,publishName,rankIndex
    </sql>

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.PublishBill">
        select * from (
            select <include refid="All_Column"/>
            from atcenter.b_publish_bill
            where fundid = #{fundId} and indexKey = #{indexKey}
            order by rankIndex ASC
        ) where rownum &lt;= #{count}
    </select>

</mapper>