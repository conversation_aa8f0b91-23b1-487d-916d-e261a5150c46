package com.eastmoney.service.handler;

import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.IPOProfitInfoBO;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.cache.IpoSettleStatusCacheService;
import com.eastmoney.service.service.profit.ipo.IpoPositionProfitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 新股新债打新收益信息查询
 */
@Service
public class IpoPositionInfoHandler {
    @Autowired
    private IpoPositionProfitService ipoPositionProfitService;
    @Autowired
    private IpoSettleStatusCacheService ipoSettleStatusCacheService;
    @Autowired
    private AssetNewService assetNewService;

    public IPOProfitInfoBO getIpoPositionProfitList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        Integer startDate = CommonUtil.convert(params.get("startDate"), Integer.class);
        Integer endDate = CommonUtil.convert(params.get("endDate"), Integer.class);

        IPOProfitInfoBO ipoProfitInfoBO = new IPOProfitInfoBO();
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew != null) {
            ipoProfitInfoBO = ipoSettleStatusCacheService.getIpoSettleStatus(assetNew.getServerId());
        }
        ipoProfitInfoBO.setIpoPositionProfitDOList(ipoPositionProfitService.getIPOPositionProfitList(fundId, startDate, endDate));
        return ipoProfitInfoBO;
    }
}
