package com.eastmoney.accessor.service;

import com.eastmoney.common.entity.FundInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
@Service
public class FundInfoCacheService {
    private static Logger LOG = LoggerFactory.getLogger(FundInfoCacheService.class);
    @Resource(name = "fundInfoCache")
    private LoadingCache<String, Optional<FundInfo>> fundInfoCache;
    @Autowired
    private FundInfoService fundInfoService;

    @Bean(name = "fundInfoCache")
    public LoadingCache<String, Optional<FundInfo>> fundInfoCache() {
        LoadingCache<String, Optional<FundInfo>> fundInfoCache = CacheBuilder.newBuilder().concurrencyLevel(Runtime.getRuntime().availableProcessors()).initialCapacity(10).maximumSize(10).refreshAfterWrite(10, TimeUnit.MINUTES).build(new CacheLoader<String, Optional<FundInfo>>() {
            @Override
            public Optional<FundInfo> load(String key) throws Exception {
                FundInfo fundInfo = null;
                try {
                    Map<String, Object> param = new HashMap<>();
                    String[] params = key.split("-");
                    param.put("serverId", params[1]);
                    param.put("fundId", params[0]);
                    fundInfo = fundInfoService.getFundInfo(param);
                } catch (Exception ex) {
                    LOG.error(ex.getMessage(), ex);
                }
                return Optional.ofNullable(fundInfo);
            }
        });
        return fundInfoCache;
    }

    public FundInfo getFundInfo(Long fundId, Integer serverId) {
        try {
            return fundInfoCache.get(String.join("-", fundId.toString(), serverId.toString())).orElse(null);
        } catch (ExecutionException e) {
            LOG.error("错误通过guava获取fundInfo信息失败", e);
        }
        return null;
    }
}
