package com.eastmoney.common.entity.cal;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.eastmoney.common.entity.BaseEntityCal;

import java.util.List;

/**
 * 新股新债打新收益信息
 *
 * <AUTHOR>
 * @date 2021/11/29
 */
public class IPOProfitInfoBO extends BaseEntityCal {
    /**
     * 清算日期
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer bizDate;
    /**
     * 清算任务标识
     */
    private String businessCode;
    /**
     * 打新收益列表
     */
    List<IPOPositionProfitDO> ipoPositionProfitDOList;

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public List<IPOPositionProfitDO> getIpoPositionProfitDOList() {
        return ipoPositionProfitDOList;
    }

    public void setIpoPositionProfitDOList(List<IPOPositionProfitDO> ipoPositionProfitDOList) {
        this.ipoPositionProfitDOList = ipoPositionProfitDOList;
    }
}
