package com.eastmoney.common.entity;

import java.util.Map;

/**
 * 日收益计算扩展信息
 *
 * <AUTHOR>
 * @date 2024/8/30
 */
public class DayProfitExtend {
    // T-1日港股通结算汇率
    private Map<String, Double> settRateMap;
    // 港股通假期收益计算标识
    private Boolean calHkProfitFlag;
    // 港股通假期收益
    private Double hkProfit;

    private DayProfitExtend(Builder builder) {
        setSettRateMap(builder.settRateMap);
        setCalHkProfitFlag(builder.calHkProfitFlag);
        setHkProfit(builder.hkProfit);
    }

    public Map<String, Double> getSettRateMap() {
        return settRateMap;
    }

    public void setSettRateMap(Map<String, Double> settRateMap) {
        this.settRateMap = settRateMap;
    }

    public Boolean getCalHkProfitFlag() {
        return calHkProfitFlag;
    }

    public void setCalHkProfitFlag(Boolean calHkProfitFlag) {
        this.calHkProfitFlag = calHkProfitFlag;
    }

    public Double getHkProfit() {
        return hkProfit;
    }

    public void setHkProfit(Double hkProfit) {
        this.hkProfit = hkProfit;
    }


    public static final class Builder {
        private Map<String, Double> settRateMap;
        private Boolean calHkProfitFlag;
        private Double hkProfit;

        public Builder() {
        }

        public Builder settRateMap(Map<String, Double> val) {
            settRateMap = val;
            return this;
        }

        public Builder calHkProfitFlag(Boolean val) {
            calHkProfitFlag = val;
            return this;
        }

        public Builder hkProfit(Double val) {
            hkProfit = val;
            return this;
        }

        public DayProfitExtend build() {
            return new DayProfitExtend(this);
        }
    }
}
