package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.sqlserver.OggMatchDao;
import com.eastmoney.common.entity.Match;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/5/31
 */
@Service
public class MatchServiceImpl implements MatchService {

    @Autowired
    private OggMatchDao oggMatchDao;

    @Override
    public List<Match> getRealTimeMatchList(Map<String, Object> params) {
        return oggMatchDao.getRealTimeMatchList(params);
    }

    @Override
    public List<Match> getAllRealTimeMatchList(Map<String, Object> params) {
        List<Match> realTimeMatchList = oggMatchDao.getAllRealTimeMatchList(params);
        return realTimeMatchList.stream()
                .sorted(Comparator.comparing(Match::getMatchSno).reversed())
                .collect(Collectors.toList());

    }
}
