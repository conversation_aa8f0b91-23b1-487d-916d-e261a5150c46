<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- 动态数据源 -->
    <bean id="dataSource" class="com.eastmoney.accessor.datasource.DynamicDataSource">
        <property name="dsConfigFile" value="dataSource.xml"/>
    </bean>

    <!-- mybatis配置 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="mapperLocations" value="classpath*:mapper/*/*.xml"/>
        <property name="configLocation" value="classpath:/mybatis-config.xml"/>
    </bean>

    <!-- 自动创建映射器，不用单独为每个 mapper映射-->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.eastmoney.accessor.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!-- 事务管理器配置,单数据源事务 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
        <qualifier value="transactionMgr"/>
    </bean>

    <!-- 可通过注解控制事务，即事物处理的机制 -->
    <tx:annotation-driven transaction-manager="transactionManager" order="2"/>
</beans>