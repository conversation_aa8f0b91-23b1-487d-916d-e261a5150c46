package com.eastmoney.accessor.dao.fundia;

import com.eastmoney.common.entity.fundia.CombAsset;
import com.eastmoney.common.entity.fundia.CombProfit;

import java.util.List;
import java.util.Map;

public interface FundInvestDao {
    /**
     * 基金投顾资产
     * @param params
     * @return
     */
    List<CombAsset> queryCombAsset(Map<String, Object> params);

    /**
     * 基金投顾收益
     * @param params
     * @return
     */
    List<CombProfit> queryCombProfit(Map<String, Object> params);

    /**
     * 基金投顾费用
     * @param params
     * @return
     */
    Map<String, Double> queryInvestFee(Map<String, Object> params);
}
