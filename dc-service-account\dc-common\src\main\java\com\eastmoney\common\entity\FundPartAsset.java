package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/3.
 */
public class FundPartAsset {
    public Integer serverid;
    public Long custid;
    public String orgid;
    public Long fundid;
    public String fundcode;
    public String moneytype;
    public Double lasttotalamt;
    public Double totalamt;
    public Double avlamt;
    public Double funduncomein;
    public Double funduncomeout;
    public Double fundrealin;
    public Double fundrealout;
    public Double fundnightout;
    public Double fundtranin;
    public Double fundtranout;
    public Double fundrealused;
    public Double fundrlusehk;
    public Double otheramt;
    public String fundflag;
    public String remark;

    public Integer getServerid() {
        return serverid;
    }

    public void setServerid(Integer serverid) {
        this.serverid = serverid;
    }

    public Long getCustid() {
        return custid;
    }

    public void setCustid(Long custid) {
        this.custid = custid;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public Long getFundid() {
        return fundid;
    }

    public void setFundid(Long fundid) {
        this.fundid = fundid;
    }

    public String getFundcode() {
        return fundcode;
    }

    public void setFundcode(String fundcode) {
        this.fundcode = fundcode;
    }

    public String getMoneytype() {
        return moneytype;
    }

    public void setMoneytype(String moneytype) {
        this.moneytype = moneytype;
    }

    public Double getLasttotalamt() {
        return lasttotalamt;
    }

    public void setLasttotalamt(Double lasttotalamt) {
        this.lasttotalamt = lasttotalamt;
    }

    public Double getTotalamt() {
        return totalamt;
    }

    public void setTotalamt(Double totalamt) {
        this.totalamt = totalamt;
    }

    public Double getAvlamt() {
        return avlamt;
    }

    public void setAvlamt(Double avlamt) {
        this.avlamt = avlamt;
    }

    public Double getFunduncomein() {
        return funduncomein;
    }

    public void setFunduncomein(Double funduncomein) {
        this.funduncomein = funduncomein;
    }

    public Double getFunduncomeout() {
        return funduncomeout;
    }

    public void setFunduncomeout(Double funduncomeout) {
        this.funduncomeout = funduncomeout;
    }

    public Double getFundrealin() {
        return fundrealin;
    }

    public void setFundrealin(Double fundrealin) {
        this.fundrealin = fundrealin;
    }

    public Double getFundrealout() {
        return fundrealout;
    }

    public void setFundrealout(Double fundrealout) {
        this.fundrealout = fundrealout;
    }

    public Double getFundnightout() {
        return fundnightout;
    }

    public void setFundnightout(Double fundnightout) {
        this.fundnightout = fundnightout;
    }

    public Double getFundtranin() {
        return fundtranin;
    }

    public void setFundtranin(Double fundtranin) {
        this.fundtranin = fundtranin;
    }

    public Double getFundtranout() {
        return fundtranout;
    }

    public void setFundtranout(Double fundtranout) {
        this.fundtranout = fundtranout;
    }

    public Double getFundrealused() {
        return fundrealused;
    }

    public void setFundrealused(Double fundrealused) {
        this.fundrealused = fundrealused;
    }

    public Double getFundrlusehk() {
        return fundrlusehk;
    }

    public void setFundrlusehk(Double fundrlusehk) {
        this.fundrlusehk = fundrlusehk;
    }

    public Double getOtheramt() {
        return otheramt;
    }

    public void setOtheramt(Double otheramt) {
        this.otheramt = otheramt;
    }

    public String getFundflag() {
        return fundflag;
    }

    public void setFundflag(String fundflag) {
        this.fundflag = fundflag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
