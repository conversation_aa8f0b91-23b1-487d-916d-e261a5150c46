package com.eastmoney.quote.mdstp.model;/**
 * Created by 1 on 16-10-31.
 */

import java.util.List;

/**
 * Created on 16-10-31
 *
 * <AUTHOR>
 */
public class HkQtRec extends Rec{

    private int dwDate;
    private String uniqueId;
    private String name;
    /**
     * 唯一标识 LengthStr 示例：HK|87001
     */
    private String code;
    /**
     * 市场 ushort 港股为116
     */
    protected short wMarket;
    /**
     * 类型 ushort
     * 外盘类型：'0': 默认 ’1‘:基金 '2':债券 '3':主板 '4':创业板 '5':牛熊证 '6':涡轮 '7':界内证
     */
    protected short wType;
    private byte tradeFlag;
    private int dwHigh;   //最高价
    private int dwLow;    //最低价
    private long volume;  //成交量
    private double amount;   //成交额
    private long tradeNum; //成交笔数
    private byte numBuy;   //
    private byte numSell; //
    // 买卖盘信息
    private List<BuySellInfo> buySellInfos;
    private long waiPan;   //外盘
    private long xCurVol;  //现手
    private byte cCurVol;  //现手方向
    private long xCurOI;  //持仓量变化
    private int dwAvg;   //均价
    private long dwOpenInterest;  //持仓量
    private int dwAvgPrice;     //今结算价
    private long preOpenInterest;  //昨持仓量
    private int dwPreAvgPrice;  //昨结算价
    private int dwPriceZT;    //涨停价
    private int dwPriceDT;   //跌停价

    public int getDwDate() {
        return dwDate;
    }

    public void setDwDate(int dwDate) {
        this.dwDate = dwDate;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public short getwMarket() {
        return wMarket;
    }

    public void setwMarket(short wMarket) {
        this.wMarket = wMarket;
    }

    public short getwType() {
        return wType;
    }

    public void setwType(short wType) {
        this.wType = wType;
    }

    public byte getTradeFlag() {
        return tradeFlag;
    }

    public void setTradeFlag(byte tradeFlag) {
        this.tradeFlag = tradeFlag;
    }

    public int getDwHigh() {
        return dwHigh;
    }

    public void setDwHigh(int dwHigh) {
        this.dwHigh = dwHigh;
    }

    public int getDwLow() {
        return dwLow;
    }

    public void setDwLow(int dwLow) {
        this.dwLow = dwLow;
    }

    public long getVolume() {
        return volume;
    }

    public void setVolume(long volume) {
        this.volume = volume;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public long getTradeNum() {
        return tradeNum;
    }

    public void setTradeNum(long tradeNum) {
        this.tradeNum = tradeNum;
    }

    public byte getNumBuy() {
        return numBuy;
    }

    public void setNumBuy(byte numBuy) {
        this.numBuy = numBuy;
    }

    public byte getNumSell() {
        return numSell;
    }

    public void setNumSell(byte numSell) {
        this.numSell = numSell;
    }

    public List<BuySellInfo> getBuySellInfos() {
        return buySellInfos;
    }

    public void setBuySellInfos(List<BuySellInfo> buySellInfos) {
        this.buySellInfos = buySellInfos;
    }

    public long getWaiPan() {
        return waiPan;
    }

    public void setWaiPan(long waiPan) {
        this.waiPan = waiPan;
    }

    public long getxCurVol() {
        return xCurVol;
    }

    public void setxCurVol(long xCurVol) {
        this.xCurVol = xCurVol;
    }

    public byte getcCurVol() {
        return cCurVol;
    }

    public void setcCurVol(byte cCurVol) {
        this.cCurVol = cCurVol;
    }

    public long getxCurOI() {
        return xCurOI;
    }

    public void setxCurOI(long xCurOI) {
        this.xCurOI = xCurOI;
    }

    public int getDwAvg() {
        return dwAvg;
    }

    public void setDwAvg(int dwAvg) {
        this.dwAvg = dwAvg;
    }

    public long getDwOpenInterest() {
        return dwOpenInterest;
    }

    public void setDwOpenInterest(long dwOpenInterest) {
        this.dwOpenInterest = dwOpenInterest;
    }

    public int getDwAvgPrice() {
        return dwAvgPrice;
    }

    public void setDwAvgPrice(int dwAvgPrice) {
        this.dwAvgPrice = dwAvgPrice;
    }

    public long getPreOpenInterest() {
        return preOpenInterest;
    }

    public void setPreOpenInterest(long preOpenInterest) {
        this.preOpenInterest = preOpenInterest;
    }

    public int getDwPreAvgPrice() {
        return dwPreAvgPrice;
    }

    public void setDwPreAvgPrice(int dwPreAvgPrice) {
        this.dwPreAvgPrice = dwPreAvgPrice;
    }

    public int getDwPriceZT() {
        return dwPriceZT;
    }

    public void setDwPriceZT(int dwPriceZT) {
        this.dwPriceZT = dwPriceZT;
    }

    public int getDwPriceDT() {
        return dwPriceDT;
    }

    public void setDwPriceDT(int dwPriceDT) {
        this.dwPriceDT = dwPriceDT;
    }

    @Override
    public String toString() {
        return "HKQtrec{" +
                "dwDate=" + dwDate +
                ", dwTime=" + dwTime +
                ", uniqueId='" + uniqueId + '\'' +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", tradeFlag=" + tradeFlag +
                ", dwClose=" + dwClose +
                ", dwDwOpen=" + dwOpen +
                ", high=" + dwHigh +
                ", low=" + dwLow +
                ", price=" + dwPrice +
                ", volume=" + volume +
                ", amount=" + amount +
                ", tradeNum=" + tradeNum +
                ", numBuy=" + numBuy +
                ", numSell=" + numSell +
                ", buySellInfos=" + buySellInfos +
                ", waiPan=" + waiPan +
                ", xCurVol=" + xCurVol +
                ", cCurVol=" + cCurVol +
                ", xCurOI=" + xCurOI +
                ", dwAvg=" + dwAvg +
                ", dwDwOpenInterest=" + dwOpenInterest +
                ", avgPrice=" + dwAvgPrice +
                ", preDwOpenInterest=" + preOpenInterest +
                ", preAvgPrice=" + dwPreAvgPrice +
                ", priceZT=" + dwPriceZT +
                ", priceDT=" + dwPriceDT +
                '}';
    }
}
