package com.eastmoney.transport.codec;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import com.eastmoney.transport.util.ZLibUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteOrder;


public class MessageEncoder extends MessageToByteEncoder {
    private static Logger LOG = LoggerFactory.getLogger(MessageEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, Object msg, ByteBuf out) throws Exception {
        out = out.order(ByteOrder.LITTLE_ENDIAN);
        if (msg instanceof Message) {
            Message message = (Message) msg;
            out.writeByte(message.getSplit());
            out.writeInt(message.getPackLen());
            out.writeByte(message.getType());
            out.writeBytes(message.getRequestId());

            out.writeByte(message.getVersion());
            out.writeByte(message.getCipher());
            out.writeByte(message.getReplyCipher());

            byte compress = 0;
            byte[] content = message.getContent();
            //如果需要压缩
            if (content != null && content.length > Constants.COMPRESS_PACKAGE_LENGTH) {
                content = ZLibUtils.compress(content);
                compress = 1;
            }
            message.setCompress(compress);
            out.writeByte(message.getCompress());
            if (content != null) {
                out.writeBytes(content);
            }
            //重置长度 将PackLen长度置为实际长度
            out.setInt(1, out.writerIndex());
            message.setPackLen(out.writerIndex());
            if (message.getType() != 3 && message.getType() != 4) {
                LOG.debug("encode============"+message + " channel=" + ctx.channel());
            }
        }
    }
}
