package test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/20.
 */
public class TestZJDBC {
    public static void main(String[] args) throws Exception{
        // 数据库连接信息
        String className = "oracle.jdbc.driver.OracleDriver";
         String url = "*******************************************";
        String userName = "emxztxkh";
        String userPwd = "tEfr23";
//        String url = "**********************************************";
//        String userName = "emxztxkh";
//        String userPwd = "tEfr23";



        // 加载驱动
        long startTime = System.currentTimeMillis();
        Class.forName(className);
        Connection conn = DriverManager.getConnection(url,userName,userPwd);
        System.out.println("连接时间：" + (System.currentTimeMillis() - startTime));

        startTime = System.currentTimeMillis();
        String sql = "select stkcode,market,stkname  from ATCENTER.stock where serverid = 1 ";
        PreparedStatement ps = conn.prepareStatement(sql);
        ResultSet resultSet =ps.executeQuery();
        System.out.println("查询时间：" + (System.currentTimeMillis() - startTime));

        startTime = System.currentTimeMillis();
        sql = "select stkcode,market,stkname  from ATCENTER.stock where serverid = 1";
        ps = conn.prepareStatement(sql);
        resultSet =ps.executeQuery();
        System.out.println("查询时间2：" + (System.currentTimeMillis() - startTime));

        startTime = System.currentTimeMillis();
        sql = "select stkcode,market,stkname  from ATCENTER.stock where serverid = 2";
        ps = conn.prepareStatement(sql);
        resultSet =ps.executeQuery();
        System.out.println("查询时间3：" + (System.currentTimeMillis() - startTime));

        startTime = System.currentTimeMillis();
        Map<String,String> map = new HashMap<>();
        while(resultSet.next()){
            String stkCode = resultSet.getString("stkCode");
            String market = resultSet.getString("market");
            String stkName = resultSet.getString("stkName");
            map.put(stkCode+"-"+market,stkName);
        }
        System.out.println("保存时间："+(System.currentTimeMillis() - startTime));
    }
}
