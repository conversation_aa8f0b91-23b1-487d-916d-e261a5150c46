package com.eastmoney.common.entity;

/**
 * Created on 2020/8/13-8:51.
 *
 * <AUTHOR>
 */
public class SectionProfitBean extends AccountTemporary {
    /**
     * 周期开始日期
     */
    private Integer indexDate;
    /**
     * 是否处在片收益率与片收益额的计算空档期
     */
    private boolean profitRateCalWindow;

    /**
     * 未清算标识
     */
    private Boolean noInit;

    public Integer getIndexDate() {
        return indexDate;
    }

    public void setIndexDate(Integer indexDate) {
        this.indexDate = indexDate;
    }

    public boolean isProfitRateCalWindow() {
        return profitRateCalWindow;
    }

    public void setProfitRateCalWindow(boolean profitRateCalWindow) {
        this.profitRateCalWindow = profitRateCalWindow;
    }

    public Boolean getNoInit() {
        return noInit;
    }

    public void setNoInit(Boolean noInit) {
        this.noInit = noInit;
    }
}
