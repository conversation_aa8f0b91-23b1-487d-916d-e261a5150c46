package com.eastmoney.accessor.enums;

/**
 * 0 正常交易
 * 1 缴款
 * 2 申购
 * 3 增发申购
 * 4 配号
 * 7 配售
 * 8 配售配号
 * 9 跨系统转托管
 * A 设置分红方式
 * B ETF认购
 * C ETF申购
 * D 基金金额认购
 * E 自主行权
 * F 基金转换
 * G 质押
 * H 债券回售
 * I 创设注销
 * J 基金拆分合并
 * K 跨市场现金替代
 * L 协议大宗交易
 * M 债券转股
 * N 盘后定价大宗
 * O 固收平台交易
 * Q 行权
 * R 融资融券划转
 * T 股票质押交易
 * X 国债购回
 * Y 企债购回
 * Z 质押购回
 * a 债券协议回购
 * b 配售减持
 * c 询价交易
 * h 债券赎回
 * j 债券借贷交易
 * m 股转做市转让
 * t 股转协议转让
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
public enum TrdIdEnum {
    TRADE("0", "正常交易"),
    PAYMENT("1", "缴款"),
    SG("2", "申购"),
    ZF_SG("3", "增发申购"),
    PH("4", "配号"),
    PS("7", "配售"),
    PS_PH("8", "配售配号"),
    ETF_RG("B", "ETF认购"),
    ETF_SG("C", "ETF申购"),
    ;
    private String value;
    private String name;

    TrdIdEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
