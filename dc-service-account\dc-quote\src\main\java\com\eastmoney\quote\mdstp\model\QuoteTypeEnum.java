package com.eastmoney.quote.mdstp.model;

/**
 * @Description 行情数据类型
 * @auther pengjiejie
 * @create 2020-07-15 10:36
 *
 * Unknown = 0,
 * /// <summary>
 * /// 交易时间
 * [Description("交易时间")]
 * /// </summary>
 * TradeTime = 1,
 * /// <summary>
 * /// 个股码表
 * /// </summary>
 * [Description("个股码表")]
 * SecurityDict = 2,
 * /// <summary>
 * /// 行情快照
 * /// </summary>
 * [Description("行情快照")]
 * QuoteSnapshot = 3,
 *
 * /// <summary>
 * /// 1分钟K线
 * /// </summary>
 * [Description("1分钟K线")]
 * KLineMinOne = 4,
 *
 * /// <summary>
 * /// 分钟行情
 * /// </summary>
 * [Description("分钟行情")]
 * MinuteQuote = 6,
 * /// <summary>
 * /// 资金流向
 * /// </summary>
 * [Description("资金流向")]
 * FundFlow = 8,
 * /// <summary>
 * /// 版本更新
 * /// </summary>
 * [Description("版本更新")]
 * Version = 10,
 * /// <summary>
 * /// 成交明细
 * /// </summary>
 * [Description("成交明细")]
 * TransactionDetail = 13,
 * /// <summary>
 * /// 板块成分
 * /// </summary>
 * [Description("板块成分")]
 * BlockMap = 14,
 * /// <summary>
 * /// 通用数据：20007国债逆回购National Debt Reverse Repurchase；20016板块涨停数家数
 * /// </summary>
 * [Description("通用数据")]
 * CommonData = 15,
 *
 * [Description("板块统计")]
 * BlockStat = 16,
 *
 *
 * /// <summary>
 * /// 港股通(沪)快照
 * /// </summary>
 * [Description("港股通(沪)快照")]
 * FILE_RT_SHKAMT = 100,
 * /// <summary>
 * /// 沪股通快照
 * /// </summary>
 * [Description("沪股通快照")]
 * FILE_RT_SHKAMT_HK = 101,
 *
 * /// <summary>
 * /// 港股通(沪)分时
 * /// </summary>
 * [Description("港股通(沪)分时")]
 * FILE_RTMIN_SHKAMT = 102,
 * /// <summary>
 * /// 沪股通分时
 * /// </summary>
 * [Description("沪股通分时")]
 * FILE_RTMIN_SHKAMT_HK = 103,
 *
 * /// <summary>
 * /// 港股通(深)快照
 * /// </summary>
 * [Description("港股通(深)快照")]
 * FILE_RT_SZHKAMT = 104,
 * /// <summary>
 * /// 深股通快照
 * /// </summary>
 * [Description("深股通快照")]
 * FILE_RT_SZHKAMT_HK = 105,
 *
 * /// <summary>
 * /// 港股通(深)分时
 * /// </summary>
 * [Description("港股通(深)分时")]
 * FILE_RTMIN_SZHKAMT = 106,
 * /// <summary>
 * /// 深股通分时
 * /// </summary>
 * [Description("深股通分时")]
 * FILE_RTMIN_SZHKAMT_HK = 107,
 *
 * /// <summary>
 * /// DK点
 * /// </summary>
 * [Description("DK点")]
 * FILE_STOCKDK = 114,
 *
 * /// <summary>
 * /// DK点文件数据
 * /// </summary>
 * [Description("DK点文件")]
 * FILE_DKGAMEWEB = 115
 */
public enum QuoteTypeEnum {
    QuoteHSSnapshot((byte) 3, "行情快照"),
    QuoteHKSnapshot((byte) 53, "港股行情快照");
    private byte value;
    private String name;

    QuoteTypeEnum(byte value, String name) {
        this.value = value;
        this.name = name;
    }

    public byte getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
