<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.LogBankTranMapper">

    <select id="selectByCondition" resultType="as_logBankTran">
        SELECT EID, SYDATE, OPERTIME, SNO, BANKID, FUNDID, BANKTRANID, FUNDEFFECT, STATUS
        FROM ATCENTER.LOGBANKTRAN
        where fundid = {fundId}
            and banktranid IN ('1','2','5','6','M','N','F') AND fundeffect != 0 AND status IN ('2','6','7','8')
            order by EID
    </select>
    <sql id="Base_Column_List">
        EID,EITIME,EUTIME,RESEND_FLAG,SYDATE
        ,OPERTIME,SNO,BANKID,FUNDID,BANKTRANID,FUNDEFFECT,STATUS
    </sql>

    <sql id="Base_Column_Value">
        XTZX.SEQ_EID.NEXTVAL,SYSDATE,SYSDATE,0,#{syDate}
        ,#{operTime},#{sno},#{bankId},#{fundId},#{bankTranId},#{fundEffect},#{status}
    </sql>

    <sql id="All_Column_Value">
        XTZX.SEQ_EID.NEXTVAL,SYSDATE,SYSDATE,0
        ,#{serverId},#{syDate},#{operDate},#{operTime},#{sno},#{linkSno},#{strikeSno},#{moneyType},#{orgId},#{brhId}
        ,#{bankCode},#{bankBranch},#{bankNetPlace},#{bankTranId},#{custId},#{custName} ,#{fundId},#{sfFundId},#{reportKind},#{custKind}
        ,#{custGroup},#{fundKind},#{fundLevel},#{fundGroup},#{linkFlag},#{linkAccId},#{idType},#{idNo},#{bankId},#{sourceType}
        ,#{fundEffect},#{fundBal},#{status},#{bankPwd},#{bankMsgId},#{bankMsg},#{sysErrId},#{dealTime},#{bankSno},#{strikeNum}
        ,#{strikeFlag},#{doHistFlag},#{agentId},#{operId},#{operWay},#{operOrg},#{operLevel},#{netAddr},#{chkOper},#{agentOper}
        ,#{remark},#{remark1},#{extSno}
    </sql>

</mapper>