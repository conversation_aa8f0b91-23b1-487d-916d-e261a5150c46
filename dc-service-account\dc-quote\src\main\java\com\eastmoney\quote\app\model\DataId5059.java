package com.eastmoney.quote.app.model;/**
 * Created by 1 on 16-10-20.
 */

/**
 * Created on 16-10-20
 *
 * <AUTHOR>
 */
public class DataId5059 {

    private int iPriceLast; // 最新价
    private int iPriceHigh; // 最高价
    private int iPriceLow; // 最低价
    private int iPreClose;// 昨收价  unsigned int,(如果是股指期货和期权，给的是昨结算数据)
    private long liVolume; // 成交量  signed large int (手)
    private long liAmount; // 成交额  signed large int  (元)
    private long liTotalShares;// 总股本  signed large int (万股)
    private long liNetShare; // 流通股本 signed large int  (万股) （对于个股，用来计算换手率和市值；对于板块和指数，只用来计算市值）
    private int iNetCapital;  // 市盈率(动)    int (两位小数，不支持推送)
    private short nsMarketCodeLength; // 代码       NString
    private byte[] nsMarketCodeContext;
    private String nsMarket;
    private short nsNameLength; // 名称       NString gbk编码（跟以前的UTF8不一样）
    private byte[] nsNameContext;
    private String nsName;
    private byte cDecimalNum; // 小数位数  byte   (不支持排序)
    private long liDrIEPS; // 每股收益   signed large int   (9位小数)   (不支持排序)
    private byte cSeason; // 季度       byte   (不支持排序)
    private int iUpPercent; // 涨幅%       int   (2位小数，不返回也不推送,仅用来排序)
    private int iUpDown; // 涨跌       int   (N位小数，不返回也不推送,仅用来排序)
    private int iTurnoverRatio; // 换手%       int   (2位小数，请求返回和只推送板块,支持排序)
    private long uliTotalValue; // 总市值      unsigned large int  (不返回也不推送,仅用来排序)
    private long uliCurrentValue;  // 流通市值     unsigned large int  (不返回也不推送,仅用来排序)
    private long liNetShareEx; // 扩展流通股本 signed large int（万股）（不支持排序。对于个股，等于流通股本liNetShare；对于板块和指数，只用来计算换手率。）
    private byte cStockStatus; // 产品状态  byte  (按位存储，目前只启用最低两位，00表示正常交易，10表示临时停牌，11表示连续停牌。其它位保留，以后可以扩展成其它产品状态。注意：客户端只显示停牌，不需要区分临时停牌和连续停牌。)

    public int getiPriceLast() {
        return iPriceLast;
    }

    public void setiPriceLast(int iPriceLast) {
        this.iPriceLast = iPriceLast;
    }

    public int getiPriceHigh() {
        return iPriceHigh;
    }

    public void setiPriceHigh(int iPriceHigh) {
        this.iPriceHigh = iPriceHigh;
    }

    public int getiPriceLow() {
        return iPriceLow;
    }

    public void setiPriceLow(int iPriceLow) {
        this.iPriceLow = iPriceLow;
    }

    public int getiPreClose() {
        return iPreClose;
    }

    public void setiPreClose(int iPreClose) {
        this.iPreClose = iPreClose;
    }

    public long getLiVolume() {
        return liVolume;
    }

    public void setLiVolume(long liVolume) {
        this.liVolume = liVolume;
    }

    public long getLiAmount() {
        return liAmount;
    }

    public void setLiAmount(long liAmount) {
        this.liAmount = liAmount;
    }

    public long getLiTotalShares() {
        return liTotalShares;
    }

    public void setLiTotalShares(long liTotalShares) {
        this.liTotalShares = liTotalShares;
    }

    public long getLiNetShare() {
        return liNetShare;
    }

    public void setLiNetShare(long liNetShare) {
        this.liNetShare = liNetShare;
    }

    public int getiNetCapital() {
        return iNetCapital;
    }

    public void setiNetCapital(int iNetCapital) {
        this.iNetCapital = iNetCapital;
    }

    public short getNsMarketCodeLength() {
        return nsMarketCodeLength;
    }

    public void setNsMarketCodeLength(short nsMarketCodeLength) {
        this.nsMarketCodeLength = nsMarketCodeLength;
    }

    public byte[] getNsMarketCodeContext() {
        return nsMarketCodeContext;
    }

    public void setNsMarketCodeContext(byte[] nsMarketCodeContext) {
        this.nsMarketCodeContext = nsMarketCodeContext;
    }

    public String getNsMarket() {
        return nsMarket;
    }

    public void setNsMarket(String nsMarket) {
        this.nsMarket = nsMarket;
    }

    public short getNsNameLength() {
        return nsNameLength;
    }

    public void setNsNameLength(short nsNameLength) {
        this.nsNameLength = nsNameLength;
    }

    public byte[] getNsNameContext() {
        return nsNameContext;
    }

    public void setNsNameContext(byte[] nsNameContext) {
        this.nsNameContext = nsNameContext;
    }

    public String getNsName() {
        return nsName;
    }

    public void setNsName(String nsName) {
        this.nsName = nsName;
    }

    public byte getcDecimalNum() {
        return cDecimalNum;
    }

    public void setcDecimalNum(byte cDecimalNum) {
        this.cDecimalNum = cDecimalNum;
    }

    public long getLiDrIEPS() {
        return liDrIEPS;
    }

    public void setLiDrIEPS(long liDrIEPS) {
        this.liDrIEPS = liDrIEPS;
    }

    public byte getcSeason() {
        return cSeason;
    }

    public void setcSeason(byte cSeason) {
        this.cSeason = cSeason;
    }

    public int getiUpPercent() {
        return iUpPercent;
    }

    public void setiUpPercent(int iUpPercent) {
        this.iUpPercent = iUpPercent;
    }

    public int getiUpDown() {
        return iUpDown;
    }

    public void setiUpDown(int iUpDown) {
        this.iUpDown = iUpDown;
    }

    public int getiTurnoverRatio() {
        return iTurnoverRatio;
    }

    public void setiTurnoverRatio(int iTurnoverRatio) {
        this.iTurnoverRatio = iTurnoverRatio;
    }

    public long getUliTotalValue() {
        return uliTotalValue;
    }

    public void setUliTotalValue(long uliTotalValue) {
        this.uliTotalValue = uliTotalValue;
    }

    public long getUliCurrentValue() {
        return uliCurrentValue;
    }

    public void setUliCurrentValue(long uliCurrentValue) {
        this.uliCurrentValue = uliCurrentValue;
    }

    public long getLiNetShareEx() {
        return liNetShareEx;
    }

    public void setLiNetShareEx(long liNetShareEx) {
        this.liNetShareEx = liNetShareEx;
    }

    public byte getcStockStatus() {
        return cStockStatus;
    }

    public void setcStockStatus(byte cStockStatus) {
        this.cStockStatus = cStockStatus;
    }
}
