package com.eastmoney.accessor.dao;

import com.eastmoney.accessor.mapper.BaseMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class BaseDao<MAPPER extends BaseMapper, T, PK extends Serializable> implements IBaseDao<T, PK> {

    private static final Logger LOG = LoggerFactory.getLogger(BaseDao.class);

    protected T tt;
    /**
     * 实体操作的自动注入Mapper(随初始化一同注入，必须用set方法)
     */
    protected MAPPER mapper;

    public MAPPER getMapper() {
        return mapper;
    }

    @Autowired
    public void setMapper(MAPPER mapper) {
        this.mapper = mapper;
    }


    @Override
    public  List<T> query(Map<String, Object> map) {
        return mapper.selectByCondition(map);
    }
}
