package com.eastmoney.service.handler;


import com.eastmoney.accessor.dao.sqlserver.OggLogBankTranDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/27.
 */
@Service
public class LogBankTranHandler {

    @Autowired
    private OggLogBankTranDao oggLogBankTranDao;

    public List<?> query(Map<String, Object> params) {
        return oggLogBankTranDao.query(params);
    }
}
