package com.eastmoney.common.entity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/11/4.
 */
public class CreditContract {
    public Integer serverId;
    public Integer orderDate;
    public Integer orderSno;
    public String orderId;
    public Long fundId;
    public String orgId;
    public Long custId;
    public String moneyType;
    public String creditId;
    public String creditDirect;
    public String market;
    public String secuId;
    public String stkCode;
    public Integer orderQty;
    public Integer matchQty;
    public Integer cancelQty;
    public Double pledgeRate;
    public Double pledgeAsset;
    public Double marginRate;
    public Double creditRepayLast;
    public Double creditRepay;
    public Double creditRepayUnfrz;
    public Integer stkRepayLast;
    public Integer stkRepay;
    public Integer stkRepayUnfrz;
    public Double orderAmt;
    public Double orderFrzAmt;
    public Double clearAmt;
    public Double loanRate;
    public Double punishIntr;
    public Double punishAmt;
    public Double creditFee;
    public Double tradeFee;
    public String repayFlag;
    public Integer rightQty;
    public Double raghtAmt;

    public Integer getServerId() {
        return serverId;
    }

    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }

    public Integer getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Integer orderDate) {
        this.orderDate = orderDate;
    }

    public Integer getOrderSno() {
        return orderSno;
    }

    public void setOrderSno(Integer orderSno) {
        this.orderSno = orderSno;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCreditDirect() {
        return creditDirect;
    }

    public void setCreditDirect(String creditDirect) {
        this.creditDirect = creditDirect;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getSecuId() {
        return secuId;
    }

    public void setSecuId(String secuId) {
        this.secuId = secuId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getMatchQty() {
        return matchQty;
    }

    public void setMatchQty(Integer matchQty) {
        this.matchQty = matchQty;
    }

    public Integer getCancelQty() {
        return cancelQty;
    }

    public void setCancelQty(Integer cancelQty) {
        this.cancelQty = cancelQty;
    }

    public Double getPledgeRate() {
        return pledgeRate;
    }

    public void setPledgeRate(Double pledgeRate) {
        this.pledgeRate = pledgeRate;
    }

    public Double getPledgeAsset() {
        return pledgeAsset;
    }

    public void setPledgeAsset(Double pledgeAsset) {
        this.pledgeAsset = pledgeAsset;
    }

    public Double getMarginRate() {
        return marginRate;
    }

    public void setMarginRate(Double marginRate) {
        this.marginRate = marginRate;
    }

    public Double getCreditRepayLast() {
        return creditRepayLast;
    }

    public void setCreditRepayLast(Double creditRepayLast) {
        this.creditRepayLast = creditRepayLast;
    }

    public Double getCreditRepay() {
        return creditRepay;
    }

    public void setCreditRepay(Double creditRepay) {
        this.creditRepay = creditRepay;
    }

    public Double getCreditRepayUnfrz() {
        return creditRepayUnfrz;
    }

    public void setCreditRepayUnfrz(Double creditRepayUnfrz) {
        this.creditRepayUnfrz = creditRepayUnfrz;
    }

    public Integer getStkRepayLast() {
        return stkRepayLast;
    }

    public void setStkRepayLast(Integer stkRepayLast) {
        this.stkRepayLast = stkRepayLast;
    }

    public Integer getStkRepay() {
        return stkRepay;
    }

    public void setStkRepay(Integer stkRepay) {
        this.stkRepay = stkRepay;
    }

    public Integer getStkRepayUnfrz() {
        return stkRepayUnfrz;
    }

    public void setStkRepayUnfrz(Integer stkRepayUnfrz) {
        this.stkRepayUnfrz = stkRepayUnfrz;
    }

    public Double getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(Double orderAmt) {
        this.orderAmt = orderAmt;
    }

    public Double getOrderFrzAmt() {
        return orderFrzAmt;
    }

    public void setOrderFrzAmt(Double orderFrzAmt) {
        this.orderFrzAmt = orderFrzAmt;
    }

    public Double getClearAmt() {
        return clearAmt;
    }

    public void setClearAmt(Double clearAmt) {
        this.clearAmt = clearAmt;
    }

    public Double getLoanRate() {
        return loanRate;
    }

    public void setLoanRate(Double loanRate) {
        this.loanRate = loanRate;
    }

    public Double getPunishIntr() {
        return punishIntr;
    }

    public void setPunishIntr(Double punishIntr) {
        this.punishIntr = punishIntr;
    }

    public Double getPunishAmt() {
        return punishAmt;
    }

    public void setPunishAmt(Double punishAmt) {
        this.punishAmt = punishAmt;
    }

    public Double getCreditFee() {
        return creditFee;
    }

    public void setCreditFee(Double creditFee) {
        this.creditFee = creditFee;
    }

    public Double getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(Double tradeFee) {
        this.tradeFee = tradeFee;
    }

    public String getRepayFlag() {
        return repayFlag;
    }

    public void setRepayFlag(String repayFlag) {
        this.repayFlag = repayFlag;
    }

    public Integer getRightQty() {
        return rightQty;
    }

    public void setRightQty(Integer rightQty) {
        this.rightQty = rightQty;
    }

    public Double getRaghtAmt() {
        return raghtAmt;
    }

    public void setRaghtAmt(Double raghtAmt) {
        this.raghtAmt = raghtAmt;
    }
}
