package com.eastmoney.common.model;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/10/23
 * Time: 16:06
 */
public class LogBean {
    private String sys_no ; //系统编号
    private String u_id ; //客户标识
    private String o_time ; //操作时间
    private String o_channel ; //操作渠道
    private String c_ip ; //客户端ip
    private String s_ip ; //服务端ip
    private String dervice_id ; //终端设备标识
    private String in_param ; //入参
    private String out_param ; //出参
    private String action ; //操作行为标识
    private String log_type ; //日志类型
    private String t_id ; //事务流水号
    private String result ; //结果
    private String error_no ; //错误编号
    private String error_msg ; //错误说明
    private String remark ; //日志说明
    private String spent_time ; //耗时
    private String operator ; //操作人

    /**
     * 备用数值1，存放请求在队列中的耗时
     */
    public long value1;

    public LogBean() {
        this.sys_no = "dc-service-account";
    }

    public LogBean(String u_id) {
        this();
        this.u_id = u_id;
    }
    public LogBean(String u_id, String action, String error_msg) {
        this();
        this.u_id = u_id;
        this.action = action;
        this.error_msg = error_msg;
    }

    public String getSys_no() {
        return sys_no;
    }

    public void setSys_no(String sys_no) {
        this.sys_no = sys_no;
    }

    public String getU_id() {
        return u_id;
    }

    public void setU_id(String u_id) {
        this.u_id = u_id;
    }

    public String getO_time() {
        return o_time;
    }

    public void setO_time(String o_time) {
        this.o_time = o_time;
    }

    public String getO_channel() {
        return o_channel;
    }

    public void setO_channel(String o_channel) {
        this.o_channel = o_channel;
    }

    public String getC_ip() {
        return c_ip;
    }

    public void setC_ip(String c_ip) {
        this.c_ip = c_ip;
    }

    public String getS_ip() {
        return s_ip;
    }

    public void setS_ip(String s_ip) {
        this.s_ip = s_ip;
    }

    public String getDervice_id() {
        return dervice_id;
    }

    public void setDervice_id(String dervice_id) {
        this.dervice_id = dervice_id;
    }

    public String getIn_param() {
        return in_param;
    }

    public void setIn_param(String in_param) {
        this.in_param = in_param;
    }

    public String getOut_param() {
        return out_param;
    }

    public void setOut_param(String out_param) {
        this.out_param = out_param;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getLog_type() {
        return log_type;
    }

    public void setLog_type(String log_type) {
        this.log_type = log_type;
    }

    public String getT_id() {
        return t_id;
    }

    public void setT_id(String t_id) {
        this.t_id = t_id;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getError_no() {
        return error_no;
    }

    public void setError_no(String error_no) {
        this.error_no = error_no;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSpent_time() {
        return spent_time;
    }

    public void setSpent_time(String spent_time) {
        this.spent_time = spent_time;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public long getValue1() {
        return value1;
    }

    public void setValue1(long value1) {
        this.value1 = value1;
    }
}
