package com.eastmoney.accessor.enums;

/**
 * Created on 6/7/2024
 * Description
 *
 * <AUTHOR>
 */
public enum DiagnoseStockEnum {

    TOP_PROFIT("1", "持仓收益最高"),
    LOWEST_PROFIT("2", "持仓收益最低"),
    TOP_POSITION("3", "仓位占比最高"),
    TOP_MKTVAL("4", "市值最大"),
    TOP_HOLDDAYS("5", "持仓天数最久");


    private final String type;

    private final String describe;

    DiagnoseStockEnum(String type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    public String getType() {
        return type;
    }

    public String getDescribe() {
        return describe;
    }
}
