package com.eastmoney.common.entity;

import java.util.Date;
import java.util.Objects;

/**
 * 证券分层信息（股转）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 14:24
 */
public class StockLayeredInfo extends BaseEntity {
    /**
     * 证券代码
     */
    private String stkCode;
    /**
     * 所属层级
     */
    private String subLevel;
    /**
     * 日期
     */
    private Date changeDate;
    /**
     * 时间
     */
    private String changeTime;

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getSubLevel() {
        return subLevel;
    }

    public void setSubLevel(String subLevel) {
        this.subLevel = subLevel;
    }

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof StockLayeredInfo)) return false;
        StockLayeredInfo that = (StockLayeredInfo) o;
        return Objects.equals(stkCode, that.stkCode) && Objects.equals(subLevel, that.subLevel) && Objects.equals(changeDate, that.changeDate) && Objects.equals(changeTime, that.changeTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(stkCode, subLevel, changeDate, changeTime);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"stkCode\":\"")
                .append(stkCode).append('\"');
        sb.append(",\"subLevel\":\"")
                .append(subLevel).append('\"');
        sb.append(",\"changeDate\":\"")
                .append(changeDate).append('\"');
        sb.append(",\"changeTime\":\"")
                .append(changeTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
