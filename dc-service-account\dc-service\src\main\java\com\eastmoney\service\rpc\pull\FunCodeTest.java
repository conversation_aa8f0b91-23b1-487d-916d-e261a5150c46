package com.eastmoney.service.rpc.pull;

import com.eastmoney.transport.client.NettyClient;
import com.eastmoney.transport.client.NettyClientHolder;
import com.eastmoney.transport.client.ResponseFuture;
import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.Constants;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/9/16
 * Time: 14:43
 */
public class FunCodeTest {
    protected static org.slf4j.Logger LOG = LoggerFactory.getLogger(FunCodeTest.class);
//    private static ScheduledExecutorService executor = Executors.newScheduledThreadPool(Constants.CLIENT_THREAD_POOL);
    private static ExecutorService executor;
    public static void main(String[] args) throws Exception{
        executor = Executors.newFixedThreadPool(1);
        List<Task> taskList = new ArrayList<Task>();
        taskList.add(new Task());
        long beginTime = System.currentTimeMillis();
        List<Future<Object>> futures = executor.invokeAll(taskList);
        long endTime = System.currentTimeMillis();
        System.out.println("====总耗时：" + (endTime - beginTime) + "ms");
        executor.shutdownNow();
        executor.shutdown();

//        Thread.sleep(5000);
        System.exit(0);
    }

   private static class Task implements Callable<Object> {
        private static AtomicInteger counter = new AtomicInteger(0);
        private static String fixContent = "";
        private static volatile long beginTime = System.currentTimeMillis();
        private static volatile long lastTime = System.currentTimeMillis();
        static {
            if (Constants.SEND_BYTE_SIZE > 0) {
                for (int i = 0; i < Constants.SEND_BYTE_SIZE - 10; i++) {
                    fixContent += "0";
                }
            }
        }
        public Object call() {
            NettyClient nettyClient = null;
            try {
                nettyClient = NettyClientHolder.getNettyClient("hexin1");
//                String inputJson = "{\"Jjrzh\":\"100000105\",\"fundIds\":\"540800000238,540800000377,540800000378,540800000460,540800000489,540800000575,540800048513,540800048537,540800048637,540800048637\",\"funCode\":\"getDeliveryOrderSum\",\"bizDate\":201605}";
//                String inputJson = "{\"Jjrzh\":\"100000105\",\"Ym\":\"1\",\"fundIds\":\"540700264576,540700264578,540800000001,540800000002,540800000009,540800000111,540800000133,540800000199,540800000208,540800000220,540800000238,540800000377,540800000378,540800000460,540800000489,540800000575,540800048513,540800048537,540800048637,540800048637\",\"funCode\":\"getDeliveryOrderSum\",\"Qqhs\":\"20\",\"bizDate\":201605}";
//                String inputJson = "{\"fundId\":\"540700265459\",\"funCode\":\"getAssetInfo\",\"Qqhs\":\"20\",\"bizDate\":20160721}";
//                String inputJson = "{\"fundId\":\"540800232077\",\"funCode\":\"getProfitInfo\"}";
//                String inputJson = "{\"startDate\":20161221,\"unit\":\"M\",\"fundId\":\"541000000025\",\"funCode\":\"getProfitRateDayList\",\"endDate\":20161221}";
                String inputJson = "{\"zjzh\":540700265588,\"khdm\":540700265992,\"jgbm\":\"5407\",\"funCode\":\"getRealTimePositionList\"}";
//                String inputJson = "{\"fundId\":540800232121,\"pageNo\":1,\"funCode\":\"getRealTimePositionFund\",\"pageSize\":100,'sss':'1'}";
//                String inputJson = "{'funCode':'getBindChangeList','beginId':0}";
//                String inputJson = "{\"Jjrzh\":\"100000105\",\"Ym\":\"1\",\"fundIds\":\"540drop 7002645761\",\"funCode\":\"getDeliveryOrderSum\",\"Qqhs\":\"20\",\"bizDate\":201605}";
//                String inputJson = "{\"unit\":\"P3M\",\"Syspm_ex\":\"\",\"fundId\":\"540300181332\",\"funCode\":\"getAssetWorthTrend\"}";
//                System.out.println(JSON.toJSONString(input));
//                ResponseFuture future = nettyClient.request(JSON.toJSONString(input));
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getRealTimePosition\"}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getRealTimePositionProfit\"}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getRealTimeAsset\"}";
//                String inputJson = "{\"custId\":\"310100010810\",\"funCode\":\"getDayProfit\",\"fundId\":\"310100010810\",\"orgId\":\"6666666\"}";
//                String inputJson = "{\"fundId\":540700265588,\"custId\":540700265992,\"orgId\":\"5407\",\"funCode\":\"getDayProfit\"}";
//                String inputJson = "{\"fundId\":310100020522,'startDate':20161201,'endDate':20161231,\"funCode\":\"getProfitDayList\"}";
//                String inputJson = "{\"fundId\"ty:540800270158,\"funCode\":\"getRealTimePosition\"}";
//                String inputJson = "{\"unit\":\"P3M\",\"fundId\":5408002701518,\"funCode\":\"getProfitRateRankInfo\"}";
                ResponseFuture future = nettyClient.request(inputJson);
                Message message = (Message) future.get();
                String replyContent = new String(message.getContent(), "UTF-8");
//                LOG.info("返回内容:" + replyContent);

                if ((counter.incrementAndGet()%10000) == 0) {
                    long nowTime = System.currentTimeMillis();
                    System.out.println("====已执行数据量:" + counter.get() + " 耗时:" + (nowTime - beginTime) + "ms,万笔耗时：" + (nowTime - lastTime) + "ms");
                    lastTime = nowTime;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }
}

