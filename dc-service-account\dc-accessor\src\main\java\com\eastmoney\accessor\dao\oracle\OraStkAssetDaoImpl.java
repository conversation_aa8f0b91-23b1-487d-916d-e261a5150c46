package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.OraStkAssetMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.StkAsset;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2016/3/8
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("stkAssetDao")
public class OraStkAssetDaoImpl extends BaseDao<OraStkAssetMapper, StkAsset, Integer> implements StkAssetDao {
    @Override
    public List<StkAsset> getRealTimePosition(Map<String, Object> params) {
        return mapper.getRealTimePosition(params);
    }

    @Override
    public List<StkAsset> selectOne(Map<String, Object> param) {
        return mapper.selectOne(param);
    }
}
