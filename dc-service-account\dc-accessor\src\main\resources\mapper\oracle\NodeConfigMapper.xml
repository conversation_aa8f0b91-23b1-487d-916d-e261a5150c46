<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.oracle.NodeConfigMapper">

    <sql id="All_Column">
        KEY,NAME,VALUE
    </sql>

    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.NodeConfigDO">
        select <include refid="All_Column"/>
        from atcenter.S_NODE_CONFIG
        where KEY = #{key}
    </select>

</mapper>