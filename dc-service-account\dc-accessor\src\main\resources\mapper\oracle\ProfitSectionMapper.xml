<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eastmoney.accessor.mapper.oracle.ProfitSectionMapper">
    <resultMap id="BaseResultMap" type="as_profitSection" >
        <result column="FUNDID" property="fundId"/>
        <result column="PROFIT" property="profit"/>
        <result column="UNIT" property="unit"/>
        <result column="eutime" property="euTime"/>
        <result column="BAK_BIZDATE" property="bakBizDate"/>
        <result column="INDEX_DATE" property="indexDate"/>
    </resultMap>

    <sql id="All_Column">
        FUNDID, PROFIT, UNIT, INDEX_DATE, BAK_BIZDATE,eutime
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="All_Column"/> FROM ATCENTER.PROFIT_SECTION
        <where>
            <if test="fundId != null">
              FUNDID = #{fundId}
            </if>
            <if test="unit != null">
                AND UNIT = #{unit}
            </if>
        </where>
    </select>

</mapper>