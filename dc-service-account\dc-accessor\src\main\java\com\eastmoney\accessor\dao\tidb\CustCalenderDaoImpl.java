package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.CustCalenderMapper;
import com.eastmoney.common.entity.CustCalenderDO;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-30 17:12
 */
@Service("custCalenderDao")
public class CustCalenderDaoImpl extends BaseDao<CustCalenderMapper, CustCalenderDO,Long> implements CustCalenderDao{


    @Override
    public List<CustCalenderDO> getByBizDateRange(Map<String, Object> params) {
        Map<String,Object> map=new HashMap<>();
        map.put("dateFrom", Integer.valueOf(params.get("dateFrom").toString()));
        map.put("dateTo", Integer.valueOf(params.get("dateTo").toString()));
        return getMapper().getByBizDateRange(map);
    }
}
