<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" output="build/resources/test" path="src/test/resources">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/test" path="src/test/java">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/resources/main" path="src/main/resources">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/main" path="src/main/java">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/test" path="build/generated/sources/annotationProcessor/java/test">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/main" path="build/generated/sources/annotationProcessor/java/main">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-lang/commons-lang/2.6/ce1edb914c94ebc388f086c6827e8bdeec71ac2/commons-lang-2.6.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-lang/commons-lang/2.6/67313d715fbf0ea4fd0bdb69217fb77f807a8ce5/commons-lang-2.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.2.10/5328406bfcae7bcdcc86810fcb2920d2c297170d/logback-core-1.2.10.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/ch.qos.logback/logback-core/1.2.10/63dc43165f77fedf926b2bcf72212f3755c87fba/logback-core-1.2.10-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.hamcrest/hamcrest/2.2/1820c0968dba3a11a1b30669bb1f01978a91dedc/hamcrest-2.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.hamcrest/hamcrest/2.2/a0a13cfc629420efb587d954f982c4c6a100da25/hamcrest-2.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-pool2/2.8.0/f944951028a860fb43f0d12ced1080981c467f33/commons-pool2-2.8.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-pool2/2.8.0/aeac6548ff5cb5f627737600aaa7e2e3835d9bf/commons-pool2-2.8.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-tx/5.3.1/191e436f0b526fbe8f3cebdfd7dc048a3e1106fc/spring-tx-5.3.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-tx/5.3.1/fd6d1481c9dc292181fd05f1b864926e45b36aa5/spring-tx-5.3.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-core/5.3.13/d2a6c3372dd337e08144f9f49f386b8ec7a8080d/spring-core-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-core/5.3.13/3fee4911177ab3b3673a64ef0e5cf25e0d1b5a38/spring-core-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.27/359d62567480b07a679dc643f82fc926b100eed5/snakeyaml-1.27.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.27/62fc33bebe2f0c71864032c0549daf0a4a0dae2b/snakeyaml-1.27-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.7.2/9415680a889f00b8205a094c5c487bc69dc7077d/junit-jupiter-engine-5.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-engine/5.7.2/7c0d81d9af26a35e3f552eaaad2aa6867218d534/junit-jupiter-engine-5.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis.spring.boot/mybatis-spring-boot-autoconfigure/2.1.4/190f1630d0f7dee0dfecbacf7216b2b60391c046/mybatis-spring-boot-autoconfigure-2.1.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis.spring.boot/mybatis-spring-boot-autoconfigure/2.1.4/568483dc56567f27ee5ec9cd29de07df4c3e74d/mybatis-spring-boot-autoconfigure-2.1.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apiguardian/apiguardian-api/1.1.0/fc9dff4bb36d627bdc553de77e1f17efd790876c/apiguardian-api-1.1.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apiguardian/apiguardian-api/1.1.0/f3c15fe970af864390c8d0634c9f16aca1b064a8/apiguardian-api-1.1.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-logging/commons-logging/1.2/4bfc12adfe4842bf07b657f0369c4cb522955686/commons-logging-1.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-logging/commons-logging/1.2/ecf26c7507d67782a3bbd148d170b31dfad001aa/commons-logging-1.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-aop/5.3.13/e0fddf47af3fbbec69a403c058c23505612ca329/spring-aop-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-aop/5.3.13/e737cb88c5c739d130d1bbd767cbda63d8954641/spring-aop-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/mysql/mysql-connector-java/5.1.49/cf76d2e4c9c3782a85c15c87bec5772b34ffd0e5/mysql-connector-java-5.1.49.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/mysql/mysql-connector-java/5.1.49/e2a794fb4d6e25f4b7fbd8bc56c62aacbb4555ff/mysql-connector-java-5.1.49-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.alibaba/druid/1.2.8/15662decd6f0a4e28babaa6e96604b3d51551b62/druid-1.2.8.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.alibaba/druid/1.2.8/c39c17d994cc771554174690201de4e8fc48227a/druid-1.2.8-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-xml/4.1.73.Final/4cf7aa18e7a1359db673feadff672a6f9123119d/netty-codec-xml-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-xml/4.1.73.Final/bfb5dc182f16a128f5ebdbbd4fffc3393fcf201a/netty-codec-xml-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/minlog/1.3.1/a406e29d3a44d5f020d7b3218aee6d0952db4f73/minlog-1.3.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/minlog/1.3.1/5bc069c1f39adb3ee23f6c5a715dfc8c8b8c3a30/minlog-1.3.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-kqueue/4.1.73.Final/ed8ee7ce57cdf35a166c977fee4b96d9e92cc33a/netty-transport-native-kqueue-4.1.73.Final-osx-x86_64.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-kqueue/4.1.73.Final/ba9d9ede17097b1b5e8bd5b7f41f36685cc5d7f1/netty-transport-native-kqueue-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-processor/2.4.13/d2146abc83fe58a44e5a3a8d82374bab0426bbd8/spring-boot-configuration-processor-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-processor/2.4.13/86f07cf97bf8dbc4d98fca7cf82da7de101686b7/spring-boot-configuration-processor-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-test/5.3.13/71d8c97d3f79e4399df223772190aaee19bbf197/spring-test-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-test/5.3.13/5e2b4524255ff2cb5c1db3b86814d486575f4a63/spring-test-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-memcache/4.1.73.Final/6def6ebae70ca5882241814f6b77b308d460c57a/netty-codec-memcache-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-memcache/4.1.73.Final/f301346898b0bd707707a85e71eb611ead207ac9/netty-codec-memcache-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.73.Final/4701063d36f390e02da6da85c13e32a0e78349d2/netty-transport-native-unix-common-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.73.Final/d541fdf891a68ccd0ca16bb2fa545c7e4ed702ea/netty-transport-native-unix-common-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-jdbc/2.4.0/536d4d8b20619286d00609743048fca313160498/spring-boot-starter-jdbc-2.4.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-jdbc/2.4.0/c87e0ce7fdb1c293ad275528c4e71a650e9eb8ea/spring-boot-starter-jdbc-2.4.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-http/4.1.73.Final/1ceeac4429b9bd517dc05e376a144bbe6b6bd038/netty-codec-http-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-http/4.1.73.Final/d7491201fe83a339361cfa74f72679cf4e299b0/netty-codec-http-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/2.4.13/d32d5886bf038ba17a835a51ef848aa707ed606e/spring-boot-autoconfigure-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/2.4.13/27b62c8664c296a328eacac9df3ded4d4291232d/spring-boot-autoconfigure-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.sourceforge.jtds/jtds/1.3.1/1527f2fc2f040898625370a1687d902aa0743bcc/jtds-1.3.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.sourceforge.jtds/jtds/1.3.1/6857ae4c370307c0e3a6273ac92872264ff682e9/jtds-1.3.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.73.Final/244a569c9aae973f6f485ac9801d79c1eca36daa/netty-buffer-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.73.Final/22925162e424484f1093273c0f4e9492542f1861/netty-buffer-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/javax.mail/mail/1.4/1aa1579ae5ecd41920c4f355b0a9ef40b68315dd/mail-1.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/javax.mail/mail/1.4/fdd508f19a5075196138c94b341120e917c10e8a/mail-1.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/2.4.13/2b51c28950e9c016034290143eb7d2785d96ae64/spring-boot-starter-test-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-test/2.4.13/241d145c28368cb97c462d30235c3f8ef694fde9/spring-boot-starter-test-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport/4.1.73.Final/abb155ddff196ccedfe85b810d4b9375ef85fcfa/netty-transport-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport/4.1.73.Final/b05b17d246dd2897ac0308e2fa416e3ede8cbb51/netty-transport-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.assertj/assertj-core/3.18.1/aaa02680dd92a568a4278bb40aa4a6305f632ec0/assertj-core-3.18.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.assertj/assertj-core/3.18.1/e9ae1d122fdce7686454558baaaccc8738db87d0/assertj-core-3.18.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-math3/3.6/825c90790e83af784be1de0a3f3a9f066cf884b9/commons-math3-3.6.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-math3/3.6/9a5078b5165713c2ac463def01701b13cfba1b5e/commons-math3-3.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/kryo/5.0.0/f0c7b78072110fd50e8ecc143b96fa16f752b3db/kryo-5.0.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/kryo/5.0.0/8ea04ba2c1f92e3072ceac61431d35531af6ae7/kryo-5.0.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.73.Final/eb41d4387b9aaff0f9de89c2eb4dfc2c74f9a29c/netty-transport-native-epoll-4.1.73.Final-linux-aarch_64.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.73.Final/b7e10499bdc81de72a48c3bf5df6bf606bbd142d/netty-transport-native-epoll-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.zaxxer/HikariCP/3.4.5/aa1a2c00aae8e4ba8308e19940711bb9525b103d/HikariCP-3.4.5.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.zaxxer/HikariCP/3.4.5/44a9f83d5baa78a1c43307400f6b9a95d39980d0/HikariCP-3.4.5-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/2.4.13/e302adae7c882f99f52e3a52def687fdb7e79830/spring-boot-test-autoconfigure-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test-autoconfigure/2.4.13/6f3850eccdcd0e5531344c2dc3ec869db5141218/spring-boot-test-autoconfigure-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-handler-proxy/4.1.73.Final/d1afa6876c3d3bdbdbe5127ddd495e6514d6e600/netty-handler-proxy-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-handler-proxy/4.1.73.Final/331da6c567a33c7372b09cd057a57e965c7e7e34/netty-handler-proxy-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis/mybatis-spring/2.0.6/eae03712acdf041a3590b816460945d4bd2691bc/mybatis-spring-2.0.6.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis/mybatis-spring/2.0.6/d169c59dec35a2a7b8b581d0b6ea763ed1fceaa5/mybatis-spring-2.0.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.alibaba/fastjson/1.2.83/9ee94951bc107d382519975d04bc950b6c6ab297/fastjson-1.2.83.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.alibaba/fastjson/1.2.83/34a89f16d415fe19be80ecff10bafa403fc1a69f/fastjson-1.2.83-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.73.Final/bfe83710f0c1739019613e81a06101020ca65def/netty-resolver-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.73.Final/16325fc07cba3508132a795b24fc1eaa7d67aa60/netty-resolver-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/jcl-over-slf4j/1.7.25/f8c32b13ff142a513eeb5b6330b1588dcb2c0461/jcl-over-slf4j-1.7.25.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/jcl-over-slf4j/1.7.25/ffd0827a7c67d5915b7dc611afbda56a1f247191/jcl-over-slf4j-1.7.25-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-native-macos/4.1.73.Final/9c9c2ad4a2cb0043860e8321391d3d9dae3c5839/netty-resolver-dns-native-macos-4.1.73.Final-osx-aarch_64.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-kqueue/4.1.73.Final/a6ca6a4d2d4ef6df7e320c9bf09e0d1f1003f970/netty-transport-native-kqueue-4.1.73.Final-osx-aarch_64.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-kqueue/4.1.73.Final/ba9d9ede17097b1b5e8bd5b7f41f36685cc5d7f1/netty-transport-native-kqueue-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.projectlombok/lombok/1.16.20/ac76d9b956045631d1561a09289cbf472e077c01/lombok-1.16.20.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.projectlombok/lombok/1.16.20/69ebf81bb97bdb3c9581c171762bb4929cb5289c/lombok-1.16.20-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/1.3.5/59eb84ee0d616332ff44aba065f3888cf002cd2d/jakarta.annotation-api-1.3.5.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.annotation/jakarta.annotation-api/1.3.5/1ad35f11d17abb52426bfe15ea7b4c583795012/jakarta.annotation-api-1.3.5-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/jul-to-slf4j/1.7.32/8a055c04ab44e8e8326901cadf89080721348bdb/jul-to-slf4j-1.7.32.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/jul-to-slf4j/1.7.32/d03fa92a2a508b5595b896b93f13f04bcde11171/jul-to-slf4j-1.7.32-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-rxtx/4.1.73.Final/94499809bd9fee9790c0648b331e6671c8e0b155/netty-transport-rxtx-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-rxtx/4.1.73.Final/73c07af93db38572fb54777c19ec6bf74e45c06/netty-transport-rxtx-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/1.3/ba035118bc8bac37d7eff77700720999acd9986d/j2objc-annotations-1.3.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/1.3/d26c56180205cbb50447c3eca98ecb617cf9f58b/j2objc-annotations-1.3-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.73.Final/eb145bc31fd32a20fd2a3e8b30736d2e0248b0c/netty-codec-http2-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-http2/4.1.73.Final/e66f6c50ba858b149cea1af5e2d10cb132ed9507/netty-codec-http2-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/2.4.13/975fd677fe3931f49139ad146cf40efd7ed76252/spring-boot-starter-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter/2.4.13/685ed7cac1cc8eb5e7dec7ceace14bb28d9d0868/spring-boot-starter-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-sctp/4.1.73.Final/a1edd6eabdaa571f76ddc7db6bab3df6e8d425f9/netty-transport-sctp-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-sctp/4.1.73.Final/28e83da706979156dbedb6edbe64bb7074e0b7e2/netty-transport-sctp-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-jdbc/5.3.1/f5ee3b0f4b2647ec9d331ff8c83bdb0ccd5fc979/spring-jdbc-5.3.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-jdbc/5.3.1/4ccd70cd58e08f7c453b297c7b2d60d72fab3bb2/spring-jdbc-5.3.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-classes-kqueue/4.1.73.Final/984b50d449f64c584cca9e4567ba158e76ed7ae6/netty-transport-classes-kqueue-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-classes-kqueue/4.1.73.Final/40c77f43b5dfadf40bc2b154ddaf35b237b68abf/netty-transport-classes-kqueue-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.skyscreamer/jsonassert/1.5.0/6c9d5fe2f59da598d9aefc1cfc6528ff3cf32df3/jsonassert-1.5.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.skyscreamer/jsonassert/1.5.0/d729b258165a2fd9b5d6156c05c4c4f7ca053117/jsonassert-1.5.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/slf4j-api/1.7.35/517f3a0687490b72d0e56d815e05608a541af802/slf4j-api-1.7.35.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.slf4j/slf4j-api/1.7.35/4ec55d81a425f9d0f6eccd7638fbf55aaca6e767/slf4j-api-1.7.35-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.2.10/f69d97ef3335c6ab82fc21dfb77ac613f90c1221/logback-classic-1.2.10.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/ch.qos.logback/logback-classic/1.2.10/734366e821747a955aa230ed8eb08b3cc4cfba2/logback-classic-1.2.10-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-pool/commons-pool/1.6/4572d589699f09d866a226a14b7f4323c6d8f040/commons-pool-1.6.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-pool/commons-pool/1.6/9376f5559455b8fe1b15308d3ad9c545f6905b1a/commons-pool-1.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-configuration/commons-configuration/1.10/2b36e4adfb66d966c5aef2d73deb6be716389dc9/commons-configuration-1.10.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-configuration/commons-configuration/1.10/2427d5eaf4011f855a95bd523febda041728393b/commons-configuration-1.10-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-smtp/4.1.73.Final/f957bd1ed7e10e13d052b6f5d292fcd7d2e97aa2/netty-codec-smtp-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-smtp/4.1.73.Final/9b68b0dd55e14a66c958cf49b16c5b7bffc52ab1/netty-codec-smtp-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.11.4/593f7b18bab07a76767f181e2a2336135ce82cc4/jackson-core-2.11.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-core/2.11.4/fe0a4493c4dbde5b3bb0316345b5b356659c3d25/jackson-core-2.11.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/javax.activation/activation/1.1/e6cb541461c2834bdea3eb920f1884d1eb508b50/activation-1.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/javax.activation/activation/1.1/3697e9f67a2ed7ac5f589cff30f21173299440/activation-1.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/b421526c5f297295adef1c886e5246c39d4ac629/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.7.2/685f832f8c54dd40100f646d61aca88ed9545421/junit-jupiter-params-5.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-params/5.7.2/45d7318c86fa4c826fab267fadc0839d40267da2/junit-jupiter-params-5.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.jayway.jsonpath/json-path/2.4.0/765a4401ceb2dc8d40553c2075eb80a8fa35c2ae/json-path-2.4.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.jayway.jsonpath/json-path/2.4.0/c43fc506196089da87a7bb36ec847b57072a818b/json-path-2.4.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/3.6.28/23149890c3b6047604a682aa3d47151d440e1bfa/mockito-junit-jupiter-3.6.28.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-junit-jupiter/3.6.28/c16ea5cf4c8b06dc63e5bb0af9a9828cce6c5e01/mockito-junit-jupiter-3.6.28-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/b19b5927c2c25b6c70f093767041e641ae0b1b35/jsr305-3.0.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-io/commons-io/2.11.0/a2503f302b11ebde7ebc3df41daebe0e4eea3689/commons-io-2.11.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-io/commons-io/2.11.0/2ef0187124bbfc7aed3038d4ffdd2241e8992937/commons-io-2.11.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/reflectasm/1.11.8/a130ad2975f31d3c3c4da57d8973261ec0b1e2d/reflectasm-1.11.8.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.esotericsoftware/reflectasm/1.11.8/c5d941c2ea5801ace01a4edec24dd010f5b7b8e0/reflectasm-1.11.8-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.minidev/accessors-smart/1.2/c592b500269bfde36096641b01238a8350f8aa31/accessors-smart-1.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.minidev/accessors-smart/1.2/c837e3903ff07b478041f761915d764b98e71e05/accessors-smart-1.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.zeromq/jeromq/0.4.3/d734adc7e118cd26407e0b8e2c8a4cb15fb23620/jeromq-0.4.3.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.zeromq/jeromq/0.4.3/9f8d618723c2d8ebe7cc9275b9f7469864a8510a/jeromq-0.4.3-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.xmlunit/xmlunit-core/2.7.0/4d014eac96329c70175116b185749765cee0aad5/xmlunit-core-2.7.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.xmlunit/xmlunit-core/2.7.0/70c050a7709f32ffe75078135531afa9bf7ca342/xmlunit-core-2.7.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.1/1dcf1de382a0bf95a3d8b0849546c88bac1292c9/failureaccess-1.0.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.1/1d064e61aad6c51cc77f9b59dc2cccc78e792f5a/failureaccess-1.0.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.7.2/f4b4079732a9c537983324cfa4e46655f21d2c56/junit-jupiter-api-5.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter-api/5.7.2/ebca04edd1975a8fd42846c4f6d0b8fec3d6efc1/junit-jupiter-api-5.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-udt/4.1.73.Final/b1b108a910d4224c06ccb250f8b22e150079a75d/netty-transport-udt-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-udt/4.1.73.Final/1d633bc0d37c15742dabe2fe6bc31bbf8092acc5/netty-transport-udt-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-all/4.1.73.Final/75c5a0ddb28adcc9e4991c75678d4a85dfe4a0b3/netty-all-4.1.73.Final.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.httpcomponents/httpclient/4.5.13/e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada/httpclient-4.5.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.httpcomponents/httpclient/4.5.13/9654e91a61d7662c36be6710ef8e720c0e3f9807/httpclient-4.5.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-collections/commons-collections/3.2.2/8ad72fe39fa8c91eaaf12aadb21e0c3661fe26d5/commons-collections-3.2.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-collections/commons-collections/3.2.2/78c50ebda5784937ca1615fc0e1d0cb35857d572/commons-collections-3.2.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.objenesis/objenesis/3.1/48f12deaae83a8dfc3775d830c9fd60ea59bbbca/objenesis-3.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.objenesis/objenesis/3.1/36e4b278b98654980a51e36ef60b186a757ed3d7/objenesis-3.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-lang3/3.12.0/c6842c86792ff03b9f1d1fe2aab8dc23aa6c6f0e/commons-lang3-3.12.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-lang3/3.12.0/198b882fdc2c72c89c63401d946f6ba46c3acea3/commons-lang3-3.12.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.10.18/1070e69ef571b326d91819b57bd06fd7efc60819/byte-buddy-agent-1.10.18.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.10.18/cf0a98d2728fb19c5737cb2b8e8af0bb1d7e5f91/byte-buddy-agent-1.10.18-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.guava/guava/31.0.1-jre/119ea2b2bc205b138974d351777b20f02b92704b/guava-31.0.1-jre.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.guava/guava/31.0.1-jre/d34772c01bd6637982d1aafe895c4fcd8b42e139/guava-31.0.1-jre-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-common/4.1.73.Final/27731b58d741b6faa6a00fa3285e7a55cc47be01/netty-common-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-common/4.1.73.Final/8b6f628b8330f1a4c05be8810994355d85e356c7/netty-common-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-tcnative-classes/2.0.46.Final/9937a832d9c19861822d345b48ced388b645aa5f/netty-tcnative-classes-2.0.46.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-tcnative-classes/2.0.46.Final/fd5373f574ac969627d50ee013c81384ec99ec5e/netty-tcnative-classes-2.0.46.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.vaadin.external.google/android-json/0.0.20131108.vaadin1/fa26d351fe62a6a17f5cda1287c1c6110dec413f/android-json-0.0.20131108.vaadin1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.vaadin.external.google/android-json/0.0.20131108.vaadin1/bf42d7e47a3228513b626dd7d37ac6f072aeca4f/android-json-0.0.20131108.vaadin1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.7.2/34adfea6c13fc4a996cf38cdad80800ce850d198/junit-platform-commons-1.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.platform/junit-platform-commons/1.7.2/1ff6be63f8bb2ef0d8c20b771a1d9160b6077a81/junit-platform-commons-1.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.10.18/20240291b4f14ffe986e45468b1f1a3c15edc37c/byte-buddy-1.10.18.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.10.18/b29479dbda55b1ce407fadf3393786071c672bf1/byte-buddy-1.10.18-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.lettuce/lettuce-core/6.0.4.RELEASE/cfa107b98ef9177f25313c8d9d241a1b32f56e1e/lettuce-core-6.0.4.RELEASE.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.lettuce/lettuce-core/6.0.4.RELEASE/9da87154c703008598f16b76003560ebeb1854de/lettuce-core-6.0.4.RELEASE-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/2.3.3/48e3b9cfc10752fba3521d6511f4165bea951801/jakarta.xml.bind-api-2.3.3.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.xml.bind/jakarta.xml.bind-api/2.3.3/d83744bae211a4072c39f007000a13f501a88395/jakarta.xml.bind-api-2.3.3-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.opentest4j/opentest4j/1.2.0/28c11eb91f9b6d8e200631d46e20a7f407f2a046/opentest4j-1.2.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.opentest4j/opentest4j/1.2.0/41d55b3c2254de9837b4ec8923cbd371b8a7eab5/opentest4j-1.2.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-context/5.3.13/e328db1c30ffe1c58328e4ab42cd3855a5307469/spring-context-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-context/5.3.13/a6f2ad29fc2124e7eb22441e2c82b096d25a8e05/spring-context-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-codec/commons-codec/1.11/3acb4705652e16236558f0f4f2192cc33c3bd189/commons-codec-1.11.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-codec/commons-codec/1.11/bce4ba84fd527950e35040b20a991c63e90e2850/commons-codec-1.11-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-redis/4.1.73.Final/9c668ed54e4677974ea1fe16f4683600c0f6c218/netty-codec-redis-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-redis/4.1.73.Final/49aefd251e0ba96de2df1290f919cd9da973c28c/netty-codec-redis-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.reactivestreams/reactive-streams/1.0.3/d9fb7a7926ffa635b3dcaa5049fb2bfa25b3e7d0/reactive-streams-1.0.3.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.reactivestreams/reactive-streams/1.0.3/e6ffdff8c438d959376c91d94b319457354a84c5/reactive-streams-1.0.3-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.73.Final/2a5682520fc756efc921da78b0f00c35196b6708/netty-transport-classes-epoll-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.73.Final/d98f9fc73c212bae23f0259186436089e7a5ed7f/netty-transport-classes-epoll-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-annotations/2.11.4/2c3f5c079330f3a01726686a078979420f547ae4/jackson-annotations-2.11.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-annotations/2.11.4/d1e2c247df682406679024f7df1a2b80fee2bfac/jackson-annotations-2.11.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.minidev/json-smart/2.3/7396407491352ce4fa30de92efb158adb76b5b/json-smart-2.3.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.minidev/json-smart/2.3/36c61f1b839bde5b284528cb76f6811efbe0f08b/json-smart-2.3-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.eastmoney.datacenter/commons-util/2.0.0/a8d7cce6d34f5cdf902815584045e22f4cffda0b/commons-util-2.0.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.eastmoney.datacenter/commons-util/2.0.0/adb2e39afe5e02de881cbdeffd7f5bba881cd53e/commons-util-2.0.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-haproxy/4.1.73.Final/5b9c5e54fa283b7ad489a7391ebebcf0551fd537/netty-codec-haproxy-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-haproxy/4.1.73.Final/495b34f667c28191e617f8d9716f8881d9d7cd2a/netty-codec-haproxy-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns/4.1.73.Final/97cdf5fb97f8d961cfa3ffb05175009b90e5cfee/netty-resolver-dns-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns/4.1.73.Final/8d059a7c2631775b66f26784030d36533fbc7192/netty-resolver-dns-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-socks/4.1.73.Final/cefa44d8f5dcaab21179d945f12b6c6d7325cce9/netty-codec-socks-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-socks/4.1.73.Final/59c4bf8a03e00df7deefd2f873d759b5a184ef14/netty-codec-socks-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.73.Final/5f465b65333dc0e867ec6f0eb9ffddb18604b6c2/netty-transport-native-epoll-4.1.73.Final-linux-x86_64.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.73.Final/b7e10499bdc81de72a48c3bf5df6bf606bbd142d/netty-transport-native-epoll-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-aop/2.4.13/95cbfca725b16067e8ec74ba9325d49f42038035/spring-boot-starter-aop-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-aop/2.4.13/62fd1c2ea4821f5101b99a26e5aebf572f1aacd9/spring-boot-starter-aop-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot/2.4.13/683a9e9cc4fa9051a38bdb8bf70058db880a79cc/spring-boot-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot/2.4.13/e6a3a78693a650fdc2bcd8d2cb8d2829de448f9e/spring-boot-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.7.2/62faa742964a9d8dab8fdb4a0eab7b01441c171f/junit-jupiter-5.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.jupiter/junit-jupiter/5.7.2/7009caa171d1d6af0f6f09dff25f9f574826a490/junit-jupiter-5.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.eastmoney.dccp/redis-cluster-client/1.0.0.7/c62a420f0fd7ab7f994281518d2feb372331a45b/redis-cluster-client-1.0.0.7.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.eastmoney.dccp/redis-cluster-client/1.0.0.7/a1d6a6306aadeaf493c80509c7b1b6dff5c1f980/redis-cluster-client-1.0.0.7-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.zeromq/jnacl/0.1.0/781d75fc80cb2ef4e89716f01fed101ca416e166/jnacl-0.1.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.zeromq/jnacl/0.1.0/62737321b92ca306413fa336024706996f6c3ade/jnacl-0.1.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.json/json/20190722/7bce7bacf0ab5e9f894d307a3de8b7f540064d5/json-20190722.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.json/json/20190722/6c40df0d93d36cdfc0fbf08e4e3b27eaa1208199/json-20190722-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/2.4.13/3c66143f7cd27bb3d7b75515513f82f0ba036520/spring-boot-test-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-test/2.4.13/1454897fcccae84198309f8951843dd0b9121fb1/spring-boot-test-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.projectreactor/reactor-core/3.3.16.RELEASE/351a210b34c4108ace4456c1c72d0ff216c7b208/reactor-core-3.3.16.RELEASE.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.projectreactor/reactor-core/3.3.16.RELEASE/a17a12b1412d892c8d228db39d77c83bf449271d/reactor-core-3.3.16.RELEASE-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-handler/4.1.73.Final/1a2231c0074f88254865c3769a4b5842939ea04d/netty-handler-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-handler/4.1.73.Final/31a07d6db55f0c05b25dd5f0a0d4a305ffa6c310/netty-handler-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-json-org/2.11.4/ef7a3acf3ceb55bbc8e39e23ebb302f9dfd5ea3a/jackson-datatype-json-org-2.11.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.datatype/jackson-datatype-json-org/2.11.4/2ac4b45594fc2075f4d014b639ebd552331206b8/jackson-datatype-json-org-2.11.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.checkerframework/checker-qual/3.12.0/d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5/checker-qual-3.12.0.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.checkerframework/checker-qual/3.12.0/10dacb8b36398debceca36cd0db5f3316967f80e/checker-qual-3.12.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-core/3.6.28/ad16f503916da658bd7b805816ae3b296f3eea4c/mockito-core-3.6.28.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-core/3.6.28/296251bf042984db28998e4c1be22c5e265a0cc7/mockito-core-3.6.28-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.7.1/458d9042f7aa6fa9a634df902b37f544e15aacac/error_prone_annotations-2.7.1.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.7.1/2ca80bf7ac820fce6c743561b550c8bfb6c87c57/error_prone_annotations-2.7.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-beans/5.3.13/1d90c96b287253ec371260c35fbbea719c24bad6/spring-beans-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-beans/5.3.13/ddd4df6d27eb3695492f3fc47fc270b6f8dce001/spring-beans-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-jcl/5.3.13/3aa15be194887fbda3912ecbb4ab6ec8dfdebdb0/spring-jcl-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-jcl/5.3.13/48e59686ad1a2dd39f4f1b3d6a63a7424091893a/spring-jcl-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis.spring.boot/mybatis-spring-boot-starter/2.1.4/1ad97a0fb96ec3630e598e5aaae2dbd2fd29fdb0/mybatis-spring-boot-starter-2.1.4.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-expression/5.3.13/8f7448f4fb296a92855fd0afea3375ce41061e84/spring-expression-5.3.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework/spring-expression/5.3.13/ba1d61de1799062448c1c682513671136c04f9fd/spring-expression-5.3.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec/4.1.73.Final/9496a30a349863a4c6fa10d5c36b4f3b495d3a31/netty-codec-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec/4.1.73.Final/e6c69d4df9771fa6858e9bfd64a5c67dd13bd29f/netty-codec-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.ow2.asm/asm/5.0.4/da08b8cce7bbf903602a25a3a163ae252435795/asm-5.0.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.ow2.asm/asm/5.0.4/112ff54474f1f04ccf1384c92e39fdc566f0bb5e/asm-5.0.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore/4.4.13/853b96d3afbb7bf8cc303fe27ee96836a10c1834/httpcore-4.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore/4.4.13/d6e6365dac44108fb2cc0684daea0d40bc6609f5/httpcore-4.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-text/1.9/ba6ac8c2807490944a0a27f6f8e68fb5ed2e80e2/commons-text-1.9.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.apache.commons/commons-text/1.9/ae5c56310246339ac93e96c576243689173142d4/commons-text-1.9-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-stomp/4.1.73.Final/ddf810711c9248069c6359337cb15f9f0062aaae/netty-codec-stomp-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-stomp/4.1.73.Final/ebbf0f359e53611a4564a017864688999856d956/netty-codec-stomp-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-beanutils/commons-beanutils/1.9.4/d52b9abcd97f38c81342bb7e7ae1eee9b73cba51/commons-beanutils-1.9.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/commons-beanutils/commons-beanutils/1.9.4/f00e0ba867d1810f255876631ab440e0725a9af0/commons-beanutils-1.9.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.aspectj/aspectjweaver/1.9.7/158f5c255cd3e4408e795b79f7c3fbae9b53b7ca/aspectjweaver-1.9.7.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.aspectj/aspectjweaver/1.9.7/8da21dfaf64693f4a9c269298dce04d12a7b10fd/aspectjweaver-1.9.7-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/2.4.13/8d698e6da30992e5bc35c92b4d62ce6076a497b9/spring-boot-starter-logging-2.4.13.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/2.4.13/89cfb475a58d76f6b2e401071ed3384ba4647534/spring-boot-starter-logging-2.4.13-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.11.4/5d9f3d441f99d721b957e3497f0a6465c764fad4/jackson-databind-2.11.4.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.fasterxml.jackson.core/jackson-databind/2.11.4/ac8b8fdc19268fe3378d3dc6d4af476341c23c4f/jackson-databind-2.11.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-dns/4.1.73.Final/46137a5b01a5202059324cf4300443e53f11a38d/netty-codec-dns-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-dns/4.1.73.Final/f5aae7e7b642500ca0428eace1c4f6adcff49fa9/netty-codec-dns-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis/mybatis/3.5.6/28ea8fe7d6c3998cf1d0cb8af64b9d58f04c7cb3/mybatis-3.5.6.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mybatis/mybatis/3.5.6/534e25e22466062e9e07424a07f9ae86912d7c49/mybatis-3.5.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-mqtt/4.1.73.Final/6fa0915da9b250fff849e9675487db289394064d/netty-codec-mqtt-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-codec-mqtt/4.1.73.Final/fc8d5bfe90cac649849f107dfd21d1be68f587fb/netty-codec-mqtt-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.activation/jakarta.activation-api/1.2.2/99f53adba383cb1bf7c3862844488574b559621f/jakarta.activation-api-1.2.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/jakarta.activation/jakarta.activation-api/1.2.2/c45b5962230be8a5f93759203870c98917bb8b31/jakarta.activation-api-1.2.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-classes-macos/4.1.73.Final/c799de22c24e7465a70942d0094270c2456c3405/netty-resolver-dns-classes-macos-4.1.73.Final.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-classes-macos/4.1.73.Final/3387340c5d07243369dbf38501be5f50885c3db1/netty-resolver-dns-classes-macos-4.1.73.Final-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/io.netty/netty-resolver-dns-native-macos/4.1.73.Final/dd2ae42a2e9c53a71ae64c2fe579ca632250e457/netty-resolver-dns-native-macos-4.1.73.Final-osx-x86_64.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/com.oracle/ojdbc6/11.2.0.3/de96f0060c7353532f4cb4a0b73d20545580fc91/ojdbc6-11.2.0.3.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.7.2/2573770b46b8a199ed5f6b0f96fb99e468bfe891/junit-platform-engine-1.7.2.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.junit.platform/junit-platform-engine/1.7.2/4d160bb45611331ab4feba5dde85ebceb05a824e/junit-platform-engine-1.7.2-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.10.15/259957c76d345cb3db5411f57a0654300aaca04c/byte-buddy-1.10.15.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.10.15/8d60df068a9009ffcde1cc39dea27878cb79819f/byte-buddy-1.10.15-sources.jar">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.10.15/7f0cc687ee3177fd451d17b9b80325a8cdd1c7ff/byte-buddy-agent-1.10.15.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.10.15/3f552c080aab2cd7d2a6a4bdad890b74207a7bad/byte-buddy-agent-1.10.15-sources.jar">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-core/3.5.15/98bea3af7262e17ecaf6efe1674b4334af5d4717/mockito-core-3.5.15.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-core/3.5.15/6abab55200d7bcb5bb4e0d50feaa0a6fcb6b9969/mockito-core-3.5.15-sources.jar">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-inline/3.5.15/1addf70b564c1f72687700d07ac9a69a9b71a6fb/mockito-inline-3.5.15.jar" sourcepath="D:/Dev/Env/repo/caches/modules-2/files-2.1/org.mockito/mockito-inline/3.5.15/560a6a7b3ddee51b908fc4a7e7084be498f74df9/mockito-inline-3.5.15-sources.jar">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="/dc-common">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="bin"/>
</classpath>
