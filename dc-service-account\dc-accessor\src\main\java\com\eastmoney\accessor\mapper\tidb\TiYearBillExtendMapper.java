package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.YearBillExtend;
import com.eastmoney.common.entity.cal.ProfitDay;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface TiYearBillExtendMapper extends BaseMapper<YearBillExtend, Long> {
    ProfitDay getBestDay(Map<String, Object> params);
}
