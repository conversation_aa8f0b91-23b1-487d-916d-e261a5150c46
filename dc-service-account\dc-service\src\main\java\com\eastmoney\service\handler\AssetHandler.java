package com.eastmoney.service.handler;

import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.entity.cal.AssetSection;
import com.eastmoney.common.sysEnum.DateUnitEnum;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.AssetNewService;
import com.eastmoney.service.service.asset.AssetSectionServiceFacade;
import com.eastmoney.service.service.profit.section.ProfitSectionServiceFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Created by sunyuncai on 2016/7/19.
 */
@Service
public class AssetHandler {
    @Resource(name = "assetSectionServiceFacade")
    private AssetSectionServiceFacade assetSectionServiceFacade;
    @Resource(name = "profitSectionServiceFacade")
    private ProfitSectionServiceFacade profitSectionServiceFacade;

    @Autowired
    private AssetNewService assetNewService;


    /**
     * 获取最新资产
     *
     * @param params
     * @return
     */
    public AssetNew getAssetInfo(Map<String, Object> params) {
        return assetNewService.getAssetInfo(params);
    }

    public AssetSection getAssetSection(Map<String, Object> params) {
        SectionProfitBean profitSection = profitSectionServiceFacade.getProfitSection(params);
        String unit = CommonUtil.convert(params.get("unit"), String.class);
        if (profitSection == null && DateUnitEnum.DAY.getValue().equals(unit)) {
            profitSection = new SectionProfitBean();
        } else if (profitSection == null) {
            return null;
        }

        AssetSection assetSection = assetSectionServiceFacade.getAssetSection(params);
        if (assetSection == null) {
            return null;
        }
        assetSection.setProfit(profitSection.getProfit());
        return assetSection;
    }
}
