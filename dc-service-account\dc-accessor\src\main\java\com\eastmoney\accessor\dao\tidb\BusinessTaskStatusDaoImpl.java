package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiBusinessTaskStatusMapper;
import com.eastmoney.common.entity.BusinessTaskStatusDO;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/10 10:27
 */
@Repository("businessTaskStatusDao")
public class BusinessTaskStatusDaoImpl extends BaseDao<TiBusinessTaskStatusMapper, BusinessTaskStatusDO, Integer> implements BusinessTaskStatusDao {
    @Override
    public BusinessTaskStatusDO selectOneTaskNewestStatus(Map<String, Object> params) {
        return getMapper().selectOneTaskNewestStatus(params);
    }
}
