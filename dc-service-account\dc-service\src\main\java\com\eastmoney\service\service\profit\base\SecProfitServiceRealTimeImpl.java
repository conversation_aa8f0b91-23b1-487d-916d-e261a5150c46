package com.eastmoney.service.service.profit.base;

import com.eastmoney.common.entity.DayProfitExtend;
import com.eastmoney.common.entity.DayStkProfit;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.service.asset.AssetHisService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 实时个股日收益明细service
 *
 * <AUTHOR>
 * @create 2024/3/14
 */
@Service("secProfitServiceRealTime")
public class SecProfitServiceRealTimeImpl implements SecProfitService {

    @Resource(name = "profitServiceRealTime")
    private ProfitServiceRealTimeImpl profitServiceRealTime;

    @Autowired
    private AssetHisService assetHisService;

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    @Override
    public List<SecProfitDayDO> getSecProfitDayByRange(Map<String, Object> params) {
        List<SecProfitDayDO> secProfitDays = new ArrayList<>();
        Boolean filterHgtProfit = CommonUtil.convert(params.getOrDefault("filterHgtProfit", false), Boolean.class);
        //获取实时计算收益额
        Map dayProfitResult = profitServiceRealTime.getDayProfitData(params, filterHgtProfit, null);
        List<DayStkProfit> dayStkProfitList = (List<DayStkProfit>) dayProfitResult.get("dayStkProfitList");

        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        if (CollectionUtils.isEmpty(dayStkProfitList)) {
            return secProfitDays;
        }

        Integer accountBizDate = CommonUtil.convert(params.get("accountBizDate"), Integer.class);
        Integer serverId = CommonUtil.convert(params.get("serverId"), Integer.class);

        // 上一个交易日的持仓
        Map<String, Double> openMktValMap = assetHisService.getOpenMktVal(fundId, accountBizDate, serverId, false);

        // 查询非交易类型配置
        List<String> noTradeIdList = nodeConfigService.getNoTradeIdList();

        return dayStkProfitList.stream()
                .map(s -> build(s, fundId, openMktValMap, noTradeIdList))
                .collect(Collectors.toList());
    }

    /**
     * @param dayStkProfit
     * @param fundId
     * @param openMktValMap
     * @return
     */
    private SecProfitDayDO build(DayStkProfit dayStkProfit, Long fundId, Map<String, Double> openMktValMap, List<String> noTradeIdList) {
        SecProfitDayDO secProfitDay = new SecProfitDayDO();

        // 个股明细剔除非交易类型且收益为0的数据
        if (noTradeIdList.contains(dayStkProfit.getTrdId()) && Objects.equals(dayStkProfit.getProfit(), 0d)) {
            return secProfitDay;
        }
        Double dayProfit = dayStkProfit.getProfit();
        secProfitDay.setMarket(dayStkProfit.getMarket());
        secProfitDay.setStkCode(StringUtils.trim(dayStkProfit.getStkCode()));
        secProfitDay.setFundId(fundId);
        secProfitDay.setProfit(dayProfit);

        String key = getKey(secProfitDay.getStkCode(), secProfitDay.getMarket());
        // 期初市值
        Double openMktVal = openMktValMap.getOrDefault(key, openMktValMap.getOrDefault(
                getKey(bseCodeAlterService.getCodeAlterOrReverse(secProfitDay.getStkCode(), secProfitDay.getMarket()), secProfitDay.getMarket()), 0d));
        // 当日买入金额
        Double matchAmtBuy = dayStkProfit.getBuyAmtWithIntr();

        if (ArithUtil.eq(openMktVal, 0)
                && ArithUtil.eq(dayStkProfit.getBuyAmt(), 0d)
                && ArithUtil.eq(dayStkProfit.getSellAmt(), 0d)
                && ArithUtil.eq(dayStkProfit.getBuyCount(), 0d)
                && ArithUtil.eq(dayStkProfit.getSellCount(), 0d)
                && ArithUtil.eq(dayStkProfit.getProfit(), 0d)
        ) {
            secProfitDay = new SecProfitDayDO();
        }

        // 个股日收益率 = 个股日收益 / （期初持仓总市值（包含bondIntr） + 当日该标的买入金额(包含利息和费用)）
        Double secProfitRateDivisor = ArithUtil.add(openMktVal, matchAmtBuy);
        if (dayProfit != null && ArithUtil.eq(dayProfit, 0d)) {
            // 收益额为0时,收益率赋值为0
            secProfitDay.setProfitRate(0d);
        } else if (secProfitRateDivisor > 0.0) {
            // 分母大于0时,计算收益率
            Double secProfitRate = ArithUtil.div(dayProfit, secProfitRateDivisor, 4);
            secProfitDay.setProfitRate(secProfitRate);
        }
        return secProfitDay;
    }

    private String getKey(String stkCode, String market) {
        return StringUtils.trim(stkCode) + "-" + market;
    }
}
