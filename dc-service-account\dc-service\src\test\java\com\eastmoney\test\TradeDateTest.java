package com.eastmoney.test;

import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.TradeDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/18
 */
@SpringBootTest
@ExtendWith(MockitoExtension.class)
public class TradeDateTest {
    private static final Logger LOG = LoggerFactory.getLogger(TradeDateTest.class);

    @InjectMocks
    private TradeDateDao tradeDateDao;

    @Test
    public void test1() {
        Boolean market1 = tradeDateDao.isMarket(20230518);
        LOG.debug("market1:{}", market1);

        boolean market2 = tradeDateDao.isMarket(null);
        LOG.debug("market2:{}", market2);

        boolean market3 = tradeDateDao.todayIsMarket();
        LOG.debug("market3:{}", market3);
    }

    @Test
    public void test2() {
        String preMarketDay = tradeDateDao.getPreMarketDay(20230518);
        LOG.debug("preMarketDay:{}", preMarketDay);

        int nextMarketDay = tradeDateDao.getNextMarketDay(20230518);
        LOG.debug("preMarketDay:{}", nextMarketDay);
    }

    @Test
    public void test3() {
        Map<String, Object> params = new HashMap<>();
        params.put("tradeDate", 20230518);
        List<TradeDate> tradeDateList = tradeDateDao.getTradeDateList(params);
        LOG.debug("getTradeDateList:");
        for (int i = 0; i < tradeDateList.size(); i++) {
            LOG.debug(tradeDateList.get(i).getTradeDate());
        }
    }

    @Test
    public void test4() {
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", 20230518);
        List<TradeDate> tradeDateList = tradeDateDao.getAllTradeDateList(params);
        LOG.debug("getAllTradeDateList:");
        for (int i = 0; i < tradeDateList.size(); i++) {
            LOG.debug(tradeDateList.get(i).getTradeDate());
        }
    }
}
