<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiSecProfitDayMapper">

    <select id="getSecProfitDay" resultType="as_secProfitDay">
        SELECT MARKET, STKCODE, PROFIT, PROFITRATE
        from ATCENTER.SEC_PROFIT_DAY use index(SEC_PROFIT_DAY_FUNDID_IDX)
        <where>
            FUNDID = #{fundId} and BIZDATE between #{startDate} and #{endDate} and STKCODE <![CDATA[<>]]> 'CASH'
            <if test='sort != null'>
                ${sort}
            </if>
            <if test="startNum != null and pageSize != null">
                limit #{startNum}, #{pageSize}
            </if>
        </where>
    </select>

    <select id="getSecProfitDayByRange" resultType="as_secProfitDay">
        SELECT * from (
            SELECT MARKET, STKCODE, sum(PROFIT) PROFIT
            from ATCENTER.SEC_PROFIT_DAY use index(SEC_PROFIT_DAY_FUNDID_IDX)
            where FUNDID = #{fundId}
                and BIZDATE between #{startDate} and #{endDate}
                and STKCODE <![CDATA[<>]]> 'CASH'
            group by MARKET, STKCODE
        ) s
        <if test='sort != null'>
            ${sort}
        </if>
        <if test="startNum != null and pageSize != null">
            limit #{startNum}, #{pageSize}
        </if>
    </select>

    <select id="getCashProfit" resultType="java.lang.Double">
        SELECT sum(PROFIT) PROFIT
        from ATCENTER.SEC_PROFIT_DAY use index(SEC_PROFIT_DAY_FUNDID_IDX)
        where FUNDID = #{fundId} and BIZDATE between #{startDate} and #{endDate} and STKCODE = 'CASH'
    </select>

    <select id="selectCountByCondition" resultType="java.lang.Integer">
        SELECT count(DISTINCT STKCODE,MARKET) num
        from ATCENTER.SEC_PROFIT_DAY use index(SEC_PROFIT_DAY_FUNDID_IDX)
        where FUNDID = #{fundId}
            and BIZDATE between #{startDate} and #{endDate}
            and STKCODE <![CDATA[<>]]> 'CASH'
    </select>
</mapper>