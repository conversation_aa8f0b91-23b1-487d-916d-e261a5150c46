package com.eastmoney.transport.server;

import com.eastmoney.transport.codec.MessageDecoder;
import com.eastmoney.transport.codec.MessageEncoder;
import com.eastmoney.transport.hearbeat.HeartBeatHandler;
import com.eastmoney.transport.listener.Listener;
import com.eastmoney.transport.util.Constants;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.timeout.ReadTimeoutHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created with IntelliJ IDEA.
 * User: sunyuncai
 * Date: 2015/8/27
 * Time: 15:51
 */
public class NettyServer {
    private static Logger LOG = LoggerFactory.getLogger(NettyServer.class);

    private volatile EventLoopGroup bossGroup;
    private volatile EventLoopGroup workerGroup;
    private volatile ServerBootstrap bootstrap;
    private volatile boolean closed = false;

    private final int port ;
    private Listener listener;
    private boolean isNeedHeartbeat;

    public NettyServer(int port) {
        this.port = port;
    }
    public NettyServer(int port,Listener listener) {
        this.port = port;
        this.listener = listener;
    }
    public NettyServer(int port,Listener listener,boolean isNeedHeartbeat) {
        this.port = port;
        this.listener = listener;
        this.isNeedHeartbeat = isNeedHeartbeat;
    }
    public void close() {
        closed = true;
        bossGroup.shutdownGracefully();
        workerGroup.shutdownGracefully();

        LOG.info("Stopped Tcp Server: {}", port);
    }

    public boolean isClosed() {
        return closed;
    }

    public void init() {
        closed = false;
        //配置服务端的NIO线程组
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
        bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup);

        bootstrap.channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG,1024)  //backlog参数指定了队列的大小
                .option(ChannelOption.SO_REUSEADDR,true)  //是否重复使用该端口
                .option(ChannelOption.SO_RCVBUF,1024*256)   //接收缓冲区大小
                .option(ChannelOption.SO_SNDBUF,1024*256)   //发送缓冲区大小
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)   //优化CPU的使用配置，及时回收不使用的对象
                .childOption(ChannelOption.SO_KEEPALIVE , true)     //是否保持长连接
                .childHandler(new ChildChannelHandler());
    }

    public void bind() throws Exception{
        if (closed) {
            return;
        }
        //绑定端口，开始绑定server，通过调用sync同步方法阻塞直到绑定成功
        ChannelFuture channelFuture = bootstrap.bind(port).sync();
        String serverName = Thread.currentThread().getName();
        LOG.info("********************** {} 启动成功，端口号： {} **********************", serverName, port);
        //应用程序会一直等待，直到channel关闭
        channelFuture.channel().closeFuture().sync();
        LOG.info("********************** {} 退出完毕，端口号： {} **********************", serverName, port);
    }

    private class ChildChannelHandler extends ChannelInitializer<SocketChannel> {

        @Override
        protected void initChannel(SocketChannel socketChannel) throws Exception {
            socketChannel.pipeline().addLast("decoder",new MessageDecoder());
            socketChannel.pipeline().addLast("encoder",new MessageEncoder());
            socketChannel.pipeline().addLast("readTimeoutHandler", new ReadTimeoutHandler(Constants.DISCONNCT_INTERVAL));
            if (isNeedHeartbeat) {
                socketChannel.pipeline().addLast("heartbeat", new HeartBeatHandler(true, Constants.HEART_BEAT_INTERVAL));
            }
            socketChannel.pipeline().addLast(new ServerHandler(listener));
        }
    }
}
