package com.eastmoney.quote.mdstp.serializer;

import com.eastmoney.quote.mdstp.model.QuoteTypeEnum;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.nio.ByteOrder;

/**
 * Created on 2019/5/29.
 *
 * @auther user
 */
public class QtDecoderRunner implements Runnable {

    private String quoteType;
    private byte[] dataBytes;

    public QtDecoderRunner(String quoteType, byte[] recvBytes) {
        this.quoteType = quoteType;
        this.dataBytes = recvBytes;
    }

    @Override
    public void run() {
        ByteBuf buffer = Unpooled.wrappedBuffer(dataBytes).order(ByteOrder.LITTLE_ENDIAN);
        byte cmdId = buffer.readByte();
        if (QuoteTypeEnum.QuoteHSSnapshot.getValue() == cmdId) {
            QtDecoder.decode(buffer, true);
        } else if (QuoteTypeEnum.QuoteHKSnapshot.getValue() == cmdId) {
            HkQtDecoder.decode(buffer, true);
        }
    }
}
