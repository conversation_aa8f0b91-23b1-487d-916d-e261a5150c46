package com.eastmoney.common.entity;

import com.eastmoney.common.annotation.ZTFiled;

/**
 * Created by Administrator on 2017/2/9.
 */
public class LogAssetChild extends LogAsset {

    @ZTFiled("yjyhs")
    private Double feeOneYhs; //一级印花税
    @ZTFiled("yjghf")
    private Double feeOneGhf;   //一级过户费
    @ZTFiled("yjqsf")
    private Double feeOneQsf;   //一级清算费
    @ZTFiled("yjjygf")
    private Double feeOneJygf;  //一级交易规费
    @ZTFiled("yjjsf")
    private Double feeOneJsf;  //一级经手费
    @ZTFiled("yjzgf")
    private Double feeOneZgf;  //一级证管费
    @ZTFiled("yjqtf")
    private Double feeOneQtf;  //一级证管费
    @ZTFiled("yjfxj")
    private Double feeOneFxj;  //一级证管费

    public Double getFeeOneYhs() {
        return feeOneYhs;
    }

    public void setFeeOneYhs(Double feeOneYhs) {
        this.feeOneYhs = feeOneYhs;
    }

    public Double getFeeOneGhf() {
        return feeOneGhf;
    }

    public void setFeeOneGhf(Double feeOneGhf) {
        this.feeOneGhf = feeOneGhf;
    }

    public Double getFeeOneQsf() {
        return feeOneQsf;
    }

    public void setFeeOneQsf(Double feeOneQsf) {
        this.feeOneQsf = feeOneQsf;
    }

    public Double getFeeOneJygf() {
        return feeOneJygf;
    }

    public void setFeeOneJygf(Double feeOneJygf) {
        this.feeOneJygf = feeOneJygf;
    }

    public Double getFeeOneJsf() {
        return feeOneJsf;
    }

    public void setFeeOneJsf(Double feeOneJsf) {
        this.feeOneJsf = feeOneJsf;
    }

    public Double getFeeOneZgf() {
        return feeOneZgf;
    }

    public void setFeeOneZgf(Double feeOneZgf) {
        this.feeOneZgf = feeOneZgf;
    }

    public Double getFeeOneQtf() {
        return feeOneQtf;
    }

    public void setFeeOneQtf(Double feeOneQtf) {
        this.feeOneQtf = feeOneQtf;
    }

    public Double getFeeOneFxj() {
        return feeOneFxj;
    }

    public void setFeeOneFxj(Double feeOneFxj) {
        this.feeOneFxj = feeOneFxj;
    }






}
