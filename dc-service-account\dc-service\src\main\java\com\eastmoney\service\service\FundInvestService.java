package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.fundia.FundInvestDao;
import com.eastmoney.accessor.dao.kgdb.OtcInstNetDao;
import com.eastmoney.common.entity.fundia.CombAsset;
import com.eastmoney.common.entity.fundia.CombProfit;
import com.eastmoney.common.entity.fundia.QueryCombFundAssetResponse;
import com.eastmoney.common.util.ArithUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/7
 */
@Service
public class FundInvestService {
    private final Logger LOGGER = LoggerFactory.getLogger(FundInvestService.class);

    @Autowired
    private FundInvestDao fundInvestDao;
    @Autowired
    private OtcInstNetDao otcInstNetDao;

    /**
     * 获取投顾组合资产map
     * key->combcde  value->组合资产信息
     */
    public Map<String, QueryCombFundAssetResponse> getInvestCombAssetMap(Map<String, Object> params) {
        Map<String, QueryCombFundAssetResponse> assetMap = new HashMap<>();
        //持仓份额中已经扣掉了实收投顾服务费，计提投顾服务费中包含了实收的部分，需要把实收服务费累加上，否则资产计算少了
        //组合总资产=Σ(基金持仓余额*净值)+资金到账余额+在途+补偿资金资产+补偿证券资产 - (每日计提投顾服务费（包含实收投顾服务费部分） - 抵扣费用 - 实收投顾服务费)
        //查询组合资产
        List<CombAsset> combAssetList = fundInvestDao.queryCombAsset(params);
        List<CombProfit> profits = fundInvestDao.queryCombProfit(params);
        if (!CollectionUtils.isEmpty(combAssetList)) {
            Map<String, CombProfit> profitMap = !CollectionUtils.isEmpty(profits) ? profits.stream().collect(Collectors.toMap(k -> k.getInvestId(), v -> v, (v1, v2) -> v1)) : new HashMap<>();

            //计算组合资产
            for (CombAsset combAsset : combAssetList) {
                QueryCombFundAssetResponse asset = assetMap.get(combAsset.getCombCode());
                Double total = 0.0;
                if (asset == null) {
                    asset = new QueryCombFundAssetResponse();
                    asset.setCombCode(combAsset.getCombCode());
                }
                //查询基金净值
                if ("0".equals(combAsset.getType())) {
                    //基金份额
                    //基金持仓 = 基金份额余额  * 基金净值
                    Double ofasset = ArithUtil.round(ArithUtil.mul(combAsset.getBal(), otcInstNetDao.getLastNet(combAsset.getFundCode())), 2);
                    total = ArithUtil.add(total, ofasset);
                    LOGGER.debug("资金账号{}获取基金投顾持仓:{},bal:{},净值:{},持仓资产:{}", params.get("fundId"), combAsset.getFundCode(),
                            combAsset.getBal(), otcInstNetDao.getLastNet(combAsset.getFundCode()), ofasset);
                } else {
                    //投顾费用 = 计提-抵扣-实收
                    Map<String, Double> feeMap = fundInvestDao.queryInvestFee(params);

                    //组合资金  = Σ 基金持仓 + 补偿资金资产+补偿证券资产 +在途 - 费用
                    Double fee = feeMap.getOrDefault(combAsset.getCombCode(), 0.0);
                    total = ArithUtil.sub(ArithUtil.add(total, combAsset.getBal(), combAsset.getUnArriveFund(),
                                    combAsset.getFundAssetAdjAmt(), combAsset.getStkAssetAdjAmt()),
                            fee.compareTo(0.0) < 0 ? 0.0 : fee);
                    LOGGER.debug("资金账号{}获取基金投顾资金:{},bal:{}, 未到账资金:{}, 资金调整:{}, 持仓调整:{}, 费用:{}", params.get("fundId"),
                            combAsset.getCombCode(), combAsset.getBal(), combAsset.getUnArriveFund(),
                            combAsset.getFundAssetAdjAmt(), combAsset.getStkAssetAdjAmt(), fee);
                }

                asset.setTotalAsset(ArithUtil.add(total, asset.getTotalAsset() == null ? 0 : asset.getTotalAsset()));
                //查询组合当前收益
                if (asset.getProfitRate() == null || asset.getTotalProfit() == null || asset.getProfitAmt() == null) {
                    if (profitMap.containsKey(combAsset.getInvestId())) {
                        String bizDate = profitMap.get(combAsset.getInvestId()).getBizDate();
                        if (!StringUtils.isEmpty(bizDate)) {
                            asset.setLastProfitDate(bizDate);
                        }
                    }
                    asset.setProfitAmt(asset.getProfitAmt() == null ? 0.0 : asset.getProfitAmt());
                    asset.setTotalProfit(asset.getTotalProfit() == null ? 0.00 : asset.getTotalProfit());
                    asset.setProfitRate(asset.getProfitRate() == null ? 0.00 : asset.getProfitRate());
                }
                assetMap.put(asset.getCombCode(), asset);
            }
        }

        return assetMap;
    }
}
