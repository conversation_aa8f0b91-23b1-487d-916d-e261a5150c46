package com.eastmoney.common.model;

/**
 * Created by Administrator on 2016/3/8.
 */
public class PushOutput extends Output{
    private Long endId;
    private Long beginId;
    //是否满足条件
    public boolean isSatisfiedCondition = false;
    //是否检查过条件
    public boolean isConditionChecked = false;

    public PushOutput() {
        super();
    }
    public PushOutput(String funCode) {
        super(funCode);
    }
    public Long getEndId() {
        return endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getBeginId() {
        return beginId;
    }

    public void setBeginId(Long beginId) {
        this.beginId = beginId;
    }
}
