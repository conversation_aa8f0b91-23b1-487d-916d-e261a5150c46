package com.eastmoney.common.entity;

/**
 * Created on 2016/3/1
 * 成交流水
 *
 * <AUTHOR>
 */
public class Match extends BaseMatch {
    private Double bondIntr;/*国债利息*/
    private String bankCode;/*外部机构*/
    private String bankBranch;/*add支行代码*/
    private String bankNetPlace;/*add银行银行网点*/
    private String sourceType;/*发起方*/
    private Long recNum;/*外部记录号*/
    private String bankOrderId;/*外部合同号*/
    private String bankId;/*银行帐号*/
    private Double extEffectAmt;/*已清算外部资金*/
    private String bankRtnFlag;/*银行回报成功标志 '1'已回报 '0'未回报*/
    private String creditId;/*融资品种标识*/
    private String creditFlag;/*融资开仓平仓强平*/

    public Double getBondIntr() {
        return bondIntr;
    }

    public void setBondIntr(Double bondIntr) {
        this.bondIntr = bondIntr;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getBankNetPlace() {
        return bankNetPlace;
    }

    public void setBankNetPlace(String bankNetPlace) {
        this.bankNetPlace = bankNetPlace;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public Long getRecNum() {
        return recNum;
    }

    public void setRecNum(Long recNum) {
        this.recNum = recNum;
    }

    public String getBankOrderId() {
        return bankOrderId;
    }

    public void setBankOrderId(String bankOrderId) {
        this.bankOrderId = bankOrderId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public Double getExtEffectAmt() {
        return extEffectAmt;
    }

    public void setExtEffectAmt(Double extEffectAmt) {
        this.extEffectAmt = extEffectAmt;
    }

    public String getBankRtnFlag() {
        return bankRtnFlag;
    }

    public void setBankRtnFlag(String bankRtnFlag) {
        this.bankRtnFlag = bankRtnFlag;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(String creditFlag) {
        this.creditFlag = creditFlag;
    }
}
