package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateContrastChartDao;
import com.eastmoney.accessor.mapper.tidb.TiProfitRateContrastChartMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.ProfitRateContrastChart;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created on 2016/7/19
 * <AUTHOR>
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("profitRateContrastChartDao")
public class TiProfitRateContrastChartDaoImpl extends BaseDao<TiProfitRateContrastChartMapper, ProfitRateContrastChart, <PERSON>> implements ProfitRateContrastChartDao {

}
