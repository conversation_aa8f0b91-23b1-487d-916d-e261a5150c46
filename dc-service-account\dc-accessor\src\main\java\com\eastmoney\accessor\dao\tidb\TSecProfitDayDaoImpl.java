package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiTSecProfitDayMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.tProfitRank;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/30 11:07
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("tSecProfitDayDao")
public class TSecProfitDayDaoImpl extends BaseDao<TiTSecProfitDayMapper, TSecProfitDayDO, Long> implements TSecProfitDayDao {
    @Override
    public List<tProfitRank> getSecTProfitRankList(Map<String, Object> params) {
        return getMapper().getSecTProfitRankList(params);
    }

    @Override
    public List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params) {
        return getMapper().getTSecProfitDayList(params);
    }

    @Override
    public TProfitBO getSecTProfitSection(Map<String, Object> params) {
        return getMapper().getSecTProfitSection(params);
    }

    @Override
    public List<TProfitDayDO> getTProfitDayList(Map<String, Object> params) {
        return getMapper().getTProfitDayList(params);
    }

    @Override
    public List<TSecProfitDayDO> getTSecProfitDayListPC(Map<String, Object> params) {
        return getMapper().getTSecProfitDayListPC(params);
    }

}
