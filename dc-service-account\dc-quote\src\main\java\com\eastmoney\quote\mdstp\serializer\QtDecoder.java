package com.eastmoney.quote.mdstp.serializer;/**
 * Created by 1 on 16-10-24.
 */

import com.eastmoney.quote.mdstp.container.QuoteContainer;
import com.eastmoney.quote.mdstp.model.BuySellInfo;
import com.eastmoney.quote.mdstp.model.FundFlow;
import com.eastmoney.quote.mdstp.model.QtRec;
import com.eastmoney.quote.mdstp.pull.serializer.Packet;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created on 16-10-24
 *
 * <AUTHOR>
 */
public class QtDecoder {

    private static Logger logger = LoggerFactory.getLogger(QtDecoder.class);

    //DATACENTER-4325 type优化
    //股指期货  168 - 256 = -88;
    public static final byte TYPE_OLD_IF = 168 - 256;
    //国债期货  169 - 256 = -87;
    public static final byte TYPE_OLD_TF = 169 - 256;
    //指数期权认购    166 - 256 = -90
    public static final byte TYPE_OLD_QQ_C = 166 - 256;
    //指数期权认沽    167 - 256 = -89
    public static final byte TYPE_OLD_QQ_P = 167 - 256;
    //上海期权认购
    public static final byte  TYPE_OLD_SH_OP_C = 173 - 256;
    //上海期权认沽
    public static final byte  TYPE_OLD_SH_OP_P = 174 - 256;
    //DATACENTER-4325 type类型错误优化
    //深圳认购期权
    public static final byte  TYPE_OLD_SZ_OP_C = 178 - 256;
    //深圳认沽期权
    public static final byte  TYPE_OLD_SZ_OP_P = 179 - 256;
    private static AtomicInteger counter = new AtomicInteger(0);

    public static void decode(ByteBuf buffer,boolean isPush){
        try {
            if (buffer != null) {
                processSnapShot(buffer,isPush);
            }

        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
    }

    private static void processSnapShot(ByteBuf buffer,boolean isPush) throws UnsupportedEncodingException {
        if(isPush){
            int stdQt = buffer.readInt();
        }
        int size = buffer.readInt();
        for (int i = 1; i <= size; i++) {
            QtRec qtrec = new QtRec();
            //股票唯一标识
            qtrec.setDwStockId(buffer.readInt());
            // 代码
            qtrec.setCode(readString(buffer));


            byte type = buffer.readByte();
            qtrec.setType(type);
            // 前日收盘价(算昨涨跌幅)
            qtrec.setDwClose(buffer.readInt());
            // 流通股数
            qtrec.setxLTG(buffer.readLong());
            // 总发行量
            qtrec.setxZFX(buffer.readLong());
            // 涨停价
            qtrec.setDwPriceZT(buffer.readInt());
            // 跌停价
            qtrec.setDwPriceDT(buffer.readInt());
            // 两日前收盘价
            qtrec.setDw2Close(buffer.readInt());
            //DATACENTER-4325
            //PreHold	Int32	昨持仓量	Type为168,169,173,174时存在
            //PreSettlementPrice	Int32	昨结算，基金为【IOPV昨收】	Type为168,169,173,174时存在
            if (type == TYPE_OLD_IF || type == TYPE_OLD_TF
                    || type == TYPE_OLD_SH_OP_C || type == TYPE_OLD_SH_OP_P) {
                // 昨持仓量 (仅Type = 168,169,173,174有该字段)
                qtrec.setxPreOpenInterest(buffer.readInt());
                // 昨结算   (仅Type = 168,169,173,174有该字段
                qtrec.setDwPreSettlementPrice(buffer.readInt());
            }

            // 交易时间hhmmss
            qtrec.setDwTime(buffer.readInt());
            // 开盘价
            qtrec.setDwOpen(buffer.readInt());
            // 最高价
            qtrec.setDwHigh(buffer.readInt());
            // 最低价
            qtrec.setDwLow(buffer.readInt());
            // 最新价
            qtrec.setDwPrice(buffer.readInt());

//            System.out.println(i + " code:" + qtrec.getCode() + ",dwNew:" + qtrec.getDwPrice() + " dwClose:" + qtrec.getDwClose());
            // 成交量
            qtrec.setxVolume(buffer.readLong());
            // 成交额
            qtrec.setxAmount(buffer.readDouble());
            // 成交笔数
            qtrec.setDwTradeNum(buffer.readInt());
            // 买盘个数
            qtrec.setcNumBuy(buffer.readByte());
            // 卖盘个数
            qtrec.setcNumSell(buffer.readByte());
            if (qtrec.getcNumBuy() + qtrec.getcNumSell() > 0) {
                List<BuySellInfo> buySellInfos = new ArrayList<BuySellInfo>();
                for (int m = 0; m < qtrec.getcNumSell(); m++) {
                    BuySellInfo buySellInfo = new BuySellInfo();
                    buySellInfo.setDwMMp(buffer.readInt());
                    buySellInfo.setxMMPVol(buffer.readLong());
                    buySellInfo.setBuyFlag((byte)1);
                    buySellInfos.add(buySellInfo);
                }


                for (int m = 0; m < qtrec.getcNumBuy(); m++) {
                    BuySellInfo buySellInfo = new BuySellInfo();
                    buySellInfo.setDwMMp(buffer.readInt());
                    buySellInfo.setxMMPVol(buffer.readLong());
                    buySellInfo.setBuyFlag((byte) 0);
                    buySellInfos.add(buySellInfo);

                }
//                qtrec.setBuySellInfos(buySellInfos);
            }
            //DATACENTER-4325 mdstp协议一致性修改,在原代码的基础上删去了dwTradeNum(成交笔数)=0的判断
            // Open,High,Low,Close,Volume,Amount为0时，该行及以下字段不存在
            if (qtrec.getDwOpen() == 0 && qtrec.getDwHigh() == 0 && qtrec.getDwLow() == 0 && qtrec.getDwPrice() == 0
                    && qtrec.getxVolume() == 0 && qtrec.getxAmount() == 0) {
                //协议优化，发现以上指标为空，下面指标略过
                QuoteContainer.updateQtRec(qtrec.getCode(),qtrec);
                continue;
            }

            // 委托买入均价
            qtrec.setDwPBuy(buffer.readInt());
            // 委托买入总量
            qtrec.setxVBuy(buffer.readLong());
            // 委托卖出均价
            qtrec.setDwPSell(buffer.readInt());
            // 委托买入总量
            qtrec.setxVSell(buffer.readLong());
            // 外盘
            qtrec.setxWaiPan(buffer.readLong());
            // 现手
            qtrec.setxCurVol(buffer.readLong());
            // 现手方向
            qtrec.setcCurVol(buffer.readByte());
            //DATACENTER-4325 mdstp协议一致性bug修改
            //type为173,174,178,179时为今结算价，其他类型为5分钟前的价格
            if (type == TYPE_OLD_SH_OP_C || type == TYPE_OLD_SH_OP_P
                    || type == TYPE_OLD_SZ_OP_C || type == TYPE_OLD_SZ_OP_P) {
                //今结算
                qtrec.setDwSettlementPrice(buffer.readInt());
            } else {
                // 5分钟前的价格
                qtrec.setDwPrice5(buffer.readInt());
            }

            // 理论价格
            qtrec.setDwNorminal(buffer.readInt());
            // 集合竞价
            qtrec.setcVirtual(buffer.readByte());
            // 集合竞价成交额
            qtrec.setxVirtualAmt(buffer.readDouble());


            //资金流向
            byte fundFlowNum = buffer.readByte();
            if (fundFlowNum > 0) {
                List<FundFlow> fundFlows = new ArrayList<FundFlow>();
                for (int m = 0; m < 4; m++) {
                    FundFlow fundFlow = new FundFlow();
                    // 买盘成交笔数
                    fundFlow.setDwNumOfBuy(buffer.readInt());
                    // 卖盘成交笔数
                    fundFlow.setDwNumOfSell(buffer.readInt());
                    // 买盘成交量
                    fundFlow.setxVolOfBuy(buffer.readLong());
                    // 卖盘成交量
                    fundFlow.setxVolOfSell(buffer.readLong());
                    // 买盘成交额
                    fundFlow.setxAmtOfBuy(buffer.readDouble());
                    // 卖盘成交额
                    fundFlow.setxAmtOfSell(buffer.readDouble());
                    fundFlows.add(fundFlow);
                }
//                qtrec.setFundFlows(fundFlows);

            }
            //DATACENTER-4325 mdstp协议一致性修改,删去type= TYPE_OLD_QQ_C || type == TYPE_OLD_QQ_P
            //Type为168,169时解析该行及以下
            if (type == TYPE_OLD_IF || type == TYPE_OLD_TF) {
                // 持仓量变化
                qtrec.setxCurOI(buffer.readLong());
                //持仓量
                qtrec.setxOpenInterest(buffer.readLong());
                //今结算
                qtrec.setDwSettlementPrice(buffer.readInt());
            }


            QuoteContainer.updateQtRec(qtrec.getCode(),qtrec);
//            System.out.println(JSON.toJSONString(qtrec));
        }
//        System.out.println("-----------------------------------"+counter.getAndIncrement()+"--------------------------------------");
    }

    private static String readString(ByteBuf buffer) throws UnsupportedEncodingException {
        byte len = buffer.readByte();
        byte[] bytes = new byte[len];
        buffer.readBytes(bytes);
        return new String(bytes, "GBK");
    }
}
