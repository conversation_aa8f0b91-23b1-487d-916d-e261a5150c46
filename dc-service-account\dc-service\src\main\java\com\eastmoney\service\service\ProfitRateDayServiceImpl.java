package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.oracle.MajorBillDao;
import com.eastmoney.accessor.dao.oracle.ProfitRateDayDao;
import com.eastmoney.common.entity.cal.ProfitRateDay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.eastmoney.accessor.enums.ProfitSectionStatEnum.*;

/**
 * 日收益率service
 * <AUTHOR>
 * @create 2024/2/20
 */
@Service
public class ProfitRateDayServiceImpl implements ProfitRateDayService {

    @Autowired
    private ProfitRateDayDao profitRateDayDao;

    @Autowired
    private MajorBillDao majorBillDao;


    @Override
    public List<ProfitRateDay> queryProfitRateDayByUnit(Long fundId, Integer startDate, Integer endDate, String unit) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("startDate", startDate);
        queryParams.put("endDate", endDate);

        if (Objects.equals(unit, M.getType()) || Objects.equals(unit, Y.getType())) {
            return profitRateDayDao.query(queryParams);
        } else if (Objects.equals(unit, YS.getType())) {
            return majorBillDao.selectYearProfitRate(fundId, startDate, endDate);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProfitRateDay> queryProfitRateByBizDate(Long fundId, Integer bizDate) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fundId", fundId);
        queryParams.put("bizDate", bizDate);
        return profitRateDayDao.query(queryParams);
    }

}
