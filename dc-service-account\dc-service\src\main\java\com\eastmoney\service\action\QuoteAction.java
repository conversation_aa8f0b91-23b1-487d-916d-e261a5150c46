package com.eastmoney.service.action;

import com.eastmoney.common.annotation.Action;
import com.eastmoney.common.annotation.FunCodeMapping;
import com.eastmoney.common.annotation.RequestMapping;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.handler.StockHandler;
import com.eastmoney.service.service.quote.QuoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017-08-18 9:25
 */
@Action
@Component
public class QuoteAction {

    @Autowired
    private QuoteService quoteService;

    @Autowired
    private StockHandler stockHandler;

    /**
     *  获取指数行情
     */
    @FunCodeMapping("getIndexCodeQuoList")
    @RequestMapping("/getIndexCodeQuoList")
    public Object getIndexCodeQuoList(Map params) {
        //"000300.SH", "000001.SH", "399001.SZ", "399006.SZ", "399005.SZ","HKI|HSI"
        String indexCode = CommonUtil.convert(params.get("indexCode"), String.class);
        String market = CommonUtil.convert(params.get("market"), String.class);
        return quoteService.getQtPrice(indexCode,market);
    }
    @FunCodeMapping("getAllQuoteList")
    @RequestMapping("/getAllQuoteList")
    public List getAllQuoteList(Map params){
        return quoteService.getAllQuoteList(params);
    }

    /**
     *  刷新码表缓存
     */
    @RequestMapping("/flushStock")
    @FunCodeMapping("flushStock")
    public Object flushStock(Map params) {
        String token = CommonUtil.convert(params, "token", String.class);
        return stockHandler.flushStock(token);
    }
}
