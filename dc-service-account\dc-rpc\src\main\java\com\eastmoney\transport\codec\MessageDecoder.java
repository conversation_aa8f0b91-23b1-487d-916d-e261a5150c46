package com.eastmoney.transport.codec;

import com.eastmoney.transport.model.Message;
import com.eastmoney.transport.util.ZLibUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteOrder;
import java.util.List;

public class MessageDecoder extends ByteToMessageDecoder {

    private static Logger LOG = LoggerFactory.getLogger(MessageDecoder.class);
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        in = in.order(ByteOrder.LITTLE_ENDIAN);
        //固定长度(split 1，packLen 4,type 1,requestId 32, version 1,cipher 1,replyCipher 1,compress 1)
        int fixLength = 42;
        if (in.readableBytes() < fixLength) {
            return;
        }

        in.markReaderIndex();
        byte split = in.readByte(); //0
        if (split != 0) {
            throw new Exception("串包了"+split);
        }
        int packLen = in.readInt(); //4
        // Wait until the whole data is available.
        if (in.readableBytes() < (packLen-5)) {
            in.resetReaderIndex();
            return;
        }
        byte type = in.readByte(); //1
//        int requestId = in.readInt(); //4
        byte[] requestId = new byte[32];
        in.readBytes(requestId);

        byte version = in.readByte();
        byte cipher = in.readByte();
        byte replyCipher = in.readByte();
        byte compress = in.readByte();

        int contentLength = packLen - fixLength;
        byte[] content = null;
        if (contentLength > 0) {
            content = new byte[contentLength];
            try {
                in.readBytes(content);
                //如果是压缩的，需要解压缩
                if (compress == 1) {
                    byte[] decompressContent = ZLibUtils.decompress(content);
                    content = decompressContent;
                }
            }catch (Exception e){
                LOG.error("", e);
                throw e;
            }
        }
        Message message =  new Message(split,packLen,type,content);
        message.setVersion(version);
        message.setCipher(cipher);
        message.setReplyCipher(replyCipher);
        message.setCompress(compress);
        message.setRequestId(requestId);
        if (message.getType() != 3 && message.getType() != 4) {
            LOG.debug("decode=============" + message + " channel=" + ctx.channel());
        }
        out.add(message);
    }
}
