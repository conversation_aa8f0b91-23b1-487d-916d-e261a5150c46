<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiTProfitDayMapper">

    <select id="getTProfitDayList" resultType="com.eastmoney.common.entity.cal.TProfitDayDO">
        select bizdate, tprofit from atcenter.t_profit_day use index(pk_lg_t_profit_day)
        <where>
            <if test="startDate != null">
                and bizdate  <![CDATA[>=]]>  #{startDate}
            </if>
            <if test="endDate != null">
                and bizdate  <![CDATA[<=]]>  #{endDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
        </where>
        order by bizdate desc
        limit #{startNum},#{pageSize}
    </select>

    <select id="getTProfitSection" resultType="com.eastmoney.common.entity.cal.TProfitBO">
        select
        totalttimes,
        totaltsuccess,
        totaltprofit,
        round(totaltsuccess/totalttimes,4) as totaltsuccessrate
        from (
        select
        sum(ttimes) as totalttimes,
        sum(tsuccess) as totaltsuccess,
        sum(tprofit) as totaltprofit from atcenter.t_profit_day use index(pk_lg_t_profit_day)
        <where>
            <if test="startDate != null">
                and bizdate  <![CDATA[>=]]>  #{startDate}
            </if>
            <if test="endDate != null">
                and bizdate  <![CDATA[<=]]>  #{endDate}
            </if>
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
        </where>
        ) res
    </select>

</mapper>