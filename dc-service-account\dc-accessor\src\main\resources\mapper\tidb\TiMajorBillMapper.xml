<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.tidb.TiMajorBillMapper">

    <sql id="All_Column">
        indexKey,profitRate,profit,profitRankIndex,profitRankPercent,VOLATILITY,SHARPERATIO,TRADECOUNT,
        gainRate,maxGain,maxLoss,maximum,totalCount,gainCount,lossCount,avgPosition,buyCount
    </sql>


    <select id="selectByCondition" resultType="com.eastmoney.common.entity.cal.MajorBill">
        select <include refid="All_Column"/>
        from ATCENTER.B_Major_Bill use index(pk_lg_b_marjor_bill)
        where fundid = #{fundId} and indexKey = #{indexKey}
    </select>

    <select id="selectByYearBill" resultType="com.eastmoney.common.entity.cal.yearbill.MajorBillStrInfo">
        select profitRate,profit,profitRankPercent
        from ATCENTER.B_Major_Bill use index(pk_lg_b_marjor_bill)
        where fundid = #{fundId} and indexKey = #{indexKey}
    </select>

    <select id="getBestMonth" resultType="com.eastmoney.common.entity.cal.MajorBill">
    SELECT <include refid="All_Column"/>
         FROM ATCENTER.B_MAJOR_BILL use index(pk_lg_b_marjor_bill)
           WHERE FUNDID = #{fundId}
           AND TIMESLOT = 'M'
           AND INDEXKEY BETWEEN concat(cast(#{indexKey} as char(4)),'01') AND concat(cast(#{indexKey} as char(4)),'12')
         ORDER BY PROFIT DESC, INDEXKEY DESC 
		limit 1
    </select>

    <select id="selectMonthProfitRate" resultType="com.eastmoney.common.entity.cal.ProfitRateDay">
        select indexKey bizDate,profitRate
        from atcenter.B_Major_Bill use index(pk_lg_b_marjor_bill)
        <where>
            timeSlot='M'
            <if test="fundId != null">
                and fundId=#{fundId}
            </if>
            <if test="startDate != null">
                and indexKey >= #{startDate}
            </if>
            <if test="endDate != null">
                and indexKey <![CDATA[<=]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="selectMonthProfit" resultType="com.eastmoney.common.entity.cal.ProfitDay">
        select indexKey bizDate, profit
        from atcenter.B_Major_Bill use index(pk_lg_b_marjor_bill)
        <where>
            fundId= #{fundId} and timeSlot='M'
            <if test="startDate != null">
                and indexKey >= #{startDate}
            </if>
            <if test="endDate != null">
                and indexKey <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        order by indexKey
    </select>

    <select id="selectYearProfit" resultType="com.eastmoney.common.entity.cal.ProfitDay">
        select left(indexkey, 4) as bizDate, sum(profit) profit
        from atcenter.B_Major_Bill use index(pk_lg_b_marjor_bill)
        <where>
            fundId= #{fundId} and timeSlot='M'
            <if test="startDate != null">
                and indexKey >= #{startDate}
            </if>
            <if test="endDate != null">
                and indexKey <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by left(indexkey, 4)
        order by left(indexkey, 4)
    </select>

    <select id="selectYearProfitRate" resultType="com.eastmoney.common.entity.cal.ProfitRateDay">
        select left(indexkey, 4) as bizDate, POWER(10, SUM(LOG(10, (1 +
            CASE SIGN(profitrate + 1)
                when 1 then  profitrate
                when 0 then 0
                when -1 then 0
            end)
            )))-1 profitRate
        from atcenter.B_Major_Bill use index(pk_lg_b_marjor_bill)
        <where>
            fundId= #{fundId} and timeSlot='M'
            <if test="startDate != null">
                and indexKey >= #{startDate}
            </if>
            <if test="endDate != null">
                and indexKey <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        group by left(indexkey, 4)
        order by left(indexkey, 4)
    </select>

</mapper>