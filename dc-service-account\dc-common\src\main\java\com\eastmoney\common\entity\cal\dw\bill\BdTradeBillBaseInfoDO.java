package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @Project dc-service-account
 * @Description  基础信息------ 2023年账单新增表,由数据中心提供
 * <AUTHOR>
 * @Date 2023/11/17 13:42
 * @Version 1.0
 */

public class BdTradeBillBaseInfoDO {
    /**
     * 用户年龄
     */
    private Integer userAge;
    /**
     * 用户性别
     */
    private String userSex;
    /**
     * 开户日期
     */
    private Integer openDate;
    /**
     * 开户日期距今天数
     */
    private Long openDays;
    /**
     * 开户天数超越股友百分比
     */
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal openDaysPerc;

    /**
     * 是否牛市开户
     * 2024年账单-新增（数据中心提供）
     */
    private Integer bullOpenDateFlag;
    /**
     * 2024首笔交易日期在09/24-10/11疯牛期间
     * 2024年账单-新增（数据中心提供）
     */
    private Integer bullFtradeFlag;

    /**
     * 2024全年月度收益金额最高且为正的月份是否为9月或10月
     * 2024年账单-新增（数据中心提供）
     */
    private Integer bullProfitBestmonthDateFlag;
    /**
     * 短线/中长线（根据平均持股天数划分）
     * 2024年账单-新增（数据中心提供）
     */
    private Integer tradeTerm;

    public Integer getUserAge() {
        return userAge;
    }

    public void setUserAge(Integer userAge) {
        this.userAge = userAge;
    }

    public String getUserSex() {
        return userSex;
    }

    public void setUserSex(String userSex) {
        this.userSex = userSex;
    }

    public Integer getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Integer openDate) {
        this.openDate = openDate;
    }

    public Long getOpenDays() {
        return openDays;
    }

    public void setOpenDays(Long openDays) {
        this.openDays = openDays;
    }

    public BigDecimal getOpenDaysPerc() {
        return openDaysPerc;
    }

    public void setOpenDaysPerc(BigDecimal openDaysPerc) {
        this.openDaysPerc = openDaysPerc;
    }

    public Integer getBullOpenDateFlag() {
        return bullOpenDateFlag;
    }

    public void setBullOpenDateFlag(Integer bullOpenDateFlag) {
        this.bullOpenDateFlag = bullOpenDateFlag;
    }

    public Integer getBullFtradeFlag() {
        return bullFtradeFlag;
    }

    public void setBullFtradeFlag(Integer bullFtradeFlag) {
        this.bullFtradeFlag = bullFtradeFlag;
    }

    public Integer getBullProfitBestmonthDateFlag() {
        return bullProfitBestmonthDateFlag;
    }

    public void setBullProfitBestmonthDateFlag(Integer bullProfitBestmonthDateFlag) {
        this.bullProfitBestmonthDateFlag = bullProfitBestmonthDateFlag;
    }

    public Integer getTradeTerm() {
        return tradeTerm;
    }

    public void setTradeTerm(Integer tradeTerm) {
        this.tradeTerm = tradeTerm;
    }
}
