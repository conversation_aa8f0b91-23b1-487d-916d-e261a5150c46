package com.eastmoney.accessor.service;

import com.eastmoney.accessor.dao.tidb.CoreConfigDao;
import com.eastmoney.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 营业部核心编码配置映射
 */
@Service
public class CoreConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CoreConfigService.class);

    @Resource
    private CoreConfigDao coreConfigDao;

    private final ConcurrentHashMap<String, Integer> CORE_CONFIG_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void loadCoreConfig() {
        LOGGER.info("------------------------正在营业部核心编码映射配置信息，请等待-------------------------{}", DateUtil.getCurDateTime());
        // 线程池创建
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        // 首次加载时，初始延迟为0，任务立即执行
        // 之后每隔15分钟执行一次任务
        int initialDelay = 0;
        int period = 15;
        executor.scheduleAtFixedRate(this::doLoadCoreConfig, initialDelay, period, TimeUnit.MINUTES);
        LOGGER.info("------------------------营业部核心编码映射配置信息加载完成，总计={}，date={}", CORE_CONFIG_MAP.size(), DateUtil.getCurDateTime());
    }

    private void doLoadCoreConfig() {
        // 查询atcenter.coreconfig中的营业部核心编码配置信息
        Map<String, Integer> coreConfig = coreConfigDao.getCoreConfig();
        CORE_CONFIG_MAP.putAll(coreConfig);
        LOGGER.info("------------------------营业部核心编码映射配置信息加载完成，总计={}，date={}", CORE_CONFIG_MAP.size(), DateUtil.getCurDateTime());
    }

    /**
     * 根据资金账号获取对应的机器核心编号
     *
     * @param fundId
     * @return
     */
    public Integer getServerId(Long fundId) {
        if (fundId == null) {
            return -1;
        }
        // 优先使用资金账号的前5位匹配，匹配不到时使用前4位匹配
        String orgId = Long.toString(fundId).substring(0, 5);
        Integer serverId = CORE_CONFIG_MAP.get(orgId);
        if (serverId != null) {
            return serverId;
        }

        //资金账号前5位匹配不到时，使用前4位匹配
        orgId = Long.toString(fundId).substring(0, 4);
        serverId = CORE_CONFIG_MAP.get(orgId);
        if (serverId != null) {
            return serverId;
        }

        return -1;
    }

}
