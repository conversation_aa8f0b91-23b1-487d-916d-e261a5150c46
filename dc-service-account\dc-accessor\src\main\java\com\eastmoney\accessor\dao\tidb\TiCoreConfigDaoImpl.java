package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiCoreConfigMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.CoreConfig;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 营业部核心编码映射配置
 */
@ZhfxDataSource("tidb")
@Conditional(ZhfxDataSourceCondition.class)
@Service("coreConfigDao")
public class TiCoreConfigDaoImpl extends BaseDao<TiCoreConfigMapper, CoreConfig, Long> implements CoreConfigDao {


    @Override
    public Map<String, Integer> getCoreConfig() {
        List<CoreConfig> configList = getMapper().selectConfig();
        return configList.stream().
                // 过滤null值
                        filter(coreConfig -> coreConfig != null && !StringUtils.isEmpty(coreConfig.getOrgId())
                        && coreConfig.getServerId() != null).
                // 聚合为Map集合
                        collect(Collectors.toMap(CoreConfig::getOrgId, CoreConfig::getServerId));
    }
}
