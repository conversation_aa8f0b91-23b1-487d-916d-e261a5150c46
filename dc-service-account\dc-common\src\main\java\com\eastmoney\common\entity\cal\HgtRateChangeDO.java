package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

/**
 * <AUTHOR>
 * @create 2022/11/20
 */
public class HgtRateChangeDO extends BaseEntity {
    /**
     * 汇率波动提示，详见HgtRateChangeResultEnum
     */
    private Integer result;
    /**
     * 最新两日的汇率波动
     */
    private Double rate;
    /**
     * 清算日期
     */
    private Integer bizDate;

    public HgtRateChangeDO() {
    }

    public HgtRateChangeDO(Integer result, Integer bizDate) {
        this.result = result;
        this.bizDate = bizDate;
    }

    public HgtRateChangeDO(Integer result, Double rate, Integer bizDate) {
        this.result = result;
        this.rate = rate;
        this.bizDate = bizDate;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    @Override
    public Integer getBizDate() {
        return bizDate;
    }

    @Override
    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }
}
