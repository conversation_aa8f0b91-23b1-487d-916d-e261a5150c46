<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.accessor.mapper.sqlserver.OggLogAssetMapper">
    <select id="selectTransferAmt" resultType="as_logAsset">
        select fundid,fundeffect,digestid,sno from run.dbo.logasset t with (nolock, index(logasset_fundid_idx))
        <where>
            moneytype = '0'
            and digestid in(160021,160022,140055,140057,140710)
            <if test="fundId != null">
                and fundid = #{fundId}
            </if>
            <if test="bizDate != null and bizDate != 0">
                and bizdate = #{bizDate}
            </if>
        </where>
        order by sno
    </select>

    <select id="getRealTimeLogassetList" resultType="as_logAsset">
        select fundid,bizdate,digestid,matchtime,ordertime,sno,matchamt,matchqty,matchprice,matchamt,fundeffect,ltrim(rtrim(stkcode)) stkcode,market
        from run.dbo.logasset with (nolock, index(logasset_fundid_idx))
        <where>
            fundid = #{fundId}
            <choose>
                <when test="stkCodeList != null and stkCodeList.size() > 0">
                    AND ltrim(rtrim(stkcode)) IN
                    <foreach item="item" index="index" collection="stkCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="stkCode != null">
                        and ltrim(rtrim(stkcode)) = #{stkCode}
                    </if>
                </otherwise>
            </choose>
            <if test="market != null">
                and market = #{market}
            </if>
            <if test="digestIdList !=null">
                and digestid in
                <foreach collection="digestIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>