package com.eastmoney.service.service;

import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.accessor.service.MatchService;
import com.eastmoney.common.entity.HgtReferRate;
import com.eastmoney.common.entity.Match;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import com.eastmoney.common.util.ArithUtil;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.HgtReferRateService;
import com.eastmoney.service.cache.NodeConfigService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/26 10:40
 */
@Service("tProfitRealTimeService")
public class TProfitRealTimeServiceImpl implements TProfitService {
    @Autowired
    private MatchService matchService;

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private StockService stockService;

    @Autowired
    private HgtReferRateService hgtReferRateService;

    @Override
    public List<TProfitDayDO> getTProfitDayList(Map<String, Object> params) {
        List<TSecProfitDayDO> realTimeTProfitStockDayList = getTSecProfitDayList(params);
        if (CollectionUtils.isEmpty(realTimeTProfitStockDayList)) {
            return new ArrayList<>();
        }
        TProfitDayDO tProfitToday = new TProfitDayDO();
        tProfitToday.setBizDate(realTimeTProfitStockDayList.get(0).getBizDate());
        Double sumDayTProfit = 0.0;
        for (TSecProfitDayDO tSecProfitDayDO : realTimeTProfitStockDayList) {
            sumDayTProfit = ArithUtil.add(sumDayTProfit, tSecProfitDayDO.getTProfit());
        }
        tProfitToday.setTProfit(ArithUtil.round(sumDayTProfit, 2));
        return Lists.newArrayList(tProfitToday);
    }

    @Override
    public TProfitBO getTProfitSection(Map<String, Object> dealParams) {
        List<TSecProfitDayDO> realTimeTSecProfitDayList = getTSecProfitDayList(dealParams);
        if (CollectionUtils.isEmpty(realTimeTSecProfitDayList)) {
            return null;
        }
        TProfitBO tProfitBO = new TProfitBO();
        tProfitBO.setTotalTTimes((long) realTimeTSecProfitDayList.size());
        Double totalTProfit = 0.0;
        Long totalTSuccess = 0L;
        for (TSecProfitDayDO tSecProfitDayDO : realTimeTSecProfitDayList) {
            if (tSecProfitDayDO.getTProfit() > 0) {
                totalTSuccess++;
            }
            totalTProfit = ArithUtil.add(totalTProfit, tSecProfitDayDO.getTProfit());
        }
        tProfitBO.setTotalTSuccess(totalTSuccess);
        tProfitBO.setTotalTProfit(ArithUtil.round(totalTProfit, 2));
        return tProfitBO;
    }

    @Override
    public List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params) {
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        HgtReferRate sHHgtReferRate = hgtReferRateService.getOggHgtReferRateService(fundId, MarketEnum.SH_HK.getValue());
        HgtReferRate sZHgtReferRate = hgtReferRateService.getOggHgtReferRateService(fundId, MarketEnum.SZ_HK.getValue());
        List<Match> realTimeTMatchList = getTMatchList(params);
        //  拿到有买卖的成交记录
        Set<String> tBuyBsFlagSet = nodeConfigService.getTBuyBsFlagSet();
        Map<String, List<Match>> realTimeTMatchMap = getRealTimeTMatchMap(realTimeTMatchList, tBuyBsFlagSet);
        return realTimeTMatchMap.values().stream()
                .map(tMatchList -> calRealTimeTSecProfitDayDO(tMatchList, tBuyBsFlagSet, sHHgtReferRate, sZHgtReferRate))
                .sorted(Comparator.comparing(TSecProfitDayDO::getLastMatchTime).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<Match> getTMatchList(Map<String, Object> params) {
        // 需要查match
        params.put("bsFlagList", nodeConfigService.getTBuySellBsFlagSet());
        List<Match> realTimeTMatchList = matchService.getAllRealTimeMatchList(params);
        Set<String> tBuyBsFlagSet = nodeConfigService.getTBuyBsFlagSet();
        realTimeTMatchList.forEach(tMatch -> tMatch.setBsFlag(tBuyBsFlagSet.contains(tMatch.getBsFlag()) ? "B" : "S"));
        return realTimeTMatchList;
    }

    /**
     * 获取有买卖操作 做T的成交记录
     *
     * @param realTimeMatchList 实时成交list
     * @param tBuyBsFlagSet
     * @return 做T记录map key market-stkCode value 实时成交list
     */
    private Map<String, List<Match>> getRealTimeTMatchMap(List<Match> realTimeMatchList, Set<String> tBuyBsFlagSet) {
        Map<String, List<Match>> buyMatchMap = new HashMap<>();
        Map<String, List<Match>> sellMatchMap = new HashMap<>();
        for (Match match : realTimeMatchList) {
            String groupKey = StringUtils.join(match.getMarket(), "-", match.getStkCode());
            if (tBuyBsFlagSet.contains(match.getBsFlag())) {
                dealBuySellMatchMap(buyMatchMap, match, groupKey);
            } else {
                dealBuySellMatchMap(sellMatchMap, match, groupKey);
            }
        }
        return buyMatchMap.entrySet().stream()
                .filter(entry -> sellMatchMap.containsKey(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> {
                            List<Match> collectRes = new ArrayList<>(entry.getValue());
                            collectRes.addAll(sellMatchMap.get(entry.getKey()));
                            return collectRes;
                        }));
    }

    /**
     * 处理买卖 match map
     */
    private void dealBuySellMatchMap(Map<String, List<Match>> matchMap, Match match, String groupKey) {
        if (matchMap.containsKey(groupKey)) {
            List<Match> matches = matchMap.get(groupKey);
            matches.add(match);
        } else {
            List<Match> matches = new ArrayList<>();
            matches.add(match);
            matchMap.put(groupKey, matches);
        }
    }

    private TSecProfitDayDO calRealTimeTSecProfitDayDO(List<Match> tMatchList, Set<String> tBuyBsFlagSet, HgtReferRate sHHgtReferRate, HgtReferRate sZHgtReferRate) {
        TSecProfitDayDO tSecProfitDayDO = new TSecProfitDayDO();
        tSecProfitDayDO.setMarket(tMatchList.get(0).getMarket());
        tSecProfitDayDO.setStkCode(tMatchList.get(0).getStkCode());
        tSecProfitDayDO.setBizDate(tMatchList.get(0).getTrdDate());
        Long totalBuyQty = 0L;
        Double totalBuyAmt = 0.0d;
        Long totalSellQty = 0L;
        Double totalSellAmt = 0.0d;
        Long lastMatchTime = 0L;
        for (Match match : tMatchList) {
            if (match.getMatchTime() > lastMatchTime) {
                lastMatchTime = match.getMatchTime();
            }
            if (tBuyBsFlagSet.contains(match.getBsFlag())) {
                totalBuyQty = ArithUtil.addIgnoreNull(totalBuyQty, match.getMatchQty());
                totalBuyAmt = ArithUtil.add(totalBuyAmt, match.getMatchAmt());
                // 根据市场类型决定使用哪个汇率
            } else {
                totalSellQty = ArithUtil.addIgnoreNull(totalSellQty, match.getMatchQty());
                totalSellAmt = ArithUtil.add(totalSellAmt, match.getMatchAmt());
            }
        }
        // 保留3位小数
        Double avgBuyPrice = ArithUtil.div(totalBuyAmt, totalBuyQty);
        Double avgSellPrice = ArithUtil.div(totalSellAmt, totalSellQty);
        // 做T股数
        Long tMatchQty = Math.min(totalBuyQty, totalSellQty);
        // 做T差价
        Double tDifferences = ArithUtil.round(ArithUtil.sub(avgSellPrice, avgBuyPrice), 3);
        // 做T收益 收益结果保留2位小数
        Double tProfit = ArithUtil.round(ArithUtil.mul(tMatchQty, tDifferences), 2);
        if (MarketEnum.SH_HK.getValue().equals(tSecProfitDayDO.getMarket())) {
            tSecProfitDayDO.setTProfitHk(tProfit);
            Double tDifferencesRmb = ArithUtil.round(ArithUtil.sub(ArithUtil.mul(avgSellPrice, sHHgtReferRate.getSettRate()),
                    ArithUtil.mul(avgBuyPrice, sHHgtReferRate.getSaleSettRate())), 3);
            tProfit = ArithUtil.round(ArithUtil.mul(tMatchQty, tDifferencesRmb), 2);

        } else if (MarketEnum.SZ_HK.getValue().equals(tSecProfitDayDO.getMarket())) {
            tSecProfitDayDO.setTProfitHk(tProfit);
            Double tDifferencesRmb = ArithUtil.round(ArithUtil.sub(ArithUtil.mul(avgSellPrice, sZHgtReferRate.getSettRate()),
                    ArithUtil.mul(avgBuyPrice, sZHgtReferRate.getSaleSettRate())), 3);
            tProfit = ArithUtil.round(ArithUtil.mul(tMatchQty, tDifferencesRmb), 2);
        }
        //  换算成RMB结果
        tSecProfitDayDO.setTProfit(tProfit);
        tSecProfitDayDO.setTDifferences(tDifferences);
        tSecProfitDayDO.setTMatchQty(tMatchQty);
        tSecProfitDayDO.setLastMatchTime(lastMatchTime);
        tSecProfitDayDO.setStkName(stockService.getStkName(tSecProfitDayDO.getStkCode(), tSecProfitDayDO.getMarket()));
        tSecProfitDayDO.setHoldFlag("1");
        return tSecProfitDayDO;
    }

}
