package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiBdTradeBillYearMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.cal.dw.bill.BdTradeBillYearDO;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * @Project dc-service-account
 * @Description
 * <AUTHOR>
 * @Date 2023/11/17 14:13
 * @Version 1.0
 */
@Conditional(ZhfxDataSourceCondition.class)
@ZhfxDataSource("tidb")
@Service("bdTradeBillYearDao")
public class TiBdTradeBillYearDaoImpl extends BaseDao<TiBdTradeBillYearMapper, BdTradeBillYearDO, Long> implements BdTradeBillYearDao {

}
