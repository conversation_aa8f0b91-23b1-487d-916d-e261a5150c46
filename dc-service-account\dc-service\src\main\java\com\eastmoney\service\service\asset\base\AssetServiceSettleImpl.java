package com.eastmoney.service.service.asset.base;

import com.eastmoney.accessor.dao.oracle.AssetHisDao;
import com.eastmoney.common.entity.cal.AssetHis;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020/8/13-14:31.
 *
 * <AUTHOR>
 */
@Service("assetServiceSettle")
public class AssetServiceSettleImpl implements AssetService {
    @Autowired
    private AssetHisDao assetHisDao;

    /**
     * 根据bizDate日期的assetHis
     * 获取区间资产变动的asset, otcAsset, shiftInTotal, shiftOutTotal, otcNetTransfer, netTransfer, otherNetTransfer
     *
     * @param params fundId - 资金账号, bizDate - 开始日的业务日期
     * @return
     */
    @Override
    public AssetHis getAsset(Map<String, Object> params) {
        Map<String, Object> dbParams = new HashMap<>(2);
        dbParams.put("fundId", params.get("fundId"));
        dbParams.put("bizDate", params.get("bizDate"));
        List<AssetHis> assetHisList = assetHisDao.query(dbParams);
        if (CollectionUtils.isEmpty(assetHisList)) {
            return null;
        }
        return assetHisList.get(0);
    }
}
