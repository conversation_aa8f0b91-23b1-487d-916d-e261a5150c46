package com.eastmoney.accessor.enums;

/**
 * 盈亏日历枚举
 * <AUTHOR>
 * @create 2024/2/22
 */
public enum ProfitSectionStatEnum {
    M("M", "盈亏日历"),
    Y("Y", "盈亏月历"),
    YS("YS", "盈亏年历"),
    ;

    private final String type;
    private final String desc;

    ProfitSectionStatEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
