package com.eastmoney.service.service.profit.base;

import com.eastmoney.accessor.dao.oracle.AccountTemporaryDao;
import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.DayProfitBean;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.handler.ProfitHandler;
import com.eastmoney.service.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created on 2020/8/12-13:58.
 *
 * <AUTHOR>
 */
@Service("profitServiceTemp")
public class ProfitServiceTempImpl implements ProfitService {
    @Autowired
    private AccountTemporaryDao accountTemporaryDao;
    @Autowired
    private TradeDateDao tradeDateService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SecProfitServiceTempImpl secProfitServiceTemp;

    @Override
    public DayProfitBean getProfitDay(Map<String, Object> params) {
        return getTempDayProfit(params);
    }

    private DayProfitBean getTempDayProfit(Map<String, Object> params) {
        int today = Integer.parseInt(DateUtil.getCuryyyyMMdd());
        Integer time = CommonUtil.convert(DateUtil.getCurHHmmss(), Integer.class);
        int tempProfitCalTime = commonService.useTempProfitTime();
        Integer preMarketDay = Integer.valueOf(tradeDateService.getPreMarketDay(today));
        Integer accountBizDate = CommonUtil.convert(params.get("accountBizDate"), Integer.class);
        boolean todayIsMarket = tradeDateService.isMarket(today);
        //只有在交易日且前一交易日已经清算完成并且时间在设定的临时收益计算时间之前，不需要获取临时收益。
        if (time < tempProfitCalTime && todayIsMarket && preMarketDay.equals(accountBizDate)) {
            return null;
        }
        Long fundId = CommonUtil.convert(params.get("fundId"), Long.class);
        DayProfitBean dayProfitBeanFromDb = accountTemporaryDao.getDayProfitBean(fundId);
        if (dayProfitBeanFromDb != null && accountBizDate != null) {
            //如果临时表的计算日期大于离线数据的计算日期，需要把临时表的数据加上去
            if (dayProfitBeanFromDb.getBizDate() > accountBizDate) {
                return dayProfitBeanFromDb;
            }
        }
        return null;
    }
}
