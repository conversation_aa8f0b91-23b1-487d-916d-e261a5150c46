package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.common.entity.TradeDate;

import java.util.List;
import java.util.Map;

/**
 * Created by robin on 2016/6/24.
 * 跑批库中的交易日查询
 * <AUTHOR>
 *
 * update on 2016/07/19
 *
 * <AUTHOR>
 */
public interface TradeDateDao {

    /**
     * 查询今天是否交易日
     * @return
     */
    boolean todayIsMarket();
    boolean isMarket(Integer bizdate);

    /**
     * 取得上一个交易日
     * @return
     */
    String getPreMarketDay(Integer bizdate);

    /**
     * 取得下一个交易日
     * @param bizdate
     * @return
     */
    int getNextMarketDay(Integer bizdate);

    /**
     * 获取交易时间
     * @param params
     * @return
     */
    List<TradeDate> getTradeDateList(Map<String, Object> params);

    /**
     * 获取账户分析起始日至今交易时间
     * @param params
     * @return
     */
    List<TradeDate> getAllTradeDateList(Map<String, Object> params);

    /**
     * 获取每个月最后一个交易日
     * @param startDate
     * @param endDate
     * @return
     */
    List<TradeDate> getLastTradeDateOfMonth(String startDate, String endDate);

    /**
     * 获取指定日期最近交易日
     * @param specifyDate
     */
    Integer getLatestTrdDateOfSpecifyDate(Integer specifyDate);
}
