package com.eastmoney.accessor.dao.oracle;

import com.eastmoney.accessor.aspect.ZhfxDataSourceCondition;
import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.oracle.OraFundAssetMapper;
import com.eastmoney.common.annotation.ZhfxDataSource;
import com.eastmoney.common.entity.FundAsset;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * Created by sunyuncai on 2017/2/8
 */
@ZhfxDataSource
@Conditional(ZhfxDataSourceCondition.class)
@Service("fundAssetDao")
public class OraFundAssetDaoImpl extends BaseDao<OraFundAssetMapper, FundAsset, Long> implements FundAssetDao {

}
