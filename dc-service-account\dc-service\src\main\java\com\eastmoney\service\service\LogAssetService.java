package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.oracle.LogAssetDao;
import com.eastmoney.common.entity.LogAsset;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created on 2023/11/22
 * Description
 *
 * <AUTHOR>
 */
@Service("logAssetService")
public class LogAssetService {

    @Autowired
    LogAssetDao logAssetDao;

    /**
     * 线上经纪人获取交割单的交易额和佣金
     * @param params
     * @return
     */
    public List<Object> getLogAssetSum(Map<String, Object> params){
        return logAssetDao.getLogAssetSum(params);
    }

    public List<LogAsset> getStkTradeList(Map<String, Object> params){
        return logAssetDao.getStkTradeList(params);
    }

    public List<LogAsset> getStkShiftList(Map<String, Object> params){
        return logAssetDao.getStkShiftList(params);
    }

    /**
     * 获取区间资产变动-其他流入
     * @param params
     * @return
     */
    public List<LogAsset> getOtherDetailList(Map<String, Object> params){
        return logAssetDao.getOtherDetailList(params);
    }

    /**
     * 获取positionsharechange 对应的交割单
     * @param params
     * @return
     */
    public List<LogAsset> getTradeShiftList(Map<String, Object> params){
        return logAssetDao.getTradeShiftList(params);
    }

    /**
     * 获取positionsharechange 对应的交割单(仅持仓)
     * @param params
     * @return
     */
    public List<LogAsset> getHoldTradeShiftList(Map<String, Object> params){
            return logAssetDao.getHoldTradeShiftList(params);
    }

    /**
     * 获取买卖交易记录
     * @param params
     * @return
     */
    public List<LogAsset> getBSTradeList(Map<String, Object> params){
        return logAssetDao.getBSTradeList(params);
    }

}
