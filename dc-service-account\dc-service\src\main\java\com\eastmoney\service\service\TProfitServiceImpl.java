package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.tidb.TProfitDayDao;
import com.eastmoney.accessor.dao.tidb.TSecProfitDayDao;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import com.eastmoney.common.entity.cal.tProfitRank;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.HoldPositionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 16:32
 */
@Service("tProfitService")
public class TProfitServiceImpl implements TProfitService {
    @Autowired
    private TProfitDayDao tProfitDayDao;

    @Autowired
    private TSecProfitDayDao tSecProfitDayDao;

    @Autowired
    private StockService stockService;

    @Autowired
    private HoldPositionService holdPositionService;

    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    @Override
    public List<TProfitDayDO> getTProfitDayList(Map<String, Object> params) {
        String stkCode = CommonUtil.convert(params, "stkCode", String.class);
        return StringUtils.isEmpty(stkCode) ?
                tProfitDayDao.getTProfitDayList(params) : tSecProfitDayDao.getTProfitDayList(params);
    }

    @Override
    @RedisCache(keyGenerator = "'jzjy_tprofitsection_' + #params.get('fundId') + '_' + #params.get('unit')")
    public TProfitBO getTProfitSection(Map<String, Object> params) {
        return tProfitDayDao.getTProfitSection(params);
    }

    @Override
    public List<TSecProfitDayDO> getTSecProfitDayList(Map<String, Object> params) {
        List<TSecProfitDayDO> tSecProfitDayList = tSecProfitDayDao.getTSecProfitDayList(params);
        setStkNameAndHoldFlag(params, tSecProfitDayList);
        return tSecProfitDayList;
    }

    @Override
    public List<tProfitRank> getSecTProfitRankList(Map<String, Object> params) {
        List<tProfitRank> tProfitRankList = tSecProfitDayDao.getSecTProfitRankList(params);
        tProfitRankList.forEach(tProfitStock ->
                tProfitStock.setStkName(stockService.getStkName(tProfitStock.getStkCode(), tProfitStock.getMarket())));
        return tProfitRankList;
    }

    @Override
    public List<TSecProfitDayDO> getTSecProfitDayListPC(Map<String, Object> params) {
        List<TSecProfitDayDO> tSecProfitDayList = tSecProfitDayDao.getTSecProfitDayListPC(params);
        setStkNameAndHoldFlag(params, tSecProfitDayList);
        return tSecProfitDayList;
    }

    @Override
    public TProfitBO getSecTProfitSection(Map<String, Object> params) {
        return tSecProfitDayDao.getSecTProfitSection(params);
    }

    private void setStkNameAndHoldFlag(Map<String, Object> params, List<TSecProfitDayDO> tSecProfitDayList) {
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        Map<String, Integer> holdPositionMap = holdPositionService.getHoldPositionStartDate(fundId);
        for (TSecProfitDayDO tSecProfitDayDO : tSecProfitDayList) {
            String stkName = stockService.getStkName(tSecProfitDayDO.getStkCode(), tSecProfitDayDO.getMarket());
            tSecProfitDayDO.setStkName(stkName);

            String key = StringUtils.joinWith("-", tSecProfitDayDO.getMarket(), tSecProfitDayDO.getStkCode());
            if(holdPositionMap.containsKey(key)) {
                tSecProfitDayDO.setHoldFlag("1");
                continue;
            }

            //830 920转换   上面判断出持仓就无需转换了
            String alterCode = bseCodeAlterService.getCodeAlterOrReverse(tSecProfitDayDO.getStkCode(), tSecProfitDayDO.getMarket());
            String alterKey = StringUtils.joinWith("-", tSecProfitDayDO.getMarket(), alterCode);
            tSecProfitDayDO.setHoldFlag(holdPositionMap.containsKey(alterKey) ? "1" : "0");
        }
    }

    @Override
    public TProfitBO getTProfitCustomizeSection(Map<String, Object> params) {
        return tProfitDayDao.getTProfitSection(params);
    }
}
