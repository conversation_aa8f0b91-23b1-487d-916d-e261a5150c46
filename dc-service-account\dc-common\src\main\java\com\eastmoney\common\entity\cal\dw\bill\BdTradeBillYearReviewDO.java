package com.eastmoney.common.entity.cal.dw.bill;

import com.alibaba.fastjson.annotation.JSONField;
import com.eastmoney.common.serializer.BigDecimalToStringSerializer;

import java.math.BigDecimal;

/**
 * @Description 行情回顾页
 * <AUTHOR>
 * @Date 2025/5/30 8:59
 */

public class BdTradeBillYearReviewDO {

    private Long indexKey;

    // 市场指数表现
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal szzsIndexChg;      // 上证指数涨幅
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal cybzIndexChg;      // 创业板指涨幅
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal kc50IndexChg;      // 科创50涨幅
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal bz50IndexChg;      // 北证50涨幅

    // 行业表现
    private Long publishCnt;        // 一级行业个数
    private Long publishRiseCnt;        // 一级行业上涨个数
    private String top1PublishName;   // TOP1行业名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal top1PublishChg;    // TOP1行业涨幅
    private String top2PublishName;   // TOP2行业名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal top2PublishChg;    // TOP2行业涨幅
    private String top3PublishName;   // TOP3行业名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal top3PublishChg;    // TOP3行业涨幅


    // 个股表现
    private Long stockRiseCnt;        // 总上涨股票数量
    private Long stockFallCnt;        // 总下跌股票数量
    private String riseTop1StkName;   // 今年涨幅最大股票名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal riseTop1StkChg;    // 今年涨幅最大股票涨幅
    private String listingTop1StkName; // 上市涨幅最大股票名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal listingTop1StkChg; // 上市涨幅最大股票涨幅
    private String consecutiveStkName; // 连涨天数最多股票名称
    @JSONField(serializeUsing = BigDecimalToStringSerializer.class)
    private BigDecimal consecutiveStkChg; // 连涨天数最多股票涨幅
    private Long consecutiveDays;     // 连涨天数最多股票天数
    private Long limitUpDays;         // 连涨天数最多股票涨停板

    // 用户行为数据
    private String optionalStkName;   // 最多自选股票名称

    private Long optionalStkCnt;       // 最多自选股票数量

    public Long getIndexKey() {
        return indexKey;
    }

    public void setIndexKey(Long indexKey) {
        this.indexKey = indexKey;
    }

    public BigDecimal getSzzsIndexChg() {
        return szzsIndexChg;
    }

    public void setSzzsIndexChg(BigDecimal szzsIndexChg) {
        this.szzsIndexChg = szzsIndexChg;
    }

    public BigDecimal getCybzIndexChg() {
        return cybzIndexChg;
    }

    public void setCybzIndexChg(BigDecimal cybzIndexChg) {
        this.cybzIndexChg = cybzIndexChg;
    }

    public BigDecimal getKc50IndexChg() {
        return kc50IndexChg;
    }

    public void setKc50IndexChg(BigDecimal kc50IndexChg) {
        this.kc50IndexChg = kc50IndexChg;
    }

    public BigDecimal getBz50IndexChg() {
        return bz50IndexChg;
    }

    public void setBz50IndexChg(BigDecimal bz50IndexChg) {
        this.bz50IndexChg = bz50IndexChg;
    }

    public Long getPublishCnt() {
        return publishCnt;
    }

    public void setPublishCnt(Long publishCnt) {
        this.publishCnt = publishCnt;
    }

    public Long getPublishRiseCnt() {
        return publishRiseCnt;
    }

    public void setPublishRiseCnt(Long publishRiseCnt) {
        this.publishRiseCnt = publishRiseCnt;
    }

    public String getTop1PublishName() {
        return top1PublishName;
    }

    public void setTop1PublishName(String top1PublishName) {
        this.top1PublishName = top1PublishName;
    }

    public BigDecimal getTop1PublishChg() {
        return top1PublishChg;
    }

    public void setTop1PublishChg(BigDecimal top1PublishChg) {
        this.top1PublishChg = top1PublishChg;
    }

    public String getTop2PublishName() {
        return top2PublishName;
    }

    public void setTop2PublishName(String top2PublishName) {
        this.top2PublishName = top2PublishName;
    }

    public BigDecimal getTop2PublishChg() {
        return top2PublishChg;
    }

    public void setTop2PublishChg(BigDecimal top2PublishChg) {
        this.top2PublishChg = top2PublishChg;
    }

    public String getTop3PublishName() {
        return top3PublishName;
    }

    public void setTop3PublishName(String top3PublishName) {
        this.top3PublishName = top3PublishName;
    }

    public BigDecimal getTop3PublishChg() {
        return top3PublishChg;
    }

    public void setTop3PublishChg(BigDecimal top3PublishChg) {
        this.top3PublishChg = top3PublishChg;
    }

    public Long getStockRiseCnt() {
        return stockRiseCnt;
    }

    public void setStockRiseCnt(Long stockRiseCnt) {
        this.stockRiseCnt = stockRiseCnt;
    }

    public Long getStockFallCnt() {
        return stockFallCnt;
    }

    public void setStockFallCnt(Long stockFallCnt) {
        this.stockFallCnt = stockFallCnt;
    }

    public String getRiseTop1StkName() {
        return riseTop1StkName;
    }

    public void setRiseTop1StkName(String riseTop1StkName) {
        this.riseTop1StkName = riseTop1StkName;
    }

    public BigDecimal getRiseTop1StkChg() {
        return riseTop1StkChg;
    }

    public void setRiseTop1StkChg(BigDecimal riseTop1StkChg) {
        this.riseTop1StkChg = riseTop1StkChg;
    }

    public String getListingTop1StkName() {
        return listingTop1StkName;
    }

    public void setListingTop1StkName(String listingTop1StkName) {
        this.listingTop1StkName = listingTop1StkName;
    }

    public BigDecimal getListingTop1StkChg() {
        return listingTop1StkChg;
    }

    public void setListingTop1StkChg(BigDecimal listingTop1StkChg) {
        this.listingTop1StkChg = listingTop1StkChg;
    }

    public String getConsecutiveStkName() {
        return consecutiveStkName;
    }

    public void setConsecutiveStkName(String consecutiveStkName) {
        this.consecutiveStkName = consecutiveStkName;
    }

    public BigDecimal getConsecutiveStkChg() {
        return consecutiveStkChg;
    }

    public void setConsecutiveStkChg(BigDecimal consecutiveStkChg) {
        this.consecutiveStkChg = consecutiveStkChg;
    }

    public Long getConsecutiveDays() {
        return consecutiveDays;
    }

    public void setConsecutiveDays(Long consecutiveDays) {
        this.consecutiveDays = consecutiveDays;
    }

    public Long getLimitUpDays() {
        return limitUpDays;
    }

    public void setLimitUpDays(Long limitUpDays) {
        this.limitUpDays = limitUpDays;
    }

    public String getOptionalStkName() {
        return optionalStkName;
    }

    public void setOptionalStkName(String optionalStkName) {
        this.optionalStkName = optionalStkName;
    }

    public Long getOptionalStkCnt() {
        return optionalStkCnt;
    }

    public void setOptionalStkCnt(Long optionalStkCnt) {
        this.optionalStkCnt = optionalStkCnt;
    }
}


