package com.eastmoney.service.service.profit.section.base;


import com.eastmoney.accessor.dao.oracle.TradeDateDao;
import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.ProfitRateSection;
import com.eastmoney.common.entity.cal.ProfitSection;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.service.cache.ProfitSectionCacheService;
import com.eastmoney.service.handler.FundAssetHandler;
import com.eastmoney.service.service.profit.section.ProfitSectionService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created on 2020/8/12-15:38.
 *
 * <AUTHOR>
 */
@Service("profitSectionServiceSettle")
public class ProfitSectionServiceSettleImpl implements ProfitSectionService {
    private static final String CAL_PROFIT_RATE = "calProfitRate";
    private static final Logger LOG = LoggerFactory.getLogger(FundAssetHandler.class);
    @Autowired
    TradeDateDao tradeDateDao;
    @Autowired
    private ProfitSectionCacheService profitSectionCacheService;

    /**
     * 查询各区间清算收益
     *
     * @param params fundId, startDate, unit
     * @return
     */
    @Override
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        ProfitSection profitSection = profitSectionCacheService.getProfitSection(params);
        if (profitSection == null) {
            LOG.warn(params.get("fundId") + ":不存在收益片表数据");
            return null;
        }

        profitSection.setBizDate(tradeDateDao.getNextMarketDay(profitSection.getBakBizDate()));
        ProfitRateSection profitRateSection = null;
        if (BooleanUtils.isTrue(CommonUtil.convert(params.get(CAL_PROFIT_RATE), Boolean.class))) {
            profitRateSection = getProfitRateSection(params);
            if (profitRateSection != null) {
                profitRateSection.setBizDate(tradeDateDao.getNextMarketDay(profitRateSection.getBakBizDate()));
            }

        }
        return build(profitSection, profitRateSection);
    }

    private SectionProfitBean build(ProfitSection profitSection, ProfitRateSection profitRateSection) {
        SectionProfitBean sectionProfitBean = new SectionProfitBean();
        sectionProfitBean.setFundId(profitSection.getFundId());
        sectionProfitBean.setEuTime(profitSection.getEuTime());
        sectionProfitBean.setProfit(profitSection.getProfit());
        sectionProfitBean.setIndexDate(profitSection.getIndexDate());
        sectionProfitBean.setBizDate(profitSection.getBizDate());
        if (profitRateSection != null) {
            sectionProfitBean.setBizDate(profitRateSection.getBizDate());
            sectionProfitBean.setProfitRate(profitRateSection.getProfitRate());
            int profitRateSectionNextTradeDay = tradeDateDao.getNextMarketDay(profitRateSection.getBizDate());
            if (profitSection.getBizDate().equals(profitRateSectionNextTradeDay)) {
                sectionProfitBean.setProfitRateCalWindow(true);
            }
        }
        return sectionProfitBean;
    }

    private ProfitRateSection getProfitRateSection(Map<String, Object> params) {
        return profitSectionCacheService.getProfitRateSection(params);
    }
}