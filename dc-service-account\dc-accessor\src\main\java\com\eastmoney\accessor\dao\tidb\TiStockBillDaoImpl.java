package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.dao.oracle.StockBillDao;
import com.eastmoney.accessor.mapper.tidb.TiStockBillMapper;
import com.eastmoney.common.entity.cal.StockBill;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/31.
 */
@Service("stockBillDao")
public class TiStockBillDaoImpl extends BaseDao<TiStockBillMapper,StockBill,Long> implements StockBillDao {
    @Override
    public List<StockBill> queryEarnLossMostStock(Map<String, Object> params) {
        return mapper.queryEarnLossMostStock(params);
    }
}
