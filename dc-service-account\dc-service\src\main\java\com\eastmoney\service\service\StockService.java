package com.eastmoney.service.service;

import com.eastmoney.accessor.dao.tidb.ChoiceStockDao;
import com.eastmoney.accessor.dao.tidb.StockDao;
import com.eastmoney.accessor.enums.ChoiceMarketCodeEnum;
import com.eastmoney.common.entity.ChoiceStock;
import com.eastmoney.common.entity.Stock;
import com.eastmoney.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 *
 * <AUTHOR>
 * @create 2023/2/3
 */
@Service("stockService")
public class StockService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockService.class);

    /**
     * stkCode-market -> stock
     */
    private final ConcurrentHashMap<String, Stock> allStockMap = new ConcurrentHashMap<>();

    @Autowired
    private ChoiceStockDao choiceStockDao;

    @Autowired
    private StockDao stockDao;

    public String getStkName(String stkCode, String market) {
        return getStockAttr(stkCode, market, Stock::getStkName);
    }

    public String getExpandNameAbbr(String stkCode, String market) {
        return getStockAttr(stkCode, market, Stock::getExpandNameAbbr);
    }

    public String getStkType(String stkCode, String market) {
        return getStockAttr(stkCode, market, Stock::getStkType);
    }

    /**
     * 获取股转分层数据
     *
     * @param stkCode
     * @param market
     * @return
     */
    public String getStkFc(String stkCode, String market) {
        return getStockAttr(stkCode, market, Stock::getStkFc);
    }

    public String getSpellId(String stkCode, String market) {
        return getStockAttr(stkCode, market, Stock::getSpellId);
    }

    private String getStockAttr(String stkCode, String market, Function<Stock, String> function) {
        Objects.requireNonNull(stkCode, "stkCode不能为null");
        Objects.requireNonNull(market, "market不能为null");
        Stock stock = allStockMap.get(StringUtils.trim(stkCode) + "-" + market);
        if (stock == null) {
            LOGGER.warn("缓存中不存在stkCode={}market={},取数据库数据更新缓存", stkCode, market);
            stock = getStock(stkCode, market);
            if (stock == null) {
                LOGGER.warn("-----------------------------柜台库码表不存在stkCode={},market={}", stkCode, market);
                return null;
            }
            allStockMap.put(StringUtils.trim(stock.getStkCode()) + "-" + stock.getMarket(), stock);
        }
        return StringUtils.trim(function.apply(stock));
    }

    public Stock getStock(String stkCode, String market) {
        return stockDao.getStock(StringUtils.trim(stkCode), market);
    }

    @PostConstruct
    @Scheduled(cron = "0 0 6 * * ?")
    public void loadStockMap() {
        LOGGER.info("------------------------正在加载码表信息，请等待-------------------------{}", DateUtil.getCurDateTime());
        doLoadStockMap();
        LOGGER.info("------------------------码表信息加载完成，总计={}-------------------------{}", allStockMap.size(),
                DateUtil.getCurDateTime());
    }

    private void doLoadStockMap() {
        // 先加载choice码表 securityCode股票代码, tradeMarketCode交易市场, securityShortName股票名称
        List<ChoiceStock> choiceStockList = choiceStockDao.queryAllStock();
        long choiceCount = choiceStockList.stream()
                .filter(choiceStock -> {
                    String tradeMarketCode = choiceStock.getTradeMarketCode();
                    if (tradeMarketCode == null) {
                        return false;
                    }
                    if (tradeMarketCode.length() > 9) {
                        if (!ChoiceMarketCodeEnum.containsChoiceMarket(tradeMarketCode.substring(0, 9))) {
                            return false;
                        }
                    } else {
                        if (!ChoiceMarketCodeEnum.containsChoiceMarket(tradeMarketCode)) {
                            return false;
                        }
                    }
                    return choiceStock.getSecurityCode() != null && choiceStock.getSecurityShortName() != null;
                })
                .map(choiceStock -> {
                    Stock stock = new Stock();
                    stock.setStkCode(StringUtils.trim(choiceStock.getSecurityCode()));
                    stock.setStkName(choiceStock.getSecurityShortName());
                    stock.setExpandNameAbbr(choiceStock.getExpandNameAbbr());
                    String tradeMarketCode = choiceStock.getTradeMarketCode();
                    if (choiceStock.getTradeMarketCode().length() > 9) {
                        tradeMarketCode = choiceStock.getTradeMarketCode().substring(0, 9);
                    }
                    stock.setMarket(ChoiceMarketCodeEnum.getMarketByChoice(tradeMarketCode));
                    stock.setSpellId(choiceStock.getSecurityPinyin());
                    return stock;
                })
                .peek(stock -> {
                    allStockMap.put(StringUtils.trim(stock.getStkCode()) + "-" + stock.getMarket().trim(), stock);

                    //对于市场为B的北交所股票,添加一条市场为6的代码信息
                    if (ChoiceMarketCodeEnum.BJ_A.getMarket().equals(stock.getMarket().trim())) {
                        try {
                            Stock stk = stock.clone();
                            stk.setMarket(ChoiceMarketCodeEnum.STOCK_A.getMarket());
                            allStockMap.put(StringUtils.trim(stk.getStkCode()) + "-" + stk.getMarket().trim(), stk);
                        } catch (CloneNotSupportedException e) {
                            LOGGER.error("stock:{},market:{}->码表信息深拷贝异常", stock.getStkCode(), stock.getMarket(), e);
                        }
                    }
                })
                .count();
        LOGGER.info("------------------------choice码表信息加载完成，总计={}，date={}", choiceCount, DateUtil.getCurDateTime());
        // 加载账户分析交易码表
        // stkcode, market, stkname, stktype
        List<Stock> stockList = stockDao.queryAllStock();
        for (Stock stock : stockList) {
            String market = stock.getMarket().trim();
            String stkCode = StringUtils.trim(stock.getStkCode());
            allStockMap.put(stkCode + "-" + market, stock);
            if ("5".equals(market)) {
                allStockMap.put(stkCode + "-" + "S", stock);
            } else if ("S".equals(market)) {
                allStockMap.put(stkCode + "-" + "5", stock);
            }
        }
        LOGGER.info("------------------------交易码表信息加载完成，总计={}，date={}", stockList.size(), DateUtil.getCurDateTime());
    }

    public Map<String, Stock> getAllStock() {
        return allStockMap;
    }

}
