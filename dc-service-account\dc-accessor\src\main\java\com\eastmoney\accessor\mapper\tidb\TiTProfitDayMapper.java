package com.eastmoney.accessor.mapper.tidb;

import com.eastmoney.accessor.mapper.BaseMapper;
import com.eastmoney.common.entity.cal.TProfitBO;
import com.eastmoney.common.entity.cal.TProfitDayDO;
import com.eastmoney.common.entity.cal.TSecProfitDayDO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/30 11:11
 */
@Repository
public interface TiTProfitDayMapper extends BaseMapper<TSecProfitDayDO, Long> {

    List<TProfitDayDO> getTProfitDayList(Map<String, Object> params);

    TProfitBO getTProfitSection(Map<String, Object> dealParams);
}
