package com.eastmoney.service.service.profit.section.concretesection;

import com.eastmoney.common.entity.SectionProfitBean;
import com.eastmoney.common.entity.cal.AssetNew;
import com.eastmoney.common.util.CommonUtil;
import com.eastmoney.common.util.DateUtil;
import com.eastmoney.service.service.profit.section.AbstractProfitSectionService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 自定义区间收益
 *
 * <AUTHOR>
 * @date 2021/7/28
 */
@Service("profitSectionServiceCustom")
public class ProfitSectionServiceCustomImpl extends AbstractProfitSectionService {

    @Override
    public SectionProfitBean getProfitSection(Map<String, Object> params) {
        AssetNew assetNew = assetNewService.getAssetInfo(params);
        if (assetNew == null) {
            return null;
        }

        return calProfitSection(params, assetNew);
    }


    /**
     * 计算自定义区间收益额和区间收益率
     *
     * @param params
     * @param assetNew
     * @return
     */
    public SectionProfitBean calProfitSection(Map<String, Object> params, AssetNew assetNew) {
        SectionProfitBean profitSectionRealTime = new SectionProfitBean();
        SectionProfitBean profitSectionSettle = new SectionProfitBean();

        Integer startDate = CommonUtil.convert(params, "startDate", Integer.class);
        Integer endDate = CommonUtil.convert(params, "endDate", Integer.class);
        Integer today = DateUtil.getCuryyyyMMddInteger();

        //确定startDate
        startDate = startDate >= assetNew.getStartDate() ? startDate : assetNew.getStartDate();

        //确定endDate
        endDate = endDate <= today ? endDate : today;

        if (endDate > assetNew.getBizDate()) {
            if (tradeDateDao.isMarket(endDate) || !Integer.valueOf(tradeDateDao.getPreMarketDay(endDate)).equals(assetNew.getBizDate())) {
                params.put("accountBizDate", assetNew.getBizDate());
                params.put("isProfitRateCalWindow", false);
                //计算当日实时收益
                profitSectionRealTime = profitSectionServiceRealTime.getProfitSection(params);
            }
        }

        //计算 startDate ~ bizDate 区间收益
        Long fundId = CommonUtil.convert(params, "fundId", Long.class);
        Double profitSection = profitDayDao.getSumProfit(fundId, startDate, endDate);
        Double profitRateSection = profitRateDayDao.getSectionProfitRate(fundId, startDate, endDate);
        profitSectionSettle.setFundId(fundId);
        profitSectionSettle.setProfit(profitSection);
        profitSectionSettle.setProfitRate(profitRateSection);
        profitSectionSettle.setBizDate(endDate);

        Date closeDate = DateUtil.strToDate(endDate + "153000", DateUtil.yyyyMMddHHmmss);
        Date now = new Date();
        profitSectionSettle.setEuTime(DateUtil.compareDate(now, closeDate) < 0 ? now : closeDate);

        return buildProfitSection(startDate, profitSectionSettle, profitSectionRealTime);
    }

    @Override
    protected void setSectionParams(Map<String, Object> params, AssetNew assetNew, SectionProfitBean sectionProfitSettle) {
    }
}
