package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntity;

/**
 * <AUTHOR>
 * @description t_profit_stock_day 做T个股收益表DO
 * @date 2024/4/29 17:25
 */
public class TSecProfitDayDO extends BaseEntity {

    /**
     * 资金账号
     */
    private Long fundId;
    /**
     * 股票代码
     */
    private String stkCode;

    /**
     * 市场
     */
    private String market;

    /**
     * 做T盈亏 （人民币）
     */
    private Double tProfit;

    /**
     * 做T盈亏 港币 仅仅港币有
     */
    private Double tProfitHk;

    /**
     * 做T差价
     */
    private Double tDifferences;

    /**
     * 做T差价 (港币 - 仅针对港股)
     */
    private Double tDifferencesHk;

    /**
     * 做T股数
     */
    private Long tMatchQty;

    /**
     * 买入均价
     */
    private Double avgBuyPrice;

    /**
     * 买入均价 (港币 - 仅针对港股)
     */
    private Double avgBuyPriceHk;

    /**
     * 卖出均价
     */
    private Double avgSellPrice;

    /**
     * 卖出均价 (港币 - 仅针对港股)
     */
    private Double avgSellPriceHk;

    private String stkName;

    private String holdFlag;

    private String moneyType;

    /**
     * 转换代码  830 -> 920
     * @return
     */
    private String corResCode;

    public String getMoneyType() {
        return moneyType;
    }

    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    public String getHoldFlag() {
        return holdFlag;
    }

    public void setHoldFlag(String holdFlag) {
        this.holdFlag = holdFlag;
    }

    /**
     * 最近一次成交时间
     */
    private Long lastMatchTime;

    public Long getLastMatchTime() {
        return lastMatchTime;
    }

    public void setLastMatchTime(Long lastMatchTime) {
        this.lastMatchTime = lastMatchTime;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public Double getTProfit() {
        return tProfit;
    }

    public void setTProfit(Double tProfit) {
        this.tProfit = tProfit;
    }

    public Double getTDifferences() {
        return tDifferences;
    }

    public void setTDifferences(Double tDifferences) {
        this.tDifferences = tDifferences;
    }

    public Double getTDifferencesHk() {
        return tDifferencesHk;
    }

    public void setTDifferencesHk(Double tDifferencesHk) {
        this.tDifferencesHk = tDifferencesHk;
    }

    public Long getTMatchQty() {
        return tMatchQty;
    }

    public void setTMatchQty(Long tMatchQty) {
        this.tMatchQty = tMatchQty;
    }

    public Double getAvgBuyPrice() {
        return avgBuyPrice;
    }

    public void setAvgBuyPrice(Double avgBuyPrice) {
        this.avgBuyPrice = avgBuyPrice;
    }

    public Double getAvgBuyPriceHk() {
        return avgBuyPriceHk;
    }

    public void setAvgBuyPriceHk(Double avgBuyPriceHk) {
        this.avgBuyPriceHk = avgBuyPriceHk;
    }

    public Double getAvgSellPrice() {
        return avgSellPrice;
    }

    public void setAvgSellPrice(Double avgSellPrice) {
        this.avgSellPrice = avgSellPrice;
    }

    public Double getAvgSellPriceHk() {
        return avgSellPriceHk;
    }

    public void setAvgSellPriceHk(Double avgSellPriceHk) {
        this.avgSellPriceHk = avgSellPriceHk;
    }

    public Double getTProfitHk() {
        return tProfitHk;
    }

    public void setTProfitHk(Double tProfitHk) {
        this.tProfitHk = tProfitHk;
    }

    public String getCorResCode() {
        return corResCode;
    }

    public void setCorResCode(String corResCode) {
        this.corResCode = corResCode;
    }
}
