package com.eastmoney.accessor.dao.tidb;

import com.eastmoney.accessor.dao.BaseDao;
import com.eastmoney.accessor.mapper.tidb.TiTemporarySecProfitDayMapper;
import com.eastmoney.common.entity.cal.SecProfitDayDO;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 盘后临时个股日收益明细Dao
 * <AUTHOR>
 * @create 2024/3/7
 */
@Service("secTemporaryProfitDayDao")
public class SecTemporaryProfitDayDaoImpl extends BaseDao<TiTemporarySecProfitDayMapper, SecProfitDayDO, Integer> implements SecTemporaryProfitDayDao {

    @Override
    public List<SecProfitDayDO> getSecTemporaryProfitDay(Long fundId) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        return getMapper().getSecTemporaryProfitDay(params);
    }

    @Override
    public List<SecProfitDayDO> getSecTemporaryProfitDay(Long fundId, List<String> markets) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("markets", markets);
        return getMapper().getSecTemporaryProfitDay(params);
    }
}
