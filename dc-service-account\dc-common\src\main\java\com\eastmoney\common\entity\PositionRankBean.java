package com.eastmoney.common.entity;

/**
 * 持仓盈亏排名
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/4.
 */
public class PositionRankBean {
    private Long fundId;//资金帐号
    private String stkCode;//证券代码
    private Double profitTotal;//盈亏总金额
    private Integer rank;//排名
    private String profitFlag;
    private int holdDay;

    private String market;
    private String stkName;//证券名称

    public String getProfitFlag() {
        return profitFlag;
    }

    public void setProfitFlag(String profitFlag) {
        this.profitFlag = profitFlag;
    }

    public int getHoldDay() {
        return holdDay;
    }

    public void setHoldDay(int holdDay) {
        this.holdDay = holdDay;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public Long getFundId() {
        return fundId;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public String getStkCode() {
        return stkCode;
    }

    public void setStkCode(String stkCode) {
        this.stkCode = stkCode;
    }

    public Double getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(Double profitTotal) {
        this.profitTotal = profitTotal;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getStkName() {
        return stkName;
    }

    public void setStkName(String stkName) {
        this.stkName = stkName;
    }
}
