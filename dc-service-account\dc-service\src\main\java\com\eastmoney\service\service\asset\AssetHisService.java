package com.eastmoney.service.service.asset;

import com.eastmoney.accessor.dao.oracle.AssetHisDao;
import com.eastmoney.accessor.enums.MarketEnum;
import com.eastmoney.common.entity.AssetDay;
import com.eastmoney.common.entity.BseCodeAlterDO;
import com.eastmoney.common.entity.DayProfitExtend;
import com.eastmoney.common.entity.StkAsset;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.service.quote.BseCodeAlterService;
import com.eastmoney.service.service.stkasset.StkAssetService;
import com.eastmoney.service.util.BusinessUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 历史资产Service
 *
 * <AUTHOR>
 * @create 2024/2/21
 */
@Service("assetHisService")
public class AssetHisService {
    @Autowired
    private AssetHisDao assetHisDao;

    @Autowired
    private StkAssetService stkAssetService;

    @Autowired
    private NodeConfigService nodeConfigService;

    @Autowired
    private BseCodeAlterService bseCodeAlterService;

    /**
     * 查询指定区间每一天的资产
     *
     * @param fundId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<AssetDay> getAssetDayTrend(Long fundId, Integer startDate, Integer endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return assetHisDao.getAssetDayTrend(params)
                .stream()
                .map(AssetDay::of)
                .sorted(Comparator.comparing(AssetDay::getBizDate))
                .collect(Collectors.toList());
    }

    /**
     * 查询每个月最后一天的资产
     *
     * @param fundId
     * @param tradeDateList
     * @return
     */
    public List<AssetDay> getMonthAssetDayTrend(Long fundId, List<Integer> tradeDateList) {
        Map<String, Object> params = new HashMap<>();
        params.put("fundId", fundId);
        params.put("tradeDateList", tradeDateList);
        return assetHisDao.getMonthAssetDayTrend(params)
                .stream()
                .map(AssetDay::of)
                .sorted(Comparator.comparing(AssetDay::getBizDate))
                .collect(Collectors.toList());
    }

    /**
     * 根据条件获取持仓列表
     *
     * @param fundId
     * @param bizDate
     * @param serverId
     * @return
     */
    public Map<String, Double> getOpenMktVal(Long fundId, Integer bizDate, Integer serverId, boolean filterHgtProfit) {
        List<StkAsset> stkAssets = stkAssetService.getStkAsset(fundId, bizDate, serverId);
        Map<String, Double> stkAssetOpenMktVal = new HashMap<>();
        boolean dayProfitCalFeeFlag = nodeConfigService.getDayProfitCalFeeFlag();
        for (StkAsset stkAsset : stkAssets) {
            String market = stkAsset.getMarket();
            if (BusinessUtil.isGgtMarket(market) && filterHgtProfit) {
                continue;
            }
            Double lastMktVal = stkAssetService.getMktVal(stkAsset, dayProfitCalFeeFlag);
            stkAssetOpenMktVal.put(stkAsset.getStkAssetKey(), lastMktVal);
        }
        return stkAssetOpenMktVal;
    }


}
