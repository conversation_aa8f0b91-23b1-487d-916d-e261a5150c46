package com.eastmoney.common.sysEnum;

public enum TaskStatusEnum {
    STATUS_SUCCESS(0, "执行成功"),
    STATUS_EXECUTING(1, "正在执行"),
    STATUS_FAILED(2, "执行失败");


    private Integer value;//枚举值
    private String name;//枚举中文名称

    TaskStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
