package com.eastmoney.common.entity;

import java.util.Objects;

/**
 * Created on 2016/3/1
 * 沪港通 成交流水
 *
 * <AUTHOR>
 */
public class HgtMatch extends BaseMatch {
    private Double clearAmt_rmb;

    public Double getClearAmt_rmb() {
        return clearAmt_rmb;
    }

    public void setClearAmt_rmb(Double clearAmt_rmb) {
        this.clearAmt_rmb = clearAmt_rmb;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HgtMatch)) return false;
        if (!super.equals(o)) return false;
        HgtMatch hgtMatch = (HgtMatch) o;
        return Objects.equals(clearAmt_rmb, hgtMatch.clearAmt_rmb);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), clearAmt_rmb);
    }
}
