package com.eastmoney.service.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.eastmoney.common.annotation.RedisCache;
import com.eastmoney.datacenter.redis.client.RedisProxy;
import com.eastmoney.service.cache.NodeConfigService;
import com.eastmoney.service.service.CommonService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/12 16:04
 */
@Aspect
@Component
public class RedisCacheAspect {
    public static final SpelExpressionParser EXPRESSION_PARSER = new SpelExpressionParser();

    private static final Random RANDOM = new Random();
    private static final Logger logger = LoggerFactory.getLogger(RedisCacheAspect.class);
    @Autowired
    private RedisProxy redisProxy;
    @Autowired
    private NodeConfigService nodeConfigService;
    @Autowired
    private CommonService commonService;

    private static Type buildType(List<Type> types) {
        ParameterizedTypeImpl beforeType = null;
        for (int i = types.size() - 1; i > 0; i--) {
            beforeType = new ParameterizedTypeImpl(new Type[]{beforeType == null
                    ? types.get(i) :
                    beforeType}, null, types.get(i - 1));
        }
        return beforeType;
    }

    @Around("@annotation(redisCache)")
    public Object cache(ProceedingJoinPoint joinPoint, RedisCache redisCache) throws Throwable {
        logger.debug("redis缓存方法切面进入:{}", joinPoint);
        if (!commonService.isInRedisCacheTimeSection(nodeConfigService.getRedisCacheFlag())) {
            return joinPoint.proceed();
        }
        Object data = null;
        try {
            // 获取方法参数名称
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String redisKey = getRedisKey(signature.getParameterNames(), joinPoint.getArgs(), redisCache.keyGenerator());
            long beginGetTime = System.currentTimeMillis();
            String jsonStr = redisProxy.get(redisKey);
            printRedisCostTime(beginGetTime, redisKey, "get");
            if (StringUtils.isNotEmpty(jsonStr)) {
                Class<?> returnType = method.getReturnType();
                if ((returnType.isAssignableFrom(List.class)) ||
                        (returnType.isAssignableFrom(Set.class))) { // 获取列表中的元素类型
                    Type genericReturnType = method.getGenericReturnType();
                    if (genericReturnType instanceof ParameterizedType) {
                        ParameterizedType parameterizedType = (ParameterizedType) genericReturnType;
                        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                        List<Type> types = new ArrayList<>(Arrays.asList(actualTypeArguments));
                        types.add(0, returnType);
                        return JSON.parseObject(jsonStr, buildType(types));
                    }
                }
                return JSON.parseObject(jsonStr, returnType);
            }
            // 如果缓存中没有数据放行后存入redis
            data = joinPoint.proceed();
            long expireSeconds = redisCache.expireSeconds();
            if (expireSeconds == 0){
                expireSeconds = ChronoUnit.SECONDS.between(LocalTime.now(), nodeConfigService.getRedisCacheEndTime());
                // 随机加上5分钟的误差
                expireSeconds = expireSeconds + RANDOM.nextInt(5 * 60);
            }
            if (expireSeconds > 0) {
                long beginSetTime = System.currentTimeMillis();
                redisProxy.setex(redisKey, (int) expireSeconds, JSON.toJSONString(data));
                printRedisCostTime(beginSetTime, redisKey, "set");
            }
        } catch (Throwable throwable) {
            throw new RuntimeException("redis切面异常" + throwable);
        }
        return data;
    }

    private String getRedisKey(String[] paramNames, Object[] args, String spelStr) {
        if (ArrayUtils.isEmpty(args)) {
            return spelStr;
        }
        EvaluationContext spelContext = new StandardEvaluationContext();
        for (int i = 0; i < paramNames.length; i++) {
            spelContext.setVariable(paramNames[i], args[i]);
        }
        return EXPRESSION_PARSER.parseExpression(spelStr).getValue(spelContext, String.class);
    }

    /**
     * 打印Redis耗时
     * @param beginTime
     * @param redisKey
     * @param operation
     */
    private void printRedisCostTime(long beginTime, String redisKey, String operation) {
        long spendTime = System.currentTimeMillis() - beginTime;
        if (spendTime > nodeConfigService.getRedisQueryTimeThreshold()) {
            logger.warn("redisKey[{}] {}耗时 {} 毫秒", redisKey, operation, spendTime);
        }
    }
}



