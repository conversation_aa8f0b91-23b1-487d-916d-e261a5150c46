package com.eastmoney.accessor.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * choice交易市场编码枚举类
 * <p>
 * Created by <PERSON><PERSON><PERSON>qi on 2017/10/16.
 */
public enum ChoiceMarketCodeEnum {
    /**
     * 沪A
     */
    SZ_A("069001001", "1"),
    /**
     * 深A
     */
    SH_A("069001002", "0"),
    /**
     * 股转A
     */
    STOCK_A("069001004", "6"),
    /**
     * 北交所
     */
    BJ_A("069001017", "B"),
    /**
     * 沪港通
     */
    HGT("002", "5"),
    /**
     * 深港通
     */
    SGT("004", "S");
    /**
     * choice代码
     */
    private String choiceCode;
    /**
     * 交易代码
     */
    private String market;

    ChoiceMarketCodeEnum(String choiceCode, String market) {
        this.choiceCode = choiceCode;
        this.market = market;
    }

    public String getChoiceCode() {
        return choiceCode;
    }

    public String getMarket() {
        return market;
    }

    /**
     * chocieCode -> market
     */
    private static Map<String, String> choiceToMarketMap = new HashMap<>();

    static {
        ChoiceMarketCodeEnum[] choiceMarketCodeEnumList = ChoiceMarketCodeEnum.values();
        for (ChoiceMarketCodeEnum choiceMarketCodeEnum : choiceMarketCodeEnumList) {
            choiceToMarketMap.put(choiceMarketCodeEnum.getChoiceCode(), choiceMarketCodeEnum.getMarket());
        }
    }

    public static boolean containsChoiceMarket(String choiceCode) {
        return choiceToMarketMap.containsKey(choiceCode);
    }

    /**
     * 基于choice代码 获取交易市场类型
     *
     * @param choiceCode
     * @return
     */
    public static String getMarketByChoice(String choiceCode) {
        return choiceToMarketMap.get(choiceCode);
    }
}
