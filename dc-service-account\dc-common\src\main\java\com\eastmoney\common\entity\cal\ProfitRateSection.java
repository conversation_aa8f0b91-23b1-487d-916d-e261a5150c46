package com.eastmoney.common.entity.cal;

import com.eastmoney.common.entity.BaseEntityCal;

import java.util.Date;

/**
 * Created by robin on 2016/7/18.
 * 收益率时间分片表
 * <AUTHOR>
 */
public class ProfitRateSection extends BaseEntityCal {

    private Date euTime;
    private Long fundId;//资金账号
    private Double profitRate;//今日收益率
    private String unit;//单位
    private Integer rankIndex;//收益率排名
    private Double rankPercent;//收益率排名百分比
    private int bakBizDate; //最新计算数据前一天日期
    private int indexDate; //对应查询周期的起始时间

    public Date getEuTime() {
        return euTime;
    }

    public void setEuTime(Date euTime) {
        this.euTime = euTime;
    }

    public int getIndexDate() {
        return indexDate;
    }

    public void setIndexDate(int indexDate) {
        this.indexDate = indexDate;
    }

    public int getBakBizDate() {
        return bakBizDate;
    }

    public void setBakBizDate(int bakBizDate) {
        this.bakBizDate = bakBizDate;
    }
    public Long getFundId() {
        return fundId;
    }

    public Double getProfitRate() {
        return profitRate;
    }

    public String getUnit() {
        return unit;
    }

    public Integer getRankIndex() {
        return rankIndex;
    }

    public Double getRankPercent() {
        return rankPercent;
    }

    public void setFundId(Long fundId) {
        this.fundId = fundId;
    }

    public void setProfitRate(Double profitRate) {
        this.profitRate = profitRate;
    }

    public void setUnit(String unit) {
        this.unit = unit.trim();
    }

    public void setRankIndex(Integer rankIndex) {
        this.rankIndex = rankIndex;
    }

    public void setRankPercent(Double rankPercent) {
        this.rankPercent = rankPercent;
    }
}
